# Farmak FE

Farmak project

## Getting Started

This project uses npm/vite. If you don't have it installed, you can follow the instructions in the [Nodejs Documentation](https://nodejs.org/en/download/package-manager).

- Recommended version of Node 20+

```bash
npm run dev # local deployment
npm run build # production deployment
```

Preview [https://farmak.az-hosting.cz/](https://farmak.az-hosting.cz/)
-- login: farmak, password: farmak1?!? -> Preview waiting for BE production

### Contribution Notation Standard

- feat: A new feature
- fix: A bug fix
- docs: Documentation-only changes
- style: Changes that do not affect the meaning of the code (white-space, formatting, missing semi-colons, etc.)
- refactor: A code change that neither fixes a bug nor adds a feature
- perf: A code change that improves performance
- test: Adding missing tests
- chore: Changes to the build process or auxiliary tools and libraries such as documentation generation

### Branching

- Create a new branch for every task.
- After the feature is pushed to the server, a merge request should be made.
- After the branch is merged, the branch should be deleted.

Each ticket should have its own branch in this form:

- Ticket project: **_development_**
- Ticket number: **_1_**
- Ticket name: **_Create model for payments_**

Ticket notation form: **_DEV-2-create-model-for-payments_**

Tickets should be connected with associated tickets in Jira Software.

For branch creation - `git checkout -b DEV-892-name-of-your-ticket-in-Jira`

For commit - `git commit -m "DEV-892 <message>"`
