{"name": "farmak", "version": "1.0.0", "private": true, "author": "quantify.sk", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "build-stage": "vue-tsc --noEmit && vite build --base=/vue/stage/", "build-prod": "vue-tsc --noEmit && vite build --base=/vue/", "preview": "vite preview --port 5050", "typecheck": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix .gitignore", "format": "prettier .  --write"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@fontsource/inter": "^5.1.1", "@fontsource/poppins": "^5.1.1", "@fontsource/public-sans": "^5.1.2", "@fontsource/roboto": "^5.1.1", "@gitgraph/js": "^1.4.0", "@img-comparison-slider/vue": "^8.0.0", "@tinymce/tinymce-vue": "^6.1.0", "@uppy/core": "^4.4.2", "@uppy/dashboard": "^4.3.2", "@uppy/drag-drop": "^4.1.1", "@uppy/file-input": "^4.1.1", "@uppy/locales": "^4.5.1", "@uppy/progress-bar": "^4.2.1", "@uppy/status-bar": "^4.1.2", "@uppy/vue": "^2.1.1", "@uppy/xhr-upload": "^4.3.3", "@vueuse/core": "^10.11.0", "ant-design-vue": "^4.2.6", "apexcharts": "4.5.0", "axios": "1.8.4", "axios-mock-adapter": "2.1.0", "date-fns": "4.1.0", "jwt-decode": "^4.0.0", "lodash": "4.17.21", "moment": "^2.30.1", "pinia": "2.1.7", "remixicon": "4.6.0", "tinymce": "^7.2.2", "vee-validate": "4.15.0", "vite-plugin-vuetify": "2.1.0", "vue": "3.4.34", "vue-draggable-next": "^2.2.1", "vue-i18n": "11.1.2", "vue-router": "4.5.0", "vue-tabler-icons": "2.21.0", "vue3-apexcharts": "1.5.3", "vue3-easy-data-table": "1.5.47", "vue3-marquee": "^4.2.2", "vue3-perfect-scrollbar": "^2.0.0", "vuetify": "^3.7.14", "yup": "1.6.1"}, "devDependencies": {"@mdi/font": "7.4.47", "@types/lodash": "^4.17.15", "@types/node": "20.14.12", "@vitejs/plugin-vue": "5.2.1", "less": "^4.2.0", "sass": "1.77.8", "sass-loader": "15.0.0", "typescript": "5.5.4", "vite": "6.2.0", "vue-cli-plugin-vuetify": "2.5.8", "vue-tsc": "2.0.29", "@eslint/js": "^9.21.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.21.0", "prettier": "^3.5.2", "eslint-config-prettier": "^10.0.2", "eslint-plugin-vue": "^9.32.0", "typescript-eslint": "^8.25.0"}}