tinymce.Resource.add(
  'tinymce.html-i18n.help-keynav.uk',
  '<h1>Початок роботи з навігацією за допомогою клавіатури</h1>\n' +
    '\n' +
    '<dl>\n' +
    '  <dt>Фокус на рядок меню</dt>\n' +
    '  <dd>Windows або Linux: Alt+F9</dd>\n' +
    '  <dd>macOS: &#x2325;F9</dd>\n' +
    '  <dt>Фокус на панелі інструментів</dt>\n' +
    '  <dd>Windows або Linux: Alt+F10</dd>\n' +
    '  <dd>macOS: &#x2325;F10</dd>\n' +
    '  <dt>Фокус на розділі "Нижній колонтитул"</dt>\n' +
    '  <dd>Windows або Linux: Alt+F11</dd>\n' +
    '  <dd>macOS: &#x2325;F11</dd>\n' +
    '  <dt>Фокус на сповіщення</dt>\n' +
    '  <dd>Windows або Linux: Alt+F12</dd>\n' +
    '  <dd>macOS: &#x2325;F12</dd>\n' +
    '  <dt>Фокус на контекстній панелі інструментів</dt>\n' +
    '  <dd>Windows, Linux або macOS: Ctrl+F9</dd>\n' +
    '</dl>\n' +
    '\n' +
    '<p>Навігація почнеться з першого елемента інтерфейсу користувача, який буде виділено або підкреслено в разі, якщо перший елемент знаходиться в\n' +
    '  шляху до елемента "Нижній колонтитул".</p>\n' +
    '\n' +
    '<h1>Навігація між розділами інтерфейсу користувача</h1>\n' +
    '\n' +
    '<p>Щоб перейти з одного розділу інтерфейсу користувача до наступного розділу, натисніть клавішу <strong>Tab</strong>.</p>\n' +
    '\n' +
    '<p>Щоб перейти з одного розділу інтерфейсу користувача до попереднього розділу, натисніть сполучення клавіш <strong>Shift+Tab</strong>.</p>\n' +
    '\n' +
    '<p>Порядок <strong>Вкладок</strong> цих розділів інтерфейсу користувача такий:</p>\n' +
    '\n' +
    '<ol>\n' +
    '  <li>Рядок меню</li>\n' +
    '  <li>Кожна група панелей інструментів</li>\n' +
    '  <li>Бічна панель</li>\n' +
    '  <li>Шлях до елементів у розділі "Нижній колонтитул"</li>\n' +
    '  <li>Кнопка перемикача "Кількість слів" у розділі "Нижній колонтитул"</li>\n' +
    '  <li>Посилання на брендинг у розділі "Нижній колонтитул"</li>\n' +
    '  <li>Маркер змінення розміру в розділі "Нижній колонтитул"</li>\n' +
    '</ol>\n' +
    '\n' +
    '<p>Якщо розділ інтерфейсу користувача відсутній, він пропускається.</p>\n' +
    '\n' +
    '<p>Якщо фокус навігації клавіатури знаходиться на розділі "Нижній колонтитул", але користувач не бачить видиму бічну панель, натисніть <strong>Shift+Tab</strong>,\n' +
    '  щоб перемістити фокус на першу групу панелі інструментів, а не на останню.</p>\n' +
    '\n' +
    '<h1>Навігація в межах розділів інтерфейсу користувача</h1>\n' +
    '\n' +
    '<p>Щоб перейти з одного елементу інтерфейсу користувача до наступного, натисніть відповідну клавішу <strong>зі стрілкою</strong>.</p>\n' +
    '\n' +
    '<p>Клавіші зі стрілками <strong>Ліворуч</strong> і <strong>Праворуч</strong></p>\n' +
    '\n' +
    '<ul>\n' +
    '  <li>переміщують між меню в рядку меню.</li>\n' +
    '  <li>відкривають вкладене меню в меню.</li>\n' +
    '  <li>переміщують користувача між кнопками в групі панелі інструментів.</li>\n' +
    '  <li>переміщують між елементами в шляху до елементів у розділі "Нижній колонтитул".</li>\n' +
    '</ul>\n' +
    '\n' +
    '<p>Клавіші зі стрілками <strong>Вниз</strong> і <strong>Вгору</strong></p>\n' +
    '\n' +
    '<ul>\n' +
    '  <li>переміщують між елементами меню в меню.</li>\n' +
    '  <li>переміщують між елементами в спливаючому меню панелі інструментів.</li>\n' +
    '</ul>\n' +
    '\n' +
    '<p>Клавіші <strong>зі стрілками</strong> переміщують фокус циклічно в межах розділу інтерфейсу користувача, на якому знаходиться фокус.</p>\n' +
    '\n' +
    '<p>Щоб закрити відкрите меню, відкрите вкладене меню або відкрите спливаюче меню, натисніть клавішу <strong>Esc</strong>.</p>\n' +
    '\n' +
    '<p>Якщо поточний фокус знаходиться на верхньому рівні певного розділу інтерфейсу користувача, натискання клавіші <strong>Esc</strong> також виконує вихід\n' +
    '  з навігації за допомогою клавіатури повністю.</p>\n' +
    '\n' +
    '<h1>Виконання елементу меню або кнопки панелі інструментів</h1>\n' +
    '\n' +
    '<p>Коли потрібний елемент меню або кнопку панелі інструментів виділено, натисніть клавіші <strong>Return</strong>, <strong>Enter</strong>,\n' +
    '  або <strong>Пробіл</strong>, щоб виконати цей елемент.</p>\n' +
    '\n' +
    '<h1>Навігація по діалоговим вікнам без вкладок</h1>\n' +
    '\n' +
    '<p>У діалогових вікнах без вкладок перший інтерактивний компонент приймає фокус, коли відкривається діалогове вікно.</p>\n' +
    '\n' +
    '<p>Переходьте між інтерактивними компонентами діалогового вікна, натискаючи клавіші <strong>Tab</strong> або <strong>Shift+Tab</strong>.</p>\n' +
    '\n' +
    '<h1>Навігація по діалоговим вікнам з вкладками</h1>\n' +
    '\n' +
    '<p>У діалогових вікнах із вкладками перша кнопка в меню вкладки приймає фокус, коли відкривається діалогове вікно.</p>\n' +
    '\n' +
    '<p>Переходьте між інтерактивними компонентами цієї вкладки діалогового вікна, натискаючи клавіші <strong>Tab</strong> або\n' +
    '  <strong>Shift+Tab</strong>.</p>\n' +
    '\n' +
    '<p>Щоб перейти на іншу вкладку діалогового вікна, перемістіть фокус на меню вкладки, а потім натисніть відповідну клавішу <strong>зі стрілкою</strong>,\n' +
    '  щоб циклічно переходити по доступним вкладкам.</p>\n'
);
