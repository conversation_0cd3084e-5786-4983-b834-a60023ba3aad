{"cSpell.language": "en,cs", "cSpell.words": ["Customizer", "kolunu", "Mgmt", "pinia", "Tabler", "Uplodedimages"], "editor.tabSize": 2, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "always", "source.fixAll.stylelint": "always"}, "editor.quickSuggestions": {"other": true, "comments": false, "strings": true}, "editor.defaultFormatter": "esbenp.prettier-vscode", "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "eslint.validate": ["vue", "javascript", "typescript"], "prettier.requireConfig": true, "files.autoSave": "onWindowChange", "javascript.validate.enable": false, "typescript.validate.enable": false, "files.eol": "\n", "typescript.preferences.importModuleSpecifier": "relative"}