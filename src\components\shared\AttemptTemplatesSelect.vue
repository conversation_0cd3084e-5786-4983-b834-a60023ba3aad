<template>
  <v-autocomplete
    v-model="selectedAttemptTemplatesState"
    v-model:search="attemptTemplateSelectOptions.search"
    hide-details
    rounded="sm"
    :items="attemptTemplateSelectOptions.results"
    variant="outlined"
    color="primary"
    label="Zadejte název šablony"
    single-line
    class="autocomplete"
    :no-data-text="'Žádn<PERSON> další políčka'"
    :slim="true"
    :loading="attemptTemplateSelectOptions.loading"
    item-title="template_name"
    item-value="attempt_template_id"
    v-bind="$attrs"
  >
    <template #chip>
      <v-chip
        label
        variant="tonal"
        color="primary"
        size="large"
        class="my-1 text-subtitle-1 font-weight-regular"
      ></v-chip>
    </template>

    <!-- <template v-slot:item="{ props, item }">
      <v-list-item v-bind="props" :title="''">
        <div v-if="!(item.raw instanceof AttemptTemplate)" class="player-wrapper pa-2">
          <h6 class="text-subtitle-1 mb-0">
            {{ item.title }}
          </h6>
        </div>
        <div v-else class="player-wrapper pa-2">
          <h6 class="text-subtitle-1 mb-0">
            {{ item.raw.template_name }} ({{ item.raw.attempt_template_id }}) -
            <v-chip color="success" v-if="item.raw.status === AttemptTemplateStatus.ACTIVE" size="small" label> Aktivní </v-chip>
            <v-chip color="warning" v-if="item.raw.status === AttemptTemplateStatus.DELETED" size="small" label> Neaktivní </v-chip>
          </h6>
          <small class="text-h6 text-lightText">
            Aktualizováno: {{ toLocale(item.raw.updated_at) }} | Vytvořeno:
            {{ toLocale(item.raw.created_at) }}
          </small>
        </div>
      </v-list-item>
    </template> -->

    <template #append>
      <slot name="append"></slot>
    </template>
  </v-autocomplete>
</template>

<script lang="ts" setup>
  import { AttemptTemplate, useAttemptTemplates } from '@/stores/attemptTemplates';
  import type { PaginatorRequestDataI } from '@/stores/projects';
  import { useDebounceFn, useVModel } from '@vueuse/core';
  import { onMounted, ref, watch, type PropType } from 'vue';

  const props = defineProps({
    attemptTemplates: {
      type: Array<AttemptTemplate>,
      required: false,
      default: () => []
    },
    modelValue: {
      type: [Number, undefined, null] as PropType<number | undefined | null>,
      required: true,
      default: () => undefined
    }
  });

  const attemptTemplateSelectOptions = ref<PaginatorRequestDataI<AttemptTemplate>>({
    loading: false,
    results: [],
    search: undefined,
    search_type: 'OR',
    search_columns: ['template_name'],
    totalItems: 0,
    options: {
      page: 1,
      rowsPerPage: 100,
      sortBy: ['status', 'project_template_id'],
      sortType: ['asc']
    },
    filterOptions: []
  });

  const attemptTemplatesStore = useAttemptTemplates();
  const selectedAttemptTemplatesState = useVModel(props, 'modelValue');

  const debouncedProjectUserSearch = useDebounceFn(() => {
    if (
      !attemptTemplateSelectOptions.value.loading &&
      attemptTemplateSelectOptions.value.search !== ''
    ) {
      attemptTemplatesStore.getAttemptTemplatesForSelect(attemptTemplateSelectOptions.value);
    }
  }, 350);

  onMounted(() => {
    attemptTemplateSelectOptions.value.results = props.attemptTemplates.filter(
      (attemptTemplate) => attemptTemplate instanceof AttemptTemplate
    ) as AttemptTemplate[];
  });

  watch(
    () => attemptTemplateSelectOptions.value.search,
    () => {
      debouncedProjectUserSearch();
    }
  );
</script>
