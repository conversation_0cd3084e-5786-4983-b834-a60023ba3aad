<script setup lang="ts">
  import { HomeOutlined, RightOutlined } from '@ant-design/icons-vue';
  import { computed, type PropType } from 'vue';

  export interface BreadcrumbI {
    title: string;
    disabled: boolean;
    href: string;
  }

  // const { t } = useI18n();

  const props = defineProps({
    title: {
      type: String,
      default: '',
      required: true
    },
    breadcrumbs: {
      type: Array as PropType<BreadcrumbI[]>,
      default: () => [],
      required: true
    },
    icon: String
  });

  const items = computed(() => {
    props.breadcrumbs.filter((breadcrumb) => {
      // breadcrumb.title = t('MainMenu.' + breadcrumb.title);

      return breadcrumb;
    });

    return props.breadcrumbs;
  });
</script>

// ===============================|| Theme Breadcrumb ||=============================== //
<template>
  <v-row class="page-breadcrumb mb-0 mt-n2">
    <v-col cols="12" md="12">
      <v-card elevation="0" variant="text">
        <v-row no-gutters class="align-center">
          <v-col sm="12">
            <v-breadcrumbs :items="items" class="text-h6 pa-1 font-weight-medium mb-0">
              <template #divider>
                <div class="d-flex align-center"><RightOutlined /></div>
              </template>
              <template #prepend>
                <router-link to="/projekty" class="text-lightText text-h6 text-decoration-none">
                  <HomeOutlined />
                </router-link>
                <div class="d-flex align-center px-2"><RightOutlined /></div>
              </template>
            </v-breadcrumbs>
            <h3 class="text-h3 mt-1 mb-0">{{ /* $t('MainMenu.' + props.title) */ props.title }}</h3>
          </v-col>
        </v-row>
      </v-card>
    </v-col>
  </v-row>
</template>

<style lang="scss">
  .page-breadcrumb {
    .v-toolbar {
      background: transparent;
    }
  }
</style>
