<template>
  <v-navigation-drawer
    v-model="showState"
    location="right"
    :width="drawerWidth"
    temporary
    :border="0"
    :elevation="0"
  >
    <v-card elevation="0" variant="outlined" class="withbg pageSize" :loading="loading">
      <template #title>
        <v-toolbar class="ma-0 pa-0" density="compact" color="white" dense flat tile>
          <v-toolbar-title>
            <h3 class="text-h4 mb-0 pb-0">
              Provedené změny &nbsp;
              <v-tooltip text="Obnovit data">
                <template #activator="{ props }">
                  <v-btn
                    v-bind="props"
                    icon
                    variant="text"
                    rounded="sm"
                    size="small"
                    :loading="loading"
                    @click.prevent="reloadLogs"
                  >
                    <ReloadOutlined :style="{ fontSize: '24px' }" class="text-lightText" />
                  </v-btn>
                </template>
              </v-tooltip>
            </h3>
          </v-toolbar-title>
          <v-spacer></v-spacer>

          <v-tooltip text="Zavřít">
            <template #activator="{ props }">
              <v-btn
                v-bind="props"
                icon
                variant="text"
                rounded="sm"
                color="error"
                size="small"
                @click.prevent="showState = false"
              >
                <CloseOutlined :style="{ fontSize: '24px' }" class="text-light Text" />
              </v-btn>
            </template>
          </v-tooltip>
        </v-toolbar>
      </template>

      <v-card-text :class="responsiveCardClass">
        <v-row v-if="options.totalItems > options.options.rowsPerPage">
          <v-col cols="12" md="7">
            <v-pagination
              v-model="options.options.page"
              :length="Math.floor(options.totalItems / options.options.rowsPerPage)"
            ></v-pagination>
          </v-col>
          <v-col cols="12" md="5" class="text-right">
            <div class="d-flex justify-end align-center gap-2 h-100">
              <v-menu transition="scale-transition">
                <template #activator="{ props }">
                  <v-btn color="inherit" variant="text" v-bind="props">
                    {{ selectedRow.title }}
                    <template #append>
                      <ChevronDownIcon size="20" stroke-width="1.5" />
                    </template>
                  </v-btn>
                </template>

                <v-list>
                  <v-list-item v-for="(row, i) in rows" :key="i" :value="i">
                    <v-list-item-title @click="selectedRow = row">
                      {{ row.title }}
                    </v-list-item-title>
                  </v-list-item>
                </v-list>
              </v-menu>
            </div>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="12">
            <template v-if="changelog.length > 0">
              <v-list-item
                v-for="(item, index) in changelog.filter((c) => c.changes.length > 0)"
                :key="index"
                class="mb-5 pa-0"
              >
                <template #default>
                  <v-row>
                    <v-col cols="12">
                      <span class="text-h5">{{ item.logName }}</span>
                      <br />
                      <span class="text-h6">{{ item.description }}</span>
                    </v-col>
                  </v-row>

                  <LogItem v-for="change of item.changes" :key="change.data" :item="change">
                    {{ change.data }}
                  </LogItem>
                </template>
              </v-list-item>
            </template>
            <NotFoundItem v-else>
              <template #notFound>Žádné změny nejsou k dispozici</template>
            </NotFoundItem>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-navigation-drawer>
</template>

<script lang="ts" setup>
  import { responsiveCardClass } from '@/config';
  import { ChangelogState, useLogsStore, type ChangelogItemI } from '@/stores/logs';
  import { toTimeLocale } from '@/utils/locales';
  import { CloseOutlined, ReloadOutlined } from '@ant-design/icons-vue';
  import { useVModel } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue';
  import LogItem from './LogItem.vue';
  import NotFoundItem from './NotFoundItem.vue';

  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    }
  });

  const logsStore = useLogsStore();
  const { loading, options } = storeToRefs(logsStore);

  const showState = useVModel(props, 'show');
  const windowWidth = ref(window.innerWidth);
  const drawerWidth = ref(400);

  const isMobile = computed(() => {
    return windowWidth.value <= 960;
  });

  function updateWindowWidth() {
    windowWidth.value = window.innerWidth;
    if (isMobile.value) {
      drawerWidth.value = windowWidth.value;
    } else {
      drawerWidth.value = windowWidth.value * 0.5 > 500 ? windowWidth.value * 0.5 : 500;
    }
  }

  onMounted(async () => {
    updateWindowWidth();
    window.addEventListener('resize', updateWindowWidth);

    await logsStore.getLogs();
    prepareChangelogDataFromOptionsResult();
  });

  const prepareChangelogDataFromOptionsResult = () => {
    return options.value.results.map((item) => {
      return {
        logName: `${tableNameDictionary(item.table_name)} (${item.table_primary_key})`,
        description: `Čas změny: ${toTimeLocale(item.timestamp)}, uživatel: ${item.user ? item.user.getName : item.user_id}`,
        changes: generateChangeLog(item.old_data, item.new_data)
      };
    });
  };

  function generateChangeLog(
    oldData: { [key: string]: unknown },
    newData: { [key: string]: unknown }
  ): ChangelogItemI[] {
    const changeLog: ChangelogItemI[] = [];

    const allKeys = new Set([...Object.keys(oldData ?? {}), ...Object.keys(newData ?? {})]);
    const completeOldData: { [key: string]: unknown } = {};
    const completeNewData: { [key: string]: unknown } = {};

    allKeys.forEach((key) => {
      completeOldData[key] = oldData?.[key] ?? null;
      completeNewData[key] = newData?.[key] ?? null;
    });

    for (const key of allKeys) {
      let state: ChangelogState = ChangelogState.EDIT;
      const oldValue = completeOldData[key] !== null ? completeOldData[key] : '/';
      const newValue = completeNewData[key] !== null ? completeNewData[key] : '/';

      if (oldValue === '/' && newValue !== '/') {
        state = ChangelogState.CREATE;
      }

      if (oldValue !== '/' && newValue === '/') {
        state = ChangelogState.DELETE;
      }
      if (
        oldValue !== newValue &&
        key !== 'updated_at' &&
        key !== 'created_at' &&
        key !== 'search_vector' &&
        key !== 'reaction_procedure' &&
        key !== 'conclusion_text'
      ) {
        changeLog.push({
          data: `${keysDictionaryWithState(key, state)}: ${formatValue(oldValue)} -> ${formatValue(newValue)}`,
          state: state
        });
      }
      if ((key === 'reaction_procedure' || key === 'conclusion_text') && oldValue !== newValue) {
        changeLog.push({
          data: `${keysDictionaryWithState(key, state)}: ${stripHtmlTags(oldValue as string)} -> ${stripHtmlTags(newValue as string)}`,
          state: state
        });
      }
    }
    return changeLog;
  }

  function tableNameDictionary(name: string): string {
    const dictionary: { [name: string]: string } = {
      project: 'Projekt',
      project_chemical: 'Chemikálie',
      department: 'Oddělení',
      chapter: 'Kapitola',
      experiment: 'Experiment',
      sample: 'Vzorek',
      file: 'Soubor',
      investigation: 'Šetření',
      attempt: 'Pokus',
      message: 'Zpráva',
      project_department: 'Oddělení',
      user_project: 'Uživatel',
      experiment_template: 'Šablona Experimentu',
      attempt_template: 'Šablona Pokusu',
      investigation_template: 'Šablona Šetření',
      conclusion: 'Závěr',
      parametr: 'Parameter',
    };
    return dictionary[name] ?? name;
  }

  function stripHtmlTags(input: string): string {
    return input.replace(/<\/?[^>]+(>|$)/g, '').trim();
  }

  function keysDictionary(key: string): string {
    const dictionary: { [key: string]: string } = {
      created_at: 'Vytvořeno',
      updated_at: 'Aktualizováno',
      deleted_at: 'Smazáno',
      user_id: 'Uživatel',
      log_id: 'ID Logu',
      timestamp: 'Čas změny',
      project_id: 'Projekt',
      name: 'Název',
      status: 'Stavu',
      search_vector: 'Vyhledávací vektor',
      first_name: 'Jméno',
      last_name: 'Příjmení',
      name_shortcut: 'Zkratka jména',
      user_email: 'Email uživatele',
      password_hash: 'Hash hesla',
      project_chemical_id: 'ID chemikálie projektu',
      csa: 'CSA',
      shortcut: 'Zkratka',
      density: 'Hustota',
      molar_mass: 'Molární hmotnost',
      api_id: 'API ID',
      error: 'Chyba',
      message: 'Zpráva',
      status_code: 'Status kód',
      current_page: 'Aktuální stránka',
      items_per_page: 'Počet položek na stránku',
      total_pages: 'Celkem stránek',
      total_items: 'Celkem položek',
      items: 'Položky',
      table_name: 'Název tabulky',
      table_primary_key: 'Primární klíč tabulky',
      old_data: 'Staré údaje',
      new_data: 'Nové údaje',
      system_permission_id: 'ID systémového oprávnění',
      form_id: 'ID Formuláře',
      batch_number_id: 'ID Šarže',
      chapter_id: 'ID Kapitoly',
      form_name: 'Název Formuláře',
      form_type: 'Typ Formuláře',
      batch_description: 'Popis Šarže',
      experiment_id: 'ID Experimentu',
      conclusion_id: 'ID Závěru',
      conclusion_text: 'Text Závěru',
      reaction_procedure: 'Tabulka Postupu Reakce',
      product_weight: 'Hmotnost Produktu',
      yield_percentage: 'Výnos v %',
      content_percentage: 'Obsah v %',
      reaction_scheme_file_id: 'Souboru s Reakčním Schématem',
      file_id: 'ID Souboru',
      file_name: 'Název Souboru',
      file_type: 'Typ Souboru',
      file_path: 'Cesta k Souboru',
      chapter_title: 'Název Kapitoly',
      editable: 'Editovatelné',
      department_id: 'ID Oddělení',
      template_name: 'Název Šablony',
      message_id: 'ID Zprávy',
      message_text: 'Text Zprávy',
      apparatus: 'Aparatura',
      attempt_id: 'ID Pokusu',
      problem_description: 'Popis Problému',
      investigation_in_production: 'Šetření v Produkci',
      investigation_in_laboratory: 'Šetření v Laboratoři',
      recommendations: 'Doporučení',
      investigation_id: 'ID Šetření',
      affected_batch_number: 'Ovlivněná Šarže',
      impact_on_quality: 'Vliv na Kvalitu',
      impact_on_yield: 'Vliv na Výtěžnost',
      sample_id: 'ID Vzorku',
      analytical_request_id: 'ID Analytického Požadavku',
      technique_id: 'ID Techniky',
      sample_number: 'Číslo Vzorku',
      sequence_name: 'Název Sekvence',
      method_name: 'Název Metody',
      preparation_of_standard_and_sample: 'Příprava Standardu a Vzorku',
      note_or_specification: 'Poznámka nebo Specifikace',
      technique_notes: 'Poznámky k Technice',
      result: 'Výsledek',
      analysis_status: 'Stav Analýzy',
      type: 'Typ',
      investigation_template_id: 'ID Šablony Šetření',
      project_template_id: 'ID Šablony Projektu',
      attempt_template_id: 'ID Šablony Pokusu'
    };
    return dictionary[key] ?? key;
  }

  function keysDictionaryWithState(key: string, state: ChangelogState): string {
    const baseKey = keysDictionary(key);
    switch (state) {
      case ChangelogState.CREATE:
        return `Vytvoření ${baseKey}`;
      case ChangelogState.DELETE:
        return `Smazání ${baseKey}`;
      case ChangelogState.EDIT:
        return `Úprava ${baseKey}`;
      default:
        return baseKey;
    }
  }

  function formatValue(value: unknown): string {
    if (typeof value === 'string') {
      if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d{3})?Z?$/.test(value)) {
        const date = new Date(value);
        if (!isNaN(date.getTime())) {
          return toTimeLocale(date);
        }
      }
      return value.charAt(0).toUpperCase() + value.slice(1);
    }
    return value !== null && value !== undefined ? String(value) : '/';
  }

  onBeforeUnmount(() => {
    window.removeEventListener('resize', updateWindowWidth);
  });

  watch(
    () => [options.value.options.page, options.value.options.rowsPerPage],
    () => {
      logsStore.getLogs();
    }
  );

  watch(
    () => [options.value.options.rowsPerPage],
    () => {
      options.value.options.page = 1;
      logsStore.getLogs();
    }
  );

  const reloadLogs = () => {
    logsStore.getLogs();
  };

  const selectedRow = ref({ title: '25 Změn na stránku', value: 25 });
  const rows = ref([
    { title: '25 Změn na stránku', value: 25 },
    { title: '50 Změn na stránku', value: 50 },
    { title: '100 Změn na stránku', value: 100 }
  ]);

  watch(selectedRow, () => {
    if (options.value.options) {
      options.value.options.rowsPerPage = selectedRow.value.value;
    }
  });

  const changelog = computed(() => prepareChangelogDataFromOptionsResult());
</script>

<style scoped>
  :deep(.v-toolbar) {
    background-color: transparent;
  }

  :deep(.v-toolbar-title) {
    margin: 0;
    padding: 0;
  }

  :deep(.v-pagination__list) {
    justify-content: start;
  }
</style>
