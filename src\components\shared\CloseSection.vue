<script setup lang="ts">
  import { useVModel } from '@vueuse/core';
  import { ref, computed } from 'vue';
  import { storeToRefs } from 'pinia';
  import { useProjectsStore } from '@/stores/projects';
  import { useChaptersStore } from '@/stores/chapters';
  import { isAllowed } from '@/utils/directive/isAllowed';
  import { notification } from 'ant-design-vue';
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import { el } from 'vuetify/locale';

  const projectStore = useProjectsStore();
  const chaptersStore = useChaptersStore();

  const ConfirmRef = ref<InstanceType<typeof ConfirmDlg> | null>(null);
  const projectsStore = useProjectsStore();
  const { closeDependencies: projectCloseDependencies } = storeToRefs(projectsStore);
  const { closeDependencies: chapterCloseDependencies } = storeToRefs(chaptersStore);
  const emits = defineEmits(['update:show', 'update']);
  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    },
    id: {
      type: Number,
      required: true
    },
    whatToClose: {
      type: String,
      required: true
    }
  });
  const showState = useVModel(props, 'show');
  const CreateTagForm = ref();
  async function submitFormToValidate() {
    if (!isAllowed(['request_brute_force_closure'])) {
      const nameOfPermission = 'request_brute_force_closure';
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nemáte oprávnění pro uzavření projektu.' + ' ' + nameOfPermission + '.'
      });
    } else {
      if (
        await ConfirmRef.value?.open(
          'Potvrzení',
          'Chcete i tak pokračovat? Dojde k uzavření všech návaznostní.',
          { color: 'error', notclosable: true, zIndex: 2400 }
        )
      ) {
        if (props.whatToClose === 'project') {
          const res = await projectStore.closeProject(props.id, true);
          if (res) emits('update');
        } else if (props.whatToClose === 'department') {
          const res = await projectStore.closeDepartment(props.id, true);
          if (res) emits('update');
        } else if (props.whatToClose === 'chapter') {
          const res = await chaptersStore.closeChapter(props.id, true);
          if (res) emits('update');
        } else if (props.whatToClose === 'deactivate') {
          const res = await projectStore.deactivateProject(props.id, true);
          if (res) emits('update');
        }
      }
    }
  }
  const buttonText = computed(() => {
    if (props.whatToClose === 'project') {
      return 'Uzavřít projekt';
    } else if (props.whatToClose === 'department') {
      return 'Uzavřít oddělení';
    } else if (props.whatToClose === 'chapter') {
      return 'Uzavřít kapitolu';
    } else if (props.whatToClose === 'deactivate') {
      return 'Deaktivovat';
    } else {
      return 'Uzavřít';
    }
  });
</script>
<template>
  <v-dialog v-model="showState" class="close-modal" style="z-index: 1010">
    <v-card>
      <v-form ref="CreateTagForm" class="createTagForm" @submit.prevent="submitFormToValidate">
        <v-card-title class="pa-5">
          <span class="text-h5">Neuzavřené návaznosti</span>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row
            v-if="
              (props.whatToClose === 'project' || props.whatToClose === 'deactivate') &&
              projectCloseDependencies
            "
          >
            <template
              v-if="
                projectCloseDependencies.missing_dependencies?.departments &&
                projectCloseDependencies.missing_dependencies?.departments.length > 0
              "
            >
              <v-col cols="12" md="4">
                <h5 class="text-h5 mb-4 pb-0">Oddělení</h5>
                <v-list-item
                  v-for="(item, index) in projectCloseDependencies.missing_dependencies.departments"
                  :key="index"
                  class="mb-4 pa-0"
                >
                  <template #default>
                    <v-row>
                      <v-col cols="12">
                        <v-label class="mb-0">Název oddělení:</v-label>
                        <div class="text-h6 mb-2">{{ item.getName }} ({{ item.shortcut }})</div>
                      </v-col>
                    </v-row>
                  </template>
                </v-list-item>
              </v-col>
            </template>
            <template
              v-if="
                projectCloseDependencies.missing_dependencies?.chapters &&
                projectCloseDependencies.missing_dependencies?.chapters.length > 0
              "
            >
              <v-col cols="12" md="4">
                <h3 class="text-h5 mb-4 pb-0">Kapitoly</h3>
                <v-list-item
                  v-for="(item, index) in projectCloseDependencies.missing_dependencies.chapters"
                  :key="index"
                  class="mb-0 pa-0"
                >
                  <template #default>
                    <v-row>
                      <v-col cols="12">
                        <v-label class="mb-0">Název kapitoly:</v-label>
                        <div class="text-h6 mb-2">{{ item.chapter_title }}</div>
                      </v-col>
                    </v-row>
                  </template>
                </v-list-item>
              </v-col>
            </template>
            <template
              v-if="
                projectCloseDependencies.missing_dependencies?.form &&
                projectCloseDependencies.missing_dependencies?.form.length > 0
              "
            >
              <v-col cols="12" md="4">
                <h3 class="text-h5 mb-4 pb-0">Formuláře</h3>
                <v-list-item
                  v-for="(item, index) in projectCloseDependencies.missing_dependencies.form"
                  :key="index"
                  class="mb-4 pa-0"
                >
                  <template #default>
                    <v-row>
                      <v-col cols="12">
                        <v-label class="mb-0">Název formuláře:</v-label>
                        <div class="text-h6 mb-2">
                          {{ item.form_name }}
                          <span v-if="item.batch_description && item.batch_description.length > 0">
                            ({{ item.batch_description }})
                          </span>
                        </div>
                      </v-col>
                    </v-row>
                  </template>
                </v-list-item>
              </v-col>
            </template>
          </v-row>
          <v-row v-if="props.whatToClose === 'department' && projectCloseDependencies">
            <template
              v-if="
                projectCloseDependencies.missing_dependencies?.chapters &&
                projectCloseDependencies.missing_dependencies?.chapters.length > 0
              "
            >
              <v-col cols="12" md="6">
                <h3 class="text-h5 mb-4 pb-0">Kapitoly</h3>
                <v-list-item
                  v-for="(item, index) in projectCloseDependencies.missing_dependencies.chapters"
                  :key="index"
                  class="mb-0 pa-0"
                >
                  <template #default>
                    <v-row>
                      <v-col cols="12">
                        <v-label class="mb-0">Název kapitoly:</v-label>
                        <div class="text-h6 mb-2">{{ item.chapter_title }}</div>
                      </v-col>
                    </v-row>
                  </template>
                </v-list-item>
              </v-col>
            </template>
            <template
              v-if="
                projectCloseDependencies.missing_dependencies?.form &&
                projectCloseDependencies.missing_dependencies?.form.length > 0
              "
            >
              <v-col cols="12" md="6">
                <h3 class="text-h5 mb-4 pb-0">Formuláře</h3>
                <v-list-item
                  v-for="(item, index) in projectCloseDependencies.missing_dependencies.form"
                  :key="index"
                  class="mb-4 pa-0"
                >
                  <template #default>
                    <v-row>
                      <v-col cols="12">
                        <v-label class="mb-0">Název formuláře:</v-label>
                        <div class="text-h6 mb-2">
                          {{ item.form_name }}
                          <span v-if="item.batch_description && item.batch_description.length > 0">
                            ({{ item.batch_description }})
                          </span>
                        </div>
                      </v-col>
                    </v-row>
                  </template>
                </v-list-item>
              </v-col>
            </template>
          </v-row>
          <v-row v-if="props.whatToClose === 'chapter' && chapterCloseDependencies">
            <template
              v-if="
                chapterCloseDependencies.missing_dependencies?.forms &&
                chapterCloseDependencies.missing_dependencies?.forms.length > 0
              "
            >
              <v-col cols="12" md="12">
                <h3 class="text-h5 mb-4 pb-0">Formuláře</h3>
                <v-list-item
                  v-for="(item, index) in chapterCloseDependencies.missing_dependencies.forms"
                  :key="index"
                  class="mb-4 pa-0"
                >
                  <template #default>
                    <v-row>
                      <v-col cols="12">
                        <v-label class="mb-0">Název formuláře:</v-label>
                        <div class="text-h6 mb-2">
                          {{ item.form_name }}
                          <span v-if="item.batch_description && item.batch_description.length > 0">
                            ({{ item.batch_description }})
                          </span>
                        </div>
                      </v-col>
                    </v-row>
                  </template>
                </v-list-item>
              </v-col>
            </template>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" variant="text" @click="showState = false">Zavřít</v-btn>
          <v-btn color="primary" variant="flat" type="submit">{{ buttonText }}</v-btn>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
  <ConfirmDlg ref="ConfirmRef" />
</template>
