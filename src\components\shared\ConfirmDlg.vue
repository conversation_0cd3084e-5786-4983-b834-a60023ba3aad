<!-- eslint-disable vue/no-v-text-v-html-on-component -->
<template>
  <v-dialog
    v-model="dialog"
    :max-width="options.width"
    :style="{ zIndex: options.zIndex }"
    :persistent="options.notclosable"
  >
    <v-card>
      <v-toolbar dark :color="options.color" dense flat>
        <v-toolbar-title>
          {{ title }}
        </v-toolbar-title>
      </v-toolbar>
      <v-card-text v-show="!!message" class="pa-4" v-html="message"></v-card-text>
      <v-card-actions class="pt-3">
        <v-spacer></v-spacer>
        <v-btn v-if="!options.noconfirm" text @click.prevent="cancel(true)">Zrušit akci</v-btn>
        <v-btn color="primary" variant="flat" outlined @click.prevent="agree">Pokračovat</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
  export default {
    data() {
      return {
        dialog: false,
        resolve: null,
        reject: null,
        message: null,
        title: null,
        options: {
          color: 'error',
          width: 400,
          zIndex: 200,
          noconfirm: false,
          notclosable: false
        }
      };
    },

    methods: {
      open(title, message, options) {
        this.dialog = true;
        this.title = title;
        this.message = message;
        this.options = Object.assign(this.options, options);

        return new Promise((resolve, reject) => {
          this.resolve = resolve;
          this.reject = reject;
        });
      },
      agree() {
        this.resolve(true);
        this.dialog = false;
      },
      cancel(force = false) {
        if (!this.options.notclosable || force) {
          this.resolve(false);
          this.dialog = false;
        }
      }
    }
  };
</script>
