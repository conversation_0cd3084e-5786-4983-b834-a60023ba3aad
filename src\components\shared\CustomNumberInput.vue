<script lang="ts" setup>
  import { useVModel } from '@vueuse/core';
  import { onMounted, useAttrs } from 'vue';

  const attrs = useAttrs();

  const props = defineProps<{
    modelValue: string | number | undefined;
    onlyPreview?: boolean;
    isRequired?: boolean;
  }>();

  const modelValueState = useVModel(props, 'modelValue');
  const onlyPreview = props.onlyPreview ?? false;
  const isRequired = props.isRequired ?? false;

  function validateInput(event: Event | null, value: number | undefined = undefined) {
    if (onlyPreview) return;

    if (value === undefined && event === null) {
      return;
    }

    let inputValue: string = event
      ? ((event?.target as HTMLInputElement)?.value ?? '0')
      : (value?.toString() ?? '0');
    // Replace comma with dot for decimal delimiter
    inputValue = inputValue.replace(',', '.');
    // Remove any non-numeric characters except dot
    inputValue = inputValue.replace(/[^0-9.]/g, '');
    // Restrict to maximum 5 decimal places
    const decimalIndex = inputValue.indexOf('.');
    if (decimalIndex !== -1 && inputValue.length - decimalIndex > 10) {
      inputValue = inputValue.slice(0, decimalIndex + 10);
    }

    const v = inputValue === '' ? undefined : parseFloat(inputValue);

    if (v && 'min' in attrs && v < parseFloat(attrs.min as string)) {
      value = parseFloat(attrs.min as string);
    }

    if (v && 'max' in attrs && v > parseFloat(attrs.max as string)) {
      value = parseFloat(attrs.max as string);
    }
    if (!v) {
      modelValueState.value = 0;
    } else {
      modelValueState.value = v;
    }
  }

  function validateNumber(value: number | undefined | string) {
    // Validate if the input is a valid number
    if (isRequired && modelValueState.value === undefined) {
      return 'Hodnota je povinná';
    }

    const numberValue = typeof value === 'string' ? Number(value) : value;
    //validateInput(null, typeof value === 'string' ? parseFloat(value) : value);
    if (numberValue && isNaN(numberValue)) {
      return 'Neplatné číslo';
    }

    if ('min' in attrs && numberValue && numberValue < parseFloat(attrs.min as string)) {
      return `Hodnota musí být větší než ${attrs.min}`;
    }

    if ('max' in attrs && numberValue && numberValue > parseFloat(attrs.max as string)) {
      return `Hodnota musí být menší než ${attrs.max}`;
    }

    return true;
  }

  onMounted(() => {
    validateNumber(modelValueState.value);
  });
</script>

<template>
  <v-text-field
    :value="modelValueState"
    type="number"
    v-bind="$attrs"
    hide-spin-buttons
    :rule="validateNumber"
    @change="validateInput"
  ></v-text-field>
</template>
