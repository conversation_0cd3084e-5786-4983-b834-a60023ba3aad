<template>
  <EasyDataTable
    v-if="newServerOptionsState"
    v-model:server-options="newServerOptionsState.options"
    table-class-name="customize-table"
    :theme-color="themeColor"
    :empty-message="'Data nenalezena'"
    :rows-per-page-message="'Položek na stránku'"
    :rows-of-page-separator-message="'z'"
    buttons-pagination
    :fixed-header="true"
    :body-row-class-name="getRowClass"
    v-bind="$attrs"
  >
    <template v-for="(slot, key) in $slots" #[key]="scope">
      <slot :name="key" v-bind="scope" />
    </template>
  </EasyDataTable>
  <EasyDataTable
    v-else
    v-model:items-selected="itemsSelectedState"
    v-model:server-options="serverOptionsState"
    table-class-name="customize-table"
    :theme-color="themeColor"
    :empty-message="'Data nenalezena'"
    :rows-per-page-message="'<PERSON>ž<PERSON> na stránku'"
    :rows-of-page-separator-message="'z'"
    buttons-pagination
    :fixed-header="true"
    :body-row-class-name="getRowClass"
    v-bind="$attrs"
  >
    <template v-for="(slot, key) in $slots" #[key]="scope">
      <slot :name="key" v-bind="scope" />
    </template>
  </EasyDataTable>
</template>

<script lang="ts">
  import { themeColor } from '@/config';
  import type { PaginatorRequestDataI } from '@/stores/projects';
  import { useDebounceFn, useVModel } from '@vueuse/core';
  import { computed, watch } from 'vue';
  import type { ServerOptions } from 'vue3-easy-data-table';

  export default {
    inheritAttrs: false,
    props: {
      itemsSelected: {
        type: Array,
        required: false,
        default: () => undefined
      },
      serverOptions: {
        type: Object as () => ServerOptions,
        required: false,
        default: () => undefined
      },
      newServerOptions: {
        type: Object as () => PaginatorRequestDataI<unknown>,
        required: false,
        default: () => undefined
      },
      getRowClass: {
        type: Function,
        required: false,
        default: () => undefined
      }
    },
    emits: ['update:itemsSelected', 'update:serverOptions', 'update:newServerOptions', 'reload'],
    setup(props, emits) {
      const itemsSelectedState = useVModel(props, 'itemsSelected');
      const serverOptionsState = useVModel(props, 'serverOptions');
      const newServerOptionsState = useVModel(props, 'newServerOptions');

      watch(
        () => newServerOptionsState.value?.search,
        async () => {
          if (
            (newServerOptionsState.value?.search?.length ?? 0) > 3 ||
            newServerOptionsState.value?.search?.length === 0 ||
            newServerOptionsState.value?.search === undefined
          ) {
            debouncedLoadFromServer();
          }
        }
      );

      const debouncedLoadFromServer = useDebounceFn(() => {
        if (newServerOptionsState.value?.loading === false) {
          emits.emit('reload');
        }
      }, 350);

      const sortby = computed(() => newServerOptionsState.value?.options.sortBy);
      const sortType = computed(() => newServerOptionsState.value?.options.sortType);

      watch(
        [sortby, sortType],
        () => {
          debouncedLoadFromServer();
        },
        { deep: true }
      );

      watch(
        () => [
          newServerOptionsState.value?.search,
          newServerOptionsState.value?.options.page,
          newServerOptionsState.value?.options.rowsPerPage
        ],
        () => {
          debouncedLoadFromServer();
        }
      );

      return {
        themeColor,
        itemsSelectedState,
        serverOptionsState,
        newServerOptionsState
      };
    }
  };
</script>
