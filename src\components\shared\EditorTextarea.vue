<template>
  <div>
    <v-row v-if="showEditButton">
      <v-col cols="12 mb-3">
        <v-btn
          variant="flat"
          density="compact"
          color="secondary"
          @click.prevent="showEditButtonState = !showEditButtonState"
        >
          Přepnout (aktuální režim: {{ !showEditButtonState ? 'náhled' : 'editace' }})
        </v-btn>
      </v-col>
    </v-row>

    <Editor
      ref="editor"
      v-model="modelValueState"
      api-key="gpl"
      license-key="gpl"
      :init="{ ...simpleConfig(), ...config }"
      toolbar=""
      v-bind="$attrs"
    />
  </div>
</template>
<script lang="ts" setup>
  import { simpleConfig } from '@/utils/SetupTinyMCE';
  import Editor from '@tinymce/tinymce-vue';
  import { useVModel } from '@vueuse/core';
  import type { EditorManager, RawEditorOptions } from 'tinymce';
  import { ref, type Ref } from 'vue';

  const props = defineProps({
    modelValue: {
      type: String,
      default: ''
    },
    showEditButton: {
      type: Boolean,
      default: null
    },
    config: {
      type: Object as () => EditorManager & RawEditorOptions,
      required: false,
      default: undefined
    }
  });

  const editor: Ref<typeof Editor | null> = ref(null);
  const modelValueState = useVModel(props, 'modelValue');
  const showEditButtonState = useVModel(props, 'showEditButton');
</script>
