<template>
  <div class="editor-wrapper" :class="{ 'websocket-enabled': enableWebSocket }">
    <div v-if="enableWebSocket && isLockedByOther && !disabled" class="lock-overlay-other">
      <div class="lock-indicator">
        <v-icon size="small" :color="lockOwnerColor">mdi-lock</v-icon>
        <span class="lock-text">{{ lockOwnerName }}</span>
        <v-btn
          v-if="!hasRequestedAccess"
          size="x-small"
          variant="outlined"
          :color="lockOwnerColor"
          class="ml-2"
          @click="requestAccess"
        >
          Požádat o přístup
        </v-btn>
        <v-chip v-else size="x-small" color="warning" variant="outlined" class="ml-2">
          Žádost odeslána
        </v-chip>
      </div>
    </div>

    <div
      v-if="enableWebSocket && isLockedByMe"
      class="editing-border"
      :style="{ borderColor: fieldBorderColor }"
    ></div>

    <div
      v-if="enableWebSocket && isLockedByOther"
      class="lock-border"
      :style="{ borderColor: fieldBorderColor }"
    ></div>

    <v-row v-if="showEditButton">
      <v-col cols="12 mb-3">
        <v-btn
          variant="flat"
          density="compact"
          color="secondary"
          @click.prevent="showEditButtonState = !showEditButtonState"
        >
          Přepnout (aktuální režim: {{ !showEditButtonState ? 'náhled' : 'editace' }})
        </v-btn>
      </v-col>
    </v-row>

    <Editor
      ref="editor"
      v-model="modelValueState"
      api-key="gpl"
      license-key="gpl"
      :init="{ ...simpleConfig(), ...config }"
      toolbar=""
      v-bind="$attrs"
      @focus="handleEditorFocus"
      @blur="handleEditorBlur"
    />
  </div>
</template>
<script lang="ts" setup>
  import { simpleConfig } from '@/utils/SetupTinyMCE';
  import Editor from '@tinymce/tinymce-vue';
  import { useVModel } from '@vueuse/core';
  import type { EditorManager, RawEditorOptions } from 'tinymce';
  import { ref, computed, watch, onUnmounted, type Ref } from 'vue';
  import { useWebSocketStore } from '@/stores/websocket';
  import { getCachedUserColor } from '@/composables/useUserColors';
  import { useUserNames } from '@/composables/useUserNames';

  const props = defineProps({
    modelValue: {
      type: String,
      default: ''
    },
    showEditButton: {
      type: Boolean,
      default: null
    },
    config: {
      type: Object as () => EditorManager & RawEditorOptions,
      required: false,
      default: undefined
    },
    enableWebSocket: {
      type: Boolean,
      default: false
    },
    fieldName: {
      type: String,
      default: ''
    },
    tablePrimaryKey: {
      type: [Number, String],
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    },
    autoLock: {
      type: Boolean,
      default: true
    }
  });

  const editor: Ref<typeof Editor | null> = ref(null);
  const modelValueState = useVModel(props, 'modelValue');
  const showEditButtonState = useVModel(props, 'showEditButton');

  const webSocketStore = props.enableWebSocket ? useWebSocketStore() : null;
  const isEditing = ref(false);
  const hasRequestedAccess = ref(false);

  const isLockedByMe = computed(() =>
    webSocketStore ? webSocketStore.isFieldLockedByMe(props.fieldName) : false
  );

  const isLockedByOther = computed(() =>
    webSocketStore ? webSocketStore.isFieldLockedByOther(props.fieldName) : false
  );

  const lockInfo = computed(() =>
    webSocketStore ? webSocketStore.getFieldLockInfo(props.fieldName) : null
  );

  const lockOwnerName = computed(() => {
    if (!lockInfo.value) return '';
    const { getUserName } = useUserNames();
    return getUserName(lockInfo.value.user_id);
  });

  const lockOwnerColor = computed(() => {
    if (!lockInfo.value) return 'grey';
    return getCachedUserColor(lockInfo.value.user_id);
  });

  const currentUserColor = computed(() => {
    if (webSocketStore && webSocketStore.currentUserId) {
      return getCachedUserColor(webSocketStore.currentUserId);
    }
    return '#ff9800';
  });

  const fieldBorderColor = computed(() => {
    if (isLockedByMe.value) {
      return currentUserColor.value;
    } else if (isLockedByOther.value) {
      return lockOwnerColor.value;
    }
    return 'transparent';
  });

  const unlockTimeout = ref<number | null>(null);

  const handleEditorFocus = async () => {
    if (!props.enableWebSocket || !webSocketStore || props.disabled) return;

    console.log('🎯 TinyMCE Editor focused - locking field:', props.fieldName);
    isEditing.value = true;

    if (unlockTimeout.value) {
      window.clearTimeout(unlockTimeout.value);
      unlockTimeout.value = null;
    }

    if (props.autoLock !== false) {
      await webSocketStore.autoLockField(props.fieldName);
    }
  };

  const handleEditorBlur = () => {
    if (!props.enableWebSocket || !webSocketStore || props.disabled) return;

    console.log('👋 TinyMCE Editor blurred - scheduling unlock:', props.fieldName);
    isEditing.value = false;

    unlockTimeout.value = window.setTimeout(() => {
      if (!isEditing.value && webSocketStore) {
        webSocketStore.unlockField(props.fieldName);
      }
    }, 1000);
  };

  const requestAccess = async () => {
    if (!webSocketStore || hasRequestedAccess.value) return;

    hasRequestedAccess.value = true;
    await webSocketStore.requestFieldAccess(props.fieldName);
  };

  const isUpdatingFromWebSocket = ref(false);
  let lastSentValue = '';

  watch(
    () => modelValueState.value,
    (newValue) => {
      console.log('🔍 EditorTextarea model watcher triggered:', {
        fieldName: props.fieldName,
        newValue: typeof newValue === 'string' ? newValue.substring(0, 30) + '...' : newValue,
        lastSentValue:
          typeof lastSentValue === 'string'
            ? lastSentValue.substring(0, 30) + '...'
            : lastSentValue,
        isUpdatingFromWebSocket: isUpdatingFromWebSocket.value,
        isLockedByOther: isLockedByOther.value,
        webSocketEnabled: props.enableWebSocket
      });

      if (isUpdatingFromWebSocket.value) {
        console.log('⏭️ Skipping send - programmatic update from WebSocket in progress');
        return;
      }

      if (isLockedByOther.value) {
        console.log('⏭️ Skipping send - field locked by other user');
        return;
      }

      if (
        props.enableWebSocket &&
        webSocketStore &&
        newValue !== undefined &&
        newValue !== lastSentValue
      ) {
        console.log('📝 TinyMCE content changed - sending real-time update:', props.fieldName);

        if (props.tablePrimaryKey !== null) {
          console.log('📝 Sending table-based update for conclusion:', {
            tablePrimaryKey: props.tablePrimaryKey,
            fieldName: props.fieldName,
            value: newValue
          });

          webSocketStore.sendRealTimeUpdate('conclusions', {
            table_primary_key: props.tablePrimaryKey,
            field: props.fieldName,
            value: newValue
          });
        } else {
          webSocketStore.sendRealTimeUpdate(props.fieldName, newValue);
        }

        lastSentValue = newValue;
      } else {
        console.log('⏭️ Skipping send - conditions not met:', {
          enableWebSocket: props.enableWebSocket,
          hasWebSocketStore: !!webSocketStore,
          valueUndefined: newValue === undefined,
          sameAsLastSent: newValue === lastSentValue
        });
      }
    },
    { flush: 'post' }
  );

  watch(
    () => isLockedByMe.value,
    (isLocked) => {
      if (isLocked && hasRequestedAccess.value) {
        hasRequestedAccess.value = false;
      }
    }
  );

  watch(
    () => {
      if (props.tablePrimaryKey !== null) {
        return webSocketStore?.fieldUpdates['conclusions'];
      } else {
        return webSocketStore?.fieldUpdates[props.fieldName];
      }
    },
    (newUpdate) => {
      console.log('📨 EditorTextarea incoming update watcher triggered:', {
        fieldName: props.fieldName,
        tablePrimaryKey: props.tablePrimaryKey,
        hasUpdate: !!newUpdate,
        updateUserId: newUpdate?.userId,
        currentUserId: webSocketStore?.currentUserId,
        isFromOtherUser: newUpdate && newUpdate.userId !== webSocketStore?.currentUserId
      });

      if (newUpdate && newUpdate.userId !== webSocketStore?.currentUserId) {
        let shouldUpdate = false;
        let updateValue = null;

        if (props.tablePrimaryKey !== null && typeof newUpdate.value === 'object') {
          const conclusionUpdate = newUpdate.value;

          if (
            conclusionUpdate.table_primary_key === props.tablePrimaryKey &&
            conclusionUpdate.field === props.fieldName
          ) {
            console.log('📥 EditorTextarea received conclusion table update:', {
              conclusionId: props.tablePrimaryKey,
              fieldName: props.fieldName,
              value: conclusionUpdate.value,
              fromUser: newUpdate.userId
            });

            shouldUpdate = true;
            updateValue = conclusionUpdate.value;
          }
        } else if (props.tablePrimaryKey === null) {
          console.log('📥 EditorTextarea received regular field update:', {
            fieldName: props.fieldName,
            value:
              typeof newUpdate.value === 'string'
                ? newUpdate.value.substring(0, 50) + '...'
                : newUpdate.value,
            fromUser: newUpdate.userId
          });

          shouldUpdate = true;
          updateValue = newUpdate.value;
        }

        if (shouldUpdate && modelValueState.value !== updateValue) {
          console.log('📝 Updating EditorTextarea content:', {
            from:
              typeof modelValueState.value === 'string'
                ? modelValueState.value.substring(0, 30) + '...'
                : modelValueState.value,
            to: typeof updateValue === 'string' ? updateValue.substring(0, 30) + '...' : updateValue
          });

          console.log('🚩 Setting flag to prevent echo/loopback');
          isUpdatingFromWebSocket.value = true;

          console.log('🚩 Now updating model value - this will trigger model watcher');
          modelValueState.value = updateValue;
          lastSentValue = updateValue;

          console.log('🚩 Resetting flag after model update');
          isUpdatingFromWebSocket.value = false;
        } else {
          console.log('⏭️ Skipping update - same value');
        }
      } else {
        console.log('⏭️ Skipping update - from same user or no update');
      }
    },
    { deep: true }
  );

  onUnmounted(() => {
    if (unlockTimeout.value) {
      window.clearTimeout(unlockTimeout.value);
    }
  });
</script>

<style scoped>
  .editor-wrapper {
    position: relative;
  }

  .lock-overlay-other {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(1px);
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    pointer-events: auto;
  }

  .lock-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.1);
    z-index: 20;
    position: relative;
  }

  .lock-text {
    font-size: 0.875rem;
    font-weight: 500;
    color: #666;
  }

  .editing-border {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px solid;
    border-radius: 4px;
    pointer-events: none;
    z-index: 5;
    animation: pulse 2s infinite;
  }

  .lock-border {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px solid;
    border-radius: 4px;
    pointer-events: none;
    z-index: 5;
    /* No animation for locked state, just solid border */
  }

  @keyframes pulse {
    0% {
      opacity: 0.6;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.6;
    }
  }
</style>
