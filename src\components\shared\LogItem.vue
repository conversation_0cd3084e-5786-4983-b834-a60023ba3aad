<template>
  <v-row>
    <v-col cols="12">
      <v-alert
        outlined
        type="warning"
        :icon="false"
        :color="stateColor"
        :border="'start'"
        variant="outlined"
        prominent
      >
        <slot></slot>
      </v-alert>
    </v-col>
  </v-row>
</template>
<script lang="ts" setup>
  import { ChangelogState, type ChangelogItemI } from '@/stores/logs';
  import { computed } from 'vue';

  const props = defineProps<{
    item: ChangelogItemI;
  }>();

  const stateColor = computed(() => {
    switch (props.item.state) {
      case ChangelogState.CREATE:
        return 'success';
      case ChangelogState.EDIT:
        return 'warning';
      case ChangelogState.DELETE:
        return 'error';
      default:
        return 'info';
    }
  });
</script>
