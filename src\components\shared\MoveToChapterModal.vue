<template>
  <v-dialog v-model="showState" class="customer-modal" :z-index="1010">
    <v-card>
      <v-form @submit.prevent="submitMove">
        <v-card-title class="pa-5">
          <span class="text-h5">Přesunout do jiné kapitoly</span>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col cols="12">
              <v-label class="mb-2">V<PERSON>berte cílovou kapitolu</v-label>
              <v-autocomplete
                v-model="selectedChapterId"
                :items="chapters"
                :item-title="chapterTitle"
                item-value="chapter_id"
                variant="outlined"
                color="primary"
         
                :no-data-text="'Žádné kapitoly'"
                :loading="loading"
                hide-details
                rounded="sm"
                class="autocomplete"
                required
              >
                <template #chip="{ item }">
                  <v-chip
                    label
                    variant="tonal"
                    color="primary"
                    size="large"
                    class="my-1 text-subtitle-1 font-weight-regular"
                  >
                    {{ item.raw.chapter_title }}
                  </v-chip>
                </template>
                <template #item="{ props, item }">
                  <v-list-item v-bind="props" :title="''">
                    <div class="player-wrapper pa-2">
                      <h6 class="text-subtitle-1 mb-0">
                        {{ item.raw.chapter_title }}
                        <v-chip
                          v-if="item.raw.status === 'active'"
                          color="success"
                          size="small"
                          label
                          class="ml-2"
                        >
                          Aktivní
                        </v-chip>
                        <v-chip
                          v-else-if="item.raw.status === 'closed'"
                          color="error"
                          size="small"
                          label
                          class="ml-2"
                        >
                          Uzavřená
                        </v-chip>
                        <v-chip
                          v-else
                          color="warning"
                          size="small"
                          label
                          class="ml-2"
                        >
                          Neaktivní
                        </v-chip>
                      </h6>
                    </div>
                  </v-list-item>
                </template>
              </v-autocomplete>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" variant="text" @click="showState = false">
            Zrušit
          </v-btn>
          <v-btn color="primary" variant="flat" type="submit" :disabled="!selectedChapterId">
            Přesunout
          </v-btn>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { useVModel } from '@vueuse/core';
import { ref, computed } from 'vue';
import type { PropType } from 'vue';

const emits = defineEmits(['confirm', 'update:show']);
const props = defineProps({
  show: {
    type: Boolean,
    required: true
  },
  chapters: {
    type: Array as PropType<Array<{ chapter_id: number; chapter_title: string; status: string }>>,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const showState = useVModel(props, 'show');
const selectedChapterId = ref<number | null>(null);

const chapterTitle = (chapter: { chapter_title: string }) => chapter.chapter_title;

function submitMove() {
  if (selectedChapterId.value) {
    emits('confirm', selectedChapterId.value);
    showState.value = false;
    selectedChapterId.value = null;
  }
}
</script> 