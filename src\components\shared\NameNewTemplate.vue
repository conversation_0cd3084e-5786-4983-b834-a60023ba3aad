<template>
  <v-dialog v-model="showState" class="customer-modal" style="z-index: 1010">
    <v-card>
      <v-form ref="CreateTagForm" class="createTagForm" @submit.prevent="submitFormToValidate">
        <v-card-title class="pa-5">
          <span class="text-h5">Název šablony</span>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col>
              <v-row>
                <v-col cols="12" md="12">
                  <v-label class="mb-2">Název šablony</v-label>
                  <v-text-field
                    v-model="templateName"
                    single-line
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" variant="text" @click="showState = false"><PERSON><PERSON><PERSON><PERSON>t</v-btn>
          <v-btn color="primary" variant="flat" type="submit">Vytvořit šablonu</v-btn>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>
<script setup lang="ts">
  import { useVModel } from '@vueuse/core';
  import { ref } from 'vue';
  const emits = defineEmits(['update:show', 'updateName']);
  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    },
    experiment: {
      type: Object,
      required: true
    }
  });
  const templateName = ref(
    props.experiment?.batch_number.batch_number + '_' + new Date().getTime()
  );
  const showState = useVModel(props, 'show');
  async function submitFormToValidate() {
    emits('updateName', templateName.value);
  }
</script>
