<template>
  <v-dialog v-model="showState" class="customer-modal" style="z-index: 1010">
    <v-card>
      <v-form ref="CreateTagForm" class="createTagForm" @submit.prevent="submitFormToValidate">
        <v-card-title class="pa-5">
          <v-row>
            <v-col cols="11">
              <span class="text-h5">Specifické vyhledávání</span>
            </v-col>
            <v-col cols="1">
              <v-btn
                icon
                color="secondary"
                variant="text"
                rounded="sm"
                @click.prevent="realoadFilters"
              >
                <ReloadOutlined />
                <v-tooltip
                  aria-label="tooltip"
                  activator="parent"
                  location="bottom"
                  :content-class="'custom-tooltip'"
                >
                  <span class="text-caption">Vymazat filtry</span>
                </v-tooltip>
              </v-btn>
            </v-col>
          </v-row>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col cols="12" md="6">
              <v-label class="mb-2">Hledan<PERSON> slovo</v-label>
              <v-text-field
                v-model="filterSearch"
                type="text"
                variant="outlined"
                persistent-placeholder
                placeholder="Hledat"
                hide-details
              ></v-text-field>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>

        <v-card-text>
          <v-row class="mb-2">
            <template v-for="(searchTable, index) in searchTables" :key="index">
              <v-col cols="12" md="3">
                <v-checkbox
                  v-model="searchTable.default"
                  :label="searchTable.translate"
                  single-line
                  hide-details="auto"
                  variant="outlined"
                  color="primary"
                  rounded="sm"
                />
              </v-col>
            </template>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <template
              v-for="(column, index) in allProjectsOptionsPageSearch.all_projects.search_columns"
              :key="index"
            >
              <v-col cols="12" md="4">
                <v-label class="mb-2">{{ search_columns_translator(column) }}</v-label>
                <v-select
                  v-if="column === 'status__project'"
                  v-model="temporaryFilteredOptions[index].value"
                  :items="[
                    {
                      title: 'Aktivní',
                      value: ProjectStatusNew.ACTIVE
                    },
                    {
                      title: 'Uzavřen',
                      value: ProjectStatusNew.CLOSED
                    }
                  ]"
                  variant="outlined"
                  persistent-placeholder
                  hide-details
                  rounded="sm"
                  :label="'Zadejte ' + search_columns_translator(column).toLowerCase()"
                  clearable
                ></v-select>
                <v-select
                  v-else-if="
                    column === 'techniques__type__analytical_technique' ||
                    column === 'departments__status__project_department' ||
                    column === 'departments__chapters__status__chapter'
                  "
                  v-model="temporaryFilteredOptions[index].value"
                  :items="[
                    {
                      title: 'Aktivní',
                      value: ProjectStatusNew.ACTIVE
                    },
                    {
                      title: 'Uzavřen',
                      value: ProjectStatusNew.CLOSED
                    }
                  ]"
                  variant="outlined"
                  persistent-placeholder
                  hide-details
                  rounded="sm"
                  :label="'Zadejte ' + search_columns_translator(column).toLowerCase()"
                  clearable
                ></v-select>
                <v-select
                  v-else-if="column === 'departments__type__project_department'"
                  v-model="temporaryFilteredOptions[index].value"
                  :items="[
                    {
                      title: 'Syntetické oddělení',
                      value: DepartmentEnum.SYNTHETIC
                    },
                    {
                      title: 'Analytické oddělení',
                      value: DepartmentEnum.ANALYTICAL
                    },
                    {
                      title: 'Technologické oddělení',
                      value: DepartmentEnum.TECHNICAL
                    }
                  ]"
                  variant="outlined"
                  persistent-placeholder
                  hide-details
                  rounded="sm"
                  :label="'Zadejte ' + search_columns_translator(column).toLowerCase()"
                  clearable
                ></v-select>
                <v-select
                  v-else-if="
                    column === 'departments__batch_numbers__form__form_type__form' &&
                    !props.department
                  "
                  v-model="temporaryFilteredOptions[index].value"
                  :items="[
                    {
                      title: 'Experiment',
                      value: FormType.EXPERIMENT
                    },
                    {
                      title: 'Pokus',
                      value: FormType.ATTEMPT
                    },
                    {
                      title: 'Šetření',
                      value: FormType.INVESTIGATION
                    },
                    {
                      title: 'Zpráva',
                      value: FormType.MESSAGE
                    }
                  ]"
                  variant="outlined"
                  persistent-placeholder
                  hide-details
                  rounded="sm"
                  :label="'Zadejte ' + search_columns_translator(column).toLowerCase()"
                  clearable
                ></v-select>
                <v-select
                  v-else-if="
                    column === 'departments__batch_numbers__form__form_type__form' &&
                    (!props.department || props.department.isTechnical())
                  "
                  v-model="temporaryFilteredOptions[index].value"
                  :items="[
                    {
                      title: 'Pokus',
                      value: FormType.ATTEMPT
                    },
                    {
                      title: 'Šetření',
                      value: FormType.INVESTIGATION
                    },
                    {
                      title: 'Zpráva',
                      value: FormType.MESSAGE
                    }
                  ]"
                  variant="outlined"
                  persistent-placeholder
                  hide-details
                  rounded="sm"
                  :label="'Zadejte ' + search_columns_translator(column).toLowerCase()"
                  clearable
                ></v-select>
                <v-select
                  v-else-if="column === 'departments__chapters__form__form_status__form'"
                  v-model="temporaryFilteredOptions[index].value"
                  :items="[
                    {
                      title: 'Vytvořen',
                      value: FormStatus.CREATED
                    },
                    {
                      title: 'Podepsán',
                      value: FormStatus.SIGNED
                    },
                    {
                      title: 'Zrušen',
                      value: FormStatus.CANCELED
                    }
                  ]"
                  variant="outlined"
                  persistent-placeholder
                  hide-details
                  rounded="sm"
                  :label="'Zadejte ' + search_columns_translator(column).toLowerCase()"
                  clearable
                ></v-select>
                <v-select
                  v-else-if="
                    column === 'techniques__type__analytical_technique' ||
                    column === 'departments__analytical_requests__request_type__analytical_request'
                  "
                  v-model="temporaryFilteredOptions[index].value"
                  :items="[
                    {
                      title: 'Interní',
                      value: SampleType.INTERNAL
                    },
                    {
                      title: 'Externí',
                      value: SampleType.EXTERNAL
                    }
                  ]"
                  variant="outlined"
                  persistent-placeholder
                  hide-details
                  rounded="sm"
                  :label="'Zadejte ' + search_columns_translator(column).toLowerCase()"
                  clearable
                ></v-select>
                <v-select
                  v-else-if="
                    column === 'departments__analytical_requests__samples__analysis_status__sample'
                  "
                  v-model="temporaryFilteredOptions[index].value"
                  :items="[
                    {
                      title: 'Vytvořen',
                      value: AnalyticalRequestStatus.CREATED
                    },
                    {
                      title: 'Probíhá',
                      value: AnalyticalRequestStatus.IN_PROGRESS
                    },
                    {
                      title: 'Hotovo',
                      value: AnalyticalRequestStatus.DONE
                    },
                    {
                      title: 'Zrušeno',
                      value: AnalyticalRequestStatus.CANCELED
                    }
                  ]"
                  variant="outlined"
                  persistent-placeholder
                  hide-details
                  rounded="sm"
                  :label="'Zadejte ' + search_columns_translator(column).toLowerCase()"
                  clearable
                ></v-select>

                <v-text-field
                  v-else
                  v-model="temporaryFilteredOptions[index].value"
                  single-line
                  hide-details="auto"
                  variant="outlined"
                  rounded="sm"
                  :placeholder="'Zadejte ' + search_columns_translator(column).toLowerCase()"
                  persistent-placeholder
                ></v-text-field>
              </v-col>
            </template>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" variant="text" @click="showState = false">Zavřít</v-btn>
          <v-btn color="primary" variant="flat" type="submit">Nastavit filtry a hledat</v-btn>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>
<script setup lang="ts">
  import { useVModel } from '@vueuse/core';
  import { ref, watchEffect, watch } from 'vue';
  import { storeToRefs } from 'pinia';
  import {
    useProjectsStore,
    type SearchResult,
    ProjectStatusNew,
    DepartmentEnum,
    Department
  } from '@/stores/projects';
  import { FormType } from '@/stores/forms';
  import { ReloadOutlined, SearchOutlined } from '@ant-design/icons-vue';
  import { message, notification } from 'ant-design-vue';

  const emits = defineEmits(['update:show', 'updateSearch', 'updateSearchAndReset']);

  const projectsStore = useProjectsStore();
  const { loadingSearch, allProjectsOptionsPageSearch, searchTables, tables } =
    storeToRefs(projectsStore);

  interface TemporaryFilteredOptions {
    value: string;
    column: string;
  }

  enum FormStatus {
    CREATED = 'created',
    SIGNED = 'signed',
    CANCELED = 'canceled'
  }
  enum SampleType {
    EXTERNAL = 'external',
    INTERNAL = 'internal'
  }
  enum AnalyticalRequestStatus {
    CREATED = 'created',
    IN_PROGRESS = 'in_progress',
    DONE = 'done',
    NA = 'na',
    CANCELED = 'canceled'
  }
  const temporaryFilteredOptions = ref<TemporaryFilteredOptions[]>([]);

  const getTableFromColumn = (column: string): string => {
    const parts = column.split('__');
    return parts[parts.length - 1];
  };

  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    },
    searchValue: {
      type: String,
      required: false,
      default: ''
    },
    department: {
      type: Department,
      required: false,
      default: null
    }
  });

  const showState = useVModel(props, 'show');

  const submitFormToValidate = async () => {
    if (
      temporaryFilteredOptions.value.every((option) => option.value === '') &&
      !filterSearch.value
    ) {
      notification.error({
        message: 'Nebyly zadány žádné filtry',
        description: 'Pro vyhledání je nutné zadat alespoň jedno kritérium'
      });
      return;
    }
    const filteredOptions = temporaryFilteredOptions.value.filter(
      (option) => option.value !== null && option.value !== ''
    );

    const newFilterOptions = filteredOptions.map((option) => ({
      column: option.column,
      value: option.value
    }));

    const addNewFilters = (existingFilters: any[] | null) => {
      const filtersMap = new Map();

      if (existingFilters) {
        existingFilters.forEach((filter) => filtersMap.set(filter.column, filter));
      }
      newFilterOptions.forEach((filter) => filtersMap.set(filter.column, filter));

      return Array.from(filtersMap.values());
    };

    if (allProjectsOptionsPageSearch.value.all_projects) {
      allProjectsOptionsPageSearch.value.all_projects.filterOptions = addNewFilters(
        allProjectsOptionsPageSearch.value.all_projects.filterOptions
      );
      allProjectsOptionsPageSearch.value.all_projects.search = filterSearch.value;
    }

    tables.value = [];
    searchTables.value.forEach((tempTable) => {
      if (tempTable.default) {
        tables.value.push(tempTable.table);
      }
    });
    notification.success({
      message: 'Filtry byly úspěšně aplikovány',
      description: 'Nastavení pro vyhledávání bylo úspěšně uloženo'
    });

    if (!filterSearch.value) {
      await projectsStore.getAllForSearch();
    }

    emits('updateSearchAndReset', filterSearch.value);
    showState.value = false;
  };

  watchEffect(() => {
    if (allProjectsOptionsPageSearch.value?.all_projects?.search_columns) {
      temporaryFilteredOptions.value =
        allProjectsOptionsPageSearch.value.all_projects.search_columns.map((column) => ({
          column,
          value: ''
        }));
    }
  });

  const search_columns_translator = (column: string) => {
    switch (true) {
      case column.startsWith('status__project'):
        return 'Stav projektu';
      case column.includes('techniques__type__analytical_technique'):
        return 'Typ analytické techniky';
      case column.includes('departments__number__project_department'):
        return 'Číslo oddělení';
      case column.includes('departments__type__project_department'):
        return 'Typ oddělení';
      case column.includes('departments__status__project_department'):
        return 'Stav oddělení';
      case column.includes('departments__analytical_requests__request_type__analytical_request'):
        return 'Typ analytické žádosti';
      case column.includes('departments__analytical_requests__samples__analysis_status__sample'):
        return 'Stav analýzy vzorku';
      case column.includes('departments__chapters__status__chapter'):
        return 'Stav kapitoly';
      case column.includes('departments__batch_numbers__form__form_type__form'):
        return 'Typ formuláře';
      case column.includes('departments__batch_numbers__batch_number'):
        return 'Číslo šarže';
      case column.includes('departments__chapters__chemicals__shortcut__chemical'):
        return 'Zkratka chemikálie';
      case column.includes('departments__chapters__chemicals__cas__chemical'):
        return 'CAS číslo chemikálie';
      case column.includes('departments__chapters__form__form_status__form'):
        return 'Stav formuláře';
      default:
        return column;
    }
  };

  const realoadFilters = () => {
    // Reset temporary filter options
    temporaryFilteredOptions.value =
      allProjectsOptionsPageSearch.value.all_projects.search_columns.map((column) => ({
        column,
        value: ''
      }));
    const columnsToRemove = allProjectsOptionsPageSearch.value.all_projects.search_columns;

    const filterOutNewFilters = (filterOptions: any[] | null) => {
      if (!filterOptions) return [];
      return filterOptions.filter((option) => !columnsToRemove.includes(option.column));
    };

    if (allProjectsOptionsPageSearch.value.all_projects) {
      allProjectsOptionsPageSearch.value.all_projects.filterOptions = filterOutNewFilters(
        allProjectsOptionsPageSearch.value.all_projects.filterOptions
      );
    }

    // Clear all table-specific searches
    searchTables.value.forEach((table) => {
      if (
        allProjectsOptionsPageSearch.value[
          table.table as keyof typeof allProjectsOptionsPageSearch.value
        ]
      ) {
        (
          allProjectsOptionsPageSearch.value[
            table.table as keyof typeof allProjectsOptionsPageSearch.value
          ] as any
        ).search = '';
      }
    });

    // Reset global search and table defaults
    allProjectsOptionsPageSearch.value.all_projects.search = '';
    searchTables.value.forEach((table) => (table.default = true));
  };

  const filterSearch = ref('');
  watch(
    () => props.searchValue,
    (newVal, oldVal) => {
      filterSearch.value = newVal;
    }
  );

  watch(
    () => searchTables.value,
    (newVal, oldVal) => {
      if (newVal.every((table) => !table.default)) {
        searchTables.value.forEach((table) => (table.default = true));
        searchTables.value.forEach((tempTable) => {
          if (tempTable.default) {
            tables.value.push(tempTable.table);
          }
        });
      }
    },
    { deep: true }
  );

  watch(
    [filterSearch, temporaryFilteredOptions],
    ([newSearch, newFilters], [oldSearch, oldFilters]) => {
      const columnsToRemove: string[] = [];
      if (oldSearch && !newSearch) {
        columnsToRemove.push('search');
        emits('updateSearchAndReset', '');
      }

      // Clear table searches for empty values
      const tableSearchMap = new Map<string, string[]>();
      const tablesToClear = new Set<string>();

      newFilters.forEach((filter, index) => {
        const tableName = getTableFromColumn(filter.column);

        if (filter.value) {
          // Add non-empty values to the search map
          if (!tableSearchMap.has(tableName)) {
            tableSearchMap.set(tableName, []);
          }
          tableSearchMap.get(tableName)!.push(`${filter.value}:*`);
        } else if (oldFilters && oldFilters[index] && !filter.value) {
          // Track tables that need their search cleared
          tablesToClear.add(tableName);
          columnsToRemove.push(filter.column);
        }
      });

      // Clear searches for tables with empty values
      tablesToClear.forEach((tableName) => {
        if (
          allProjectsOptionsPageSearch.value[
            tableName as keyof typeof allProjectsOptionsPageSearch.value
          ]
        ) {
          (
            allProjectsOptionsPageSearch.value[
              tableName as keyof typeof allProjectsOptionsPageSearch.value
            ] as any
          ).search = '';
        }
      });

      tableSearchMap.forEach((values, tableName) => {
        const baseSearch = '';
        const tableSpecificSearch = values.join(' & ');

        const finalSearch =
          baseSearch && tableSpecificSearch
            ? `${baseSearch} & ${tableSpecificSearch}`
            : baseSearch || tableSpecificSearch;

        if (
          allProjectsOptionsPageSearch.value[
            tableName as keyof typeof allProjectsOptionsPageSearch.value
          ]
        ) {
          (
            allProjectsOptionsPageSearch.value[
              tableName as keyof typeof allProjectsOptionsPageSearch.value
            ] as any
          ).search = finalSearch;
        }
      });

      if (columnsToRemove.length > 0) {
        filterOutColumns(columnsToRemove);
      }
    },
    { deep: true }
  );

  const filterOutColumns = (columnsToRemove: string[]) => {
    const filterOutNewFilters = (filterOptions: any[] | null) => {
      if (!filterOptions) return [];
      return filterOptions.filter((option) => !columnsToRemove.includes(option.column));
    };

    if (allProjectsOptionsPageSearch.value.all_projects) {
      allProjectsOptionsPageSearch.value.all_projects.filterOptions = filterOutNewFilters(
        allProjectsOptionsPageSearch.value.all_projects.filterOptions
      );
      if (columnsToRemove.includes('search'))
        allProjectsOptionsPageSearch.value.all_projects.search = '';
    }
  };
</script>
