<script setup lang="ts">
  import { useTagsStore } from '@/stores/tags';
  import { useDebounceFn, useVModel } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import { onMounted, ref, watch } from 'vue';

  export interface TagListI {
    tag_id: number;
    tag_name: string;
  }

  const emits = defineEmits(['update:show', 'updateTags']);
  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    },
    tags: {
      type: Array as () => TagListI[],
      required: true
    }
  });

  const tagsStore = useTagsStore();
  const { loading } = storeToRefs(tagsStore);

  const showState = useVModel(props, 'show');
  const search = ref('');
  const items = ref<TagListI[]>([]);
  const selected = ref<(string | TagListI)[]>([]);

  onMounted(() => {
    items.value = props.tags;
    selected.value = props.tags.map((tag) => tag.tag_name);
  });

  watch(search, () => {
    debouncedTagsSearch();
  });

  const debouncedTagsSearch = useDebounceFn(async () => {
    if (loading.value === false && search.value !== '') {
      tagsStore.search = search.value;
      const res = await tagsStore.getTags();
      const combinedItems = [
        ...items.value,
        ...res.data.map((tag) => ({ tag_id: tag.tag_id, tag_name: tag.tag_name }) as TagListI)
      ];
      const uniqueItems = combinedItems.reduce((acc, current) => {
        const x = acc.find((item) => item.tag_id === current.tag_id);
        if (!x) {
          return acc.concat([current]);
        } else {
          return acc;
        }
      }, [] as TagListI[]);
      items.value = uniqueItems;
    }
  }, 350);

  const CreateTagForm = ref();
  async function submitFormToValidate() {
    if (CreateTagForm.value.isValid && selected.value.length <= 5) {
      const tagsToBeAdded: TagListI[] = [];
      for (const tag_name of selected.value) {
        if (typeof tag_name === 'string') {
          const _t = props.tags.find((tag) => tag.tag_name === tag_name);

          if (_t === undefined) {
            const tag = await tagsStore.createTag(tag_name);
            if (tag)
              tagsToBeAdded.push({
                tag_id: tag.tag_id,
                tag_name: tag.tag_name
              } as TagListI);
          } else {
            tagsToBeAdded.push(_t);
          }
        } else {
          tagsToBeAdded.push(tag_name);
        }
      }
      showState.value = false;
      emits('updateTags', tagsToBeAdded);
    }
  }
</script>
<template>
  <v-dialog v-model="showState" class="customer-modal" style="z-index: 1010">
    <v-card :loading="loading">
      <v-form ref="CreateTagForm" class="createTagForm" @submit.prevent="submitFormToValidate">
        <v-card-title class="pa-5">
          <span class="text-h5">Správa štítků</span>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col>
              <v-row>
                <v-col cols="12">
                  <!-- :rules="[(v) => !!v || 'Vyberte alespoň jeden štítek', (v) => v.length <= 5 || 'Maximálně 5 štítků']" hint="Maximálně 5 štítků"F -->

                  <v-combobox
                    v-model="selected"
                    v-model:search="search"
                    :hide-no-data="false"
                    :items="items"
                    color="primary"
                    rounded="sm"
                    variant="outlined"
                    hide-details="auto"
                    persistent-hint
                    small-chips
                    max="5"
                    clearable
                    chips
                    multiple
                    :loading="loading"
                    item-value="tag_name"
                    item-title="tag_name"
                  >
                    <template #no-data>
                      <v-list-item>
                        <v-list-item-title>
                          <template v-if="search.length > 0">
                            Žádné výsledky pro hledaný výraz "
                            <strong>{{ search }}</strong>
                            ".
                            <br />
                            Pro přidání klikněte na
                            <kbd>enter</kbd>
                            .
                          </template>
                          <template v-else>Žádné štítky nejsou k dispozici.</template>
                        </v-list-item-title>
                      </v-list-item>
                    </template>
                  </v-combobox>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" variant="text" @click="showState = false">Zavřít</v-btn>
          <v-btn color="primary" variant="flat" type="submit" :loading="loading">Uložit</v-btn>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>
