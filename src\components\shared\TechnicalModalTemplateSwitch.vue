<template>
  <v-dialog v-model="showState" class="customer-modal" style="z-index: 1010">
    <v-card>
      <v-form ref="CreateTagForm" class="createTagForm" @submit.prevent="submitFormToValidate">
        <v-card-title class="pa-5">
          <span class="text-h5">Druh oddělení</span>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col cols="12">
              <v-label class="mb-2">Oddělení</v-label>
              <v-select
                v-model="technicalDepartment"
                :clearable="!true"
                hide-details
                :rules="itemRequiredRule"
                rounded="sm"
                :items="selectItems"
                variant="outlined"
                color="primary"
                label="Vyberte druh"
                single-line
                class="autocomplete"
                :no-data-text="'Žádná další políčka'"
                :slim="true"
                item-title="name"
                item-value="code"
              ></v-select>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" variant="text" @click="showState = false">Zavřít</v-btn>
          <v-btn color="primary" variant="flat" type="submit">Vybrat oddělení</v-btn>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>
<script setup lang="ts">
  import { useVModel } from '@vueuse/core';
  import { ref, onUnmounted } from 'vue';
  import { itemRequiredRule } from '@/utils/formValidation';
  const emits = defineEmits(['update:show', 'selectTechnicalDepartment']);
  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    }
  });
  const technicalDepartment = ref<string | null>(null);
  const selectItems = ref([
    { name: 'Pokus', code: 'attempt' },
    { name: 'Šetření', code: 'investigation' }
  ]);
  const CreateTagForm = ref();
  const showState = useVModel(props, 'show');
  async function submitFormToValidate() {
    if (CreateTagForm.value.isValid) {
      emits('selectTechnicalDepartment', technicalDepartment.value);
    }
  }
  onUnmounted(() => {
    technicalDepartment.value = null;
  });
</script>
