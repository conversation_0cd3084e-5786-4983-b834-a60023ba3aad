<template>
  <v-autocomplete
    v-model="selectedTechniquesState"
    v-model:search="techniqueSelectOptions.search"
    hide-details
    rounded="sm"
    :items="
      techniqueSelectOptions.results
        .filter((technique) => technique.isActive)
        .map((technique) => {
          return {
            value: technique.analytical_technique_id,
            title: `${technique.name} ${technique.shortcut} (${technique.type})`,
            technique: technique
          };
        })
    "
    variant="outlined"
    color="primary"
    label="Zadejte název techniky"
    single-line
    class="autocomplete"
    :no-data-text="'Žádná další políčka'"
    :slim="true"
    :loading="techniqueSelectOptions.loading"
    v-bind="$attrs"
    :rules="itemRequiredRule"
  >
    <template #chip>
      <v-chip
        label
        variant="tonal"
        color="primary"
        size="large"
        class="my-1 text-subtitle-1 font-weight-regular"
      ></v-chip>
    </template>

    <template #item="{ props, item }">
      <v-list-item v-bind="props" :title="''">
        <div class="player-wrapper pa-2">
          <h6 class="text-subtitle-1 mb-0">
            {{ item.raw.technique.parent_technique_id === null ? 'Hlavní' : 'Sub' }} -
            {{ item.raw.technique.name }} ({{ item.raw.technique.shortcut }}) -
            <v-chip
              v-if="item.raw.technique.status === 'active'"
              color="success"
              size="small"
              label
            >
              Aktivní
            </v-chip>
            <v-chip
              v-if="item.raw.technique.status === 'inactive'"
              color="warning"
              size="small"
              label
            >
              Neaktivní
            </v-chip>
            <v-chip v-if="item.raw.technique.status === 'deleted'" color="error" size="small" label>
              Odstraněno
            </v-chip>
          </h6>
          <small class="text-h6 text-lightText">
            Typ: {{ translateType(item.raw.technique.type) }} | Vytvořena:
            {{ toLocale(item.raw.technique.created_at) }}
          </small>
        </div>
      </v-list-item>
    </template>
    <template #append>
      <slot name="append"></slot>
    </template>
  </v-autocomplete>
</template>

<script lang="ts" setup>
  import type { PaginatorRequestDataI } from '@/stores/projects';
  import { Technique, useTechniquesStore } from '@/stores/techniques';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { toLocale } from '@/utils/locales';
  import { useDebounceFn, useVModel } from '@vueuse/core';
  import { isArray } from 'lodash';
  import { onBeforeUnmount, onMounted, ref, watch, type PropType } from 'vue';

  export interface TechniquesSelectProps {
    column: string;
    value: null | number | string | string[] | number[] | object | object[] | number[];
  }

  const props = defineProps({
    techniques: {
      type: Array<Technique>,
      required: false,
      default: () => []
    },
    selectedTechniques: {
      type: [Array, Number, Object, undefined] as PropType<
        number[] | number | Technique | Technique[] | undefined
      >,
      required: true,
      default: () => []
    },
    filterOptions: {
      type: Array<TechniquesSelectProps>,
      required: false,
      default: () => []
    }
  });

  const techniqueSelectOptions = ref<PaginatorRequestDataI<Technique>>({
    loading: false,
    results: [],

    search: undefined,
    search_type: 'OR',

    search_columns: ['name', 'shortcut', 'type'],
    totalItems: 0,
    options: {
      page: 1,
      rowsPerPage: 25,
      sortBy: [],
      sortType: []
    },
    filterOptions: props.filterOptions ?? []
  });

  const techniquesStore = useTechniquesStore();
  const selectedTechniquesState = useVModel(props, 'selectedTechniques');

  onBeforeUnmount(() => {
    techniqueSelectOptions.value.search = '';
    techniqueSelectOptions.value.results = [];
  });

  onMounted(async () => {
    techniqueSelectOptions.value.search = '';
    techniqueSelectOptions.value.results = props.techniques.filter(
      (technique) => technique instanceof Technique
    ) as Technique[];

    const addTechniqueIfNotExists = (technique: Technique) => {
      if (
        technique &&
        !techniqueSelectOptions.value.results.some(
          (t) => t.analytical_technique_id === technique.analytical_technique_id
        )
      ) {
        techniqueSelectOptions.value.results.push(technique);
      }
    };

    if (isArray(props.selectedTechniques)) {
      if (props.selectedTechniques.length === 0) {
        return;
      }
      const techniquePromises = props.selectedTechniques.map((technique) => {
        if (technique && typeof technique === 'number') {
          return techniquesStore.getTechnique(technique);
        } else if (technique instanceof Technique) {
          return Promise.resolve(technique);
        }
        return Promise.resolve(null);
      });

      const _techniques = await Promise.all(techniquePromises);
      _techniques.forEach((_t) => {
        if (_t) addTechniqueIfNotExists(_t);
      });
    } else {
      if (props.selectedTechniques && typeof props.selectedTechniques === 'number') {
        const _t = await techniquesStore.getTechnique(props.selectedTechniques);
        if (_t) addTechniqueIfNotExists(_t);
      } else if (props.selectedTechniques instanceof Technique) {
        addTechniqueIfNotExists(props.selectedTechniques);
      }
    }
    techniquesStore.getTechniquesForSelect(techniqueSelectOptions.value);
  });

  watch(
    () => techniqueSelectOptions.value.search,
    () => {
      debouncedProjectTechniqueSearch();
    }
  );

  const debouncedProjectTechniqueSearch = useDebounceFn(() => {
    if (
      techniqueSelectOptions.value.loading === false &&
      techniqueSelectOptions.value.search !== ''
    ) {
      techniquesStore.getTechniquesForSelect(techniqueSelectOptions.value);
    }
  }, 350);

  const translateType = (type: string): string => {
    const translations: { [key: string]: string } = {
      external: 'externí',
      internal: 'interní'
    };
    return translations[type] || type;
  };
</script>
