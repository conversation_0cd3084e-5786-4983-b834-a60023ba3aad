<template>
  <BaseBreadcrumb :title="title" :breadcrumbs="_breadcrumbs"></BaseBreadcrumb>
</template>
<script lang="ts" setup>
  import { type PropType } from 'vue';
  import type { BreadcrumbI } from './BaseBreadcrumb.vue';
  import BaseBreadcrumb from './BaseBreadcrumb.vue';

  defineProps({
    title: {
      type: String,
      default: '',
      required: true
    },
    _breadcrumbs: {
      type: Array as PropType<BreadcrumbI[]>,
      required: false,
      default: () => []
    }
  });
</script>
