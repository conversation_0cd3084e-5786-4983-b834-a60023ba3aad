<template>
  <v-row class="pt-4">
    <v-col cols="12">
      <UsersSelect v-model:selected-users="selectedUser" single-line :slim="true">
        <template #append>
          <v-btn
            variant="flat"
            color="primary"
            :disabled="selectedUser === null"
            @click.prevent="addUser"
          >
            Přidat uživatele
          </v-btn>
        </template>
      </UsersSelect>
    </v-col>
  </v-row>
  <v-row v-if="combinedUsers" style="max-height: 360px; overflow-y: auto" class="mt-4">
    <v-col v-for="user in combinedUsers" :key="user.user_id" cols="12">
      <v-card
        variant="outlined"
        class="customListCard card-hover-border bg-containerBg"
        :subtitle="`Status: ${getStatusTitle(user.status) || 'Neznámý'}`"
        :title="`${getfullName(user.first_name, user.last_name)} - ${user.user_email}`"
        pa-2
        :data-user-id="user.user_id"
      >
        <template #append>
          <v-menu>
            <template #activator="{ props }">
              <v-btn
                size="x-small"
                v-bind="props"
                variant="text"
                style="height: auto"
                :disabled="user.fromResponsible"
                @click.prevent.stop="props.isActive = true"
              >
                <EllipsisOutlined :style="{ fontSize: '28px' }" />
              </v-btn>
            </template>
            <v-list elevation="24" density="compact" class="py-0">
              <v-list-item v-for="(item, index) in actionDD" :key="index" :value="index">
                <v-list-item-title @click="item.action(user.user_id)">
                  {{ item.title }}
                </v-list-item-title>
              </v-list-item>

              <slot name="customMenuItems" :user_id="user.user_id"></slot>
            </v-list>
          </v-menu>
        </template>
      </v-card>
    </v-col>
  </v-row>

  <ConfirmDlg ref="_ConfirmRef" />
</template>

<script lang="ts" setup>
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import { User, type UserI } from '@/stores/auth';
  import type { ProjectUser, ProjectUserI } from '@/stores/projects';
  import { EllipsisOutlined } from '@ant-design/icons-vue';
  import { computed, ref } from 'vue';
  import UsersSelect from './UsersSelect.vue';
  import { useProjectsStore } from '@/stores/projects';
  import { storeToRefs } from 'pinia';

  const projectStore = useProjectsStore();
  const { project } = storeToRefs(projectStore);
  const _ConfirmRef = ref<InstanceType<typeof ConfirmDlg> | null>(null);

  const emits = defineEmits(['removeUser', 'addUser']);
  const props = defineProps<{
    users: (User | ProjectUser)[];
  }>();

  const removeProjectUser = async (id: number) => {
    if (
      await _ConfirmRef.value?.open('Opravdu chcete odebrat uživatele?', '', {
        color: 'error',
        notclosable: true,
        zIndex: 2400
      })
    ) {
      emits('removeUser', id);
      selectedUser.value = undefined;
    }
  };
  const combinedUsers = computed(() => {
    const flaggedResponsibleUsers = project.value?.responsible_users?.map((user) => ({
      ...user,
      fromResponsible: true
    }));

    const allUsers = [...(flaggedResponsibleUsers || []), ...props.users];

    const uniqueUsers = allUsers.reduce(
      (acc, user) => {
        const existingUserIndex = acc.findIndex(
          (u: { user_id: any }) => u.user_id === user.user_id
        );
        if (existingUserIndex === -1) {
          acc.push(user);
        } else if (!user.fromResponsible) {
          acc[existingUserIndex] = user;
        }
        return acc;
      },
      [] as (UserI | (ProjectUserI & { fromResponsible?: boolean }))[]
    );

    return uniqueUsers;
  });
  const selectedUser = ref<number | undefined>(undefined);
  const addUser = async () => {
    if (selectedUser.value === null) return;
    emits('addUser', selectedUser.value);
    selectedUser.value = undefined;
  };
  const getStatusTitle = (status: string) => {
    return status === 'active' ? 'Aktivní' : 'Neaktivní';
  };
  const getfullName = (first_name: string, last_name: string) => {
    return `${first_name.charAt(0).toUpperCase()}${first_name.slice(1)} ${last_name.charAt(0).toUpperCase()}${last_name.slice(1)}`;
  };
  const actionDD = ref([{ title: 'Odebrat uživatele', action: removeProjectUser }]);
</script>
