<template>
  <v-autocomplete
    v-model="selectedUsersState"
    :custom-filter="customFilter"
    hide-details
    rounded="sm"
    :items="combinedUsers"
    variant="outlined"
    color="primary"
    label="Vyberte uživatele"
    single-line
    class="autocomplete"
    :no-data-text="'Žádn<PERSON> další políč<PERSON>'"
    :slim="true"
    :loading="loading"
    :item-title="itemTitle"
    item-value="user_id"
    v-bind="$attrs"
  >
    <template #chip>
      <v-chip
        label
        variant="tonal"
        color="primary"
        size="large"
        class="my-1 text-subtitle-1 font-weight-regular"
      ></v-chip>
    </template>

    <template #item="{ props, item }">
      <v-list-item v-bind="props" :title="''">
        <div class="player-wrapper pa-2">
          <h6 class="text-subtitle-1 mb-0">
            {{ item.raw.first_name }} {{ item.raw.last_name }} ({{ item.raw.user_email }}) -
            <v-chip v-if="item.raw.status === 'active'" color="success" size="small" label>
              Aktivní
            </v-chip>
            <v-chip v-if="item.raw.status === 'inactive'" color="warning" size="small" label>
              Neaktivní
            </v-chip>
            <v-chip v-if="item.raw.status === 'deleted'" color="error" size="small" label>
              Odstraněno
            </v-chip>
          </h6>
          <small class="text-h6 text-lightText">
            Aktualizováno: {{ toLocale(item.raw.updated_at) }} | Vytvořeno:
            {{ toLocale(item.raw.created_at) }}
          </small>
        </div>
      </v-list-item>
    </template>

    <template #append>
      <slot name="append"></slot>
    </template>
  </v-autocomplete>
</template>

<script lang="ts" setup>
  import { toLocale } from '@/utils/locales';
  import { useVModel } from '@vueuse/core';
  import { onMounted, computed, type PropType } from 'vue';
  import type { VAutocomplete } from 'vuetify/components';
  import { useProjectsStore, type ProjectUser, type ProjectUserI } from '@/stores/projects';
  import { storeToRefs } from 'pinia';
  import { User, type UserI } from '@/stores/auth';
  import { useAuthStore } from '@/stores/auth';

  const projectStore = useProjectsStore();
  const { project, projectUsers, loading } = storeToRefs(projectStore);
  const authStore = useAuthStore();
  const { user } = storeToRefs(authStore);

  const props = defineProps({
    users: {
      type: Array as PropType<Array<User | ProjectUser>>,
      required: false,
      default: () => []
    },
    selectedUsers: {
      type: [Array, Number, Object, undefined] as PropType<
        number[] | number | User | User[] | undefined
      >,
      required: true,
      default: () => []
    },
    whatToShow: {
      type: Array,
      required: false,
      default: () => ['user_email']
    }
  });

  function customFilter(value: string, query: string, item?: { raw: User } | undefined) {
    const user = item?.raw as User;
    let score = 0;
    const lowerQuery = query.toLowerCase();

    if (user.first_name.toLowerCase().includes(lowerQuery)) score += 1;
    if (user.last_name.toLowerCase().includes(lowerQuery)) score += 1;
    if (user.user_email.toLowerCase().includes(lowerQuery)) score += 1;

    if (score === 0) return false;

    return score;
  }

  const selectedUsersState = useVModel(props, 'selectedUsers');

  const itemTitle = (user: User) => {
    return props.whatToShow
      .map((field) => user[field as keyof User])
      .filter(Boolean)
      .join(' ');
  };

  const combinedUsers = computed(() => {
    const flaggedResponsibleUsers = project.value?.responsible_users?.map((user) => ({
      ...user,
      fromResponsible: true
    }));

    const userLaborants = user?.value?.userLaborants?.map((user) => ({
      ...user
    }));

    const allUsers = [
      ...(flaggedResponsibleUsers || []),
      ...projectUsers.value,
      ...(userLaborants || [])
    ];

    const uniqueUsers = allUsers.reduce(
      (acc, user) => {
        const existingUserIndex = acc.findIndex(
          (u: { user_id: any }) => u.user_id === user.user_id
        );
        if (existingUserIndex === -1) {
          acc.push(user);
        } else if (!user.fromResponsible) {
          acc[existingUserIndex] = user;
        }
        return acc;
      },
      [] as (UserI | (ProjectUserI & { fromResponsible?: boolean }))[]
    );

    return uniqueUsers;
  });
</script>
