<template>
  <v-autocomplete
    v-model="selectedUsersState"
    v-model:search="userSelectOptions.search"
    :return-object="true"
    hide-details
    rounded="sm"
    :items="userSelectOptions.results"
    variant="outlined"
    color="primary"
    label="Zadejte email/j<PERSON>no uživatele"
    single-line
    class="autocomplete"
    :no-data-text="'Žádn<PERSON> další políčka'"
    :slim="true"
    :loading="userSelectOptions.loading"
    :item-title="itemTitle"
    item-value="user_id"
    v-bind="$attrs"
  >
    <template #chip>
      <v-chip
        label
        variant="tonal"
        color="primary"
        size="large"
        class="my-1 text-subtitle-1 font-weight-regular"
      ></v-chip>
    </template>

    <template #item="{ props, item }">
      <v-list-item v-bind="props" :title="''">
        <div v-if="!(item.raw instanceof User)" class="player-wrapper pa-2">
          <h6 class="text-subtitle-1 mb-0">
            {{ item.title }}
          </h6>
        </div>
        <div v-else class="player-wrapper pa-2">
          <h6 class="text-subtitle-1 mb-0">
            {{ item.raw.fullName }} ({{ item.raw.user_email }}) -
            <v-chip v-if="item.raw.status === 'active'" color="success" size="small" label>
              Aktivní
            </v-chip>
            <v-chip v-if="item.raw.status === 'inactive'" color="warning" size="small" label>
              Neaktivní
            </v-chip>
            <v-chip v-if="item.raw.status === 'deleted'" color="error" size="small" label>
              Odstraněno
            </v-chip>
          </h6>
          <small class="text-h6 text-lightText">
            Aktualizováno: {{ toLocale(item.raw.updated_at) }} | Vytvořeno:
            {{ toLocale(item.raw.created_at) }}
          </small>
        </div>
      </v-list-item>
    </template>

    <template #append>
      <slot name="append"></slot>
    </template>
  </v-autocomplete>
</template>

<script lang="ts" setup>
  import { User } from '@/stores/auth';
  import type { PaginatorRequestDataI, ProjectUser } from '@/stores/projects';
  import { useUsersStore } from '@/stores/users';
  import { toLocale } from '@/utils/locales';
  import { useDebounceFn } from '@vueuse/core';
  import { onMounted, ref, watch, type PropType, computed } from 'vue';

  const props = defineProps({
    users: {
      type: Array as PropType<Array<User | ProjectUser>>,
      required: false,
      default: () => []
    },
    selectedUsers: {
      type: [Array, Number, Object, undefined] as PropType<
        number[] | number | User | User[] | undefined
      >,
      required: true,
      default: () => []
    },
    whatToShow: {
      type: Array,
      required: false,
      default: () => ['user_email']
    }
  });

  const userSelectOptions = ref<PaginatorRequestDataI<User>>({
    loading: false,
    results: [],
    search: undefined,
    search_type: 'OR',
    search_columns: ['first_name', 'last_name', 'user_email'],
    totalItems: 0,
    options: {
      page: 1,
      rowsPerPage: 100,
      sortBy: [],
      sortType: []
    },
    filterOptions: [
      {
        column: 'status',
        value: 'active'
      }
    ]
  });

  const usersStore = useUsersStore();
  const emit = defineEmits(['update:selectedUsers']);
  const internalSelectedUsers = ref<User[]>([]);

  const selectedUsersState = computed({
    get() {
      return internalSelectedUsers.value;
    },
    set(newValue: any) {
      internalSelectedUsers.value = newValue;

      if (Array.isArray(newValue)) {
        const userIds = newValue.map((user) => (user instanceof User ? user.user_id : user));
        emit('update:selectedUsers', userIds);
      } else if (newValue instanceof User) {
        emit('update:selectedUsers', newValue.user_id);
      } else {
        emit('update:selectedUsers', newValue);
      }
    }
  });

  const debouncedProjectUserSearch = useDebounceFn(() => {
    if (!userSelectOptions.value.loading && userSelectOptions.value.search !== '') {
      // if (selectedUsersState.value) {
      //   if (Array.isArray(selectedUsersState.value)) {
      //     if (selectedUsersState.value.length > 0) {
      //       userSelectOptions.value.filterOptions = [
      //         {
      //           column: 'user_id',
      //           value: selectedUsersState.value.map((user) => {
      //             if (user instanceof User) {
      //               return user.user_id;
      //             } else {
      //               return user;
      //             }
      //           })
      //         }
      //       ];
      //     }
      //   } else {
      //     if (selectedUsersState.value instanceof User) {
      //       userSelectOptions.value.filterOptions = [{ column: 'user_id', value: selectedUsersState.value.user_id }];
      //     } else {
      //       userSelectOptions.value.filterOptions = [{ column: 'user_id', value: selectedUsersState.value }];
      //     }
      //   }
      // }

      usersStore.getUsersForSelect(userSelectOptions.value, false);
    }
  }, 350);

  onMounted(() => {
    if (Array.isArray(props.selectedUsers)) {
      const userIds = props.selectedUsers.map((item) =>
        item instanceof User ? item.user_id : item
      );

      internalSelectedUsers.value = props.users.filter(
        (user) => user instanceof User && userIds.includes(user.user_id)
      ) as User[];
    } else if (props.selectedUsers instanceof User) {
      internalSelectedUsers.value = [props.selectedUsers];
    } else if (typeof props.selectedUsers === 'number') {
      const foundUser = props.users.find(
        (user) => user instanceof User && user.user_id === props.selectedUsers
      );
      if (foundUser) {
        internalSelectedUsers.value = [foundUser as User];
      }
    }

    userSelectOptions.value.results = props.users.filter((user) => user instanceof User) as User[];
    usersStore.getUsersForSelect(userSelectOptions.value, false);
  });

  watch(
    () => userSelectOptions.value.search,
    () => {
      debouncedProjectUserSearch();
    }
  );
  const itemTitle = (user: User) => {
    return props.whatToShow
      .map((field) => user[field as keyof User])
      .filter(Boolean)
      .join(' ');
  };
  watch(internalSelectedUsers, () => {
    userSelectOptions.value.search = '';
    usersStore.getUsersForSelect(userSelectOptions.value, false);
  });
</script>
