<template>
  <LoaderWrapper v-if="!baseDataLoaded" />
  <template v-else>
    <ApexChartComponent
      type="line"
      height="350"
      :options="chartOptions"
      :series="lineChart.series"
    ></ApexChartComponent>
  </template>
</template>

<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import { getdarkPrimary, getSecondary } from '@/stores/updateColors';
  import { useInstrumentsStore, type WeeklyOverviewDto } from '@/stores/instruments';
  import { storeToRefs } from 'pinia';
  import LoaderWrapper from '@/layouts/dashboard/LoaderWrapper.vue';

  const instrumentsStore = useInstrumentsStore();
  const { loading } = storeToRefs(instrumentsStore);
  const weeklyOverview = ref<WeeklyOverviewDto | null>(null);
  const baseDataLoaded = ref(false);

  onMounted(async () => {
    const res = await instrumentsStore.getWeeklyOverviewOfInstrument();
    if (res) {
      weeklyOverview.value = res;
    }
    baseDataLoaded.value = true;
  });

  const chartOptions = computed(() => {
    return {
      chart: {
        type: 'line',
        height: 350,
        fontFamily: `inherit`,
        foreColor: getSecondary.value,
        toolbar: {
          show: false
        },
        zoom: {
          enabled: false
        },
        animations: {
          enabled: false
        }
      },
      colors: [getdarkPrimary.value],
      xaxis: {
        categories: weeklyOverview.value?.weekly_overviews.map((item) => item.week_number) || []
      },
      markers: {
        size: 6,
        hover: {
          size: 8
        }
      },
      dataLabels: {
        enabled: false
      },
      stroke: {
        curve: 'straight'
      },
      tooltip: {
        y: {
          formatter: function (val: number) {
            return val + ' analýz';
          }
        }
      }
    };
  });

  const lineChart = computed(() => {
    return {
      series: [
        {
          name: 'Analýzy',
          data: weeklyOverview.value?.weekly_overviews.map((item) => item.count) || []
        }
      ]
    };
  });
</script>
