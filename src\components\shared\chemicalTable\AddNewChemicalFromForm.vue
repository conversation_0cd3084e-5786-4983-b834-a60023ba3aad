<script setup lang="ts">
  import CustomNumberInput from '@/components/shared/CustomNumberInput.vue';
  import { useProjectsChemicalsStore, ProjectChemicalStatus } from '@/stores/projectChemicals';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { useVModel } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import { computed, ref, onMounted } from 'vue';
  import { type UpdateChemicalDto } from '@/stores/forms';
  import { notification } from 'ant-design-vue';
  import { isAllowed } from '@/utils/directive/isAllowed';
  import { useRoute } from 'vue-router';
  const route = useRoute();

  const project_id = computed(() => route.params.project_id as string);
  const projectId = computed(() => parseInt(project_id.value));

  const emits = defineEmits(['update:show']);

  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    },
    chemicalRow: {
      type: Object as () => UpdateChemicalDto,
      required: false
    }
  });
  onMounted(() => {
    modalOptions.value = {
      isEditing: false,
      isCreating: true,
      baseData: undefined,
      newData: {
        csa: undefined,
        shortcut: undefined,
        name: undefined,
        density: undefined,
        molar_mass: undefined,
        status: ProjectChemicalStatus.ACTIVE,
        confirm: false
      }
    };
  });
  const projectChemicals = useProjectsChemicalsStore();
  const { loading, modalOptions } = storeToRefs(projectChemicals);
  const CreateColumnForm = ref();
  async function submitFormToValidate() {
    if (!isAllowed(['manage_project_chemicals'])) {
      const nameOfPermission = 'manage_project_chemicals';
      notification.error({
        message: 'Nedostatečné oprávnění',
        description:
          'Nemáte oprávnění pro přidávání, odebírání a editaci chemikálií v projektu: ' +
          nameOfPermission +
          '.'
      });
    } else {
      if (
        CreateColumnForm.value.isValid &&
        modalOptions.value &&
        modalOptions.value.newData &&
        props.chemicalRow &&
        projectId.value
      ) {
        modalOptions.value.newData.csa = simpleCAS.value;
        modalOptions.value.newData.shortcut = simpleShortcut.value;
        modalOptions.value.newData.confirm = simpleConfirm.value;
        modalOptions.value.newData.name = props.chemicalRow.name;
        modalOptions.value.newData.density = props.chemicalRow.density;
        modalOptions.value.newData.molar_mass = props.chemicalRow.molar_mass;
        modalOptions.value.newData.status = ProjectChemicalStatus.ACTIVE;
        const res = await projectChemicals.createChemicalforTable(projectId.value);
        if (res) {
          emits('update:show');
        }
      }
    }
  }
  const showState = useVModel(props, 'show');
  const simpleConfirm = ref<boolean>(false);
  const simpleShortcut = ref<string | undefined>(undefined);
  const simpleCAS = ref<string | undefined>(undefined);
</script>
<template>
  <v-dialog v-model="showState" class="customer-modal" :z-index="1010">
    <v-card :loading="loading">
      <v-form
        v-if="modalOptions && modalOptions.newData && props.chemicalRow"
        ref="CreateColumnForm"
        class="createColumnForm"
        @submit.prevent="submitFormToValidate"
      >
        <v-card-title class="pa-5">
          <span class="text-h5">Nová chemikálie</span>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col>
              <v-row>
                <v-col cols="12" md="6">
                  <v-label class="mb-2">CAS</v-label>
                  <v-text-field
                    v-model="simpleCAS"
                    single-line
                    placeholder="Zadejte cas"
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></v-text-field>
                </v-col>

                <v-col cols="12" md="6">
                  <v-label class="mb-2">Zkratka</v-label>
                  <v-text-field
                    v-model="simpleShortcut"
                    :rules="itemRequiredRule"
                    single-line
                    placeholder="Zadejte zkratku"
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></v-text-field>
                </v-col>

                <v-col cols="12" md="6">
                  <v-label class="mb-2">Název</v-label>
                  <v-text-field
                    v-model="props.chemicalRow.name"
                    :rules="itemRequiredRule"
                    single-line
                    placeholder="Zadejte název"
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></v-text-field>
                </v-col>

                <v-col cols="12" md="6">
                  <v-label class="mb-2">Hustota (g/cm³)</v-label>
                  <CustomNumberInput
                    v-model="props.chemicalRow.density"
                    single-line
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></CustomNumberInput>
                </v-col>

                <v-col cols="12" md="6">
                  <v-label class="mb-2">Molární hmotnost (g/mol)</v-label>
                  <CustomNumberInput
                    v-model="props.chemicalRow.molar_mass"
                    single-line
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></CustomNumberInput>
                </v-col>
                <v-col cols="12">
                  <div class="d-flex justify-space-between gap-2">
                    <div class="pb-4">
                      <h6 class="text-subtitle-1 mb-0">Potvrzení přidání</h6>
                    </div>
                    <v-switch
                      v-model="simpleConfirm"
                      color="primary"
                      class="switchRight"
                      hide-details
                    ></v-switch>
                  </div>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" variant="text" @click="emits('update:show')">Zrušit</v-btn>
          <v-btn color="primary" variant="flat" type="submit" :loading="loading">
            Přidat chemikálii
          </v-btn>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>
