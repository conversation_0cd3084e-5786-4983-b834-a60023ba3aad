<script setup lang="ts">
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import { ChemicalType, type Chemical, type NewChemicalDataI } from '@/stores/chemicals';
  import HeaderUnitSelect from '@/views/pages/dashboard/projects/project/chemicals/components/HeaderUnitSelect.vue';
  import {
    convertFromBaseUnit,
    convertToBaseUnit
  } from '@/views/pages/dashboard/projects/project/chemicals/components/shared';
  import { computed, onMounted, onUnmounted, provide, ref, watch } from 'vue';
  import Item from './Item.vue';
  import Items from './Items.vue';
  import type { ChemicalTableChemicalDataI } from '@/views/pages/dashboard/projects/project/chemicals/components/ChemicalsTable2.vue';
  import { type PaginatorRequestDataI } from '@/stores/projects';
  import { ProjectChemical, useProjectsChemicalsStore } from '@/stores/projectChemicals';
  import { useRoute } from 'vue-router';
  import { notification } from 'ant-design-vue';
  import { useChemicalTableWebSocket } from '@/composables/useChemicalTableWebSocket';
  import { useWebSocketStore } from '@/stores/websocket';
  import { getCachedUserColor } from '@/composables/useUserColors';
  import { useUserNames } from '@/composables/useUserNames';

  const route = useRoute();
  const projectChemicals = useProjectsChemicalsStore();
  const emits = defineEmits(['changeMass', 'saveOrder']);
  const ConfirmRef = ref<undefined | typeof ConfirmDlg>(undefined);
  const props = defineProps({
    initValue: {
      type: Array<Chemical>,
      required: false,
      default: []
    },
    formId: {
      type: Number,
      required: false,
      default: 0
    },
    isEditable: {
      type: Boolean,
      default: true
    },
    isReadonly: {
      type: Boolean,
      default: false
    },
    enableWebSocket: {
      type: Boolean,
      default: false
    }
  });

  const chemicals = ref<Array<ChemicalTableChemicalDataI>>([]);
  const firstChemical = ref<ChemicalTableChemicalDataI | undefined>(undefined);
  const resultChemical = ref<ChemicalTableChemicalDataI | undefined>(undefined);

  // WebSocket integration for chemical table (conditional)
  const chemicalTableWS = props.enableWebSocket
    ? useChemicalTableWebSocket({
        tableName: 'chemicals',
        enabled: true,
        syncDelay: 300
      })
    : null;

  // WebSocket store for locking functionality (only if enabled)
  const webSocketStore = props.enableWebSocket ? useWebSocketStore() : null;
  const { getUserName } = useUserNames();

  // Chemical table locking state
  const isEditing = ref(false);
  const hasRequestedAccess = ref(false);
  const unlockTimeout = ref<number | null>(null);

  // Computed properties for locking
  const isLockedByOther = computed(() =>
    webSocketStore ? webSocketStore.isFieldLockedByOther('chemicals') : false
  );

  const isLockedByMe = computed(() =>
    webSocketStore ? webSocketStore.isFieldLockedByMe('chemicals') : false
  );

  const lockInfo = computed(() =>
    webSocketStore ? webSocketStore.getFieldLockInfo('chemicals') : null
  );

  const lockOwnerName = computed(() => {
    if (!lockInfo.value) return '';
    return getUserName(lockInfo.value.user_id);
  });

  const lockOwnerColor = computed(() => {
    if (!lockInfo.value) return 'grey';
    return getCachedUserColor(lockInfo.value.user_id);
  });

  const tableBorderColor = computed(() => {
    if (isLockedByMe.value) return getCachedUserColor(webSocketStore?.currentUserId || '');
    if (isLockedByOther.value) return lockOwnerColor.value;
    if (isEditing.value) return getCachedUserColor(webSocketStore?.currentUserId || '');
    return 'transparent';
  });

  // Chemical table locking methods
  const handleTableFocusIn = async () => {
    if (!props.enableWebSocket || !webSocketStore || props.isReadonly) return;

    console.log('🧪 Chemical table focused - locking table');
    isEditing.value = true;

    // Clear any pending unlock timeout
    if (unlockTimeout.value) {
      window.clearTimeout(unlockTimeout.value);
      unlockTimeout.value = null;
    }

    const success = await webSocketStore.autoLockField('chemicals');
    if (!success) {
      console.warn('⚠️ Failed to lock chemical table');
    }
  };

  const handleTableFocusOut = () => {
    if (!props.enableWebSocket || !webSocketStore || props.isReadonly) return;

    console.log('🧪 Chemical table blurred - scheduling unlock');
    isEditing.value = false;

    // Clear any existing unlock timeout
    if (unlockTimeout.value) {
      window.clearTimeout(unlockTimeout.value);
    }

    // Auto-unlock after 1 second when focus is lost
    if (isLockedByMe.value) {
      unlockTimeout.value = window.setTimeout(() => {
        if (!isEditing.value && webSocketStore) {
          console.log('🔓 Auto-unlocking chemical table');
          webSocketStore.unlockField('chemicals');
        }
        unlockTimeout.value = null;
      }, 1000);
    }
  };

  const requestTableAccess = () => {
    if (!webSocketStore || hasRequestedAccess.value) return;

    hasRequestedAccess.value = true;
    webSocketStore.requestFieldAccess('chemicals', 'Prosím o povolení úpravy tabulky chemikálií');

    // Reset the flag after some time
    setTimeout(() => {
      hasRequestedAccess.value = false;
    }, 30000); // 30 seconds
  };

  // Watch for access response
  watch(
    () => isLockedByMe.value,
    (isLocked) => {
      if (isLocked && hasRequestedAccess.value) {
        hasRequestedAccess.value = false;
      }
    }
  );

  // Auto-unlock function for action buttons
  const autoUnlockAfterAction = () => {
    if (chemicalTableWS && props.enableWebSocket) {
      // Calculate delay based on pending updates
      const pendingCount = pendingUpdates.value.size;
      const baseDelay = 2000; // 2 seconds minimum
      const updateDelay = pendingCount * 2000; // 1.5 seconds per update
      const totalDelay = baseDelay + updateDelay;

      console.log('⏰ Setting auto-unlock timer:', {
        pendingUpdates: pendingCount,
        baseDelay,
        updateDelay,
        totalDelay: totalDelay + 'ms'
      });

      // Reset editing state and unlock after calculated delay
      setTimeout(() => {
        isEditing.value = false;
        console.log('👁️ Reset isEditing state after action button');
      }, totalDelay);

      // Use the built-in scheduleAutoUnlock method with calculated delay
      chemicalTableWS.scheduleAutoUnlock(totalDelay);
    }
  };

  // Cleanup on unmount
  onUnmounted(() => {
    if (unlockTimeout.value) {
      window.clearTimeout(unlockTimeout.value);
    }
    if (updateTimeout.value) {
      window.clearTimeout(updateTimeout.value);
      // Process any remaining updates before unmounting
      processPendingUpdates();
    }
  });

  // Provide WebSocket functionality to child components (only if enabled)
  provide('chemicalTableWebSocket', chemicalTableWS);

  const selectedRowIndex = ref(0);
  const selectedUnitDensity = ref('g');
  const unitDensity = ref([
    {
      title: 'g/cm³',
      value: 'g'
    },
    {
      title: 'kg/m³',
      value: 'kg'
    }
  ]);

  const selectedUnitMolarMass = ref('g');
  const unitMolarMass = ref([
    {
      title: 'g/mol',
      value: 'g'
    }
  ]);

  const selectedUnitVolume = ref('ml');
  const unitVolume = ref([
    {
      title: 'µl',
      value: 'ul'
    },
    {
      title: 'ml',
      value: 'ml'
    },
    {
      title: 'L',
      value: 'l'
    }
  ]);
  const selectedUnitMass = ref('g');
  const unitMass = ref([
    {
      title: 'mg',
      value: 'mg'
    },
    {
      title: 'g',
      value: 'g'
    },
    {
      title: 'kg',
      value: 'kg'
    }
  ]);
  const selectedUnitMoles = ref('mol');
  const unitMoles = ref([
    {
      title: 'mmol',
      value: 'mmol'
    },
    {
      title: 'mol',
      value: 'mol'
    }
  ]);

  const columnVisibility = ref({
    name: true,
    concentration: true,
    density: true,
    molar_mass: true,
    grams: true,
    moles: true,
    volume_ml: true,
    equivalent: true,
    notes: true,
    action: true
  });

  const baseColumnWidths = {
    name: 400,
    concentration: 45,
    density: 50,
    molar_mass: 60,
    grams: 60,
    moles: 60,
    volume_ml: 45,
    equivalent: 50,
    notes: 400,
    action: 160
  } as const;
  type ColumnKey = keyof typeof baseColumnWidths;
  const hiddenColumnWidth = 28;
  const gridTemplateColumns = computed(() => {
    const columns: ColumnKey[] = Object.keys(baseColumnWidths) as ColumnKey[];
    const visibleColumns = columns.filter((col) => col === 'action' || columnVisibility.value[col]);
    const hiddenColumns = columns.filter((col) => col !== 'action' && !columnVisibility.value[col]);
    const totalFreedWidth = hiddenColumns.reduce(
      (sum, col) => sum + (baseColumnWidths[col] - hiddenColumnWidth),
      0
    );
    const extraPerVisible = visibleColumns.length > 0 ? totalFreedWidth / visibleColumns.length : 0;
    return columns
      .map((col) => {
        if (col === 'action' || columnVisibility.value[col]) {
          return `${baseColumnWidths[col] + extraPerVisible}px`;
        } else {
          return `${hiddenColumnWidth}px`;
        }
      })
      .join(' ');
  });

  const toggleColumnVisibility = (columnName: keyof typeof columnVisibility.value) => {
    const visibleCount = Object.values(columnVisibility.value).filter((visible) => visible).length;
    if (visibleCount <= 1 && columnVisibility.value[columnName]) return;

    columnVisibility.value[columnName] = !columnVisibility.value[columnName];

    localStorage.setItem('chemicalTableColumnVisibility', JSON.stringify(columnVisibility.value));
  };

  provide(
    'selectedUnitDensity',
    computed(() => selectedUnitDensity.value)
  );
  provide(
    'selectedUnitMolarMass',
    computed(() => selectedUnitMolarMass.value)
  );
  provide(
    'selectedUnitVolume',
    computed(() => selectedUnitVolume.value)
  );
  provide(
    'selectedUnitMass',
    computed(() => selectedUnitMass.value)
  );
  provide(
    'selectedUnitMoles',
    computed(() => selectedUnitMoles.value)
  );
  provide(
    'columnVisibility',
    computed(() => columnVisibility.value)
  );

  onMounted(async () => {
    // Load column visibility from localStorage
    const savedColumnVisibility = localStorage.getItem('chemicalTableColumnVisibility');
    if (savedColumnVisibility) {
      try {
        const parsed = JSON.parse(savedColumnVisibility);
        Object.assign(columnVisibility.value, parsed);
      } catch (e) {
        console.warn('Failed to parse saved column visibility:', e);
      }
    }

    baseDataLoaded.value = false;
    const projectChemicalForAutocompleteOptions = ref<PaginatorRequestDataI<ProjectChemical>>({
      loading: false,

      search: '',
      search_type: 'OR',
      search_columns: ['name', 'csa', 'shortcut'],

      totalItems: 0,
      options: {
        page: 1,
        rowsPerPage: 5,
        sortBy: [],
        sortType: []
      },

      filterOptions: [],
      results: []
    });
    await projectChemicals.getProjectChemicalForAutocomplete(
      projectChemicalForAutocompleteOptions.value,
      false,
      projectId.value
    );
    const firstChemicalInit = props.initValue.find(
      (chemical) => chemical.type === ChemicalType.FIRST_CHEMICAL
    );
    if (firstChemicalInit) {
      firstChemical.value = {
        chemical_id: firstChemicalInit.chemical_id,
        name: firstChemicalInit.name,
        density: firstChemicalInit.density,
        molar_mass: firstChemicalInit.molar_mass,
        notes: validateString(firstChemicalInit.notes),
        grams: firstChemicalInit.grams,
        moles: firstChemicalInit.moles,
        equivalent: firstChemicalInit.equivalent,
        volume_ml: firstChemicalInit.volume_ml,
        concentration: validateInput(firstChemicalInit.concentration),
        type: firstChemicalInit.type,

        options: {
          isSave: true,
          isEdit: true,
          cellData: {
            name: {
              editable: true,
              isEdited: false
            },
            concentration: {
              editable: true,
              isEdited: false
            },
            density: {
              editable: true,
              isEdited: false
            },
            molar_mass: {
              editable: true,
              isEdited: false
            },
            grams: {
              editable: true,
              isEdited: false
            },
            moles: {
              editable: true,
              isEdited: false
            },
            volume_ml: {
              editable: true,
              isEdited: false
            },
            equivalent: {
              editable: false,
              isEdited: false
            },
            notes: {
              editable: true,
              isEdited: false
            }
          }
        }
      };
    } else {
      firstChemical.value = {
        chemical_id: getUniqueChemicalIdAndTestIsNotIn(),
        name: '',
        density: 0,
        molar_mass: 0,
        notes: '',
        grams: 0,
        moles: 0,
        equivalent: 1,
        volume_ml: 0,
        concentration: 100,
        type: ChemicalType.FIRST_CHEMICAL,

        options: {
          isSave: false,
          isEdit: false,
          cellData: {
            name: {
              editable: true,
              isEdited: false
            },
            concentration: {
              editable: true,
              isEdited: false
            },
            density: {
              editable: true,
              isEdited: false
            },
            molar_mass: {
              editable: true,
              isEdited: false
            },
            grams: {
              editable: true,
              isEdited: false
            },
            moles: {
              editable: true,
              isEdited: false
            },
            volume_ml: {
              editable: true,
              isEdited: false
            },
            equivalent: {
              editable: false,
              isEdited: false
            },
            notes: {
              editable: true,
              isEdited: false
            }
          }
        }
      };
    }

    const resultChemicalInit = props.initValue.find(
      (chemical) => chemical.type === ChemicalType.RESULT
    );
    if (resultChemicalInit) {
      resultChemical.value = resultChemical.value = {
        chemical_id: resultChemicalInit.chemical_id,

        name: resultChemicalInit.name || '',
        density: resultChemicalInit.density,
        molar_mass: resultChemicalInit.molar_mass,
        notes: validateString(resultChemicalInit.notes),
        grams: resultChemicalInit.grams,
        moles: resultChemicalInit.moles,
        equivalent: resultChemicalInit.equivalent,
        volume_ml: resultChemicalInit.volume_ml,
        concentration: validateInput(resultChemicalInit.concentration),
        type: resultChemicalInit.type,

        options: {
          isSave: true,
          isEdit: false,
          cellData: {
            name: {
              editable: false,
              isEdited: false
            },
            concentration: {
              editable: true,
              isEdited: false
            },
            density: {
              editable: false,
              isEdited: false
            },
            molar_mass: {
              editable: true,
              isEdited: false
            },
            grams: {
              editable: true,
              isEdited: false
            },
            moles: {
              editable: true,
              isEdited: false
            },
            volume_ml: {
              editable: false,
              isEdited: false
            },
            equivalent: {
              editable: true,
              isEdited: false
            },
            notes: {
              editable: true,
              isEdited: false
            }
          }
        }
      };
    } else {
      resultChemical.value = {
        chemical_id: getUniqueChemicalIdAndTestIsNotIn(),
        name: '',
        density: 0,
        molar_mass: 0,
        notes: '',
        grams: 0,
        moles: 0,
        equivalent: 0,
        volume_ml: 0,
        concentration: 100,
        type: ChemicalType.RESULT,

        options: {
          isSave: false,
          isEdit: false,
          cellData: {
            name: {
              editable: true,
              isEdited: false
            },
            concentration: {
              editable: true,
              isEdited: false
            },
            density: {
              editable: false,
              isEdited: false
            },
            molar_mass: {
              editable: true,
              isEdited: false
            },
            grams: {
              editable: true,
              isEdited: false
            },
            moles: {
              editable: true,
              isEdited: false
            },
            volume_ml: {
              editable: false,
              isEdited: false
            },
            equivalent: {
              editable: true,
              isEdited: false
            },
            notes: {
              editable: true,
              isEdited: false
            }
          }
        }
      };
    }

    chemicals.value = props.initValue
      .filter((chemical) => chemical.type === ChemicalType.CHEMICAL)
      .map((row) => {
        return {
          chemical_id: row.chemical_id,

          name: row.name || '',
          density: row.density,
          molar_mass: row.molar_mass,
          notes: validateString(row.notes),
          grams: row.grams,
          moles: row.moles,
          equivalent: row.equivalent,
          volume_ml: row.volume_ml,
          concentration: validateInput(
            row.concentration === undefined || row.concentration === 0 ? 100 : row.concentration
          ),
          type: row.type,

          options: {
            isSave: true,
            isEdit: false,
            cellData: {
              name: {
                editable: true,
                isEdited: false,
                valueBeforeEdit: row.name
              },
              concentration: {
                editable: true,
                isEdited: false,
                valueBeforeEdit: row.concentration
              },
              density: {
                editable: true,
                isEdited: false,
                valueBeforeEdit: row.density
              },
              molar_mass: {
                editable: true,
                isEdited: false,
                valueBeforeEdit: row.molar_mass
              },
              grams: {
                editable: true,
                isEdited: false,
                valueBeforeEdit: row.grams
              },
              moles: {
                editable: true,
                isEdited: false,
                valueBeforeEdit: row.moles
              },
              volume_ml: {
                editable: true,
                isEdited: false,
                valueBeforeEdit: row.volume_ml
              },
              equivalent: {
                editable: true,
                isEdited: false,
                valueBeforeEdit: row.equivalent
              },
              notes: {
                editable: true,
                isEdited: false,
                valueBeforeEdit: row.notes
              }
            }
          }
        };
      });
    //await checkLocalStorage();
    emits('changeMass', selectedUnitMass.value);
    baseDataLoaded.value = true;
  });

  const baseDataLoaded = ref(false);

  const project_id = computed(() => route.params.project_id as string);
  const projectId = computed(() => parseInt(project_id.value));

  const project_department_id = computed(() => route.params.project_department_id as string);
  const projectDepartmentId = computed(() => parseInt(project_department_id.value));

  const chapter_id = computed(() => route.params.chapter_id as string);
  const chapterId = computed(() => parseInt(chapter_id.value));

  const form_id = computed(() => route.params.form_id as string);
  const experimentId = computed(() => parseInt(form_id.value));

  const getUniqueChemicalIdAndTestIsNotIn = (): number => {
    let id = chemicals.value.length
      ? Math.max(...chemicals.value.map((chemical) => chemical.chemical_id)) + 1
      : 1;

    while (chemicals.value.find((chemical) => chemical.chemical_id === id)) {
      id++;
    }
    return id;
  };

  const addRow = () => {
    chemicals.value.push({
      chemical_id: getUniqueChemicalIdAndTestIsNotIn(),
      name: '',
      notes: '',
      density: 0,
      molar_mass: 0,
      grams: 0,
      moles: 0,
      equivalent: 0,
      volume_ml: 0,
      concentration: 100,
      type: ChemicalType.CHEMICAL,

      options: {
        isSave: false,
        isEdit: false,
        cellData: {
          name: {
            editable: true,
            isEdited: false,
            valueBeforeEdit: ''
          },
          concentration: {
            editable: true,
            isEdited: false,
            valueBeforeEdit: 100
          },
          density: {
            editable: true,
            isEdited: false,
            valueBeforeEdit: 0
          },
          molar_mass: {
            editable: true,
            isEdited: false,
            valueBeforeEdit: 0
          },
          grams: {
            editable: true,
            isEdited: false,
            valueBeforeEdit: 0
          },
          moles: {
            editable: true,
            isEdited: false,
            valueBeforeEdit: 0
          },
          volume_ml: {
            editable: true,
            isEdited: false,
            valueBeforeEdit: 0
          },
          equivalent: {
            editable: true,
            isEdited: false,
            valueBeforeEdit: 0
          },
          notes: {
            editable: true,
            isEdited: false,
            valueBeforeEdit: ''
          }
        }
      }
    });
  };

  const removeChemical = (chemical_id: number) => {
    const index = chemicals.value.findIndex((chemical) => chemical.chemical_id === chemical_id);
    if (index !== -1) {
      chemicals.value.splice(index, 1);
    }
  };

  const saveChemicalRow = (chemical_id: number) => {
    const row = allChemicals.value.find((chemical) => chemical.chemical_id === chemical_id);
  };

  const triggerSuccessNotification = (message: string) => {
    notification.success({
      message: message
    });
  };

  const triggerErrorNotification = (message: string) => {
    notification.error({
      message: message
    });
  };

  function calculateFirstRowData(row: ChemicalTableChemicalDataI) {
    let calculationMade = false;

    const molarMassBase = ref(row.molar_mass);
    const gramsBase = ref(convertToBaseUnit(row.grams, selectedUnitMass.value, 'mass'));
    const densityBase = ref(
      convertToBaseUnit(row.density, selectedUnitDensity.value, 'density') / 1000
    );

    if (
      (row.volume_ml === undefined || row.volume_ml === 0) &&
      gramsBase.value &&
      densityBase.value
    ) {
      row.volume_ml = dynamicRound(
        convertFromBaseUnit(gramsBase.value / densityBase.value, selectedUnitVolume.value, 'volume')
      );
      calculationMade = true;
    }
    if ((row.moles === undefined || row.moles === 0) && gramsBase.value && molarMassBase.value) {
      row.moles = dynamicRound(
        convertFromBaseUnit(
          gramsBase.value / molarMassBase.value,
          selectedUnitMoles.value,
          'moles'
        ) * 1000
      );
      calculationMade = true;
    }

    return calculationMade;
  }

  function calculateFromMass(
    row: ChemicalTableChemicalDataI,
    firstrow: ChemicalTableChemicalDataI
  ) {
    let calculationMade = false;

    const molarMassBase = ref(row.molar_mass);
    const gramsBase = ref(convertToBaseUnit(row.grams, selectedUnitMass.value, 'mass'));
    const densityBase = ref(
      convertToBaseUnit(row.density, selectedUnitDensity.value, 'density') / 1000
    );
    const validConcentration = validateConcentration(row.concentration);
    const concentrationBase = ref(validConcentration !== null ? validConcentration / 100 : 1);
    const molesBase = ref(convertToBaseUnit(row.moles, selectedUnitMoles.value, 'moles'));
    const firstRowMoles = ref(convertToBaseUnit(firstrow.moles, selectedUnitMoles.value, 'moles'));

    if (
      (row.volume_ml === undefined || row.volume_ml === 0) &&
      gramsBase.value &&
      densityBase.value
    ) {
      row.volume_ml = dynamicRound(
        convertFromBaseUnit(gramsBase.value / densityBase.value, selectedUnitVolume.value, 'volume')
      );
      calculationMade = true;
    }

    if ((row.moles === undefined || row.moles === 0) && gramsBase.value && molarMassBase.value) {
      row.moles = dynamicRound(
        convertFromBaseUnit(
          (gramsBase.value * concentrationBase.value) / molarMassBase.value,
          selectedUnitMoles.value,
          'moles'
        ) * 1000
      );
      calculationMade = true;
    }

    if ((row.equivalent === undefined || row.equivalent === 0) && row.moles && firstrow.moles) {
      row.equivalent = dynamicRound(molesBase.value / firstRowMoles.value);
      calculationMade = true;
    }

    return calculationMade;
  }

  function calculateFromVolume(
    row: ChemicalTableChemicalDataI,
    firstrow: ChemicalTableChemicalDataI
  ) {
    let calculationMade = false;

    const molarMassBase = ref(row.molar_mass);
    const gramsBase = ref(convertToBaseUnit(row.grams, selectedUnitMass.value, 'mass'));
    const volumeBase = ref(convertToBaseUnit(row.volume_ml, selectedUnitVolume.value, 'volume'));
    const densityBase = ref(
      convertToBaseUnit(row.density, selectedUnitDensity.value, 'density') / 1000
    );
    const validConcentration = validateConcentration(row.concentration);
    const concentrationBase = ref(validConcentration !== null ? validConcentration / 100 : 1);
    const molesBase = ref(convertToBaseUnit(row.moles, selectedUnitMoles.value, 'moles'));
    const firstRowMoles = ref(convertToBaseUnit(firstrow.moles, selectedUnitMoles.value, 'moles'));

    if ((row.grams === undefined || row.grams === 0) && volumeBase.value && densityBase.value) {
      row.grams = dynamicRound(
        convertFromBaseUnit(volumeBase.value * densityBase.value, selectedUnitMass.value, 'mass')
      );
      gramsBase.value = convertToBaseUnit(row.grams, selectedUnitMass.value, 'mass');
      calculationMade = true;
    }
    if ((row.moles === undefined || row.moles === 0) && gramsBase.value && molarMassBase.value) {
      row.moles = dynamicRound(
        convertFromBaseUnit(
          (gramsBase.value * concentrationBase.value) / molarMassBase.value,
          selectedUnitMoles.value,
          'moles'
        ) * 1000
      );
      calculationMade = true;
    }
    if ((row.equivalent === undefined || row.equivalent === 0) && row.moles && firstrow.moles) {
      row.equivalent = dynamicRound(molesBase.value / firstRowMoles.value);
      calculationMade = true;
    }

    return calculationMade;
  }

  function calculateFromEquivalent(
    row: ChemicalTableChemicalDataI,
    firstrow: ChemicalTableChemicalDataI
  ) {
    let calculationMade = false;

    const molarMassBase = ref(row.molar_mass);
    const equivalentBase = ref(row.equivalent);
    const gramsBase = ref(convertToBaseUnit(row.grams, selectedUnitMass.value, 'mass'));
    const volumeBase = ref(convertToBaseUnit(row.volume_ml, selectedUnitVolume.value, 'volume'));
    const densityBase = ref(
      convertToBaseUnit(row.density, selectedUnitDensity.value, 'density') / 1000
    );
    const validConcentration = validateConcentration(row.concentration);
    const concentrationBase = ref(validConcentration !== null ? validConcentration / 100 : 1);
    const molesBase = ref(convertToBaseUnit(row.moles, selectedUnitMoles.value, 'moles'));
    const firstRowMoles = ref(convertToBaseUnit(firstrow.moles, selectedUnitMoles.value, 'moles'));

    if ((row.moles === undefined || row.moles === 0) && firstrow.moles && equivalentBase.value) {
      row.moles = dynamicRound(
        convertFromBaseUnit(
          firstRowMoles.value * equivalentBase.value,
          selectedUnitMoles.value,
          'moles'
        )
      );
      calculationMade = true;
    }
    if ((row.grams === undefined || row.grams === 0) && row.moles && molarMassBase.value) {
      row.grams = (molesBase.value * molarMassBase.value) / concentrationBase.value;
      row.grams = dynamicRound(
        convertFromBaseUnit(row.grams, selectedUnitMass.value, 'mass') / 1000
      );
      gramsBase.value = convertToBaseUnit(row.grams, selectedUnitMass.value, 'mass');
      calculationMade = true;
    }

    if ((row.volume_ml === undefined || row.volume_ml === 0) && gramsBase && densityBase.value) {
      row.volume_ml = dynamicRound(
        convertFromBaseUnit(gramsBase.value / densityBase.value, selectedUnitVolume.value, 'volume')
      );
      volumeBase.value = convertToBaseUnit(row.volume_ml, selectedUnitVolume.value, 'volume');
      calculationMade = true;
    }

    return calculationMade;
  }

  function calculateLastRowData(
    row: ChemicalTableChemicalDataI,
    firstrow: ChemicalTableChemicalDataI
  ) {
    let calculationMade = false;
    const molarMassBase = ref(row.molar_mass);
    const gramsBase = ref(convertToBaseUnit(row.grams, selectedUnitMass.value, 'mass'));
    const validConcentration = validateConcentration(row.concentration);
    const concentrationBase = ref(validConcentration !== null ? validConcentration / 100 : 1);
    const equivalentBase = ref(row.equivalent);
    const molesBase = ref(convertToBaseUnit(row.moles, selectedUnitMoles.value, 'moles'));
    const firstRowMoles = ref(convertToBaseUnit(firstrow.moles, selectedUnitMoles.value, 'moles'));
    if ((row.moles === undefined || row.moles === 0) && firstrow.moles && equivalentBase.value) {
      row.moles = dynamicRound(
        convertFromBaseUnit(
          firstRowMoles.value * equivalentBase.value,
          selectedUnitMoles.value,
          'moles'
        )
      );
      calculationMade = true;
    }
    if ((row.grams === undefined || row.grams === 0) && row.moles && molarMassBase.value) {
      row.grams = (molesBase.value * molarMassBase.value) / concentrationBase.value;
      row.grams = dynamicRound(
        convertFromBaseUnit(row.grams, selectedUnitMass.value, 'mass') / 1000
      );
      gramsBase.value = convertToBaseUnit(row.grams, selectedUnitMass.value, 'mass');
      calculationMade = true;
    }
    return calculationMade;
  }

  function validateConcentration(concentration: any): number | null {
    const parsedConcentration = parseFloat(concentration);
    if (
      !isNaN(parsedConcentration) &&
      isFinite(parsedConcentration) &&
      parsedConcentration >= 0 &&
      parsedConcentration <= 100
    ) {
      return parsedConcentration;
    }
    return null;
  }

  async function calculateRowData(
    chemical_id: number,
    suppressErrorNotification: boolean = false
  ): Promise<void> {
    const firstRow = firstChemical.value;
    const row = allChemicals.value.find((chemical) => chemical.chemical_id === chemical_id);

    if (!firstRow || !row) return;

    if (row.concentration === undefined || row.concentration === 0) {
      row.concentration = 100;
    }

    const initialState = {
      volume_ml: row.volume_ml,
      moles: row.moles,
      grams: row.grams,
      equivalent: row.equivalent
    };

    const calculateRecursively = (iterationsLeft: number = 5): boolean => {
      if (iterationsLeft <= 0) return false;

      let madeChanges = false;

      if (row.type === ChemicalType.FIRST_CHEMICAL) {
        madeChanges = calculateFirstRowData(row);
      } else {
        const massResult = calculateFromMass(row, firstRow);
        const volumeResult = calculateFromVolume(row, firstRow);
        const equivalentResult = calculateFromEquivalent(row, firstRow);

        madeChanges = massResult || volumeResult || equivalentResult;
      }
      if (madeChanges) {
        return calculateRecursively(iterationsLeft - 1) || true;
      }

      return madeChanges;
    };

    const calculationSuccess = calculateRecursively();
    const finalStateChanged =
      initialState.volume_ml !== row.volume_ml ||
      initialState.moles !== row.moles ||
      initialState.grams !== row.grams ||
      initialState.equivalent !== row.equivalent;

    if (calculationSuccess && finalStateChanged) {
      triggerSuccessNotification('Údaje byli úspěšně dopočítany.');
    } else if (!suppressErrorNotification) {
      triggerErrorNotification('Nepodařilo se dopočítat údaje.');
    }

    // Auto-unlock the table after action button
    autoUnlockAfterAction();
  }

  const calculateRowDataLast = async (
    chemical_id: number,
    suppressErrorNotification: boolean = false
  ) => {
    const firstRow = firstChemical.value;
    const row = allChemicals.value.find((chemical) => chemical.chemical_id === chemical_id);

    let calculationTriggered = false;
    if (!firstRow || !row) return;
    calculationTriggered = calculateLastRowData(row, firstRow);

    if (calculationTriggered) {
      triggerSuccessNotification('Údaje byli úspěšně dopočítany.');
    } else if (!suppressErrorNotification) {
      triggerErrorNotification('Nepodařilo se dopočítat údaje.');
    }

    // Auto-unlock the table after action button
    autoUnlockAfterAction();
  };

  function dynamicRound(number: number): number {
    if (number === 0) return 0;

    const significantFigures = 4;
    const digits = -Math.floor(Math.log10(Math.abs(number)));
    const factor = Math.pow(10, digits + significantFigures - 1);

    return Math.round(number * factor) / factor;
  }

  // Function to convert density units
  function convertDensity(value: number, fromUnit: string, toUnit: string): number {
    if (fromUnit === 'g' && toUnit === 'kg') {
      return value * 1000; // g/cm³ to Kg/m³
    } else if (fromUnit === 'kg' && toUnit === 'g') {
      return value / 1000; // Kg/m³ to g/cm³
    }
    return value; // no conversion needed
  }

  // Function to convert molar mass units
  function convertMolarMass(value: number, fromUnit: string, toUnit: string): number {
    if (fromUnit === 'g' && toUnit === 'kg') {
      return value / 1000; // g/mol to Kg/mol
    } else if (fromUnit === 'kg' && toUnit === 'g') {
      return value * 1000; // Kg/mol to g/mol
    }
    return value; // no conversion needed
  }

  function convertMass(value: number, fromUnit: string, toUnit: string): number {
    const massConversions: Record<string, number> = {
      g: 1,
      kg: 1000, // 1 kg = 1000 g
      mg: 0.001 // 1 mg = 0.001 g
    };

    if (massConversions[fromUnit] && massConversions[toUnit]) {
      return value * (massConversions[fromUnit] / massConversions[toUnit]);
    }

    return value; // no conversion if units are invalid or identical
  }

  // Function to convert volume units
  function convertVolume(value: number, fromUnit: string, toUnit: string): number {
    const volumeConversions: Record<string, number> = {
      ml: 1,
      l: 1000, // 1 L = 1000 ml
      ul: 0.001 // 1 µl = 0.001 ml
    };

    if (volumeConversions[fromUnit] && volumeConversions[toUnit]) {
      return value * (volumeConversions[fromUnit] / volumeConversions[toUnit]);
    }

    return value; // no conversion if units are invalid or identical
  }

  function convertMoles(value: number, fromUnit: string, toUnit: string): number {
    const moleConversions: Record<string, number> = {
      mol: 1,
      mmol: 0.001 // 1 mmol = 0.001 mol
    };

    if (moleConversions[fromUnit] && moleConversions[toUnit]) {
      return value * (moleConversions[fromUnit] / moleConversions[toUnit]);
    }

    return value; // no conversion if units are invalid or identical
  }

  function updateDensityUnit(newUnit: string, oldUnit: string) {
    allChemicals.value.forEach((row) => {
      row.density = convertDensity(row.density, oldUnit, newUnit);
    });
    selectedUnitDensity.value = newUnit;
  }

  function updateMolarMassUnit(newUnit: string, oldUnit: string) {
    allChemicals.value.forEach((row) => {
      row.molar_mass = convertMolarMass(row.molar_mass, oldUnit, newUnit);
    });
    selectedUnitMolarMass.value = newUnit;
  }

  function updateVolumeUnit(newUnit: string, oldUnit: string) {
    allChemicals.value.forEach((row) => {
      row.volume_ml = convertVolume(row.volume_ml, oldUnit, newUnit);
    });
    selectedUnitVolume.value = newUnit;
  }

  function updateMolesUnit(newUnit: string, oldUnit: string) {
    allChemicals.value.forEach((row) => {
      row.moles = convertMoles(row.moles, oldUnit, newUnit);
    });
    selectedUnitMoles.value = newUnit;
  }

  function updateMassUnit(newUnit: string, oldUnit: string) {
    allChemicals.value.forEach((row) => {
      row.grams = convertMass(row.grams, oldUnit, newUnit);
    });
    selectedUnitMass.value = newUnit;
    emits('changeMass', newUnit);
  }

  watch(selectedUnitDensity, (newUnit, oldUnit) => {
    updateDensityUnit(newUnit, oldUnit);
  });

  watch(selectedUnitMolarMass, (newUnit, oldUnit) => {
    updateMolarMassUnit(newUnit, oldUnit);
  });

  watch(selectedUnitVolume, (newUnit, oldUnit) => {
    updateVolumeUnit(newUnit, oldUnit);
  });

  watch(selectedUnitMass, (newUnit, oldUnit) => {
    updateMassUnit(newUnit, oldUnit);
  });
  watch(selectedUnitMoles, (newUnit, oldUnit) => {
    updateMolesUnit(newUnit, oldUnit);
  });
  const allChemicals = computed(() => {
    const all = [...chemicals.value];
    if (firstChemical.value) all.push(firstChemical.value);
    if (resultChemical.value) all.push(resultChemical.value);

    return all;
  });

  function getAllChemicalInBaseUnitToUpdateModel() {
    const modelValue: NewChemicalDataI[] = [];

    allChemicals.value.forEach((chemical) => {
      modelValue.push({
        name: chemical.name,
        density: selectedUnitDensity.value
          ? convertDensity(chemical.density, selectedUnitDensity.value, 'g')
          : chemical.density,
        molar_mass: selectedUnitMolarMass.value
          ? convertMolarMass(chemical.molar_mass, selectedUnitMolarMass.value, 'g')
          : chemical.molar_mass,
        notes: chemical.notes,
        grams: selectedUnitMass.value
          ? convertMass(chemical.grams, selectedUnitMass.value, 'g')
          : chemical.grams,
        moles: chemical.moles,
        equivalent: chemical.equivalent,
        volume_ml: selectedUnitVolume.value
          ? convertVolume(chemical.volume_ml, selectedUnitVolume.value, 'ml')
          : chemical.volume_ml,
        concentration: validateInput(chemical.concentration ? chemical.concentration : 100),
        type: chemical.type
      });
    });

    return modelValue;
  }

  const validateString = (value: string) => {
    if (value === 'N/A') {
      return '';
    }
    return value;
  };

  const validateInput = (concentration: any) => {
    const input = concentration.toString();
    if (input) {
      if (/^\d+([.,]\d+)?$/.test(input)) {
        return parseFloat(input.replace(',', '.'));
      } else {
        return '';
      }
    } else {
      return '';
    }
  };

  defineExpose({
    getAllChemicalInBaseUnitToUpdateModel
  });

  const combinedChemicals = computed(() => {
    const all = [...chemicals.value];
    if (firstChemical.value) all.push(firstChemical.value);
    if (resultChemical.value) all.push(resultChemical.value);
    return all;
  });

  // Store previous state for comparison
  const previousChemicalsState = ref<any[]>([]);

  // Debounced WebSocket updates system
  const pendingUpdates = ref<Map<string, any>>(new Map());
  const updateTimeout = ref<number | null>(null);
  const DEBOUNCE_DELAY = 1000; // ms

  // Flag to prevent triggering updates on remote changes
  const isReceivingRemoteUpdate = ref(false);

  // Function to queue a WebSocket update
  const queueWebSocketUpdate = (chemicalId: number, field: string, value: any) => {
    const updateKey = `${chemicalId}-${field}`;
    pendingUpdates.value.set(updateKey, {
      chemicalId,
      field,
      value,
      timestamp: Date.now()
    });

    console.log('📝 Queued update:', {
      chemicalId,
      field,
      value,
      queueSize: pendingUpdates.value.size
    });
    console.log('Pending updates:', pendingUpdates.value);

    // Clear existing timeout
    if (updateTimeout.value) {
      window.clearTimeout(updateTimeout.value);
    }

    // Set new timeout to process all pending updates
    updateTimeout.value = window.setTimeout(() => {
      processPendingUpdates();
    }, DEBOUNCE_DELAY);
  };

  // Function to process all pending updates
  const processPendingUpdates = () => {
    if (!chemicalTableWS || !props.enableWebSocket) {
      pendingUpdates.value.clear();
      return;
    }

    const updates = Array.from(pendingUpdates.value.values());
    console.log('🚀 Processing batch updates:', updates.length, 'updates');

    // Send each update with a small delay to prevent conflicts
    updates.forEach((update, index) => {
      setTimeout(() => {
        console.log('📤 Sending WebSocket update:', {
          chemicalId: update.chemicalId,
          field: update.field,
          value: update.value
        });

        // Determine action based on chemical ID
        const action = update.chemicalId ? 'update' : 'add';
        chemicalTableWS.sendChemicalFieldUpdate(
          update.chemicalId,
          update.field,
          update.value,
          action
        );
      }, index * 2000); // 50ms delay between each update
    });

    // Clear pending updates
    pendingUpdates.value.clear();
    updateTimeout.value = null;
  };

  watch(
    combinedChemicals,
    (newVal, oldVal) => {
      // Skip WebSocket updates if this is a remote update to prevent infinite loops
      if (isReceivingRemoteUpdate.value) {
        console.log('⏭️ Skipping watcher WebSocket updates - remote update in progress');
        return;
      }

      // WebSocket updates for chemical table changes (only if WebSocket is enabled)
      if (chemicalTableWS && props.enableWebSocket && newVal && Array.isArray(newVal)) {
        // Skip initial load or if no previous state
        if (!previousChemicalsState.value || previousChemicalsState.value.length === 0) {
          previousChemicalsState.value = JSON.parse(JSON.stringify(newVal));
          return;
        }

        // Compare each chemical for changes
        newVal.forEach((newChemical: any) => {
          if (!newChemical.chemical_id) return;

          // Find the corresponding previous chemical
          const previousChemical = previousChemicalsState.value.find(
            (prev: any) => prev.chemical_id === newChemical.chemical_id
          );

          if (!previousChemical) {
            // New chemical added - send all fields
            console.log('🆕 New chemical detected:', newChemical.chemical_id);
            const fieldsToSend = [
              'name',
              'concentration',
              'density',
              'molar_mass',
              'grams',
              'moles',
              'volume_ml',
              'equivalent',
              'notes'
            ];

            fieldsToSend.forEach((field) => {
              if (newChemical[field] !== undefined && newChemical[field] !== null) {
                queueWebSocketUpdate(newChemical.chemical_id, field, newChemical[field]);
              }
            });
            return;
          }

          // Compare each field for changes
          const fieldsToWatch = [
            'name',
            'concentration',
            'density',
            'molar_mass',
            'grams',
            'moles',
            'volume_ml',
            'equivalent',
            'notes'
          ];

          fieldsToWatch.forEach((field) => {
            const newValue = newChemical[field];
            const oldValue = previousChemical[field];

            if (newValue !== oldValue) {
              console.log('🔄 Chemical field changed via watcher:', {
                chemicalId: newChemical.chemical_id,
                field,
                from: oldValue,
                to: newValue
              });

              // Queue WebSocket update for the changed field
              queueWebSocketUpdate(newChemical.chemical_id, field, newValue);
            }
          });
        });

        // Update previous state for next comparison
        previousChemicalsState.value = JSON.parse(JSON.stringify(newVal));
      }

      /*
      if (experimentId.value && baseDataLoaded.value) {
        const experimentChemicalsFormStore = JSON.parse(
          localStorage.getItem('experimentChemicalsFormStore') || '{}'
        );
        experimentChemicalsFormStore[experimentId.value] = newVal;
        localStorage.setItem(
          'experimentChemicalsFormStore',
          JSON.stringify(experimentChemicalsFormStore)
        );
      }
        */
    },
    { deep: true }
  );

  // Helper function to check if a chemical is empty (no meaningful data)
  const isChemicalEmpty = (chemical: any): boolean => {
    if (!chemical) return true;

    // Check if all important fields are empty/default
    const isEmpty =
      (!chemical.name || chemical.name.trim() === '') &&
      (!chemical.concentration || chemical.concentration === 100) &&
      (!chemical.density || chemical.density === 0) &&
      (!chemical.molar_mass || chemical.molar_mass === 0) &&
      (!chemical.grams || chemical.grams === 0) &&
      (!chemical.moles || chemical.moles === 0) &&
      (!chemical.volume_ml || chemical.volume_ml === 0) &&
      (!chemical.notes || chemical.notes.trim() === '');

    console.log('🔍 Checking if chemical is empty:', {
      chemical_id: chemical.chemical_id,
      name: chemical.name,
      isEmpty
    });

    return isEmpty;
  };

  // Helper function to create a new chemical with given ID
  const createNewChemicalWithId = (chemicalId: number): any => {
    console.log('🏗️ Creating new chemical with ID:', chemicalId);

    return {
      chemical_id: chemicalId,
      name: '',
      concentration: 100,
      density: 0,
      molar_mass: 0,
      grams: 0,
      moles: 0,
      volume_ml: 0,
      equivalent: 1,
      notes: '',
      type: 'chemical' as const,
      options: {
        showConcentration: true,
        showDensity: true,
        showMolarMass: true,
        showGrams: true,
        showMoles: true,
        showVolumeML: true,
        showEquivalent: true,
        showNotes: true,
        showActions: true
      }
    };
  };

  // Handle incoming chemical updates from other users
  const handleIncomingChemicalUpdate = (update: any) => {
    const action = update.action || 'update';

    console.log('📥 Processing incoming chemical update:', {
      chemical_id: update.chemical_id,
      field_name: update.field_name,
      value: update.value,
      action: action,
      userId: update.userId
    });

    if (action === 'add') {
      // Handle new chemical creation
      handleIncomingChemicalAdd(update);
    } else if (action === 'delete') {
      // Handle chemical deletion
      handleIncomingChemicalDelete(update);
    } else {
      // Handle regular field update
      handleIncomingChemicalFieldUpdate(update);
    }
  };

  // Handle incoming chemical addition
  const handleIncomingChemicalAdd = (update: any) => {
    console.log('🆕 Handling incoming chemical addition:', update);

    const newChemicalId = update.chemical_id;
    const fieldName = update.field_name;
    const fieldValue = update.value;

    if (!newChemicalId || !fieldName) {
      console.warn('⚠️ Invalid chemical addition data:', update);
      return;
    }

    // Check if this chemical already exists
    const existingChemical = allChemicals.value.find((c) => c.chemical_id === newChemicalId);
    if (existingChemical) {
      console.log('📝 Chemical already exists, updating field:', newChemicalId, fieldName);
      // Update existing chemical field
      if (fieldName in existingChemical) {
        (existingChemical as any)[fieldName] = fieldValue;
      }
      return;
    }

    // Chemical doesn't exist, need to create new row
    console.log('🆕 Creating new chemical row for User 2:', newChemicalId);

    // Try to use empty default rows first
    if (
      firstChemical.value &&
      (!firstChemical.value.chemical_id || firstChemical.value.chemical_id === 0) &&
      isChemicalEmpty(firstChemical.value)
    ) {
      console.log('🎯 Using empty first chemical row for new chemical:', newChemicalId);
      firstChemical.value.chemical_id = newChemicalId;
      (firstChemical.value as any)[fieldName] = fieldValue;
      return;
    }

    if (
      resultChemical.value &&
      (!resultChemical.value.chemical_id || resultChemical.value.chemical_id === 0) &&
      isChemicalEmpty(resultChemical.value)
    ) {
      console.log('🎯 Using empty result chemical row for new chemical:', newChemicalId);
      resultChemical.value.chemical_id = newChemicalId;
      (resultChemical.value as any)[fieldName] = fieldValue;
      return;
    }

    // Default rows are occupied, create new chemical in main array
    console.log('➕ Adding new chemical to main chemicals array:', newChemicalId);
    const newChemical = createNewChemicalWithId(newChemicalId);
    (newChemical as any)[fieldName] = fieldValue;
    chemicals.value.push(newChemical);
  };

  // Handle incoming chemical deletion
  const handleIncomingChemicalDelete = (update: any) => {
    console.log('🗑️ Handling incoming chemical deletion:', update);

    const chemicalId = update.chemical_id;
    if (!chemicalId) return;

    // Find and remove the chemical from the appropriate array
    const chemicalIndex = chemicals.value.findIndex((c) => c.chemical_id === chemicalId);
    if (chemicalIndex !== -1) {
      console.log('🗑️ Removing chemical from chemicals array:', chemicalId);
      chemicals.value.splice(chemicalIndex, 1);
      return;
    }

    // Check if it's the first chemical
    if (firstChemical.value?.chemical_id === chemicalId) {
      console.log('🗑️ Resetting first chemical to default state');
      // Reset to default empty state
      if (firstChemical.value) {
        firstChemical.value.chemical_id = 0;
        firstChemical.value.name = '';
        firstChemical.value.concentration = 100;
        firstChemical.value.density = 0;
        firstChemical.value.molar_mass = 0;
        firstChemical.value.grams = 0;
        firstChemical.value.moles = 0;
        firstChemical.value.volume_ml = 0;
        firstChemical.value.equivalent = 1;
        firstChemical.value.notes = '';
      }
      return;
    }

    // Check if it's the result chemical
    if (resultChemical.value?.chemical_id === chemicalId) {
      console.log('🗑️ Resetting result chemical to default state');
      // Reset to default empty state
      if (resultChemical.value) {
        resultChemical.value.chemical_id = 0;
        resultChemical.value.name = '';
        resultChemical.value.concentration = 100;
        resultChemical.value.density = 0;
        resultChemical.value.molar_mass = 0;
        resultChemical.value.grams = 0;
        resultChemical.value.moles = 0;
        resultChemical.value.volume_ml = 0;
        resultChemical.value.equivalent = 0;
        resultChemical.value.notes = '';
      }
      return;
    }
  };

  // Handle regular field updates
  const handleIncomingChemicalFieldUpdate = (update: any) => {
    console.log('📝 Handling incoming chemical field update:', update);
    // Regular field updates are already handled by the existing ItemCell watchers
    // This is just for logging and potential future enhancements
  };

  // Watch for incoming WebSocket updates to prevent infinite loops
  if (chemicalTableWS) {
    // Monitor all chemical field updates from other users
    watch(
      () => {
        // Create a reactive dependency on all possible chemical field updates
        const allUpdates: any[] = [];
        allChemicals.value.forEach((chemical) => {
          if (chemical.chemical_id) {
            const fieldsToWatch = [
              'name',
              'concentration',
              'density',
              'molar_mass',
              'grams',
              'moles',
              'volume_ml',
              'equivalent',
              'notes'
            ];
            fieldsToWatch.forEach((field) => {
              const update = chemicalTableWS.getChemicalFieldUpdate(chemical.chemical_id, field);
              if (update) {
                allUpdates.push(update);
              }
            });
          }
        });
        return allUpdates;
      },
      (updates) => {
        // Check if any update is from another user
        const hasRemoteUpdate = updates.some(
          (update) => update && update.userId !== chemicalTableWS.webSocketStore?.currentUserId
        );

        if (hasRemoteUpdate) {
          console.log(
            '🔄 Setting isReceivingRemoteUpdate flag - incoming WebSocket updates detected'
          );
          isReceivingRemoteUpdate.value = true;

          // Process different types of updates
          updates.forEach((update) => {
            if (update && update.userId !== chemicalTableWS.webSocketStore?.currentUserId) {
              handleIncomingChemicalUpdate(update);
            }
          });

          // Reset flag after a short delay to allow the watcher to process
          setTimeout(() => {
            isReceivingRemoteUpdate.value = false;
            console.log('✅ Cleared isReceivingRemoteUpdate flag');
          }, 500);
        }
      },
      { deep: true }
    );
  }
  const checkLocalStorage = async () => {
    if (experimentId.value) {
      const experimentChemicalsFormStore = JSON.parse(
        localStorage.getItem('experimentChemicalsFormStore') || '{}'
      );
      if (experimentChemicalsFormStore[experimentId.value]) {
        const storageChemicals = experimentChemicalsFormStore[experimentId.value];
        if (
          storageChemicals.filter(
            (chemical: ChemicalTableChemicalDataI) => chemical.type === ChemicalType.CHEMICAL
          ).length > 0
        ) {
          chemicals.value = [];
        }
        storageChemicals.forEach((chemical: ChemicalTableChemicalDataI) => {
          if (chemical.type === ChemicalType.FIRST_CHEMICAL) {
            firstChemical.value = chemical;
          } else if (chemical.type === ChemicalType.RESULT) {
            resultChemical.value = chemical;
          } else {
            chemicals.value.push(chemical);
          }
        });
      }
    }
  };

  // Move row up in chemicals array
  function moveRowUp(index: number) {
    if (index > 0 && index < chemicals.value.length) {
      const temp = chemicals.value[index - 1];
      chemicals.value[index - 1] = chemicals.value[index];
      chemicals.value[index] = temp;
      autoUnlockAfterAction();
    }
  }

  // Move row down in chemicals array
  function moveRowDown(index: number) {
    if (index < chemicals.value.length - 1) {
      const temp = chemicals.value[index + 1];
      chemicals.value[index + 1] = chemicals.value[index];
      chemicals.value[index] = temp;
      autoUnlockAfterAction();
    }
  }

  function emitSaveOrder() {
    emits('saveOrder');
  }

  function resetCalculatedValues(chemical_id: number) {
    const row = allChemicals.value.find((chemical) => chemical.chemical_id === chemical_id);

    if (!row) return;

    const originalGrams = row.grams;
    const originalMoles = row.moles;
    const originalEquivalent = row.equivalent;
    const originalVolume = row.volume_ml;

    row.grams = 0;
    row.moles = 0;
    row.equivalent = 0;

    if (originalGrams !== 0) {
      row.options.cellData.grams.isEdited = true;
      row.options.isEdit = true;
    }

    if (originalMoles !== 0) {
      row.options.cellData.moles.isEdited = true;
      row.options.isEdit = true;
    }

    if (originalEquivalent !== 0) {
      row.options.cellData.equivalent.isEdited = true;
      row.options.isEdit = true;
    }
    if (originalVolume !== 0) {
      row.volume_ml = 0;
      row.options.cellData.volume_ml.isEdited = true;
      row.options.isEdit = true;
    }

    triggerSuccessNotification('Hodnoty byly úspěšně vymazány.');

    // Auto-unlock the table after action button
    autoUnlockAfterAction();
  }
</script>

<template>
  <v-row>
    <v-col class="scroll-container">
      <!-- Chemical Table WebSocket Wrapper -->
      <div
        class="chemical-table-wrapper"
        :class="{
          'field-locked': enableWebSocket && isLockedByOther,
          'field-locked-by-me': enableWebSocket && isLockedByMe,
          'field-editing': enableWebSocket && isEditing
        }"
        @focusin="handleTableFocusIn"
        @focusout="handleTableFocusOut"
        @click="isLockedByOther ? requestTableAccess() : null"
      >
        <!-- Lock Overlay (only when locked by other) -->
        <div v-if="enableWebSocket && isLockedByOther" class="lock-overlay-other">
          <div class="lock-indicator">
            <v-icon size="small" :color="lockOwnerColor">mdi-lock</v-icon>
            <span class="lock-text">{{ lockOwnerName }}</span>
            <v-btn
              v-if="!hasRequestedAccess"
              size="x-small"
              variant="outlined"
              :color="lockOwnerColor"
              class="ml-2"
              @click="requestTableAccess"
            >
              Požádat o přístup
            </v-btn>
            <v-chip v-else size="x-small" color="warning" variant="outlined" class="ml-2">
              Žádost odeslána
            </v-chip>
          </div>
        </div>

        <!-- User Indicator (when editing or locked by me) -->
        <div
          v-if="enableWebSocket && (isEditing || isLockedByMe)"
          class="user-indicator"
          :style="{ backgroundColor: tableBorderColor }"
        >
          <v-icon size="12" color="white">mdi-account</v-icon>
          <span class="user-text">Upravujete</span>
        </div>

        <!-- Field content wrapper (matches FieldLockWrapper structure) -->
        <div class="field-content" :style="{ borderColor: tableBorderColor }">
          <ul
            :class="isEditable ? 'items normal' : 'items normal is-preview'"
            :style="{ gridTemplateColumns: gridTemplateColumns }"
          >
            <li class="header">
              <!-- <div class="status">Stav</div> -->
              <div class="name" :class="{ 'column-hidden': !columnVisibility.name }">
                <div class="header-wrapper" :title="!columnVisibility.name ? 'Chemikálie' : ''">
                  <div class="header-title">
                    <!-- Row 1: Column Label -->
                    <div class="header-labels">
                      <span>Chemikálie</span>
                    </div>
                    <!-- Row 2: Units (invisible spacer for columns without units) -->
                    <div class="header-units">
                      <div class="unit-spacer"></div>
                    </div>
                    <!-- Row 3: Toggle Button -->
                    <div v-if="isEditable" class="header-toggle">
                      <v-tooltip location="top">
                        <template #activator="{ props: tooltipProps }">
                          <v-btn
                            v-bind="tooltipProps"
                            icon
                            size="small"
                            variant="text"
                            :color="columnVisibility.name ? 'primary' : 'error'"
                            class="toggle-btn"
                            @click="toggleColumnVisibility('name')"
                          >
                            <v-icon size="16">
                              {{ columnVisibility.name ? 'mdi-eye' : 'mdi-eye-off' }}
                            </v-icon>
                          </v-btn>
                        </template>
                        <span>
                          {{ columnVisibility.name ? 'Skrýt sloupec' : 'Zobrazit sloupec' }}
                        </span>
                      </v-tooltip>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="concentration"
                :class="{ 'column-hidden': !columnVisibility.concentration }"
              >
                <div
                  class="header-wrapper"
                  :title="!columnVisibility.concentration ? 'Obsah (%)' : ''"
                >
                  <div class="header-title">
                    <!-- Row 1: Column Label -->
                    <div class="header-labels">
                      <span>Obsah (%)</span>
                    </div>
                    <!-- Row 2: Units (invisible spacer for columns without units) -->
                    <div class="header-units">
                      <div class="unit-spacer"></div>
                    </div>
                    <!-- Row 3: Toggle Button -->
                    <div v-if="isEditable" class="header-toggle">
                      <v-tooltip location="top">
                        <template #activator="{ props: tooltipProps }">
                          <v-btn
                            v-bind="tooltipProps"
                            icon
                            size="small"
                            variant="text"
                            :color="columnVisibility.concentration ? 'primary' : 'error'"
                            class="toggle-btn"
                            @click="toggleColumnVisibility('concentration')"
                          >
                            <v-icon size="16">
                              {{ columnVisibility.concentration ? 'mdi-eye' : 'mdi-eye-off' }}
                            </v-icon>
                          </v-btn>
                        </template>
                        <span>
                          {{
                            columnVisibility.concentration ? 'Skrýt sloupec' : 'Zobrazit sloupec'
                          }}
                        </span>
                      </v-tooltip>
                    </div>
                  </div>
                </div>
              </div>
              <div class="density" :class="{ 'column-hidden': !columnVisibility.density }">
                <div class="header-wrapper" :title="!columnVisibility.density ? 'Hustota (ρ)' : ''">
                  <div class="header-title">
                    <!-- Row 1: Column Label -->
                    <div class="header-labels">
                      <span>&rho;</span>
                    </div>
                    <!-- Row 2: Units -->
                    <div class="header-units">
                      <HeaderUnitSelect
                        v-if="columnVisibility.density"
                        v-model:selected-unit="selectedUnitDensity"
                        label="&rho;"
                        :items="unitDensity"
                      />
                      <div v-else class="unit-spacer"></div>
                    </div>
                    <!-- Row 3: Toggle Button -->
                    <div v-if="isEditable" class="header-toggle">
                      <v-tooltip location="top">
                        <template #activator="{ props: tooltipProps }">
                          <v-btn
                            v-bind="tooltipProps"
                            icon
                            size="small"
                            variant="text"
                            :color="columnVisibility.density ? 'primary' : 'error'"
                            class="toggle-btn"
                            @click="toggleColumnVisibility('density')"
                          >
                            <v-icon size="16">
                              {{ columnVisibility.density ? 'mdi-eye' : 'mdi-eye-off' }}
                            </v-icon>
                          </v-btn>
                        </template>
                        <span>
                          {{ columnVisibility.density ? 'Skrýt sloupec' : 'Zobrazit sloupec' }}
                        </span>
                      </v-tooltip>
                    </div>
                  </div>
                </div>
              </div>
              <div class="molar_mass" :class="{ 'column-hidden': !columnVisibility.molar_mass }">
                <div
                  class="header-wrapper"
                  :title="!columnVisibility.molar_mass ? 'Molární hmotnost (M)' : ''"
                >
                  <div class="header-title">
                    <!-- Row 1: Column Label -->
                    <div class="header-labels">
                      <span>M</span>
                    </div>
                    <!-- Row 2: Units -->
                    <div class="header-units">
                      <HeaderUnitSelect
                        v-if="columnVisibility.molar_mass"
                        v-model:selected-unit="selectedUnitMolarMass"
                        label="M"
                        :items="unitMolarMass"
                      />
                      <div v-else class="unit-spacer"></div>
                    </div>
                    <!-- Row 3: Toggle Button -->
                    <div v-if="isEditable" class="header-toggle">
                      <v-tooltip location="top">
                        <template #activator="{ props: tooltipProps }">
                          <v-btn
                            v-bind="tooltipProps"
                            icon
                            size="small"
                            variant="text"
                            :color="columnVisibility.molar_mass ? 'primary' : 'error'"
                            class="toggle-btn"
                            @click="toggleColumnVisibility('molar_mass')"
                          >
                            <v-icon size="16">
                              {{ columnVisibility.molar_mass ? 'mdi-eye' : 'mdi-eye-off' }}
                            </v-icon>
                          </v-btn>
                        </template>
                        <span>
                          {{ columnVisibility.molar_mass ? 'Skrýt sloupec' : 'Zobrazit sloupec' }}
                        </span>
                      </v-tooltip>
                    </div>
                  </div>
                </div>
              </div>
              <div class="grams" :class="{ 'column-hidden': !columnVisibility.grams }">
                <div class="header-wrapper" :title="!columnVisibility.grams ? 'Hmotnost (m)' : ''">
                  <div class="header-title">
                    <!-- Row 1: Column Label -->
                    <div class="header-labels">
                      <span>m</span>
                    </div>
                    <!-- Row 2: Units -->
                    <div class="header-units">
                      <HeaderUnitSelect
                        v-if="columnVisibility.grams"
                        v-model:selected-unit="selectedUnitMass"
                        label="m"
                        :items="unitMass"
                      />
                      <div v-else class="unit-spacer"></div>
                    </div>
                    <!-- Row 3: Toggle Button -->
                    <div v-if="isEditable" class="header-toggle">
                      <v-tooltip location="top">
                        <template #activator="{ props: tooltipProps }">
                          <v-btn
                            v-bind="tooltipProps"
                            icon
                            size="small"
                            variant="text"
                            :color="columnVisibility.grams ? 'primary' : 'error'"
                            class="toggle-btn"
                            @click="toggleColumnVisibility('grams')"
                          >
                            <v-icon size="16">
                              {{ columnVisibility.grams ? 'mdi-eye' : 'mdi-eye-off' }}
                            </v-icon>
                          </v-btn>
                        </template>
                        <span>
                          {{ columnVisibility.grams ? 'Skrýt sloupec' : 'Zobrazit sloupec' }}
                        </span>
                      </v-tooltip>
                    </div>
                  </div>
                </div>
              </div>
              <div class="moles" :class="{ 'column-hidden': !columnVisibility.moles }">
                <div class="header-wrapper">
                  <div class="header-title">
                    <!-- Row 1: Column Label -->
                    <div class="header-labels">
                      <span>n</span>
                    </div>
                    <!-- Row 2: Units -->
                    <div class="header-units">
                      <HeaderUnitSelect
                        v-if="columnVisibility.moles"
                        v-model:selected-unit="selectedUnitMoles"
                        label="n"
                        :items="unitMoles"
                      />
                      <div v-else class="unit-spacer"></div>
                    </div>
                    <!-- Row 3: Toggle Button -->
                    <div v-if="isEditable" class="header-toggle">
                      <v-tooltip location="top">
                        <template #activator="{ props: tooltipProps }">
                          <v-btn
                            v-bind="tooltipProps"
                            icon
                            size="small"
                            variant="text"
                            :color="columnVisibility.moles ? 'primary' : 'error'"
                            class="toggle-btn"
                            @click="toggleColumnVisibility('moles')"
                          >
                            <v-icon size="16">
                              {{ columnVisibility.moles ? 'mdi-eye' : 'mdi-eye-off' }}
                            </v-icon>
                          </v-btn>
                        </template>
                        <span>
                          {{ columnVisibility.moles ? 'Skrýt sloupec' : 'Zobrazit sloupec' }}
                        </span>
                      </v-tooltip>
                    </div>
                  </div>
                </div>
              </div>
              <div class="volume_ml" :class="{ 'column-hidden': !columnVisibility.volume_ml }">
                <div class="header-wrapper">
                  <div class="header-title">
                    <!-- Row 1: Column Label -->
                    <div class="header-labels">
                      <span>V</span>
                    </div>
                    <!-- Row 2: Units -->
                    <div class="header-units">
                      <HeaderUnitSelect
                        v-if="columnVisibility.volume_ml"
                        v-model:selected-unit="selectedUnitVolume"
                        label="V"
                        :items="unitVolume"
                      />
                      <div v-else class="unit-spacer"></div>
                    </div>
                    <!-- Row 3: Toggle Button -->
                    <div v-if="isEditable" class="header-toggle">
                      <v-tooltip location="top">
                        <template #activator="{ props: tooltipProps }">
                          <v-btn
                            v-bind="tooltipProps"
                            icon
                            size="small"
                            variant="text"
                            :color="columnVisibility.volume_ml ? 'primary' : 'error'"
                            class="toggle-btn"
                            @click="toggleColumnVisibility('volume_ml')"
                          >
                            <v-icon size="16">
                              {{ columnVisibility.volume_ml ? 'mdi-eye' : 'mdi-eye-off' }}
                            </v-icon>
                          </v-btn>
                        </template>
                        <span>
                          {{ columnVisibility.volume_ml ? 'Skrýt sloupec' : 'Zobrazit sloupec' }}
                        </span>
                      </v-tooltip>
                    </div>
                  </div>
                </div>
              </div>
              <div class="equivalent" :class="{ 'column-hidden': !columnVisibility.equivalent }">
                <div class="header-wrapper">
                  <div class="header-title">
                    <!-- Row 1: Column Label -->
                    <div class="header-labels">
                      <span>ekv.</span>
                    </div>
                    <!-- Row 2: Units (invisible spacer for columns without units) -->
                    <div class="header-units">
                      <div class="unit-spacer"></div>
                    </div>
                    <!-- Row 3: Toggle Button -->
                    <div v-if="isEditable" class="header-toggle">
                      <v-tooltip location="top">
                        <template #activator="{ props: tooltipProps }">
                          <v-btn
                            v-bind="tooltipProps"
                            icon
                            size="small"
                            variant="text"
                            :color="columnVisibility.equivalent ? 'primary' : 'error'"
                            class="toggle-btn"
                            @click="toggleColumnVisibility('equivalent')"
                          >
                            <v-icon size="16">
                              {{ columnVisibility.equivalent ? 'mdi-eye' : 'mdi-eye-off' }}
                            </v-icon>
                          </v-btn>
                        </template>
                        <span>
                          {{ columnVisibility.equivalent ? 'Skrýt sloupec' : 'Zobrazit sloupec' }}
                        </span>
                      </v-tooltip>
                    </div>
                  </div>
                </div>
              </div>
              <div class="notes" :class="{ 'column-hidden': !columnVisibility.notes }">
                <div class="header-wrapper" :title="!columnVisibility.notes ? 'Poznámka' : ''">
                  <div class="header-title">
                    <!-- Row 1: Column Label -->
                    <div class="header-labels">
                      <span>Poznámka</span>
                    </div>
                    <!-- Row 2: Units (invisible spacer for columns without units) -->
                    <div class="header-units">
                      <div class="unit-spacer"></div>
                    </div>
                    <!-- Row 3: Toggle Button -->
                    <div v-if="isEditable" class="header-toggle">
                      <v-tooltip location="top">
                        <template #activator="{ props: tooltipProps }">
                          <v-btn
                            v-bind="tooltipProps"
                            icon
                            size="small"
                            variant="text"
                            :color="columnVisibility.notes ? 'primary' : 'error'"
                            class="toggle-btn"
                            @click="toggleColumnVisibility('notes')"
                          >
                            <v-icon size="16">
                              {{ columnVisibility.notes ? 'mdi-eye' : 'mdi-eye-off' }}
                            </v-icon>
                          </v-btn>
                        </template>
                        <span>
                          {{ columnVisibility.notes ? 'Skrýt sloupec' : 'Zobrazit sloupec' }}
                        </span>
                      </v-tooltip>
                    </div>
                  </div>
                </div>
              </div>
              <div
                v-if="isEditable"
                class="action"
                :class="{ 'column-hidden': !columnVisibility.action }"
              >
                <div class="header-wrapper" :title="!columnVisibility.action ? 'Akce' : ''">
                  <div class="header-title">
                    <!-- Row 1: Column Label -->
                    <div class="header-labels">
                      <span>Akce</span>
                    </div>
                    <!-- Row 2: Units (invisible spacer for columns without units) -->
                    <div class="header-units">
                      <div class="unit-spacer"></div>
                    </div>
                  </div>
                </div>
              </div>
            </li>

            <Item
              v-if="firstChemical"
              class="first_chemical"
              :chemical="firstChemical"
              :form-id="formId"
              :is-editable="isEditable"
              :data-chemicals-length="chemicals.length"
              @calculate-chemical="(chemical_id: number) => calculateRowData(chemical_id)"
              @reset-calculated-values="(chemical_id: number) => resetCalculatedValues(chemical_id)"
            />
            <Items
              :chemicals="chemicals"
              :form-id="formId"
              :is-editable="isEditable"
              @remove-chemical="(chemical_id: number) => removeChemical(chemical_id)"
              @calculate-chemical="(chemical_id: number) => calculateRowData(chemical_id)"
              @reset-calculated-values="(chemical_id: number) => resetCalculatedValues(chemical_id)"
              @save-chemical-row="(chemical_id: number) => saveChemicalRow(chemical_id)"
              @move-row-up="moveRowUp"
              @move-row-down="moveRowDown"
            />

            <Item
              v-if="resultChemical"
              class="result"
              :chemical="resultChemical"
              :form-id="formId"
              :is-editable="isEditable"
              :data-chemicals-length="chemicals.length"
              @reset-calculated-values="(chemical_id: number) => resetCalculatedValues(chemical_id)"
              @calculate-chemical="(chemical_id: number) => calculateRowDataLast(chemical_id)"
            />
          </ul>
        </div>
        <!-- Close field-content -->
      </div>
      <!-- Close chemical-table-wrapper -->
    </v-col>

    <v-col v-if="isEditable" cols="12" class="d-flex gap-2">
      <v-btn variant="outlined" density="compact" color="primary" @click.prevent="addRow">
        Přidat řádek
      </v-btn>
      <v-btn variant="outlined" density="compact" color="success" @click.prevent="emitSaveOrder">
        Uložit pořadí
      </v-btn>
    </v-col>
  </v-row>
  <ConfirmDlg ref="ConfirmRef" />
</template>

<style scoped>
  .action {
    min-width: 120px;
    width: 120px;
    text-align: left;
  }
  .scroll-container {
    width: 0;
    overflow: auto;
  }

  .items {
    display: grid;
    grid-gap: 8px;
    padding: 8px;
    align-items: center;
  }

  .header {
    font-weight: bold;
    padding: 8px 0;
    border-bottom: 1px solid #e0e0e0;
  }

  .divider {
    grid-column: 1 / -1;
    margin: 0;
    padding: 0;
    height: 1px;
  }

  .divider hr {
    margin: 8px 0;
    border: none;
    border-top: 1px solid #e0e0e0;
  }

  .action {
    min-width: 150px;
    text-align: left;
  }

  .notes-field {
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    vertical-align: middle;
  }

  .icon-wrapper {
    display: flex;
    align-items: center;
  }

  .notes:hover {
    overflow: visible;
  }

  .notes:hover .notes-field {
    white-space: normal;
    overflow: visible;
    text-overflow: clip;
  }

  /* Column visibility styles */
  .column-hidden {
    overflow: hidden;
    min-width: 0 !important;
    max-width: none !important;
  }

  /* New header structure */
  .header-wrapper {
    position: relative;
  }

  .header-title {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    min-height: 70px;
    font-weight: bold;
    text-align: center;
    font-size: 13px;
    gap: 4px;
    padding: 4px 2px;
  }

  /* Row 1: Column Labels */
  .header-labels {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 18px;
    font-size: 13px;
    line-height: 1.2;
    font-weight: bold;
  }

  /* Row 2: Units */
  .header-units {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 24px;
    width: 100%;
  }

  .unit-spacer {
    height: 24px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    opacity: 0.5;
  }

  /* Row 3: Toggle Buttons */
  .header-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 28px;
    margin-top: auto;
  }

  /* Hidden column styling */
  .column-hidden .header-wrapper {
    justify-content: center;
    min-width: 0;
    padding: 0;
  }

  .column-hidden .toggle-btn {
    min-width: 28px !important;
    width: 28px !important;
    height: 28px !important;
    margin: 0 auto;
    padding: 0 !important;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .column-hidden:hover .header-title {
    opacity: 1;
  }

  /* Legacy header-content support for columns not yet updated */
  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 4px;
    min-height: 24px;
  }

  .column-toggle-btn {
    opacity: 0.7;
    transition: opacity 0.2s;
  }

  .column-toggle-btn:hover {
    opacity: 1;
  }

  .column-hidden .header-content {
    justify-content: center;
  }

  .column-hidden .header-content span {
    display: none;
  }

  .column-hidden .header-content .v-field {
    display: none;
  }

  /* Make input components flexible */
  :deep(.flexible-width) {
    width: 100% !important;
    min-width: 0 !important;
    max-width: 100% !important;
  }

  :deep(.flexible-width .v-field) {
    width: 100% !important;
    min-width: 0 !important;
    max-width: 100% !important;
  }

  :deep(.flexible-width .v-field__input) {
    width: 100% !important;
    min-width: 0 !important;
    max-width: 100% !important;
  }

  /* Chemical Table WebSocket Locking Styles (matches FieldLockWrapper) */
  .chemical-table-wrapper {
    position: relative;
    transition: all 0.2s ease;
  }

  .field-content {
    border: 1px solid transparent;
    border-radius: 4px;
    transition: border-color 0.2s ease;
  }

  .lock-overlay-other {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(1px);
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    pointer-events: auto;
    opacity: 0.9;
  }

  .lock-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.1);
    pointer-events: auto;
  }

  .lock-text {
    font-size: 12px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.7);
  }

  /* Ensure buttons in lock overlay are clickable */
  .lock-indicator .v-btn {
    pointer-events: auto !important;
    cursor: pointer !important;
    z-index: 11;
  }

  .field-locked {
    /* Don't disable pointer events - we want to intercept them */
    position: relative;
  }

  .field-locked .field-content {
    background: rgba(255, 193, 7, 0.1);
    /* Add subtle indication that field is still there */
    opacity: 0.8;
  }

  .field-locked-by-me .field-content {
    background: rgba(76, 175, 80, 0.05);
  }

  .field-editing .field-content {
    background: rgba(33, 150, 243, 0.05);
  }

  /* User indicator styles */
  .user-indicator {
    position: absolute;
    top: -2px;
    right: -2px;
    z-index: 999;
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 500;
    color: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }

  .user-text {
    font-size: 10px;
    line-height: 1;
  }

  :deep(.flexible-width .v-field__field) {
    width: 100% !important;
    min-width: 0 !important;
    max-width: 100% !important;
  }

  /* Ensure cells can shrink */
  .items > li > div {
    min-width: 0;
    overflow: hidden;
  }

  /* Make sure the icon wrapper takes full width */
  .icon-wrapper {
    width: 100%;
    min-width: 0;
  }

  /* Make HeaderUnitSelect components flexible */
  :deep(.header-content) {
    width: 100%;
    min-width: 0;
  }

  :deep(.header-content .v-field) {
    width: 100% !important;
    min-width: 0 !important;
    flex: 1;
  }

  /* Ensure hidden columns show only toggle button */
  .column-hidden .header-content {
    width: 100%;
    justify-content: center;
  }

  .column-hidden .header-content > *:not(.column-toggle-btn) {
    display: none !important;
  }

  /* Hide content in hidden columns for data cells */
  :deep(.hidden-content) {
    opacity: 0;
    pointer-events: none;
    overflow: hidden;
  }

  :deep(.hidden-content *) {
    visibility: hidden;
  }
</style>
