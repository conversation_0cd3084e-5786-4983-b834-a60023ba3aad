<!-- eslint-disable vue/multi-word-component-names -->
<!-- eslint-disable vue/no-parsing-error -->
<template>
  <template v-if="chemicalStatus !== ChemicalItemStatus.WAITING">
    <li v-bind="$attrs" :class="$attrs.class">
      <!-- <div class="status">
        <v-chip color="success" v-if="chemicalStatus === ChemicalItemStatus.SAVED" size="small" label> (S) Uloženo na serveru </v-chip>
        <v-chip color="warning" v-if="chemicalStatus === ChemicalItemStatus.SERVER_EDITED" size="small" label> (S) Čeká na uložení</v-chip>
        <v-chip color="primary" v-if="chemicalStatus === ChemicalItemStatus.NEW" size="small" label> (L) Nově přidáno</v-chip>
        <v-chip color="warning" v-if="chemicalStatus === ChemicalItemStatus.LOCALLY_EDITED" size="small" label>
          (L) Čeká na vytvoření
        </v-chip>

        <v-progress-linear
          v-model="progress"
          :active="isEditable"
          :indeterminate="false"
          :absolute="false"
          :max="100"
          :rounded-bar="true"
          color="info"
          class="mt-1"
        />
      </div> -->

      <ItemCell
        v-for="baseColumn in baseColumns"
        :key="baseColumn"
        v-model:value="chemical[baseColumn as keyof ChemicalTableChemicalDataI]"
        :options="chemical.options"
        :is-editable="isEditable"
        :base-row="chemical"
        :selected-unit-density="selectedUnitDensity"
        :object-key="
          baseColumn as
            | 'name'
            | 'concentration'
            | 'density'
            | 'molar_mass'
            | 'grams'
            | 'moles'
            | 'volume_ml'
            | 'equivalent'
            | 'notes'
        "
      />

      <div class="action">
        <div v-if="isEditable" class="operation-wrapper">
          <v-tooltip location="top">
            <template #activator="{ props: tooltipProps }">
              <v-btn
                v-bind="tooltipProps"
                icon
                size="x-small"
                color="success"
                variant="text"
                rounded="sm"
                @click.prevent="$emit('calculateChemical', chemical.chemical_id)"
              >
                <v-icon size="small"><CalculatorOutlined /></v-icon>
              </v-btn>
            </template>
            <span>Dopočítat hodnoty</span>
          </v-tooltip>

          <v-tooltip location="top">
            <template #activator="{ props: tooltipProps }">
              <v-btn
                v-bind="tooltipProps"
                icon
                size="x-small"
                color="warning"
                variant="text"
                rounded="sm"
                @click.prevent="$emit('resetCalculatedValues', chemical.chemical_id)"
              >
                <v-icon size="small"><ReloadOutlined /></v-icon>
              </v-btn>
            </template>
            <span>Vymazat dopočítané hodnoty</span>
          </v-tooltip>

          <v-tooltip location="top">
            <template #activator="{ props: tooltipProps }">
              <v-btn
                v-bind="tooltipProps"
                icon
                size="x-small"
                color="success"
                variant="text"
                rounded="sm"
                @click.prevent="checkStatus()"
              >
                <v-icon size="small"><SaveOutlined /></v-icon>
              </v-btn>
            </template>
            <span>Uložit chemikálii</span>
          </v-tooltip>

          <template v-if="chemical.type === ChemicalType.CHEMICAL">
            <v-tooltip v-if="typeof index === 'number' && index > 0" location="top">
              <template #activator="{ props: tooltipProps }">
                <v-btn
                  v-bind="tooltipProps"
                  icon
                  size="x-small"
                  color="primary"
                  variant="text"
                  rounded="sm"
                  @click.prevent="$emit('moveRowUp', index)"
                >
                  <v-icon size="small"><ArrowUpOutlined /></v-icon>
                </v-btn>
              </template>
              <span>Posunout řádek nahoru</span>
            </v-tooltip>

            <v-tooltip
              v-if="typeof index === 'number' && index < dataChemicalsLength - 1"
              location="top"
            >
              <template #activator="{ props: tooltipProps }">
                <v-btn
                  v-bind="tooltipProps"
                  icon
                  size="x-small"
                  color="primary"
                  variant="text"
                  rounded="sm"
                  @click.prevent="$emit('moveRowDown', index)"
                >
                  <v-icon size="small"><ArrowDownOutlined /></v-icon>
                </v-btn>
              </template>
              <span>Posunout řádek dolů</span>
            </v-tooltip>

            <v-tooltip location="top">
              <template #activator="{ props: tooltipProps }">
                <v-btn
                  v-bind="tooltipProps"
                  icon
                  size="x-small"
                  color="error"
                  variant="text"
                  rounded="sm"
                  @click.prevent="
                    async () => {
                      if (!chemical.options.isEdit && chemical.options.isSave) {
                        if (
                          await ConfirmRef?.open(
                            'Potvrzení',
                            'Opravdu chcete smazat chemikálii ze serveru ?',
                            {
                              color: 'error',
                              notclosable: true,
                              zIndex: 2400
                            }
                          )
                        ) {
                          deleteChemical(chemical.chemical_id);
                        }
                      } else {
                        $emit('removeChemical', chemical.chemical_id);
                      }
                    }
                  "
                >
                  <v-icon size="small"><DeleteOutlined /></v-icon>
                </v-btn>
              </template>
              <span>Smazat chemikálii</span>
            </v-tooltip>
          </template>
        </div>
      </div>
    </li>
    <li class="divider">
      <hr />
    </li>
  </template>
  <li v-else v-bind="$attrs" :class="$attrs.class" class="divider">
    <div class="loading">
      <v-progress-circular indeterminate color="secondary"></v-progress-circular>
    </div>
  </li>
  <NewChemicalModal
    v-if="isModalOpen"
    v-model:show="isModalOpen"
    :chemical-row="chemicalRowForModal"
  />
</template>

<script setup lang="ts">
  import { Chemical, ChemicalType } from '@/stores/chemicals';
  import {
    NewChemicalType,
    useFormsStore,
    type NewChemicalDto,
    type UpdateChemicalDto
  } from '@/stores/forms';
  import type { ChemicalTableChemicalDataI } from '@/views/pages/dashboard/projects/project/chemicals/components/ChemicalsTable2.vue';
  import {
    baseColumns,
    convertFromBaseUnit,
    convertToBaseUnit
  } from '@/views/pages/dashboard/projects/project/chemicals/components/shared';
  import {
    CalculatorOutlined,
    DeleteOutlined,
    SaveOutlined,
    ArrowUpOutlined,
    ArrowDownOutlined,
    ReloadOutlined
  } from '@ant-design/icons-vue';
  import { computed, inject, onMounted, onUnmounted, ref, watch, type Ref } from 'vue';
  import ConfirmDlg from '../ConfirmDlg.vue';
  import ItemCell from './ItemCell.vue';
  import NewChemicalModal from './AddNewChemicalFromForm.vue';

  const ConfirmRef = ref<undefined | typeof ConfirmDlg>(undefined);
  enum ChemicalItemStatus {
    NEW = 'new',
    LOCALLY_EDITED = 'locally_edited',
    SERVER_EDITED = 'server_edited',
    SAVED = 'saved',
    WAITING = 'waiting'
  }

  const emits = defineEmits([
    'calculateChemical',
    'removeChemical',
    'saveChemicalRow',
    'moveRowUp',
    'moveRowDown',
    'resetCalculatedValues'
  ]);
  const chemical = defineModel<ChemicalTableChemicalDataI>('chemical', { required: true });
  const props = defineProps({
    formId: {
      type: Number,
      required: false,
      default: undefined
    },
    isEditable: {
      type: Boolean,
      default: true
    },
    index: {
      type: Number,
      required: false,
      default: undefined
    },
    dataChemicalsLength: {
      type: Number,
      required: true
    }
  });

  const selectedUnitDensity = inject<Ref<string>>('selectedUnitDensity');
  const selectedUnitMolarMass = inject<Ref<string>>('selectedUnitMolarMass');
  const selectedUnitVolume = inject<Ref<string>>('selectedUnitVolume');
  const selectedUnitMass = inject<Ref<string>>('selectedUnitMass');
  const selectedUnitMoles = inject<Ref<string>>('selectedUnitMoles');

  const formsStore = useFormsStore();
  const intervalDuration = 15000;

  const chemicalStatus = computed<ChemicalItemStatus>(() => {
    if (chemical.value.options.isSave && !chemical.value.options.isEdit) {
      return ChemicalItemStatus.SAVED;
    } else if (chemical.value.options.isSave && chemical.value.options.isEdit) {
      return ChemicalItemStatus.SERVER_EDITED;
    } else if (!chemical.value.options.isSave && !chemical.value.options.isEdit) {
      return ChemicalItemStatus.NEW;
    } else if (!chemical.value.options.isSave && chemical.value.options.isEdit) {
      return ChemicalItemStatus.LOCALLY_EDITED;
    }

    return ChemicalItemStatus.WAITING;
  });

  watch(
    [
      () => chemical.value.name,
      () => chemical.value.density,
      () => chemical.value.molar_mass,
      () => chemical.value.notes,
      () => chemical.value.grams,
      () => chemical.value.moles,
      () => chemical.value.equivalent,
      () => chemical.value.volume_ml,
      () => chemical.value.concentration
    ],
    () => {
      chemical.value.options.isEdit = false;

      if (chemical.value.options.cellData.name.valueBeforeEdit !== chemical.value.name) {
        chemical.value.options.cellData.name.isEdited = true;
        chemical.value.options.isEdit = true;
      } else {
        chemical.value.options.cellData.name.isEdited = false;
      }

      if (chemical.value.options.cellData.density.valueBeforeEdit !== chemical.value.density) {
        chemical.value.options.cellData.density.isEdited = true;
        chemical.value.options.isEdit = true;
      } else {
        chemical.value.options.cellData.density.isEdited = false;
      }

      if (
        chemical.value.options.cellData.molar_mass.valueBeforeEdit !== chemical.value.molar_mass
      ) {
        chemical.value.options.cellData.molar_mass.isEdited = true;
        chemical.value.options.isEdit = true;
      } else {
        chemical.value.options.cellData.molar_mass.isEdited = false;
      }

      if (chemical.value.options.cellData.notes.valueBeforeEdit !== chemical.value.notes) {
        chemical.value.options.cellData.notes.isEdited = true;
        chemical.value.options.isEdit = true;
      } else {
        chemical.value.options.cellData.notes.isEdited = false;
      }

      if (chemical.value.options.cellData.grams.valueBeforeEdit !== chemical.value.grams) {
        chemical.value.options.cellData.grams.isEdited = true;
        chemical.value.options.isEdit = true;
      } else {
        chemical.value.options.cellData.grams.isEdited = false;
      }

      if (chemical.value.options.cellData.moles.valueBeforeEdit !== chemical.value.moles) {
        chemical.value.options.cellData.moles.isEdited = true;
        chemical.value.options.isEdit = true;
      } else {
        chemical.value.options.cellData.moles.isEdited = false;
      }

      if (
        chemical.value.options.cellData.equivalent.valueBeforeEdit !== chemical.value.equivalent
      ) {
        chemical.value.options.cellData.equivalent.isEdited = true;
        chemical.value.options.isEdit = true;
      } else {
        chemical.value.options.cellData.equivalent.isEdited = false;
      }

      if (chemical.value.options.cellData.volume_ml.valueBeforeEdit !== chemical.value.volume_ml) {
        chemical.value.options.cellData.volume_ml.isEdited = true;
        chemical.value.options.isEdit = true;
      } else {
        chemical.value.options.cellData.volume_ml.isEdited = false;
      }

      if (
        chemical.value.options.cellData.concentration.valueBeforeEdit !==
        chemical.value.concentration
      ) {
        chemical.value.options.cellData.concentration.isEdited = true;
        chemical.value.options.isEdit = true;
      } else {
        chemical.value.options.cellData.concentration.isEdited = false;
      }
    }
  );

  // const progress = ref(0);
  // const intervalId = ref<number | undefined>(undefined);
  // const progressInterval = ref<number | undefined>(undefined);
  // onMounted(() => {
  //   intervalId.value = window.setInterval(checkStatus, intervalDuration);

  //   progressInterval.value = window.setInterval(() => {
  //     if (progress.value < 100) {
  //       progress.value += 100 / (intervalDuration / 100);
  //     }
  //   }, 100);
  // });

  // onUnmounted(() => {
  //   if (intervalId.value !== undefined) {
  //     clearInterval(intervalId.value);
  //   }

  //   if (progressInterval.value !== undefined) {
  //     clearInterval(progressInterval.value);
  //   }
  // });

  const validateInput = (concentration: number | string) => {
    const input = concentration.toString();
    if (input) {
      if (/^\d+([.,]\d+)?$/.test(input)) {
        return parseFloat(input.replace(',', '.'));
      } else {
        return -1;
      }
    } else {
      return -1;
    }
  };

  const checkStatus = async () => {
    formsStore.form_id = props.formId;
    const updateChemicalData: UpdateChemicalDto = {
      name: chemical.value.name,
      density: selectedUnitDensity
        ? convertDensity(chemical.value.density, selectedUnitDensity.value, 'g')
        : chemical.value.density,
      molar_mass: selectedUnitMolarMass
        ? convertMolarMass(chemical.value.molar_mass, selectedUnitMolarMass.value, 'g')
        : chemical.value.molar_mass,
      notes: chemical.value.notes,
      grams: selectedUnitMass
        ? convertMass(chemical.value.grams, selectedUnitMass.value, 'g')
        : chemical.value.grams,
      moles: selectedUnitMoles
        ? convertMoles(chemical.value.moles, selectedUnitMoles.value, 'mol')
        : chemical.value.moles,
      equivalent: chemical.value.equivalent,
      volume_ml: selectedUnitVolume
        ? convertVolume(chemical.value.volume_ml, selectedUnitVolume.value, 'ml')
        : chemical.value.volume_ml,
      concentration: validateInput(chemical.value.concentration ?? 100)
    };

    const newChemicalData: NewChemicalDto = {
      name: chemical.value.name,
      density: selectedUnitDensity
        ? convertDensity(chemical.value.density, selectedUnitDensity.value, 'g')
        : chemical.value.density,
      molar_mass: selectedUnitMolarMass
        ? convertMolarMass(chemical.value.molar_mass, selectedUnitMolarMass.value, 'g')
        : chemical.value.molar_mass,
      notes: chemical.value.notes,
      grams: selectedUnitMass
        ? convertMass(chemical.value.grams, selectedUnitMass.value, 'g')
        : chemical.value.grams,
      moles: selectedUnitMoles
        ? convertMoles(chemical.value.moles, selectedUnitMoles.value, 'mol')
        : chemical.value.moles,
      equivalent: chemical.value.equivalent,
      volume_ml: selectedUnitVolume
        ? convertVolume(chemical.value.volume_ml, selectedUnitVolume.value, 'ml')
        : chemical.value.volume_ml,
      concentration: validateInput(chemical.value.concentration ?? 100),
      type: NewChemicalType.CHEMICAL
    };
    openNewChemicalModal(updateChemicalData);
    switch (chemicalStatus.value) {
      case ChemicalItemStatus.NEW:
        if (chemical.value.options.isSave && !chemical.value.options.isEdit) {
          chemical.value.options.isEdit = true;
        }
        break;
      case ChemicalItemStatus.LOCALLY_EDITED: {
        const create_newChemicalData = await formsStore.createNewChemical(newChemicalData);
        if (create_newChemicalData instanceof Chemical) {
          chemical.value.chemical_id = create_newChemicalData.chemical_id;
          setDefaultValueFromNewChemicalData(create_newChemicalData, true, false);
        }
        break;
      }
      case ChemicalItemStatus.SERVER_EDITED: {
        const update_newChemicalData = await formsStore.updateChemical(
          chemical.value.chemical_id,
          updateChemicalData
        );
        if (update_newChemicalData instanceof Chemical) {
          setDefaultValueFromNewChemicalData(update_newChemicalData, true, false);
        }
        break;
      }
      case ChemicalItemStatus.SAVED:
        break;
    }
  };

  const deleteChemical = async (chemical_id: number) => {
    if (props.formId) {
      formsStore.form_id = props.formId;
      const isDeleted = await formsStore.deleteChemical(chemical_id);
      if (isDeleted) {
        emits('removeChemical', chemical_id);
      }
    } else {
      emits('removeChemical', chemical_id);
    }
  };

  const setDefaultValueFromNewChemicalData = (
    newChemicalData: Chemical,
    isSave: boolean,
    isEdit: boolean
  ) => {
    chemical.value.options.isEdit = isEdit;
    chemical.value.options.isSave = isSave;
    chemical.value.options.cellData.name.valueBeforeEdit = newChemicalData.name;
    chemical.value.options.cellData.name.isEdited = false;

    chemical.value.options.cellData.density.valueBeforeEdit = selectedUnitDensity
      ? convertFromBaseUnit(newChemicalData.density, selectedUnitDensity.value, 'density')
      : newChemicalData.density;
    chemical.value.options.cellData.molar_mass.isEdited = false;

    chemical.value.options.cellData.molar_mass.valueBeforeEdit = selectedUnitMolarMass
      ? convertFromBaseUnit(newChemicalData.molar_mass, selectedUnitMolarMass.value, 'molar_mass')
      : newChemicalData.molar_mass;
    chemical.value.options.cellData.molar_mass.isEdited = false;

    chemical.value.options.cellData.notes.valueBeforeEdit = newChemicalData.notes;
    chemical.value.options.cellData.notes.isEdited = false;

    chemical.value.options.cellData.grams.valueBeforeEdit = selectedUnitMass
      ? convertFromBaseUnit(newChemicalData.grams, selectedUnitMass.value, 'mass')
      : newChemicalData.grams;
    chemical.value.options.cellData.grams.isEdited = false;

    chemical.value.options.cellData.moles.valueBeforeEdit = selectedUnitMoles
      ? convertFromBaseUnit(newChemicalData.moles, selectedUnitMoles.value, 'moles')
      : newChemicalData.moles;
    chemical.value.options.cellData.moles.isEdited = false;

    chemical.value.options.cellData.equivalent.valueBeforeEdit = newChemicalData.equivalent;
    chemical.value.options.cellData.equivalent.isEdited = false;

    chemical.value.options.cellData.volume_ml.valueBeforeEdit = selectedUnitVolume
      ? convertFromBaseUnit(newChemicalData.volume_ml, selectedUnitVolume.value, 'volume')
      : newChemicalData.volume_ml;
    chemical.value.options.cellData.volume_ml.isEdited = false;

    chemical.value.options.cellData.concentration.valueBeforeEdit = newChemicalData.concentration;
    chemical.value.options.cellData.concentration.isEdited = false;
  };

  // Function to convert density units
  function convertDensity(value: number, fromUnit: string, toUnit: string): number {
    if (fromUnit === 'g' && toUnit === 'kg') {
      return value * 1000; // g/cm³ to Kg/m³
    } else if (fromUnit === 'kg' && toUnit === 'g') {
      return value / 1000; // Kg/m³ to g/cm³
    }
    return value; // no conversion needed
  }

  // Function to convert molar mass units
  function convertMolarMass(value: number, fromUnit: string, toUnit: string): number {
    if (fromUnit === 'g' && toUnit === 'kg') {
      return value / 1000; // g/mol to Kg/mol
    } else if (fromUnit === 'kg' && toUnit === 'g') {
      return value * 1000; // Kg/mol to g/mol
    }
    return value; // no conversion needed
  }

  function convertMass(value: number, fromUnit: string, toUnit: string): number {
    const massConversions: Record<string, number> = {
      g: 1,
      kg: 1000, // 1 kg = 1000 g
      mg: 0.001 // 1 mg = 0.001 g
    };

    if (massConversions[fromUnit] && massConversions[toUnit]) {
      return value * (massConversions[fromUnit] / massConversions[toUnit]);
    }

    return value; // no conversion if units are invalid or identical
  }

  // Function to convert volume units
  function convertVolume(value: number, fromUnit: string, toUnit: string): number {
    const volumeConversions: Record<string, number> = {
      ml: 1,
      l: 1000, // 1 L = 1000 ml
      ul: 0.001 // 1 µl = 0.001 ml
    };

    if (volumeConversions[fromUnit] && volumeConversions[toUnit]) {
      return value * (volumeConversions[fromUnit] / volumeConversions[toUnit]);
    }

    return value; // no conversion if units are invalid or identical
  }

  function convertMoles(value: number, fromUnit: string, toUnit: string): number {
    const moleConversions: Record<string, number> = {
      mol: 1,
      mmol: 0.001 // 1 mmol = 0.001 mol
    };

    if (moleConversions[fromUnit] && moleConversions[toUnit]) {
      return value * (moleConversions[fromUnit] / moleConversions[toUnit]);
    }

    return value; // no conversion if units are invalid or identical
  }
  const isModalOpen = ref(false);
  const chemicalRowForModal = ref<UpdateChemicalDto>();
  const openNewChemicalModal = (chemicalRow: UpdateChemicalDto) => {
    if (chemicalRow) {
      chemicalRowForModal.value = chemicalRow;
      isModalOpen.value = true;
    }
  };
</script>
<style scoped>
  .operation-wrapper {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: nowrap;
    gap: 2px;
  }

  .button-group {
    display: flex;
    gap: 1px;
  }

  :deep(.v-btn--icon.v-btn--size-small) {
    width: 28px;
    height: 28px;
    min-width: 28px;
  }
</style>
