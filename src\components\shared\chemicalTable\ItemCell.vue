<template>
  <div :class="[objectKey, { 'column-hidden': !columnVisibility[objectKey] }]">
    <div class="icon-wrapper" :class="{ 'hidden-content': !columnVisibility[objectKey] }">
      <template v-if="isAutoComplete.includes(objectKey)">
        <v-tooltip location="top" :open-delay="1000" :disabled="!valueState">
          <template #activator="{ props: tooltipProps }">
            <span v-bind="tooltipProps">
              <v-combobox
                v-model="selectedName"
                v-model:search="projectChemicalForAutocompleteOptions.search"
                item-value="uniqueId"
                :items="computedChemicals"
                :item-title="
                  (selectedName) => {
                    if (typeof selectedName === 'string') {
                      return selectedName;
                    } else {
                      return (
                        selectedName.shortcut +
                        ' - ' +
                        selectedName.name +
                        ' ' +
                        '(' +
                        selectedName.csa +
                        ')'
                      );
                    }
                  }
                "
                :clearable="false"
                hide-details
                rounded="sm"
                variant="outlined"
                color="primary"
                single-line
                class="autocomplete no-dropdown-arrow"
                :width="currentFieldWidth"
                :slim="true"
                :allow-new="true"
                placeholder="N/A"
                @update:model-value="onSelect"
                @change="validateInputComboBox"
                @focus="handleFocus"
                @blur="handleBlur"
              >
                <template #item="{ props: itemProps, item }">
                  <v-list-item v-bind="itemProps" :title="''">
                    <div class="player-wrapper pa-2">
                      <h6 class="text-subtitle-1 mb-0">
                        {{ item.raw.name }} - {{ item.raw.shortcut }}
                      </h6>
                      <small class="text-h6 text-lightText">({{ item.raw.csa }})</small>
                    </div>
                  </v-list-item>
                </template>
              </v-combobox>
            </span>
          </template>
          <span>{{ valueState || 'N/A' }}</span>
        </v-tooltip>
      </template>
      <template v-else-if="isConsetration.includes(objectKey)">
        <v-tooltip location="top" :open-delay="1000" :disabled="!valueState">
          <template #activator="{ props: tooltipProps }">
            <span v-bind="tooltipProps">
              <v-text-field
                v-if="isEditable && options.cellData[objectKey].editable"
                v-model="valueState"
                :hide-details="true"
                variant="outlined"
                density="compact"
                class="pa-0 ma-0 number-input-reduced-padding"
                :width="currentFieldWidth"
                :color="options.cellData[objectKey].isEdited ? 'success' : 'primary'"
                placeholder="N/A"
                @change="validateInput"
                @focus="handleFocus"
                @blur="handleBlur"
              ></v-text-field>
              <span v-else class="cell-value">{{ valueState || 'N/A' }}</span>
            </span>
          </template>
          <span>{{ valueState || 'N/A' }}</span>
        </v-tooltip>
      </template>
      <template v-else-if="textObjectKey.includes(objectKey) && typeof valueState === 'string'">
        <v-tooltip location="top" :open-delay="1000" :disabled="!valueState">
          <template #activator="{ props: tooltipProps }">
            <span v-bind="tooltipProps">
              <v-text-field
                v-if="options.cellData[objectKey].editable"
                v-model="valueState"
                :hide-details="true"
                placeholder="N/A"
                variant="outlined"
                density="compact"
                class="pa-0 ma-0"
                :width="currentFieldWidth"
                :color="options.cellData[objectKey].isEdited ? 'success' : 'primary'"
                @change="validateInput"
                @focus="handleFocus"
                @blur="handleBlur"
              ></v-text-field>
              <span v-else class="cell-value">{{ valueState || 'N/A' }}</span>
            </span>
          </template>
          <span>{{ valueState || 'N/A' }}</span>
        </v-tooltip>
      </template>
      <template
        v-else-if="
          typeof valueState === 'string' ||
          typeof valueState === 'number' ||
          typeof valueState === 'undefined'
        "
      >
        <v-tooltip location="top" :open-delay="1000" :disabled="!valueState">
          <template #activator="{ props: tooltipProps }">
            <span v-bind="tooltipProps">
              <CustomNumberInput
                v-if="isEditable && options.cellData[objectKey].editable"
                v-model="valueState"
                :hide-details="true"
                variant="outlined"
                :color="options.cellData[objectKey].isEdited ? 'success' : 'primary'"
                density="compact"
                class="pa-0 ma-0 number-input-reduced-padding"
                :min="0"
                @change="validateInput"
                @focus="handleFocus"
                @blur="handleBlur"
              ></CustomNumberInput>
              <span v-else class="cell-value">{{ valueState || 'N/A' }}</span>
            </span>
          </template>
          <span>{{ valueState || 'N/A' }}</span>
        </v-tooltip>
      </template>
      <v-tooltip v-else location="top" :open-delay="1000" :disabled="!valueState">
        <template #activator="{ props: tooltipProps }">
          <span v-bind="tooltipProps" class="cell-value">
            {{ valueState || 'N/A' }}
          </span>
        </template>
        <span>{{ valueState || 'N/A' }}</span>
      </v-tooltip>
    </div>
  </div>
</template>

<script setup lang="ts">
  import type { ChemicalOptions } from '@/views/pages/dashboard/projects/project/chemicals/components/ChemicalsTable2.vue';
  import { computed, watch, ref, inject, type Ref } from 'vue';
  import CustomNumberInput from '../CustomNumberInput.vue';
  import type { ChemicalTableChemicalDataI } from '@/views/pages/dashboard/projects/project/chemicals/components/ChemicalsTable2.vue';
  import { ProjectChemical, useProjectsChemicalsStore } from '@/stores/projectChemicals';
  import { type PaginatorRequestDataI } from '@/stores/projects';
  import { useRoute } from 'vue-router';
  import { useDebounceFn } from '@vueuse/core';

  const route = useRoute();
  const projectChemicals = useProjectsChemicalsStore();

  // Inject WebSocket functionality from parent (optional)
  const chemicalTableWS = inject('chemicalTableWebSocket', null) as any;

  const textObjectKey = ['name', 'notes'];
  const isAutoComplete = ['name'];
  const isConsetration = ['concentration'];

  // Define static widths for each field
  const fieldWidths: Record<string, number> = {
    name: 400,
    concentration: 50,
    density: 50,
    molar_mass: 60,
    grams: 60,
    moles: 60,
    volume_ml: 60,
    equivalent: 50,
    notes: 400,
    action: 160
  };

  // Computed property to get width for current field
  const currentFieldWidth = computed(() => {
    return `${fieldWidths[props.objectKey] || 80}px`; // Default width if not specified
  });

  const props = defineProps({
    options: {
      type: Object as () => ChemicalOptions,
      required: true
    },
    objectKey: {
      type: String as () => keyof ChemicalOptions['cellData'],
      required: true
    },
    isEditable: {
      type: Boolean,
      required: true
    },
    baseRow: {
      type: Object as () => ChemicalTableChemicalDataI,
      required: false,
      default: null
    },
    selectedUnitDensity: {
      type: String,
      required: false,
      default: 'g'
    }
  });

  // Inject column visibility state
  const columnVisibility = inject<Ref<Record<string, boolean>>>('columnVisibility', ref({}));

  // Watch for incoming chemical field updates
  if (chemicalTableWS && props.baseRow?.chemical_id) {
    watch(
      () => chemicalTableWS.getChemicalFieldUpdate(props.baseRow.chemical_id, props.objectKey),
      (update) => {
        if (update && update.userId !== chemicalTableWS.webSocketStore?.currentUserId) {
          console.log('📥 ItemCell received chemical field update:', {
            chemical_id: props.baseRow.chemical_id,
            field: props.objectKey,
            value: update.value,
            fromUser: update.userId
          });

          // Update the local value with the remote update
          if (valueState.value !== update.value) {
            console.log('📝 Updating ItemCell value:', {
              from: valueState.value,
              to: update.value
            });
            valueState.value = update.value;

            // Update selectedName for autocomplete fields
            if (isAutoComplete.includes(props.objectKey)) {
              selectedName.value = update.value as string;
            }
          }
        }
      },
      { deep: true }
    );
  }

  const project_id = computed(() => route.params.project_id as string);
  const projectId = computed(() => parseInt(project_id.value));

  const dotStatus = computed(() => {
    return props.options.isSave && props.options.cellData[props.objectKey].isEdited
      ? 'se'
      : props.options.isSave && !props.options.cellData[props.objectKey].isEdited
        ? 'same'
        : !props.options.isSave && !props.options.cellData[props.objectKey].isEdited
          ? 'same'
          : 'le';
  });

  const computedChemicals = computed(() => {
    return projectChemicalForAutocompleteOptions.value.results.map((item) => ({
      ...item,
      uniqueId: `${item.name}-${item.shortcut}-${item.csa}`
    }));
  });

  const projectChemicalForAutocompleteOptions = ref<PaginatorRequestDataI<ProjectChemical>>({
    loading: false,

    search: '',
    search_type: 'OR',
    search_columns: ['name', 'csa', 'shortcut'],

    totalItems: 0,
    options: {
      page: 1,
      rowsPerPage: 5,
      sortBy: [],
      sortType: []
    },

    filterOptions: [{ column: 'status', value: 'active' }],
    results: []
  });

  watch(
    () => [
      projectChemicalForAutocompleteOptions.value.search,
      projectChemicalForAutocompleteOptions.value.options.page,
      projectChemicalForAutocompleteOptions.value.options.rowsPerPage
    ],
    async () => {
      debouncedSearch();
    }
  );

  const debouncedSearch = useDebounceFn(async () => {
    projectChemicals.getProjectChemicalForAutocomplete(
      projectChemicalForAutocompleteOptions.value,
      false,
      projectId.value
    );
  }, 350);

  const validateInput = () => {
    const input = valueState.value?.toString();
    if (input) {
      if (/^\d+([.,]\d+)?$/.test(input)) {
        valueState.value = input;
      } else {
        valueState.value = '';
      }
    } else {
      valueState.value = '';
    }

    // Send WebSocket update for chemical table field
    if (chemicalTableWS && props.baseRow?.chemical_id) {
      console.log('🧪 Sending chemical field update:', {
        chemicalId: props.baseRow.chemical_id,
        field: props.objectKey,
        value: valueState.value
      });

      if (chemicalTableWS) {
        chemicalTableWS.sendChemicalFieldUpdate(
          props.baseRow.chemical_id,
          props.objectKey,
          valueState.value
        );
      }
    }
  };
  const validateInputComboBox = () => {
    if (!selectedName.value) {
      valueState.value = '';
      selectedName.value = '';
    }

    // Send WebSocket update for chemical table field
    if (chemicalTableWS && props.baseRow?.chemical_id) {
      console.log('🧪 Sending chemical combobox update:', {
        chemicalId: props.baseRow.chemical_id,
        field: props.objectKey,
        value: valueState.value
      });

      if (chemicalTableWS) {
        chemicalTableWS.sendChemicalFieldUpdate(
          props.baseRow.chemical_id,
          props.objectKey,
          valueState.value
        );
      }
    }
  };

  const valueState = defineModel<string | number | undefined | ChemicalOptions>('value', {
    required: true
  });
  const selectedName = ref<string | null>(valueState.value as string | null);

  const onSelect = (selectedChemical: ProjectChemical | string) => {
    if (typeof selectedChemical === 'string') {
      valueState.value = selectedChemical;
      return;
    }

    if (!selectedChemical) {
      return;
    }

    valueState.value = selectedChemical.name;

    if (!props.baseRow) return;

    const updateBaseRow = (
      property: keyof typeof props.baseRow,
      chemicalValue: number | undefined
    ) => {
      if (props.baseRow) {
        if (
          !props.baseRow[property] &&
          chemicalValue &&
          (property === 'density' || property === 'molar_mass')
        ) {
          // eslint-disable-next-line vue/no-mutating-props
          props.baseRow[property] = chemicalValue;
        }
      }
    };
    updateBaseRow('density', convertDensity(selectedChemical.density));
    updateBaseRow('molar_mass', selectedChemical.molar_mass);
  };

  function convertDensity(value: number) {
    if (props.selectedUnitDensity === 'g') {
      return value;
    } else if (props.selectedUnitDensity === 'kg') {
      return value * 1000;
    }
  }

  // WebSocket focus/blur handlers for table locking
  const handleFocus = async () => {
    if (chemicalTableWS) {
      console.log('🔒 Locking chemical table on focus');
      await chemicalTableWS.lockTable();
    }
  };

  const handleBlur = () => {
    if (chemicalTableWS) {
      console.log('🔓 Scheduling chemical table unlock on blur');
      chemicalTableWS.scheduleAutoUnlock(1000); // 1 second delay
    }
  };
</script>
<style>
  .v-field__append-inner {
    display: none;
  }

  .number-input-reduced-padding .v-field__input {
    padding-left: 6px !important;
    padding-right: 6px !important;
  }
</style>
