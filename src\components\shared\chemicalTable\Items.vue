<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <Item
    v-for="(chemical, index) in chemicals"
    :key="chemical.chemical_id"
    :chemical="chemical"
    :form-id="formId"
    :is-editable="isEditable"
    :index="index"
    :data-chemicals-length="chemicals.length"
    @remove-chemical="(chemical_id: number) => $emit('removeChemical', chemical_id)"
    @calculate-chemical="(chemical_id: number) => $emit('calculateChemical', chemical_id)"
    @save-chemical-row="(chemical_id: number) => $emit('saveChemicalRow', chemical_id)"
    @reset-calculated-values="(chemical_id: number) => $emit('resetCalculatedValues', chemical_id)"
    @move-row-up="$emit('moveRowUp', index)"
    @move-row-down="$emit('moveRowDown', index)"
  />
</template>

<script setup lang="ts">
  import type { ChemicalTableChemicalDataI } from '@/views/pages/dashboard/projects/project/chemicals/components/ChemicalsTable2.vue';
  import Item from './Item.vue';

  defineEmits([
    'calculateChemical',
    'removeChemical',
    'saveChemicalRow',
    'moveRowUp',
    'moveRowDown',
    'resetCalculatedValues'
  ]);
  defineProps({
    chemicals: {
      type: Array<ChemicalTableChemicalDataI>,
      required: true,
      default: []
    },
    formId: {
      type: Number,
      required: false,
      default: 0
    },
    isEditable: {
      type: Boolean,
      default: true
    }
  });
</script>
