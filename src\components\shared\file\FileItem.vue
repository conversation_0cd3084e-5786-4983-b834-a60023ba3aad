<template>
  <v-card
    variant="outlined"
    :class="'card-hover-border bg-containerBg'"
    :subtitle="`Typ souboru: ${file.file_type}`"
    :title="file.file_name"
    link
    @click.prevent="redirectToDownloadFileLink(file.file_id)"
  >
    <template #prepend>
      <v-avatar v-if="filePreview" size="32" class="mr-2">
        <img :src="filePreview.file_url" width="32" alt="imag" />
      </v-avatar>
      <FileTextOutlined v-else :style="{ fontSize: '28px' }" />
    </template>

    <v-row>
      <v-col cols="12">
        <v-divider class="my-1"></v-divider>
        <v-col class="v-card-subtitle">
          Datum nahrání souboru: {{ toLocale(file.created_at) }}
        </v-col>
      </v-col>
    </v-row>
    <template #append>
      <v-menu>
        <template #activator="{ props }">
          <v-btn
            size="x-small"
            v-bind="props"
            variant="text"
            style="height: auto"
            @click.prevent.stop="props.isActive = true"
          >
            <EllipsisOutlined :style="{ fontSize: '28px' }" />
          </v-btn>
        </template>

        <v-list elevation="24" density="compact" class="py-0">
          <v-list-item
            v-if="!(isReadOnly === true) && !isFromMethods"
            :value="file.file_id + '_update_file_name'"
          >
            <v-list-item-title @click="filesStore.showFileUpdateModal(file.file_id)">
              Aktualizovat název
            </v-list-item-title>
          </v-list-item>

          <v-list-item :value="file.file_id + '_download'">
            <v-list-item-title @click="downloadFile">Stáhnout soubor</v-list-item-title>
          </v-list-item>

          <v-list-item v-if="!(isReadOnly === true)" :value="file.file_id + '_delete'">
            <v-list-item-title @click="handleDeleteFile">Odebrat soubor</v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>
    </template>
  </v-card>
  <ConfirmDlg ref="ConfirmRef" />
</template>

<script lang="ts" setup>
  import { File, useFilesStore, type FilePreviewI } from '@/stores/files';
  import { EllipsisOutlined, FileTextOutlined } from '@ant-design/icons-vue';
  import { notification } from 'ant-design-vue';
  import { onMounted, ref } from 'vue';
  import ConfirmDlg from '../ConfirmDlg.vue';
  import { toLocale } from '@/utils/locales';
  const ConfirmRef = ref<InstanceType<typeof ConfirmDlg> | null>(null);
  const emits = defineEmits(['reload', 'fileRemove']);

  const props = defineProps({
    file: {
      type: Object as () => File,
      required: true
    },
    isReadOnly: {
      type: Boolean,
      required: false,
      default: false
    },
    customRemoveFile: {
      type: Boolean,
      required: false,
      default: false
    },
    isFromMethods: {
      type: Boolean,
      required: false,
      default: false
    }
  });

  const filePreview = ref<FilePreviewI | null>(null);

  onMounted(async () => {
    const _filePreview = await filesStore.getFilePreview(props.file.file_id);
    if (_filePreview && props.file.file_type.includes('image')) {
      filePreview.value = _filePreview;
    }
  });

  const filesStore = useFilesStore();
  const redirectToDownloadFileLink = async (file_id: number) => {
    /*
  const filePreview = await filesStore.getFilePreview(file_id);
  if (filePreview) {
    window.open(filePreview.file_url, '_blank');
  } else {
    notification.error({
      message: 'Soubor nenalezen',
      description: 'Nepodařilo se stáhnout soubor',
      duration: 5
    });
  }
  */
    const FilePreview = await filesStore.getFilePreview(file_id);
    if (!FilePreview) {
      notification.error({
        message: 'Soubor nenalezen',
        description: 'Nepodařilo se stáhnout soubor',
        duration: 5
      });
      return;
    }

    try {
      const response = await fetch(FilePreview.file_url);
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', props.file.file_name);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      notification.error({
        message: 'Soubor nenalezen',
        description: 'Nepodařilo se stáhnout soubor',
        duration: 5
      });
    }
  };

  const downloadFile = async () => {
    const FilePreview = await filesStore.getFilePreview(props.file.file_id);
    if (!FilePreview) {
      notification.error({
        message: 'Soubor nenalezen',
        description: 'Nepodařilo se stáhnout soubor',
        duration: 5
      });
      return;
    }

    try {
      const response = await fetch(FilePreview.file_url);
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', props.file.file_name);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      notification.error({
        message: 'Soubor nenalezen',
        description: 'Nepodařilo se stáhnout soubor',
        duration: 5
      });
    }
  };

  const handleDeleteFile = async () => {
    if (
      await ConfirmRef.value?.open('Opravdu chcete odebrat soubor?', '', {
        color: 'error',
        notclosable: true,
        zIndex: 2400
      })
    ) {
      if (props?.customRemoveFile) {
        emits('fileRemove', props.file.file_id);
        return;
      } else {
        await filesStore.deleteFile(props.file.file_id);
        emits('fileRemove', props.file.file_id);
        emits('reload');
      }
    }
  };
</script>
