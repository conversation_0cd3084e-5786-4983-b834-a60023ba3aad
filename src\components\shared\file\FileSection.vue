<template>
  <section v-if="!minimal" id="fileSection" class="my-5">
    <span class="text-h4">Soubory</span>
  </section>
  <template v-if="files.length > 0">
    <v-row v-if="!minimal">
      <v-col
        v-for="file in files.filter((file) =>
          fileSearch
            ? file.file_name.toUpperCase().includes(fileSearch.toUpperCase().replace(/\s+/g, ''))
            : true
        )"
        :key="file.file_id"
        cols="12"
      >
        <FileItem
          :file="file"
          :is-read-only="isReadOnly"
          :custom-remove-file="customRemoveFile"
          :is-from-methods="isFromMethods"
          @reload="$emit('reload')"
          @file-remove="(file_id: number) => $emit('fileRemove', file_id)"
        />
      </v-col>
    </v-row>
    <template v-else>
      <v-chip-group column link class="my-0">
        <v-chip
          v-for="file in files.filter((file) =>
            fileSearch
              ? file.file_name.toUpperCase().includes(fileSearch.toUpperCase().replace(/\s+/g, ''))
              : true
          )"
          :key="'minimal_' + file.file_id"
          class="my-0"
          color="error"
          size="small"
          label
          @click.prevent.stop="downloadFile(file.file_id)"
        >
          {{ file.file_name }}
        </v-chip>
      </v-chip-group>
    </template>
  </template>
  <template v-else-if="minimal">
    <v-row>
      <v-col cols="12" class="mt-2"><span class="font-weight-bold">Žádné soubory</span></v-col>
    </v-row>
  </template>
  <NotFoundItem v-else>
    <template #notFound>Nebyly nalezeny žádné soubory</template>
  </NotFoundItem>

  <UpdateFileNameModal
    :show="filesStore.showUpdateFileNameModal"
    @update-file-name="$emit('reload')"
  />
</template>

<script lang="ts" setup>
  import { useFilesStore, type File } from '@/stores/files';
  import { notification } from 'ant-design-vue';
  import NotFoundItem from '../NotFoundItem.vue';
  import FileItem from './FileItem.vue';
  import UpdateFileNameModal from './UpdateFileNameModal.vue';

  defineEmits(['reload', 'fileRemove']);

  const props = defineProps({
    files: {
      type: Array<File>,
      required: true
    },
    isReadOnly: {
      type: Boolean,
      required: false,
      default: false
    },
    fileSearch: {
      type: String,
      required: false,
      default: ''
    },
    customRemoveFile: {
      type: Boolean,
      required: false,
      default: false
    },
    minimal: {
      type: Boolean,
      required: false,
      default: false
    },
    isFromMethods: {
      type: Boolean,
      required: false,
      default: false
    }
  });

  const filesStore = useFilesStore();
  const downloadFile = async (file_id: number) => {
    const FilePreview = await filesStore.getFilePreview(file_id);
    if (!FilePreview) {
      notification.error({
        message: 'Soubor nenalezen',
        description: 'Nepodařilo se stáhnout soubor',
        duration: 5
      });
      return;
    }

    try {
      const response = await fetch(FilePreview.file_url);
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute(
        'download',
        props.files.find((file) => file.file_id === file_id)?.file_name || 'soubor'
      );
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      notification.error({
        message: 'Soubor nenalezen',
        description: 'Nepodařilo se stáhnout soubor',
        duration: 5
      });
    }
  };
</script>
