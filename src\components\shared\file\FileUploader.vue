<template>
  <Dashboard
    v-if="uppy"
    ref="uppyFileUploader"
    :uppy="uppy"
    :plugins="[]"
    class="uppyFileUploader"
    :props="{
      theme: isDark ? 'dark' : 'light',
      hideUploadButton: false,
      hideProgressAfterFinish: false,
      hidePauseResumeButton: true,
      hideCancelButton: true,
      hideRetryButton: true,
      showRemoveButtonAfterComplete: !disabled,
      disabled: disabled
    }"
  />
</template>
<script lang="ts" setup>
  import { useAuthStore } from '@/stores/auth';
  import { useCustomizerStore } from '@/stores/customizer';
  import { File, useFilesStore, type FileDto } from '@/stores/files';
  import type { BaseResponseI } from '@/utils/axios';
  import { fetchWrapper } from '@/utils/helpers/fetch-wrapper';
  import { simpleConfig } from '@/utils/SetupUppy';
  import type { Body } from '@uppy/core';
  import Uppy from '@uppy/core';
  import '@uppy/core/dist/style.css';
  import '@uppy/dashboard/dist/style.css';
  import { Dashboard } from '@uppy/vue';
  import XHRUpload from '@uppy/xhr-upload';
  import { useVModel } from '@vueuse/core';
  import { notification } from 'ant-design-vue';
  import { computed, onMounted, ref } from 'vue';

  const uppyFileUploader = ref<HTMLElement | null>(null);

  export interface ResponseBody extends Body {
    file_id: number;
    data: BaseResponseI<FileDto>;
  }

  const props = defineProps({
    modelValue: {
      type: Array<File>,
      required: true
    },
    remoteFileIds: {
      type: Array<number>,
      required: false,
      default: []
    },
    disabled: {
      type: Boolean,
      required: false,
      default: false
    },
    processRemoveFile: {
      type: Function,
      required: false
    },
    processSaveFile: {
      type: Function,
      required: false
    },
    uppyOptions: {
      type: Object,
      required: false,
      default: () => ({})
    }
  });

  const modelValueState = useVModel(props, 'modelValue');
  const remoteFileIdsState = useVModel(props, 'remoteFileIds');

  const customizer = useCustomizerStore();
  const isDark = computed(() => customizer.theme.toUpperCase().includes('DARK'));

  const uppy = new Uppy(
    props.uppyOptions ? { ...simpleConfig(), ...props.uppyOptions } : simpleConfig()
  )
    .use(XHRUpload, {
      endpoint: `${import.meta.env.VITE_API_URL}/file/upload`,
      formData: true,
      async onBeforeRequest(xhr) {
        const authStore = useAuthStore();
        const filesStore = useFilesStore();

        filesStore.loading = true;
        let token = authStore.token?.access_token;

        if (!token) {
          token = authStore.getAccessToken();
        }

        xhr.setRequestHeader('Authorization', `Bearer ${token}`);
      },
      async onAfterResponse(response) {
        const filesStore = useFilesStore();

        if (response.status === 200) {
          filesStore.processUploadResponse(response.responseText).then(async (file) => {
            if (file) {
              if (!modelValueState.value.map((f) => f.file_id).includes(file.file_id)) {
                if (props.processSaveFile) {
                  await props.processSaveFile(file.file_id);
                }

                modelValueState.value.push(file);
              }
            }
          });
        } else {
          filesStore.error = response.responseText;
          filesStore.loading = false;

          throw new Error(response.responseText);
        }
      }
    })
    .on('file-removed', async (file) => {
      const file_id = (file.response?.body?.data as any)?.file_id as number | undefined;
      const foundFile = modelValueState.value.find((f) => f.file_id === file_id);

      if (foundFile && modelValueState.value.indexOf(foundFile) > -1) {
        modelValueState.value.splice(modelValueState.value.indexOf(foundFile), 1);
      }

      if (file_id) {
        if (props.processRemoveFile) {
          await props.processRemoveFile(file_id);
        } else {
          const filesStore = useFilesStore();
          filesStore.deleteFile(file_id);
        }
      }
    });

  const fetchPreviouslyUplodedImagesUrl = async () => {
    remoteFileIdsState.value.forEach(async (file_id: number) => {
      processSetUppyFile(file_id);
    });
  };

  const processSetUppyFile = async (file_id: number) => {
    const filesStore = useFilesStore();
    const file = await filesStore.getFileById(file_id, false);

    if (file) {
      const FilePreview = await filesStore.getFilePreview(file.file_id);

      if (FilePreview) {
        fetchWrapper.blob(`${FilePreview.file_url}`).then((blob) => {
          uppy.addFile({
            id: file_id.toString(),
            name: file.file_path,
            type: blob.type,
            data: blob,
            remote: {
              host: `${import.meta.env.VITE_HOST}`,
              url: FilePreview.file_url,
              requestClientId: file_id.toString(),
              companionUrl: `${import.meta.env.VITE_HOST}`
            }
          });
          uppy.setFileState(file_id.toString(), {
            progress: undefined // { uploadStarted: null, bytesUploaded: false, uploadComplete: true }
          });
        });

        if (!modelValueState.value.map((f) => f.file_id).includes(file.file_id)) {
          modelValueState.value.push(file);
        }
      }
    }
  };

  onMounted(() => {
    fetchPreviouslyUplodedImagesUrl();
  });
</script>
<style scoped>
  :deep(.uppy-Dashboard-inner) {
    width: 100% !important;
  }
</style>
