<script setup lang="ts">
  import { File, useFilesStore } from '@/stores/files';
  import { useVModel } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import { ref } from 'vue';
  import FileUploader from './FileUploader.vue';

  const emit = defineEmits(['update:show', 'submitFiles']);

  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    },
    subTitle: {
      type: String,
      required: false,
      default: undefined
    }
  });

  const files = ref<File[]>([]);
  const filesStore = useFilesStore();
  const { loading } = storeToRefs(filesStore);

  const CreateInstrumentForm = ref();
  async function submitFormToValidate() {
    emit(
      'submitFiles',
      files.value.map((file) => file.file_id)
    );
    filesStore.showFileUploaderModal = false;
  }

  const showState = useVModel(props, 'show');
</script>
<template>
  <v-dialog v-model="showState" class="customer-modal" :z-index="1010">
    <v-card :loading="loading">
      <v-form
        ref="CreateInstrumentForm"
        class="createInstrumentForm"
        @submit.prevent="submitFormToValidate"
      >
        <v-card-title class="pa-5">
          <span class="text-h5">Přidat soubory</span>
          <br />
          <div class="d-flex justify-content gap-2">
            <small v-if="subTitle" class="text-caption">
              <strong>{{ subTitle }}</strong>
            </small>
          </div>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col>
              <v-row>
                <v-col cols="12">
                  <FileUploader v-model="files" />
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" variant="text" @click="filesStore.showFileUploaderModal = false">
            Zavřít
          </v-btn>
          <v-btn color="primary" variant="flat" type="submit" :loading="loading">
            Přidat nahrané soubory
          </v-btn>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>
