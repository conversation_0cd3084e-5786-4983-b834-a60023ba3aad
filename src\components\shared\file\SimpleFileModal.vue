<template>
  <v-dialog v-model="showState" class="customer-modal" style="z-index: 1010">
    <v-card>
      <v-form ref="CreateTagForm" class="createTagForm" @submit.prevent="submitFormToValidate">
        <v-card-title class="pa-5">
          <span class="text-h5">V<PERSON>žte soubor</span>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col cols="12">
              <v-label class="mb-2">Příloha</v-label>
              <FileUploader
                v-model="selectedFile"
                :uppy-options="{
                  restrictions: {
                    maxNumberOfFiles: 1,
                    minNumberOfFiles: 0,
                    allowedFileTypes: null
                  }
                }"
              />
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" variant="text" @click="showState = false"><PERSON>av<PERSON><PERSON>t</v-btn>
          <v-btn color="primary" variant="flat" type="submit" :loading="loading">
            Přidat nahraný soubor
          </v-btn>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>
<script setup lang="ts">
  import FileUploader from '@/components/shared/file/FileUploader.vue';
  import { useAnalyticalRequestsStore } from '@/stores/analyticalRequests/analyticalRequests';
  import { useSamplesStore } from '@/stores/sample/samples';
  import { File, useFilesStore } from '@/stores/files';
  import { useVModel } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import { onMounted, ref } from 'vue';
  const analyticalRequestsStore = useAnalyticalRequestsStore();
  const samplesStore = useSamplesStore();
  const filesStore = useFilesStore();
  const { loading } = storeToRefs(filesStore);
  const selectedFile = ref<File[]>([]);
  const emits = defineEmits(['update:show', 'reload']);
  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    },
    selectedSample: {
      type: Number,
      required: true
    }
  });
  const showState = useVModel(props, 'show');
  const submitFormToValidate = async () => {
    if (selectedFile.value.length > 0) {
      await samplesStore.addFiletoSample(selectedFile.value[0].file_id, props.selectedSample);
      showState.value = false;
      emits('reload');
    }
  };
  onMounted(() => {
    selectedFile.value = [];
  });
</script>
