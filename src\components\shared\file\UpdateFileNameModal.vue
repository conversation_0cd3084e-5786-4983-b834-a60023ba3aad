<script setup lang="ts">
  import { useFilesStore } from '@/stores/files';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { useVModel } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import { ref } from 'vue';

  const emit = defineEmits(['update:show', 'updateFileName']);

  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    }
  });

  const filesStore = useFilesStore();
  const { file, loading, showUpdateFileNameModal } = storeToRefs(filesStore);

  const CreateInstrumentForm = ref();
  const showState = useVModel(props, 'show');

  async function submitFormToValidate() {
    if (CreateInstrumentForm.value.isValid && file.value) {
      return filesStore.updateFileName(file.value.file_id, file.value.file_name).then(() => {
        emit('updateFileName');
        showUpdateFileNameModal.value = false;
      });
    }
  }
</script>
<template>
  <v-dialog v-model="showState" class="customer-modal" :z-index="1010">
    <v-card :loading="loading">
      <v-form
        v-if="file"
        ref="CreateInstrumentForm"
        class="createInstrumentForm"
        @submit.prevent="submitFormToValidate"
      >
        <v-card-title class="pa-5">
          <span class="text-h5">Editace názvu souboru</span>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col>
              <v-row>
                <v-col>
                  <v-label class="mb-2">Název souboru</v-label>
                  <v-text-field
                    v-model="file.file_name"
                    :rules="itemRequiredRule"
                    single-line
                    placeholder="Zadejte název souboru"
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" variant="text" @click="showUpdateFileNameModal = false">
            Zrušit
          </v-btn>
          <v-btn color="primary" variant="flat" type="submit" :loading="loading">Upravit</v-btn>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>
