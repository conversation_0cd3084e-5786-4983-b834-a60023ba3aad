<template>
  <div v-if="activeUsers.length > 0" class="active-users-indicator">
    <!-- Label on the left -->
    <div class="active-users-header">
      <v-icon size="small" class="mr-2">mdi-account-multiple</v-icon>
      <span class="text-caption">Aktivn<PERSON> uživatelé</span>
    </div>

    <!-- Spacer to push icons to the right -->
    <div class="spacer"></div>

    <!-- User icons on the right -->
    <div class="active-users-list" :class="{ 'many-users': activeUsers.length > 8 }">
      <div class="users-scroll-container">
        <v-tooltip
          v-for="user in displayedUsers"
          :key="user.userId"
          :text="`${user.name || user.userId} - ${user.status}`"
          location="bottom"
        >
          <template #activator="{ props }">
            <v-avatar
              v-bind="props"
              :color="getUserColor(user.userId)"
              size="32"
              class="user-avatar"
              :class="{ me: user.userId === currentUserId }"
            >
              <span class="text-caption font-weight-bold text-white">
                {{ getUserInitials(user.name || user.userId) }}
              </span>
            </v-avatar>
          </template>
        </v-tooltip>

        <!-- Show overflow indicator if there are more users -->
        <v-tooltip
          v-if="hasOverflow"
          text="Klikněte pro zobrazení všech uživatelů"
          location="bottom"
        >
          <template #activator="{ props }">
            <v-avatar
              v-bind="props"
              color="grey-darken-1"
              size="32"
              class="user-avatar overflow-indicator"
              @click="toggleShowAll"
            >
              <span class="text-caption font-weight-bold text-white">+{{ overflowCount }}</span>
            </v-avatar>
          </template>
        </v-tooltip>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, onMounted } from 'vue';
  import { useWebSocketStore } from '@/stores/websocket';
  import { useAuthStore } from '@/stores/auth';
  import { getCachedUserColor } from '@/composables/useUserColors';
  import { useUserNames } from '@/composables/useUserNames';

  interface ActiveUser {
    userId: string;
    name?: string;
    status: string;
    lastActivity?: Date;
  }

  const webSocketStore = useWebSocketStore();
  const authStore = useAuthStore();
  const { loadUsers, getUserName, getUserInitials } = useUserNames();

  // Performance settings
  const MAX_VISIBLE_USERS = 8; // Show max 8 users before overflow
  const showAllUsers = ref(false);

  const currentUserId = computed(() => authStore.user?.user_id?.toString() || '');

  const activeUsers = computed((): ActiveUser[] => {
    return webSocketStore.activeUsers.map((userId) => ({
      userId,
      name: getUserName(userId),
      status: userId === currentUserId.value ? 'Vy' : 'Online'
    }));
  });

  // Performance optimization: limit displayed users
  const displayedUsers = computed(() => {
    if (showAllUsers.value || activeUsers.value.length <= MAX_VISIBLE_USERS) {
      return activeUsers.value;
    }
    return activeUsers.value.slice(0, MAX_VISIBLE_USERS);
  });

  const hasOverflow = computed(() => {
    return !showAllUsers.value && activeUsers.value.length > MAX_VISIBLE_USERS;
  });

  const overflowCount = computed(() => {
    return Math.max(0, activeUsers.value.length - MAX_VISIBLE_USERS);
  });

  function toggleShowAll() {
    showAllUsers.value = !showAllUsers.value;
  }

  // Use optimized color system from composable
  function getUserColor(userId: string): string {
    return getCachedUserColor(userId);
  }

  // Load all users on component mount
  onMounted(async () => {
    await loadUsers();
  });
</script>

<style scoped>
  .active-users-indicator {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 8px 16px;
    background: rgba(var(--v-theme-surface), 0.8);
    border-radius: 8px;
    border: 1px solid rgba(var(--v-theme-outline), 0.2);
    backdrop-filter: blur(4px);
    overflow: hidden; /* Prevent content from overflowing */
  }

  .active-users-header {
    display: flex;
    align-items: center;
    color: rgba(var(--v-theme-on-surface), 0.7);
    white-space: nowrap;
    flex-shrink: 0; /* Don't shrink the label */
  }

  .spacer {
    flex: 1; /* Take up all available space */
  }

  .active-users-list {
    display: flex;
    align-items: center;
    flex-shrink: 0; /* Don't shrink the user icons */
  }

  .users-scroll-container {
    display: flex;
    gap: 4px;
    align-items: center;
    overflow-x: auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE/Edge */
    padding: 4px; /* Add padding to prevent clipping on hover */
    max-width: 100%;
  }

  .users-scroll-container::-webkit-scrollbar {
    display: none; /* Chrome/Safari */
  }

  /* Smooth scrolling for many users */
  .active-users-list.many-users .users-scroll-container {
    max-width: 300px; /* Limit width when many users */
    padding-right: 12px; /* Extra padding for overflow indicator */
  }

  .user-avatar {
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
    flex-shrink: 0; /* Prevent avatars from shrinking */
    margin: 2px; /* Add margin to prevent clipping on hover */
  }

  .user-avatar:hover {
    transform: scale(1.1);
    z-index: 10; /* Ensure hovered avatar appears above others */
  }

  .user-avatar.me {
    border-color: rgba(var(--v-theme-primary), 0.5);
    box-shadow: 0 0 0 2px rgba(var(--v-theme-primary), 0.2);
  }

  .user-avatar.overflow-indicator {
    background: linear-gradient(45deg, #666, #888) !important;
    border: 2px solid rgba(255, 255, 255, 0.3);
  }

  .user-avatar.overflow-indicator:hover {
    background: linear-gradient(45deg, #777, #999) !important;
    transform: scale(1.1);
  }

  @media (max-width: 768px) {
    .active-users-indicator {
      padding: 6px 12px;
      gap: 8px;
    }

    .active-users-header span {
      display: none;
    }

    .user-avatar {
      width: 28px !important;
      height: 28px !important;
    }
  }
</style>
