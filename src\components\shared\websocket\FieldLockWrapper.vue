<template>
  <div
    ref="wrapperRef"
    class="field-lock-wrapper"
    :class="{
      'field-locked': isLockedByOther,
      'field-locked-by-me': isLockedByMe,
      'field-editing': isEditing
    }"
  >
    <!-- Lock indicator overlay - only shows for OTHER users' locks -->
    <div v-if="isLockedByOther" class="lock-overlay-other">
      <div class="lock-indicator">
        <v-icon size="small" :color="lockOwnerColor">mdi-lock</v-icon>
        <span class="lock-text">{{ lockOwnerName }}</span>
        <v-btn
          v-if="!hasRequestedAccess"
          size="x-small"
          variant="outlined"
          :color="lockOwnerColor"
          class="ml-2"
          @click="requestAccess"
        >
          Požádat o přístup
        </v-btn>
        <v-chip v-else size="x-small" color="warning" variant="outlined" class="ml-2">
          Žádost odeslána
        </v-chip>
      </div>
    </div>

    <!-- Slot for the actual form field -->
    <div
      class="field-content"
      :style="{ borderColor: fieldBorderColor }"
      @focusin="handleFocusIn"
      @focusout="handleFocusOut"
      @keydown="handleKeyDown"
      @input="handleInputEvent"
      @change="handleChangeEvent"
      @paste="handlePaste"
      @click="handleClick"
    >
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, watch, onUnmounted } from 'vue';
  import { useWebSocketStore } from '@/stores/websocket';
  import { getCachedUserColor } from '@/composables/useUserColors';
  import { useRealTimeFieldSync } from '@/composables/useRealTimeFieldSync';
  import { useUserNames } from '@/composables/useUserNames';

  interface Props {
    fieldName: string;
    autoLock?: boolean;
    disabled?: boolean;
    realTimeSync?: boolean; // Enable real-time field synchronization
    showTypingIndicators?: boolean; // Show typing indicators from other users
    disableFieldUpdates?: boolean; // Disable field update sending (for chemical tables)
  }

  const props = withDefaults(defineProps<Props>(), {
    autoLock: true,
    disabled: false,
    realTimeSync: true,
    showTypingIndicators: true,
    disableFieldUpdates: false
  });

  const webSocketStore = useWebSocketStore();
  const isEditing = ref(false);
  const hasRequestedAccess = ref(false);
  const wrapperRef = ref<HTMLElement | null>(null);
  const unlockTimeout = ref<number | null>(null);

  const isLockedByOther = computed(() => webSocketStore.isFieldLockedByOther(props.fieldName));

  const isLockedByMe = computed(() => webSocketStore.isFieldLockedByMe(props.fieldName));

  const lockInfo = computed(() => webSocketStore.getFieldLockInfo(props.fieldName));

  const lockOwnerName = computed(() => {
    if (!lockInfo.value) return '';
    const { getUserName } = useUserNames();
    return getUserName(lockInfo.value.user_id);
  });

  const lockOwnerColor = computed(() => {
    if (!lockInfo.value) return 'grey';
    return getUserColor(lockInfo.value.user_id);
  });

  // Real-time field synchronization (disabled when field updates are disabled)
  const realTimeSync = useRealTimeFieldSync({
    fieldName: props.fieldName,
    enabled: props.realTimeSync && !props.disabled && !props.disableFieldUpdates,
    showTypingIndicators: props.showTypingIndicators,
    syncDelay: 300
  });

  const fieldBorderColor = computed(() => {
    if (isLockedByMe.value) return getUserColor(webSocketStore.currentUserId || ''); // my color
    if (isLockedByOther.value) return lockOwnerColor.value; // other user's color
    if (isEditing.value) return getUserColor(webSocketStore.currentUserId || ''); // my color when editing
    return 'transparent';
  });

  // Color palette for users (same as ActiveUsersIndicator)
  // Use optimized color system from composable
  function getUserColor(userId: string): string {
    return getCachedUserColor(userId);
  }

  async function handleFocusIn() {
    if (props.disabled) return;

    // Clear any pending unlock timeout since user is editing again
    if (unlockTimeout.value) {
      console.log('🔒 Canceling auto-unlock - user is editing again');
      window.clearTimeout(unlockTimeout.value);
      unlockTimeout.value = null;
    }

    isEditing.value = true;

    if (props.autoLock && webSocketStore.isConnected) {
      const success = await webSocketStore.autoLockField(props.fieldName);
      if (!success) {
        // Field is locked by someone else, blur the field
        const activeElement = document.activeElement as HTMLElement;
        if (activeElement) {
          activeElement.blur();
        }
      }
    }
  }

  function handleClick() {
    // Handle clicks on TinyMCE editors which don't trigger focusin properly
    if (!isEditing.value) {
      handleFocusIn();
    }
  }

  function handleFocusOut() {
    isEditing.value = false;

    // Clear any existing unlock timeout
    if (unlockTimeout.value) {
      window.clearTimeout(unlockTimeout.value);
    }

    // Auto-unlock after 1 second when focus is lost
    if (isLockedByMe.value && props.autoLock) {
      console.log('🔓 Scheduling auto-unlock for field:', props.fieldName);
      unlockTimeout.value = window.setTimeout(() => {
        if (!isEditing.value && isLockedByMe.value) {
          console.log('🔓 Auto-unlocking field:', props.fieldName);
          webSocketStore.unlockField(props.fieldName);
        } else {
          console.log('🔓 Skipping auto-unlock - field state changed:', {
            isEditing: isEditing.value,
            isLockedByMe: isLockedByMe.value
          });
        }
        unlockTimeout.value = null;
      }, 1000); // 1 second delay
    }
  }

  function requestAccess() {
    hasRequestedAccess.value = true;
    webSocketStore.requestFieldAccess(
      props.fieldName,
      `Prosím o povolení úpravy pole "${props.fieldName}"`
    );

    // Reset the flag after some time
    setTimeout(() => {
      hasRequestedAccess.value = false;
    }, 30000); // 30 seconds
  }

  // Input interception methods
  function handleKeyDown(event: KeyboardEvent) {
    if (isLockedByOther.value && !props.disabled) {
      event.preventDefault();
      event.stopPropagation();
      showLockedFieldNotification();
    }
  }

  function handleInputEvent(event: Event) {
    // Skip validation if this is a remote update
    if (isReceivingRemoteUpdate.value) {
      console.log('⏭️ Skipping validation - remote update in progress');
      return;
    }

    if (isLockedByOther.value && !props.disabled) {
      event.preventDefault();
      event.stopPropagation();
      showLockedFieldNotification();
      return;
    }

    // Handle real-time sync for input events (skip if field updates are disabled)
    if (props.realTimeSync && !props.disabled && !props.disableFieldUpdates) {
      const target = event.target as HTMLInputElement | HTMLTextAreaElement;
      if (target && target.value !== undefined) {
        console.log('🔄 Real-time input sync:', {
          fieldName: props.fieldName,
          value: target.value
        });
        realTimeSync.handleFieldInput(target.value);
      }
    }
  }

  function handleChangeEvent(event: Event) {
    // Skip validation if this is a remote update
    if (isReceivingRemoteUpdate.value) {
      console.log('⏭️ Skipping validation - remote update in progress');
      return;
    }

    if (isLockedByOther.value && !props.disabled) {
      event.preventDefault();
      event.stopPropagation();
      showLockedFieldNotification();
      return;
    }

    // Handle real-time sync for change events (skip if field updates are disabled)
    if (props.realTimeSync && !props.disabled && !props.disableFieldUpdates) {
      const target = event.target as HTMLInputElement | HTMLTextAreaElement;
      if (target && target.value !== undefined) {
        console.log('🔄 Real-time change sync:', {
          fieldName: props.fieldName,
          value: target.value
        });
        realTimeSync.handleFieldChange(target.value);
      }
    }
  }

  function handlePaste(event: ClipboardEvent) {
    if (isLockedByOther.value && !props.disabled) {
      event.preventDefault();
      event.stopPropagation();
      showLockedFieldNotification();
    }
  }

  function showLockedFieldNotification() {
    if (!hasRequestedAccess.value) {
      webSocketStore.autoLockField(props.fieldName);
    }
  }

  onUnmounted(() => {
    // Clean up unlock timeout
    if (unlockTimeout.value) {
      window.clearTimeout(unlockTimeout.value);
    }
  });

  // Flag to prevent triggering validation on remote updates
  const isReceivingRemoteUpdate = ref(false);

  // Watch for incoming real-time updates
  watch(
    () => realTimeSync.remoteUpdate.value,
    (newUpdate) => {
      if (newUpdate && newUpdate.userId !== webSocketStore.currentUserId) {
        console.log('📥 FieldLockWrapper received remote update:', {
          fieldName: props.fieldName,
          value:
            typeof newUpdate.value === 'string'
              ? newUpdate.value.substring(0, 50) + '...'
              : newUpdate.value,
          fromUser: newUpdate.userId
        });

        // Handle regular input/textarea fields
        const fieldElement = wrapperRef.value?.querySelector('input, textarea') as
          | HTMLInputElement
          | HTMLTextAreaElement;
        if (fieldElement && fieldElement.value !== newUpdate.value) {
          console.log('📝 Updating field value:', {
            from: fieldElement.value,
            to: newUpdate.value
          });

          // Set flag to prevent validation warnings
          isReceivingRemoteUpdate.value = true;

          fieldElement.value = newUpdate.value;
          // Trigger input event to update v-model
          fieldElement.dispatchEvent(new Event('input', { bubbles: true }));

          // Reset flag after a short delay
          setTimeout(() => {
            isReceivingRemoteUpdate.value = false;
          }, 100);
        }
      }
    },
    { deep: true }
  );

  // Watch for access response
  watch(
    () => webSocketStore.isFieldLockedByMe(props.fieldName),
    (isLocked) => {
      if (isLocked && hasRequestedAccess.value) {
        hasRequestedAccess.value = false;
      }
    }
  );
</script>

<style scoped>
  .field-lock-wrapper {
    position: relative;
    transition: all 0.2s ease;
  }

  .field-content {
    border: 1px solid transparent;
    border-radius: 4px;
    transition: border-color 0.2s ease;
  }

  .lock-overlay-other {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(1px);
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    pointer-events: auto;
    opacity: 0.9;
  }

  .lock-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.1);
    pointer-events: auto;
  }

  .lock-text {
    font-size: 12px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.7);
  }

  /* Ensure buttons in lock overlay are clickable */
  .lock-indicator .v-btn {
    pointer-events: auto !important;
    cursor: pointer !important;
    z-index: 11;
  }

  .typing-indicator {
    position: absolute;
    top: -4em;
    left: 0;
    z-index: 6;
    animation: fadeInOut 2s infinite;
  }

  @keyframes fadeInOut {
    0%,
    100% {
      opacity: 0.7;
    }
    50% {
      opacity: 1;
    }
  }

  .field-locked {
    /* Don't disable pointer events - we want to intercept them */
    position: relative;
  }

  .field-locked .field-content {
    background: rgba(255, 193, 7, 0.1);
    /* Add subtle indication that field is still there */
    opacity: 0.8;
  }

  .field-locked-by-me .field-content {
    background: rgba(76, 175, 80, 0.05);
  }

  .field-editing .field-content {
    background: rgba(33, 150, 243, 0.05);
  }

  /* Ensure form controls inside are properly styled */
  .field-lock-wrapper :deep(.v-field) {
    transition: all 0.2s ease;
    margin: 0;
  }

  .field-lock-wrapper :deep(.v-input) {
    margin: 0;
    padding: 0;
  }

  .field-lock-wrapper :deep(.v-input__control) {
    margin: 0;
  }

  .field-locked :deep(.v-field) {
    opacity: 0.6;
  }

  .field-locked-by-me :deep(.v-field) {
    border-color: rgba(76, 175, 80, 0.5) !important;
  }
</style>
