// Chemical table WebSocket integration composable
import { ref, computed, watch, onUnmounted } from 'vue';
import { useWebSocketStore } from '@/stores/websocket';

export interface ChemicalTableWebSocketOptions {
  tableName: string; // e.g., 'chemicals'
  enabled?: boolean;
  syncDelay?: number; // Delay in ms before sending updates
}

/**
 * Composable for chemical table WebSocket integration
 * Handles sending and receiving live updates for chemical table fields
 */
export function useChemicalTableWebSocket(options: ChemicalTableWebSocketOptions) {
  const webSocketStore = useWebSocketStore();

  const { tableName, enabled = true, syncDelay = 300 } = options;

  // Local state
  const syncTimeout = ref<number | null>(null);
  const isTableLocked = ref(false);
  const lockedByUser = ref<string | null>(null);

  // Computed properties
  const isConnected = computed(() => webSocketStore.isConnected);

  // Check if table is locked by current user
  const isLockedByMe = computed(() => webSocketStore.isFieldLockedByMe(tableName));

  // Check if table is locked by another user
  const isLockedByOther = computed(() => webSocketStore.isFieldLockedByOther(tableName));

  // Methods
  function sendChemicalFieldUpdate(tablePrimaryKey: number, fieldName: string, value: any) {
    if (!enabled || !isConnected.value) {
      console.warn('⚠️ Chemical table WebSocket not enabled or not connected');
      return;
    }

    // Clear any pending sync
    if (syncTimeout.value) {
      window.clearTimeout(syncTimeout.value);
    }

    // Debounce the update
    syncTimeout.value = window.setTimeout(() => {
      console.log('🧪 Sending chemical table update:', {
        tableName,
        tablePrimaryKey,
        fieldName,
        value
      });

      webSocketStore.sendChemicalTableUpdate(tablePrimaryKey, fieldName, value);
    }, syncDelay);
  }

  function sendImmediateChemicalFieldUpdate(
    tablePrimaryKey: number,
    fieldName: string,
    value: any
  ) {
    if (!enabled || !isConnected.value) {
      console.warn('⚠️ Chemical table WebSocket not enabled or not connected');
      return;
    }

    console.log('🧪 Sending immediate chemical table update:', {
      tableName,
      tablePrimaryKey,
      fieldName,
      value
    });

    webSocketStore.sendChemicalTableUpdate(tablePrimaryKey, fieldName, value);
  }

  // Lock the entire table when user starts editing any field
  async function lockTable(): Promise<boolean> {
    if (!enabled || !isConnected.value) return false;

    console.log('🔒 Locking chemical table:', tableName);
    const success = await webSocketStore.autoLockField(tableName);
    if (success) {
      isTableLocked.value = true;
      lockedByUser.value = webSocketStore.currentUserId;
    }
    return success;
  }

  // Unlock the table when user stops editing
  function unlockTable(): boolean {
    if (!enabled || !isConnected.value) return false;

    console.log('🔓 Unlocking chemical table:', tableName);
    const success = webSocketStore.unlockField(tableName);
    if (success) {
      isTableLocked.value = false;
      lockedByUser.value = null;
    }
    return success;
  }

  // Auto-unlock with delay
  function scheduleAutoUnlock(delay: number = 1000) {
    setTimeout(() => {
      if (isLockedByMe.value) {
        console.log('🔓 Auto-unlocking chemical table:', tableName);
        unlockTable();
      }
    }, delay);
  }

  // Get update for specific chemical field
  function getChemicalFieldUpdate(chemical_id: number, field_name: string) {
    const updateKey = `${chemical_id}_${field_name}`;
    return chemicalFieldUpdates.value.get(updateKey);
  }

  // Check if specific chemical field has pending update
  function hasChemicalFieldUpdate(chemical_id: number, field_name: string): boolean {
    const updateKey = `${chemical_id}_${field_name}`;
    return chemicalFieldUpdates.value.has(updateKey);
  }

  // Watch for remote chemical table updates
  const remoteUpdates = computed(() => webSocketStore.fieldUpdates['chemicals']);
  const chemicalFieldUpdates = ref<Map<string, any>>(new Map());

  watch(
    remoteUpdates,
    (newUpdate) => {
      if (newUpdate && newUpdate.userId !== webSocketStore.currentUserId) {
        console.log('📥 Chemical table received remote update:', {
          tableName,
          update: newUpdate
        });

        // Handle chemical table specific update structure
        if (newUpdate.value && typeof newUpdate.value === 'object') {
          const { table_primary_key, field, value } = newUpdate.value;

          if (table_primary_key && field) {
            const updateKey = `${table_primary_key}_${field}`;
            chemicalFieldUpdates.value.set(updateKey, {
              chemical_id: table_primary_key,
              field_name: field,
              value: value,
              userId: newUpdate.userId,
              timestamp: newUpdate.timestamp || Date.now()
            });

            console.log('🧪 Chemical field update processed:', {
              chemical_id: table_primary_key,
              field_name: field,
              value: value,
              updateKey,
              userId: newUpdate.userId
            });
          }
        }
      }
    },
    { deep: true }
  );

  // Cleanup
  onUnmounted(() => {
    if (syncTimeout.value) {
      window.clearTimeout(syncTimeout.value);
    }
  });

  return {
    // State
    isConnected,
    isTableLocked: computed(() => isTableLocked.value),
    isLockedByMe,
    isLockedByOther,
    lockedByUser: computed(() => lockedByUser.value),
    remoteUpdates,
    chemicalFieldUpdates: computed(() => chemicalFieldUpdates.value),

    // Methods
    sendChemicalFieldUpdate,
    sendImmediateChemicalFieldUpdate,
    lockTable,
    unlockTable,
    scheduleAutoUnlock,
    getChemicalFieldUpdate,
    hasChemicalFieldUpdate,

    // Utilities
    canEdit: computed(() => !isLockedByOther.value),
    webSocketStore // Expose webSocketStore for access to currentUserId
  };
}
