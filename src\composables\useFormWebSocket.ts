import { onMounted, onUnmounted, watch, type Ref } from 'vue';
import { useWebSocketStore } from '@/stores/websocket';
import { useAuthStore } from '@/stores/auth';

interface UseFormWebSocketOptions {
  formId: Ref<number | null>;
  autoConnect?: boolean;
  enableNotifications?: boolean;
}

export function useFormWebSocket(options: UseFormWebSocketOptions) {
  const { formId, autoConnect = true, enableNotifications = true } = options;

  const webSocketStore = useWebSocketStore();
  const authStore = useAuthStore();

  // Connect to WebSocket when form ID is available
  const connectToForm = async () => {
    if (!formId.value || !authStore.user?.user_id || !authStore.token?.access_token) {
      return;
    }

    try {
      await webSocketStore.connect(
        formId.value,
        authStore.user.user_id.toString(),
        authStore.token.access_token
      );

      if (enableNotifications) {
        webSocketStore.showNotifications = true;
      }
    } catch (error) {
      console.error('Failed to connect to WebSocket:', error);
    }
  };

  // Disconnect from WebSocket
  const disconnect = () => {
    webSocketStore.disconnect();
  };

  // Handle form field updates
  const updateField = (fieldName: string, value: any) => {
    if (!webSocketStore.isConnected) {
      console.warn('WebSocket not connected, cannot send field update');
      return false;
    }

    // Check if we can edit this field
    if (!webSocketStore.canEditField(fieldName)) {
      console.warn(`Cannot edit field "${fieldName}" - locked by another user`);
      return false;
    }

    return webSocketStore.updateFormField(fieldName, value);
  };

  // Lock a field for editing
  const lockField = (fieldName: string) => {
    return webSocketStore.lockField(fieldName);
  };

  // Unlock a field
  const unlockField = (fieldName: string) => {
    return webSocketStore.unlockField(fieldName);
  };

  // Check if field can be edited
  const canEditField = (fieldName: string) => {
    return webSocketStore.canEditField(fieldName);
  };

  // Get field lock information
  const getFieldLockInfo = (fieldName: string) => {
    return webSocketStore.getFieldLockInfo(fieldName);
  };

  // Request access to a locked field
  const requestFieldAccess = (fieldName: string, message?: string) => {
    return webSocketStore.requestFieldAccess(fieldName, message);
  };

  // Auto-connect when form ID changes
  if (autoConnect) {
    watch(
      () => formId.value,
      (newFormId) => {
        if (newFormId) {
          connectToForm();
        } else {
          disconnect();
        }
      },
      { immediate: true }
    );

    // Watch for auth changes (login/logout/user change)
    watch(
      () => [authStore.user?.user_id, authStore.token?.access_token],
      ([userId, token], [prevUserId, prevToken]) => {
        // Handle logout
        if (!userId || !token) {
          console.log('Auth lost, disconnecting WebSocket');
          disconnect();
          return;
        }

        // Handle login or user change
        if (userId !== prevUserId || token !== prevToken) {
          console.log('Auth changed, reconnecting WebSocket');
          if (formId.value) {
            connectToForm();
          }
        }
      },
      { deep: true }
    );
  }

  // Cleanup on unmount
  onUnmounted(() => {
    webSocketStore.cleanup();
  });

  // Handle page visibility changes (disconnect when tab is hidden)
  const handleVisibilityChange = () => {
    if (document.hidden) {
      // Page is hidden, disconnect to free up resources
      disconnect();
    } else if (formId.value && autoConnect) {
      // Page is visible again, reconnect
      connectToForm();
    }
  };

  onMounted(() => {
    document.addEventListener('visibilitychange', handleVisibilityChange);
  });

  onUnmounted(() => {
    document.removeEventListener('visibilitychange', handleVisibilityChange);
  });

  return {
    // Connection management
    connect: connectToForm,
    disconnect,

    // Field operations
    updateField,
    lockField,
    unlockField,
    canEditField,
    getFieldLockInfo,
    requestFieldAccess,

    // Store access
    webSocketStore,

    // Computed properties
    isConnected: () => webSocketStore.isConnected,
    activeUsers: () => webSocketStore.activeUsers,
    connectionStatus: () => webSocketStore.status,
    lockedFields: () => webSocketStore.lockedFields,
    myLockedFields: () => webSocketStore.myLockedFields,
    pendingAccessRequests: () => webSocketStore.pendingAccessRequests
  };
}

// Helper function for debounced field updates
export function useDebouncedFieldUpdate(
  updateFn: (fieldName: string, value: any) => boolean,
  delay: number = 500
) {
  const timeouts = new Map<string, number>();

  const debouncedUpdate = (fieldName: string, value: any) => {
    // Clear existing timeout for this field
    const existingTimeout = timeouts.get(fieldName);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    // Set new timeout
    const timeout = window.setTimeout(() => {
      updateFn(fieldName, value);
      timeouts.delete(fieldName);
    }, delay);

    timeouts.set(fieldName, timeout);
  };

  const clearAllTimeouts = () => {
    timeouts.forEach((timeout) => window.clearTimeout(timeout));
    timeouts.clear();
  };

  onUnmounted(() => {
    clearAllTimeouts();
  });

  return {
    debouncedUpdate,
    clearAllTimeouts
  };
}
