// Real-time field synchronization composable
import { ref, computed, watch, onUnmounted } from 'vue';
import { useWebSocketStore } from '@/stores/websocket';
import { translateFieldName } from '@/utils/fieldNameTranslations';

export interface RealTimeFieldOptions {
  fieldName: string;
  enabled?: boolean;
  showTypingIndicators?: boolean;
  syncDelay?: number; // Delay in ms before sending updates
}

/**
 * Composable for real-time field synchronization
 * Handles sending and receiving live updates for form fields
 */
export function useRealTimeFieldSync(options: RealTimeFieldOptions) {
  const webSocketStore = useWebSocketStore();

  const { fieldName, enabled = true, showTypingIndicators = true, syncDelay = 300 } = options;

  // Local state
  const isTyping = ref(false);
  const lastUpdateTime = ref(0);
  const typingTimeout = ref<number | null>(null);
  const syncTimeout = ref<number | null>(null);

  // Computed properties
  const isConnected = computed(() => webSocketStore.isConnected);

  const remoteUpdate = computed(() => webSocketStore.fieldUpdates[fieldName]);

  const typingIndicator = computed(() => {
    const indicator = webSocketStore.typingIndicators[fieldName];
    return indicator
      ? {
          userId: indicator.userId,
          timestamp: indicator.timestamp,
          isActive: Date.now() - indicator.timestamp < 3000
        }
      : null;
  });

  const translatedFieldName = computed(() => translateFieldName(fieldName));

  // Methods
  function sendRealTimeUpdate(value: any) {
    if (!enabled || !isConnected.value) return;

    // Clear any pending sync
    if (syncTimeout.value) {
      window.clearTimeout(syncTimeout.value);
    }

    // Debounce the update
    syncTimeout.value = window.setTimeout(() => {
      webSocketStore.sendRealTimeUpdate(fieldName, value);
      lastUpdateTime.value = Date.now();
    }, syncDelay);
  }

  function sendLiveTyping(value: any) {
    if (!enabled || !isConnected.value || !showTypingIndicators) return;

    // Send live typing update
    webSocketStore.sendLiveTyping(fieldName, value);

    // Set typing state
    isTyping.value = true;

    // Clear existing timeout
    if (typingTimeout.value) {
      window.clearTimeout(typingTimeout.value);
    }

    // Auto-clear typing state after 1 second of inactivity
    typingTimeout.value = window.setTimeout(() => {
      isTyping.value = false;
    }, 1000);
  }

  function handleFieldInput(value: any) {
    // Send live typing for immediate feedback
    sendLiveTyping(value);

    // Send real-time update with debounce
    sendRealTimeUpdate(value);
  }

  function handleFieldChange(value: any) {
    // Send immediate update on field change (blur, etc.)
    if (enabled && isConnected.value) {
      webSocketStore.sendRealTimeUpdate(fieldName, value);
    }
  }

  // Watch for remote updates
  watch(remoteUpdate, (newUpdate) => {
    if (newUpdate && newUpdate.timestamp > lastUpdateTime.value) {
      console.log(`Remote update for ${fieldName}:`, newUpdate);
      // Components can watch this to update their local values
    }
  });

  // Cleanup
  onUnmounted(() => {
    if (typingTimeout.value) {
      window.clearTimeout(typingTimeout.value);
    }
    if (syncTimeout.value) {
      window.clearTimeout(syncTimeout.value);
    }
  });

  return {
    // State
    isTyping: computed(() => isTyping.value),
    isConnected,
    remoteUpdate,
    typingIndicator,
    translatedFieldName,

    // Methods
    sendRealTimeUpdate,
    sendLiveTyping,
    handleFieldInput,
    handleFieldChange,

    // Utilities
    isRemoteUpdateNewer: (timestamp: number) => timestamp > lastUpdateTime.value
  };
}

/**
 * Simplified composable for basic real-time sync
 * Just handles input/change events automatically
 */
export function useSimpleRealTimeSync(fieldName: string, enabled = true) {
  const sync = useRealTimeFieldSync({
    fieldName,
    enabled,
    showTypingIndicators: true,
    syncDelay: 300
  });

  // Return simplified interface
  return {
    isConnected: sync.isConnected,
    remoteUpdate: sync.remoteUpdate,
    typingIndicator: sync.typingIndicator,

    // Event handlers for v-model
    onInput: sync.handleFieldInput,
    onChange: sync.handleFieldChange
  };
}
