// Centralized user color system for WebSocket collaboration
import { computed } from 'vue';

// Extended color palette with 20 distinct colors for better user differentiation
const USER_COLOR_PALETTE = [
  // Primary colors
  '#1976d2', // Blue
  '#388e3c', // Green
  '#f57c00', // Orange
  '#7b1fa2', // Purple
  '#c2185b', // Pink

  // Secondary colors
  '#00796b', // Teal
  '#5d4037', // <PERSON>
  '#455a64', // Blue Grey
  '#e91e63', // Deep Pink
  '#9c27b0', // Deep Purple

  // Tertiary colors
  '#ff5722', // Deep Orange
  '#607d8b', // Blue Grey
  '#795548', // <PERSON>
  '#009688', // Teal
  '#673ab7', // Deep Purple

  // Additional colors
  '#3f51b5', // Indigo
  '#2196f3', // Light Blue
  '#4caf50', // Light Green
  '#ff9800', // Amber
  '#e91e63', // Pink

  // Extra colors for 20+ users
  '#8bc34a', // Light Green
  '#cddc39', // Lime
  '#ffeb3b', // Yellow
  '#ffc107', // Amber
  '#ff5722' // Deep Orange
];

/**
 * Generates a consistent color for a user based on their ID
 * Colors cycle through the palette if there are more users than colors
 * @param userId - The user ID to generate color for
 * @returns Hex color string
 */
export function getUserColor(userId: string): string {
  // Generate consistent hash from user ID
  let hash = 0;
  for (let i = 0; i < userId.length; i++) {
    hash = userId.charCodeAt(i) + ((hash << 5) - hash);
  }

  // Use modulo to cycle through colors
  const index = Math.abs(hash) % USER_COLOR_PALETTE.length;
  return USER_COLOR_PALETTE[index];
}

/**
 * Gets user initials for avatar display
 * @param name - User name or ID
 * @returns 1-2 character initials
 */
export function getUserInitials(name: string): string {
  if (!name) return '?';

  // If it's a user ID (number), just take first 2 characters
  if (/^\d+$/.test(name)) {
    return name.substring(0, 2);
  }

  // If it's a name, take initials
  const parts = name.split(' ');
  if (parts.length >= 2) {
    return (parts[0][0] + parts[1][0]).toUpperCase();
  }
  return name.substring(0, 2).toUpperCase();
}

/**
 * Composable for user color management
 * @returns Object with color utilities
 */
export function useUserColors() {
  const getColorForUser = (userId: string) => getUserColor(userId);
  const getInitialsForUser = (name: string) => getUserInitials(name);

  // Get all available colors (useful for testing/debugging)
  const availableColors = computed(() => [...USER_COLOR_PALETTE]);

  // Get total number of unique colors
  const colorCount = computed(() => USER_COLOR_PALETTE.length);

  return {
    getUserColor: getColorForUser,
    getUserInitials: getInitialsForUser,
    availableColors,
    colorCount
  };
}

/**
 * Performance optimization: Memoized color cache
 * Stores computed colors to avoid recalculation
 */
const colorCache = new Map<string, string>();

/**
 * Optimized color getter with caching for better performance
 * @param userId - User ID
 * @returns Cached or newly computed color
 */
export function getCachedUserColor(userId: string): string {
  if (colorCache.has(userId)) {
    return colorCache.get(userId)!;
  }

  const color = getUserColor(userId);
  colorCache.set(userId, color);
  return color;
}

/**
 * Clear color cache (useful for testing or memory management)
 */
export function clearColorCache(): void {
  colorCache.clear();
}
