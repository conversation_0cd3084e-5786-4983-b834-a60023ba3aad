import { computed, onMounted } from 'vue';
import { useUsersStore } from '@/stores/users';

/**
 * Composable for fetching and managing user names
 * Provides functions to get user names and initials from user IDs
 */
export function useUserNames() {
  const usersStore = useUsersStore();

  // Load all users on first use
  const loadUsers = async () => {
    if (usersStore.allUsers.length === 0) {
      await usersStore.getAll();
    }
  };

  /**
   * Get user's full name from user ID
   * @param userId - User ID as string or number
   * @returns Full name "First Last" or user ID as fallback
   */
  const getUserName = (userId: string | number): string => {
    const user = usersStore.allUsers.find((u) => u.user_id.toString() === userId.toString());
    if (user) {
      return `${user.first_name} ${user.last_name}`;
    }
    return userId.toString(); // Fallback to user ID if not found
  };

  /**
   * Get user's initials in format "F. Lastname"
   * @param userId - User ID as string or number
   * @returns Initials "F. Lastname" or user ID as fallback
   */
  const getUserInitials = (userId: string | number): string => {
    const user = usersStore.allUsers.find((u) => u.user_id.toString() === userId.toString());
    if (user) {
      return `${user.first_name.charAt(0)}. ${user.last_name}`;
    }
    return userId.toString(); // Fallback to user ID if not found
  };

  /**
   * Get user's short initials (just letters)
   * @param userId - User ID as string or number
   * @returns Short initials "FL" or user ID as fallback
   */
  const getUserShortInitials = (userId: string | number): string => {
    const user = usersStore.allUsers.find((u) => u.user_id.toString() === userId.toString());
    if (user) {
      return `${user.first_name.charAt(0)}${user.last_name.charAt(0)}`;
    }
    return userId.toString().substring(0, 2).toUpperCase(); // Fallback to first 2 chars of ID
  };

  /**
   * Check if users are loaded
   */
  const usersLoaded = computed(() => usersStore.allUsers.length > 0);

  return {
    loadUsers,
    getUserName,
    getUserInitials,
    getUserShortInitials,
    usersLoaded
  };
}
