import { ref } from 'vue';

export type ConfigProps = {
  Sidebar_drawer: boolean;
  Customizer_drawer: boolean;
  mini_sidebar: boolean;
  setHorizontalLayout: boolean;
  theme: string;
  fontTheme: string;
  inputBg: boolean;
  boxed: boolean;
};

const config: ConfigProps = {
  Sidebar_drawer: true,
  Customizer_drawer: false,
  mini_sidebar: false,
  setHorizontalLayout: false,
  theme: 'LightTheme6', // DarkTheme6 || LightTheme6
  fontTheme: 'Roboto',
  inputBg: false,
  boxed: false
};

export const themeColor = ref('rgb(var(--v-theme-secondary))');
export const responsiveCardClass = 'pa-3 pa-sm-3 pa-md-5';
export default config;
