<template>
  <v-app
    :theme="customizer.theme"
    :class="[
      customizer.theme,
      customizer.fontTheme,
      customizer.mini_sidebar ? 'mini-sidebar' : '',
      customizer.setHorizontalLayout ? 'horizontalLayout' : 'verticalLayout',
      customizer.inputBg ? 'inputWithbg' : ''
    ]"
  >
    <LoaderWrapper />
    <RouterView />
  </v-app>
</template>
<script setup lang="ts">
  import { RouterView } from 'vue-router';
  import { useCustomizerStore } from '../../stores/customizer';
  import LoaderWrapper from '../dashboard/LoaderWrapper.vue';
  const customizer = useCustomizerStore();
</script>
