<script setup lang="ts">
  import { RouterView } from 'vue-router';
  import { useCustomizerStore } from '../../stores/customizer';
  import LoaderWrapper from './LoaderWrapper.vue';
  import FooterPanel from './footer/FooterPanel.vue';
  import VerticalHeaderVue from './vertical-header/VerticalHeader.vue';
  import VerticalSidebarVue from './vertical-sidebar/VerticalSidebar.vue';
  const customizer = useCustomizerStore();
</script>

<template>
  <!-- The v-locale-provider component provides localization support for the application - feasters -->
  <!-- <v-locale-provider> -->
  <!-- The v-app component is the root of the Vuetify application, with dynamic theming and class binding based on the customizer object -->
  <v-app
    :theme="customizer.theme"
    :class="[
      customizer.theme,
      customizer.fontTheme,
      customizer.mini_sidebar ? 'mini-sidebar' : '',
      customizer.setHorizontalLayout ? 'horizontalLayout' : 'verticalLayout',
      customizer.inputBg ? 'inputWithbg' : ''
    ]"
  >
    <!-- VerticalSidebarVue component represents the vertical sidebar of the dashboard -->
    <VerticalSidebarVue />
    <!-- VerticalHeaderVue component represents the header of the dashboard -->
    <VerticalHeaderVue />

    <!-- v-main component is the main content area of the application -->
    <v-main class="page-wrapper pageSizeMain">
      <!-- v-container component is used to contain the main page content with padding -->
      <v-container class="pageSizeTop pa-2 pa-md-5">
        <!-- LoaderWrapper component is used to show a loading indicator -->
        <LoaderWrapper />
        <!-- RouterView component is used to render the matched component for the given route -->
        <RouterView />
      </v-container>
      <!-- Another v-container component for additional content or footer with no top padding -->
      <v-container class="pt-0">
        <!-- FooterPanel component represents the footer of the dashboard -->
        <FooterPanel />
      </v-container>
    </v-main>
  </v-app>
  <!-- </v-locale-provider> -->
</template>
