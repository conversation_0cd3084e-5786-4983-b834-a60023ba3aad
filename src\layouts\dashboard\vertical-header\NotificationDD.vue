<script setup lang="ts">
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import { FormType, useNotificationsStore, type NotificationI } from '@/stores/notifications';
  import { toLocale } from '@/utils/locales';
  import {
    BellOutlined,
    CheckCircleOutlined,
    MessageOutlined,
    DeleteOutlined
  } from '@ant-design/icons-vue';
  import { storeToRefs } from 'pinia';
  import { computed, onMounted, ref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { notification as notificationToast } from 'ant-design-vue';
  const router = useRouter();
  const ConfirmRef = ref<undefined | typeof ConfirmDlg>(undefined);

  const notificationsStore = useNotificationsStore();
  const { notifications, unreadNotifications } = storeToRefs(notificationsStore);

  const isActive = computed(() => unreadNotifications.value.length > 0);

  const deleteSingleNotification = async (notification_id: number) => {
    const res = await notificationsStore.deleteNotification(notification_id);
    if (res) {
      await notificationsStore.getNotifications();
    }
  };

  const showNotificationInfo = async (notification: NotificationI) => {
    if (!notification) return;
    if (notification.notification_type === 'NEW_ANALYTICAL_REQUEST') {
      if (notification.notification_data && notification.notification_data.sample_id) {
        if (notification.notification_status === 'unread') {
          await notificationsStore.markNotificationAsRead(notification.notification_id);
        }
        const go = router.resolve({
          name: 'SampleEdit',
          params: { sample_id: notification.notification_data.sample_id, type: 'qc-a-vt' }
        });
        router.push(go);
      }
    } else if (notification.notification_type === 'NEW_RESULT') {
      if (notification.notification_data && notification.notification_data.sample_id) {
        if (notification.notification_status === 'unread') {
          await notificationsStore.markNotificationAsRead(notification.notification_id);
        }
        const go = router.resolve({
          name: 'SampleEdit',
          params: { sample_id: notification.notification_data.sample_id, type: 'rd' }
        });
        router.push(go);
      }
    } else if (notification.notification_type === 'REOPEN_FORM_REQUEST') {
      if (
        notification.notification_data &&
        notification.notification_data.form_id &&
        notification.notification_data.chapter_id &&
        notification.notification_data.project_department_id &&
        notification.notification_data.project_id &&
        notification.notification_data.form_type
      ) {
        if (notification.notification_status === 'unread') {
          await notificationsStore.markNotificationAsRead(notification.notification_id);
        }
        let goWhere = '';
        if (notification.notification_data.form_type === FormType.ATTEMPT) {
          goWhere = 'Attempt';
        } else if (notification.notification_data.form_type === FormType.EXPERIMENT) {
          goWhere = 'Experiment';
        } else if (notification.notification_data.form_type === FormType.MESSAGE) {
          goWhere = 'Message';
        } else if (notification.notification_data.form_type === FormType.INVESTIGATION) {
          goWhere = 'Investigation';
        }
        if (goWhere !== '') {
          const go = router.resolve({
            name: goWhere,
            params: {
              project_id: notification.notification_data.project_id,
              project_department_id: notification.notification_data.project_department_id,
              chapter_id: notification.notification_data.chapter_id,
              form_id: notification.notification_data.form_id
            }
          });
          router.push(go);
        }
      }
    }
  };

  onMounted(() => {
    notificationsStore.getNotifications();
  });
</script>

<template>
  <!-- ---------------------------------------------- -->
  <!-- notifications DD -->
  <!-- ---------------------------------------------- -->
  <v-menu :key="notificationsStore.forceRefreshKey" :close-on-content-click="false" offset="6, 0">
    <template #activator="{ props }">
      <v-btn
        icon
        class="text-secondary ml-sm-2 ml-1"
        color="darkText"
        rounded="sm"
        size="small"
        v-bind="props"
      >
        <v-badge
          v-if="isActive"
          :content="unreadNotifications.length > 0 ? unreadNotifications.length : '0'"
          color="primary"
          offset-x="-4"
          offset-y="-5"
        >
          <BellOutlined :style="{ fontSize: '16px' }" />
        </v-badge>
        <BellOutlined v-else :style="{ fontSize: '16px' }" />
      </v-btn>
    </template>
    <v-sheet rounded="md" width="387" class="notification-dropdown">
      <div class="pa-4">
        <div class="d-flex align-center justify-space-between">
          <h6 class="text-subtitle-1 mb-0">Upozornění</h6>
          <div class="d-flex align-center">
            <v-btn
              variant="text"
              color="success"
              icon
              rounded="sm"
              size="small"
              :class="isActive ? 'd-block' : 'd-none'"
              @click.prevent.stop="notificationsStore.markAllNotificationsAsRead()"
            >
              <CheckCircleOutlined :style="{ fontSize: '16px' }" />
              <v-tooltip
                aria-label="tooltip"
                activator="parent"
                location="bottom"
                :content-class="isActive ? 'custom-tooltip' : 'd-none'"
              >
                <span class="text-caption">Označit vše za přečtené</span>
              </v-tooltip>
            </v-btn>
            <v-btn
              variant="text"
              color="error"
              icon
              rounded="sm"
              size="small"
              @click.prevent.stop="
                async () =>
                  (await ConfirmRef?.open('Opravdu chcete smazat všechny notifikace?', '', {
                    color: 'info',
                    notclosable: true,
                    zIndex: 2400
                  }))
                    ? notificationsStore.removeAllNotifications()
                    : null
              "
            >
              <DeleteOutlined :style="{ fontSize: '16px' }" />
              <v-tooltip
                aria-label="tooltip"
                activator="parent"
                location="bottom"
                :content-class="isActive ? 'custom-tooltip' : 'd-none'"
              >
                <span class="text-caption">Smazat všechny notifikace</span>
              </v-tooltip>
            </v-btn>
          </div>
        </div>
      </div>

      <v-divider></v-divider>
      <!-- style="height: calc(100vh - 300px)" max-height: 132px -->
      <perfect-scrollbar>
        <v-list class="py-0" lines="two" aria-label="notification list" aria-busy="true">
          <template v-for="notification in notifications" :key="notification.notification_id">
            <v-list-item
              :value="notification.notification_id"
              color="secondary"
              class="no-spacer py-1"
              :active="notification.isActive"
              @click.prevent.stop="showNotificationInfo(notification)"
            >
              <template #prepend>
                <v-btn
                  icon
                  color="success"
                  variant="tonal"
                  rounded="circle"
                  @click.prevent.stop="
                    async () =>
                      notification.isActive
                        ? notificationsStore.markNotificationAsRead(notification.notification_id)
                        : null
                  "
                >
                  <CheckCircleOutlined />
                </v-btn>
              </template>
              <template #append>
                <v-btn
                  icon
                  color="error"
                  variant="tonal"
                  rounded="circle"
                  @click.prevent.stop="
                    async () =>
                      (await ConfirmRef?.open('Opravdu chcete smazat notifikaci?', '', {
                        color: 'info',
                        notclosable: true,
                        zIndex: 2400
                      }))
                        ? deleteSingleNotification(notification.notification_id)
                        : null
                  "
                >
                  <DeleteOutlined />
                </v-btn>
              </template>
              <div class="d-inline-flex justify-space-between w-100" style="margin-left: 10px">
                <h6
                  v-if="notification.notification_data?.batch_number"
                  class="text-subtitle-1 font-weight-regular mb-0"
                >
                  {{ notification.notification_data?.batch_number }}
                </h6>
                <h6 v-else class="text-subtitle-1 font-weight-regular mb-0">
                  {{ notification.notification_id }}
                </h6>

                <v-badge
                  v-if="notification.notification_status === 'unread'"
                  color="error"
                  offset-x="20"
                  offset-y="5"
                  dot
                  :dot-size="12"
                ></v-badge>
                <span class="text-caption text-medium-emphasis my-0" style="margin-right: 20px">
                  {{ toLocale(notification.notification_date) }}
                </span>
              </div>
              <p
                v-if="notification.notification_data?.analytical_request_name"
                class="text-caption text-medium-emphasis my-0"
                style="margin-left: 10px"
              >
                Název analytického požadavku:
                {{ notification.notification_data?.analytical_request_name }}
              </p>
              <p
                v-if="notification.notification_data?.requested_by"
                class="text-caption text-medium-emphasis my-0"
                style="margin-left: 10px"
              >
                Otevření bylo vyžádáno: {{ notification.notification_data?.requested_by.user_name }}
              </p>

              <p class="text-caption text-medium-emphasis my-0" style="margin-left: 10px">
                {{ notification.notification_content }}
              </p>
            </v-list-item>

            <v-divider></v-divider>
          </template>
        </v-list>
      </perfect-scrollbar>

      <v-divider></v-divider>
    </v-sheet>

    <ConfirmDlg ref="ConfirmRef" />
  </v-menu>
</template>

<style lang="scss">
  .v-tooltip {
    > .v-overlay__content.custom-tooltip {
      padding: 2px 6px;
    }
  }
</style>
