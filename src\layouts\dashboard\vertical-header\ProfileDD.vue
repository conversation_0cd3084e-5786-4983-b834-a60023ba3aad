<script setup lang="ts">
  import { useAuthStore } from '@/stores/auth';
  import { EditOutlined, LogoutOutlined } from '@ant-design/icons-vue';
  import { storeToRefs } from 'pinia';

  const authStore = useAuthStore();
  const { user } = storeToRefs(authStore);
</script>

<template>
  <!-- ---------------------------------------------- -->
  <!-- profile DD -->
  <!-- ---------------------------------------------- -->
  <div v-if="user">
    <div class="d-flex align-center pa-5">
      <v-avatar size="32" class="mr-2">
        <img src="@/assets/images/users/avatar-1.png" width="32" alt="Julia" />
      </v-avatar>
      <div>
        <h6 class="text-h6 mb-0">{{ user.first_name + ' ' + user.last_name }}</h6>
        <p class="text-caption mb-0">{{ user.user_email }}</p>
      </div>
      <div class="ml-auto">
        <v-btn
          variant="text"
          color="primary"
          rounded="sm"
          icon
          size="large"
          @click="authStore.logout()"
        >
          <LogoutOutlined :style="{ fontSize: '20px' }" />
        </v-btn>
      </div>
    </div>
    <v-list class="py-0" aria-label="profile list" aria-busy="true">
      <v-list-item
        color="primary"
        rounded="sm"
        value="Editovat profil"
        :to="{ name: 'MySettings' }"
      >
        <template #prepend>
          <EditOutlined :style="{ fontSize: '14px' }" class="mr-4" />
        </template>

        <v-list-item-title class="text-h6">Můj profil</v-list-item-title>
      </v-list-item>
      <v-list-item color="secondary" rounded="sm" @click="authStore.logout()">
        <template #prepend>
          <LogoutOutlined :style="{ fontSize: '14px' }" class="mr-4" />
        </template>

        <v-list-item-title class="text-subtitle-2">Odhlásit</v-list-item-title>
      </v-list-item>
    </v-list>
  </div>
</template>
