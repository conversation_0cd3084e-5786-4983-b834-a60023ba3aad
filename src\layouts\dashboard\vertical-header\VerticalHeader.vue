<script setup lang="ts">
  import { useAuthStore } from '@/stores/auth';
  import { useCustomizerStore } from '@/stores/customizer';
  import { MenuFoldOutlined } from '@ant-design/icons-vue';
  import { storeToRefs } from 'pinia';
  import { ref, watch } from 'vue';
  import NotificationDD from './NotificationDD.vue';
  import ProfileDD from './ProfileDD.vue';

  const authStore = useAuthStore();
  const { user } = storeToRefs(authStore);

  const customizer = useCustomizerStore();
  const priority = ref(customizer.setHorizontalLayout ? 0 : 0);
  watch(priority, (newPriority) => {
    priority.value = newPriority;
  });
  const checkUrl = import.meta.env.VITE_HOST;
</script>

<template>
  <v-app-bar elevation="0" :priority="priority" height="60">
    <!-- ---------------------------------------------- -->
    <!-- left part -->
    <!-- ---------------------------------------------- -->

    <!-- ---------------------------------------------- -->
    <!-- sidebar opener -->
    <!-- ---------------------------------------------- -->
    <v-btn
      class="hidden-md-and-down text-secondary mr-3"
      color="darkText"
      icon
      rounded="sm"
      variant="text"
      size="small"
      @click.stop="customizer.SET_MINI_SIDEBAR(!customizer.mini_sidebar)"
    >
      <MenuFoldOutlined :style="{ fontSize: '16px' }" />
    </v-btn>
    <v-btn
      class="hidden-lg-and-up text-secondary ms-3"
      color="darkText"
      icon
      rounded="sm"
      variant="text"
      size="small"
      @click.stop="customizer.SET_SIDEBAR_DRAWER"
    >
      <MenuFoldOutlined :style="{ fontSize: '16px' }" />
    </v-btn>

    <template
      v-if="checkUrl === 'https://auth-test.quantify.sk' || checkUrl === 'https://auth.quantify.sk'"
    >
      <v-spacer />
      <v-app-bar-title>
        <h4 class="text-h3 mt-1 mb-0">Testovací server</h4>
      </v-app-bar-title>
    </template>
    <v-spacer />

    <!-- ---------------------------------------------- -->
    <!-- right part -->
    <!-- ---------------------------------------------- -->

    <!-- ---------------------------------------------- -->
    <!-- translate -->
    <!-- ---------------------------------------------- -->

    <!-- <v-menu :close-on-content-click="false" location="bottom" offset="6, 80">
      <template v-slot:activator="{ props }">
        <v-btn icon class="ml-sm-2 ml-1" color="darkText" rounded="sm" size="small" v-bind="props">
          <TranslationOutlined :style="{ fontSize: '16px' }" />
        </v-btn>
      </template>
      <v-sheet rounded="md" width="200">
        <LanguageDD />
      </v-sheet>
    </v-menu> -->

    <!-- ---------------------------------------------- -->
    <!-- Notification -->
    <!-- ---------------------------------------------- -->

    <NotificationDD />

    <!-- ---------------------------------------------- -->
    <!-- Fullscreen -->
    <!-- ---------------------------------------------- -->

    <!-- <FullScreen /> -->

    <!-- ---------------------------------------------- -->
    <!-- User Profile -->
    <!-- ---------------------------------------------- -->
    <v-menu v-if="user" :close-on-content-click="false" offset="8, 0">
      <template #activator="{ props }">
        <v-btn class="profileBtn" variant="text" rounded="sm" v-bind="props">
          <div class="d-flex align-center">
            <v-avatar class="mr-sm-2 mr-0 py-2">
              <img src="@/assets/images/users/avatar-1.png" :alt="user.getName" />
            </v-avatar>
            <h6 class="text-subtitle-1 mb-0 d-sm-block d-none">{{ user.getName }}</h6>
          </div>
        </v-btn>
      </template>
      <v-sheet rounded="md" width="290">
        <!-- ---------------------------------------------- -->
        <!-- User Profile Box -->
        <!-- ---------------------------------------------- -->
        <ProfileDD />
      </v-sheet>
    </v-menu>
  </v-app-bar>
</template>
