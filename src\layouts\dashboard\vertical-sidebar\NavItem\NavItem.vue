<script setup>
  const props = defineProps({ item: Object, level: Number });
</script>

<template>
  <!---Single Item-->
  <v-list-item
    :to="item.type === 'external' ? '' : item.to"
    :href="item.type === 'external' ? item.to : ''"
    rounded="sm"
    class="mb-1"
    color="primary"
    :disabled="item.disabled"
  >
    <!-- ---------------------------------------------- -->
    <!-- right part -->
    <!-- ---------------------------------------------- -->

    <!---If external link-->
    <!-- :target="item.type === 'external' ? '_blank' : ''" -->

    <!---If icon-->
    <template #prepend>
      <component :is="props.item.icon" class="iconClass" :level="props.level"></component>
    </template>

    <!---
      /**
      * Component: NavItem
      * Filepath: /d:/FarMak/src/layouts/dashboard/vertical-sidebar/NavItem/NavItem.vue
      * 
      * Description:
      * This component represents a navigation item in the vertical sidebar of the dashboard layout.
      * It is used to display a single item in the navigation menu.
      * 
      * Example:
      * /* $t('MainMenu.' + item.title) */ -> example for enable translation features
      */
    -->
    <v-list-item-title>{{ item.title }}</v-list-item-title>
    <!---If Caption-->
    <v-list-item-subtitle v-if="item.subCaption" class="text-caption mt-n1 hide-menu">
      {{ item.subCaption }}
    </v-list-item-subtitle>
    <!---If any chip or label-->
    <template v-if="item.chip" #append>
      <v-chip
        label
        :color="item.chipColor"
        class="sidebarchip hide-menu"
        size="small"
        :variant="item.chipVariant"
        :prepend-icon="item.chipIcon"
      >
        {{ item.chip }}
      </v-chip>
    </template>
  </v-list-item>
</template>
