<script setup lang="ts">
  import { useAuthStore } from '@/stores/auth';
  import { FormOutlined } from '@ant-design/icons-vue';
  import { storeToRefs } from 'pinia';
  import { computed, onBeforeUnmount, onMounted, ref } from 'vue';
  import { useCustomizerStore } from '../../../stores/customizer';
  import Logo from '../logo/LogoMain.vue';
  import NavCollapse from './NavCollapse/NavCollapse.vue';
  import NavGroup from './NavGroup/NavGroup.vue';
  import NavItem from './NavItem/NavItem.vue';
  import sidebarItems, { type menu } from './sidebarItem';
  import { isAllowedAny } from '@/utils/directive/isAllowed';
  const authStore = useAuthStore();
  const { user } = storeToRefs(authStore);
  const customizer = useCustomizerStore();

  const customUserSidebarItems = ref<menu[]>([]);
  const windowWidth = ref(window.innerWidth);
  const isMobile = computed(() => {
    return windowWidth.value <= 960;
  });

  function updateWindowWidth() {
    windowWidth.value = window.innerWidth;
    if (isMobile.value) {
      customizer.Sidebar_drawer = false;
    }
  }

  /**
   * @file VerticalSidebar.vue
   * @description This file represents the vertical sidebar layout component for the dashboard.
   *              It contains the sidebar menu with custom links added on mounted.
   */

  onMounted(() => {
    updateWindowWidth();
    window.addEventListener('resize', updateWindowWidth);

    const _sidebarItems = [...sidebarItems];
    const permissionMapping: { [key: string]: string[] } = {
      'Vzorky RD': ['view_rd_samples', 'fill_rd_analyses'],
      'Vzorky QC a VT': ['view_qc_vt_samples', 'fill_qc_vt_analyses'],
      'Analytické přístroje': ['view_analytical_instruments', 'add_edit_analytical_instruments'],
      'Seznam kolon': ['view_columns', 'add_edit_columns'],
      'Správa uživatelů': ['add_edit_users', 'view_users'],
      'Správa standardů': ['add_edit_standards', 'view_standards'],
      'Správa analytických technik': ['view_techniques', 'add_edit_techniques'],
      'Správa externích technik': ['view_techniques', 'add_edit_techniques']
    };

    const filteredSidebarItems = _sidebarItems.filter((item) => {
      if (item.divider || item.header) {
        return true;
      }
      if (item && item.title) {
        if (permissionMapping[item.title]) {
          return isAllowedAny(permissionMapping[item.title]);
        }
        return true;
      }
    });

    if (user.value && user.value.shortcuts && user.value.shortcuts.length > 0) {
      filteredSidebarItems.push({ divider: true });
      filteredSidebarItems.push({ header: 'Moje odkazy' });
      user.value.shortcuts
        .sort((a, b) => a.order - b.order)
        .forEach((shortcut) => {
          filteredSidebarItems.push({
            title: shortcut.name,
            icon: FormOutlined,
            to: shortcut.url_path,
            type: 'external'
          });
        });
    }
    customUserSidebarItems.value = filteredSidebarItems;
  });

  onBeforeUnmount(() => {
    window.removeEventListener('resize', updateWindowWidth);
  });
</script>

<template>
  <v-navigation-drawer
    v-model="customizer.Sidebar_drawer"
    left
    elevation="0"
    rail-width="60"
    mobile-breakpoint="lg"
    app
    class="leftSidebar"
    :rail="isMobile ? false : customizer.mini_sidebar"
    :expand-on-hover="isMobile ? false : true"
  >
    <!---Logo part -->
    <div class="pa-5">
      <Logo />
    </div>

    <!-- ---------------------------------------------- -->
    <!---Navigation -->
    <!-- ---------------------------------------------- -->

    <v-list class="scrollnavbar" aria-busy="true" aria-label="menu list">
      <!---Menu Loop -->
      <template v-for="(item, i) in customUserSidebarItems" :key="i">
        <!---Item Sub Header -->
        <NavGroup v-if="item.header" :key="item.title" :item="item" />
        <!---Item Divider -->
        <v-divider v-else-if="item.divider" class="my-3" />
        <!---If Has Child -->
        <NavCollapse v-else-if="item.children" class="leftPadding" :item="item" :level="0" />
        <!---Single Item-->
        <NavItem v-else :item="item" />
        <!---End Single Item-->
      </template>
    </v-list>
  </v-navigation-drawer>
</template>
