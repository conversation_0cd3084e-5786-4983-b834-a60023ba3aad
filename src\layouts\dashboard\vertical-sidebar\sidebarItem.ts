// icons
import {
  ExperimentOutlined,
  ExportOutlined,
  UnorderedListOutlined,
  Dot<PERSON>hartOutlined,
  FormOutlined,
  ProfileOutlined,
  HomeOutlined,
  UserOutlined,
  UserSwitchOutlined
} from '@ant-design/icons-vue';

export interface menu {
  header?: string;
  title?: string;
  icon?: object;
  to?: string;
  divider?: boolean;
  chip?: string;
  chipColor?: string;
  chipVariant?: string;
  chipIcon?: string;
  children?: menu[];
  disabled?: boolean;
  type?: string;
  subCaption?: string;
}

const sidebarItem: menu[] = [
  { header: 'Hlavní menu' },
  {
    title: 'Projekty',
    icon: ProfileOutlined,
    to: '/projekty',
    chipColor: 'error'
  },
  {
    title: 'Vzorky RD',
    icon: DotChartOutlined,
    to: '/vzorky/rd'
  },
  {
    title: 'Vzorky QC a VT',
    icon: DotChartOutlined,
    to: '/vzorky/qc-a-vt'
  },
  {
    title: 'Analytické přístro<PERSON>',
    icon: HomeOutlined,
    to: '/analyticke-pristroje'
  },
  {
    title: 'Seznam kolon',
    icon: FormOutlined,
    to: '/seznam-kolon'
  },
  { divider: true },
  { header: 'Správa aplikace' },
  {
    title: 'Můj profil',
    icon: UserOutlined,
    to: '/nastaveni'
  },
  {
    title: 'Správa uživatelů',
    icon: UserSwitchOutlined,
    to: '/sprava-uzivatelu'
  },
  {
    title: 'Správa standardů',
    icon: UnorderedListOutlined,
    to: '/sprava-standardu'
  },
  {
    title: 'Správa analytických technik',
    icon: ExperimentOutlined,
    to: '/sprava-analytickych-technik'
  },
  {
    title: 'Správa externích technik',
    icon: ExportOutlined,
    to: '/sprava-externich-technik'
  }
];

export default sidebarItem;
