import { createApp } from 'vue';
import { createPinia } from 'pinia';
import App from './App.vue';
import { router } from './router';
import vuetify from './plugins/vuetify';
import VueApexCharts from 'vue3-apexcharts';
import '@/scss/style.scss';
import { PerfectScrollbarPlugin } from 'vue3-perfect-scrollbar';
import Vue3EasyDataTable from 'vue3-easy-data-table';
import VueTablerIcons from 'vue-tabler-icons';
import Vue3Marquee from 'vue3-marquee';
import Ant from 'ant-design-vue';
import 'ant-design-vue/dist/reset.css';
import 'vue3-perfect-scrollbar/style.css';
import 'vue3-easy-data-table/dist/style.css';

// google-fonts
import '@fontsource/roboto/400.css';
import '@fontsource/roboto/500.css';
import '@fontsource/roboto/300.css';
import '@fontsource/roboto/700.css';

//Mock Api data
import './_mockApis';
import { fakeBackend } from '@/utils/helpers/fake-backend';

//i18
import { createI18n } from 'vue-i18n';
import messages from '@/utils/locales/messages';
import isAllowed from './utils/directive/isAllowed';
import { useAuthStore } from './stores/auth';

import './scss/chemicalTable/item.less';

const pinia = createPinia();
const i18n = createI18n({
  legacy: false,
  locale: 'cz',
  messages: messages,
  silentTranslationWarn: true,
  silentFallbackWarn: true
});

const app = createApp(App).use(pinia);

const auth = useAuthStore();
auth.init();

fakeBackend();
app.use(router);
app.component('EasyDataTable', Vue3EasyDataTable);

app.use(PerfectScrollbarPlugin);
app.use(VueTablerIcons);
app.use(Vue3Marquee);
app.use(i18n);
app.use(vuetify);
app.use(Ant);
app.component('ApexChartComponent', VueApexCharts);
app.directive('isAllowed', isAllowed).mount('#app');
