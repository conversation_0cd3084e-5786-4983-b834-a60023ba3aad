import { createVuetify } from 'vuetify';
import '@mdi/font/css/materialdesignicons.css';
import * as components from 'vuetify/components';
import * as directives from 'vuetify/directives';
import { VDateInput } from 'vuetify/labs/VDateInput';
import { VNumberInput } from 'vuetify/labs/VNumberInput';
import { cs, en } from 'vuetify/locale';
import { createVueI18nAdapter } from 'vuetify/locale/adapters/vue-i18n';
import { createI18n, useI18n } from 'vue-i18n';

import {
  DefaultTheme,
  LightTheme1,
  LightTheme2,
  LightTheme3,
  LightTheme4,
  LightTheme5,
  LightTheme6,
  LightTheme7,
  LightTheme8
} from '@/theme/LightTheme';
import {
  DarkDefaultTheme,
  DarkTheme1,
  DarkTheme6,
  DarkTheme7,
  DarkTheme8,
  DarkTheme5,
  DarkTheme2,
  DarkTheme3,
  DarkTheme4
} from '@/theme/DarkTheme';

const messages = {
  en: {
    $vuetify: {
      ...en
    }
  },
  cs: {
    $vuetify: {
      ...cs,
      badge: '<PERSON>na<PERSON><PERSON>'
    }
  }
};

const i18n = createI18n({
  legacy: false, // Vuetify does not support the legacy mode of vue-i18n
  locale: 'cs',
  fallbackLocale: 'en',
  messages,
  silentTranslationWarn: true
});

export default createVuetify({
  locale: {
    adapter: createVueI18nAdapter({ i18n, useI18n })
  },

  components: {
    ...components,
    VDateInput,
    VNumberInput
  },

  directives,

  theme: {
    defaultTheme: 'DefaultTheme',
    themes: {
      DefaultTheme,
      LightTheme1,
      LightTheme2,
      LightTheme3,
      LightTheme4,
      LightTheme5,
      LightTheme6,
      LightTheme7,
      LightTheme8,
      DarkDefaultTheme,
      DarkTheme1,
      DarkTheme8,
      DarkTheme7,
      DarkTheme6,
      DarkTheme5,
      DarkTheme2,
      DarkTheme3,
      DarkTheme4
    }
  },
  defaults: {
    VBtn: {},
    VCard: {
      rounded: 'md'
    },
    VTextField: {
      rounded: 'lg'
    },
    VTooltip: {
      // set v-tooltip default location to top
      location: 'top'
    }
  }
});
