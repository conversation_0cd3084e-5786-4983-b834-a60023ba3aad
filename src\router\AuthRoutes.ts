const AuthRoutes = {
  path: '/auth',
  component: () => import('@/layouts/blank/BlankLayout.vue'),
  meta: {
    requiresAuth: false
  },
  children: [
    {
      name: 'HomePage',
      path: '/',
      meta: {
        title: '<PERSON><PERSON><PERSON><PERSON><PERSON>'
      },
      component: () => import('@/views/authentication/auth/LoginPage.vue')
    },
    {
      name: 'Login',
      path: '/auth/login',
      meta: {
        title: '<PERSON><PERSON><PERSON><PERSON><PERSON>šení'
      },
      component: () => import('@/views/authentication/auth/LoginPage.vue')
    },
    // {
    //   name: 'Register',
    //   path: '/auth/register',
    //   component: () => import('@/views/authentication/auth/RegisterPage.vue')
    // },
    {
      name: 'Forgot Password',
      path: '/auth/forgot-pwd',
      meta: {
        title: 'Zapomenuté heslo'
      },
      component: () => import('@/views/authentication/auth/ForgotPwd.vue')
    },
    // {
    //   name: 'Check Mail',
    //   path: '/auth/check-mail',
    //   component: () => import('@/views/authentication/auth/CheckMail.vue')
    // },
    {
      name: 'Reset Password',
      path: '/auth/reset-pwd',
      meta: {
        title: 'Obnovení hesla'
      },
      component: () => import('@/views/authentication/auth/ResetPwd.vue')
    },
    // {
    //   name: 'Code Verification',
    //   path: '/auth/code-verify',
    //   component: () => import('@/views/authentication/auth/CodeVerification.vue')
    // },
    // {
    //   name: 'Coming Soon ',
    //   path: '/pages/comingsoon',
    //   component: () => import('@/views/pages/maintenance/comingsoon/Comingsoon1.vue')
    // },
    // {
    //   name: 'Under Construction',
    //   path: '/pages/construction',
    //   component: () => import('@/views/pages/underconstruction/UnderConstruction.vue')
    // },
    {
      name: 'Error404',
      path: '/pages/error',
      meta: {
        title: 'Chyba 404'
      },
      component: () => import('@/views/pages/maintenance/error/Error404Page.vue')
    }
    // {
    //   name: 'Error 500',
    //   path: '/pages/error500',
    //   component: () => import('@/views/pages/maintenance/error/Error500Page.vue')
    // }
  ]
};

export default AuthRoutes;
