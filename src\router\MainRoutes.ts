import type { RouteLocationNormalizedLoadedGeneric } from 'vue-router';

const MainRoutes = {
  path: '/',
  meta: {
    requiresAuth: true
  },
  redirect: '/',
  component: () => import('@/layouts/dashboard/DashboardLayout.vue'),
  children: [
    {
      name: 'Farmak',
      path: '/',
      meta: {
        title: 'FarMak'
      },
      component: () => import('@/views/pages/dashboard/projects/ListOfProjectsPage.vue')
    },
    {
      name: 'Projects',
      path: '/projekty',
      meta: {
        isAllowed: ['view_projects'],
        title: 'Projekty'
      },
      children: [
        {
          name: 'ListOfProjects',
          path: '',
          component: () => import('@/views/pages/dashboard/projects/ListOfProjectsPage.vue')
        },
        {
          path: 'projekt/:project_id',
          children: [
            {
              name: 'ProjectDetail',
              path: '',
              component: () => import('@/views/pages/dashboard/projects/project/ProjectPage.vue')
            },
            {
              path: 'oddeleni/:project_department_id',
              children: [
                {
                  name: 'DepartmentDetail',
                  path: '',
                  component: () =>
                    import('@/views/pages/dashboard/projects/project/department/DepartmentPage.vue')
                },
                {
                  name: 'Chapter',
                  path: 'kapitola/:chapter_id',
                  children: [
                    {
                      name: 'ChapterDetail',
                      path: '',
                      component: () =>
                        import(
                          '@/views/pages/dashboard/projects/project/department/chapter/ChapterPage.vue'
                        )
                    },
                    {
                      name: 'ExternalInsertAnalyticalRequest',
                      path: 'analyza/:sample_id/vlozeni',
                      component: () =>
                        import('@/stores/analyticalRequests/InsertAnalyticalRequest.vue')
                    },
                    {
                      name: 'ExperimentPage',
                      path: 'experiment/:form_id',
                      children: [
                        {
                          name: 'Experiment',
                          path: '',
                          component: () =>
                            import(
                              '@/views/pages/dashboard/projects/project/department/chapter/experiment/ExperimentDetail.vue'
                            )
                        },
                        {
                          name: 'NewAnalyticalRequest',
                          path: 'zadost/nova',
                          component: () =>
                            import(
                              '@/views/pages/dashboard/projects/project/department/chapter/experiment/request/NewAnalyticalRequest.vue'
                            )
                        },
                        {
                          name: 'NewAnalyticalRequestStandard',
                          path: 'zadost/nova/standard',
                          component: () =>
                            import(
                              '@/views/pages/dashboard/projects/project/department/chapter/experiment/request/NewAnalyticalRequestStandard.vue'
                            )
                        },
                        {
                          name: 'InsertAnalyticalRequest',
                          path: 'zadost/vlozeni',
                          component: () =>
                            import(
                              '@/views/pages/dashboard/projects/project/department/chapter/experiment/request/InsertAnalyticalRequest.vue'
                            )
                        }
                      ]
                    },
                    {
                      name: 'MessagePage',
                      path: 'sprava/:form_id',
                      children: [
                        {
                          name: 'Message',
                          path: '',
                          component: () =>
                            import(
                              '@/views/pages/dashboard/projects/project/department/chapter/message/MessageDetail.vue'
                            )
                        },
                        {
                          name: 'NewAnalyticalRequest',
                          path: 'zadost/nova',
                          component: () =>
                            import(
                              '@/views/pages/dashboard/projects/project/department/chapter/experiment/request/NewAnalyticalRequest.vue'
                            )
                        },
                        {
                          name: 'NewAnalyticalRequestStandard',
                          path: 'zadost/nova/standard',
                          component: () =>
                            import(
                              '@/views/pages/dashboard/projects/project/department/chapter/experiment/request/NewAnalyticalRequestStandard.vue'
                            )
                        },
                        {
                          name: 'InsertAnalyticalRequest',
                          path: 'zadost/vlozeni',
                          component: () =>
                            import(
                              '@/views/pages/dashboard/projects/project/department/chapter/experiment/request/InsertAnalyticalRequest.vue'
                            )
                        }
                      ]
                    },
                    {
                      name: 'InvestigationPage',
                      path: 'setreni/:form_id',
                      children: [
                        {
                          name: 'Investigation',
                          path: '',
                          component: () =>
                            import(
                              '@/views/pages/dashboard/projects/project/department/chapter/investigation/InvestigationDetail.vue'
                            )
                        },
                        {
                          name: 'NewAnalyticalRequest',
                          path: 'zadost/nova',
                          component: () =>
                            import(
                              '@/views/pages/dashboard/projects/project/department/chapter/experiment/request/NewAnalyticalRequest.vue'
                            )
                        },
                        {
                          name: 'NewAnalyticalRequestStandard',
                          path: 'zadost/nova/standard',
                          component: () =>
                            import(
                              '@/views/pages/dashboard/projects/project/department/chapter/experiment/request/NewAnalyticalRequestStandard.vue'
                            )
                        },
                        {
                          name: 'InsertAnalyticalRequest',
                          path: 'zadost/vlozeni',
                          component: () =>
                            import(
                              '@/views/pages/dashboard/projects/project/department/chapter/experiment/request/InsertAnalyticalRequest.vue'
                            )
                        }
                      ]
                    },

                    {
                      name: 'AttemptPage',
                      path: 'pokus/:form_id',
                      children: [
                        {
                          name: 'Attempt',
                          path: '',
                          component: () =>
                            import(
                              '@/views/pages/dashboard/projects/project/department/chapter/attempt/AttemptDetail.vue'
                            )
                        },
                        {
                          name: 'NewAnalyticalRequest',
                          path: 'zadost/nova',
                          component: () =>
                            import(
                              '@/views/pages/dashboard/projects/project/department/chapter/experiment/request/NewAnalyticalRequest.vue'
                            )
                        },
                        {
                          name: 'NewAnalyticalRequestStandard',
                          path: 'zadost/nova/standard',
                          component: () =>
                            import(
                              '@/views/pages/dashboard/projects/project/department/chapter/experiment/request/NewAnalyticalRequestStandard.vue'
                            )
                        },
                        {
                          name: 'InsertAnalyticalRequest',
                          path: 'zadost/vlozeni',
                          component: () =>
                            import(
                              '@/views/pages/dashboard/projects/project/department/chapter/experiment/request/InsertAnalyticalRequest.vue'
                            )
                        }
                      ]
                    },
                    {
                      name: 'ChapterInsertAnalyticalRequest',
                      path: 'zadost/vlozeni/:type',
                      component: () =>
                        import(
                          '@/views/pages/dashboard/projects/project/department/chapter/experiment/request/InsertAnalyticalRequest.vue'
                        )
                    },
                    {
                      name: 'ListOfMethodForTechnique',
                      path: 'technika/:analytical_technique_id/metody',
                      component: () =>
                        import(
                          '@/views/pages/dashboard/projects/project/department/chapter/method/ListOfMethodForTechnique.vue'
                        )
                    },
                    {
                      name: 'ListOfMethodForTechniqueUpdate',
                      path: 'technika/:analytical_technique_id/metody/:method_id/zmena',
                      component: () =>
                        import(
                          '@/views/pages/dashboard/projects/project/department/chapter/method/EditMethodPage.vue'
                        )
                    },
                    {
                      name: 'MethodHistory',
                      path: 'technika/:analytical_technique_id/metody/:method_id/historie',
                      component: () =>
                        import(
                          '@/views/pages/dashboard/projects/project/department/chapter/method/MethodHistory.vue'
                        )
                    },
                    {
                      name: 'MethodDetail',
                      path: 'technika/:analytical_technique_id/metody/:method_id/historie/:method_version_id',
                      component: () =>
                        import(
                          '@/views/pages/dashboard/projects/project/department/chapter/method/MethodDetail.vue'
                        )
                    },
                    {
                      name: 'VersionCompare',
                      path: 'technika/:analytical_technique_id/metody/:method_id/historie/:method_version_id/porovnani/:version_a_id/:version_b_id',
                      component: () =>
                        import(
                          '@/views/pages/dashboard/projects/project/department/chapter/method/VersionsDetails.vue'
                        )
                    }
                  ]
                },
                {
                  path: 'sablony',
                  children: [
                    {
                      name: 'ExperimentTemplates',
                      path: 'experimenty',
                      component: () =>
                        import(
                          '@/views/pages/dashboard/projects/project/department/templates/experiments/ListOfTemplatesPageExperiment.vue'
                        )
                    },
                    {
                      name: 'ExperimentNewTemplate',
                      path: 'experimenty/pridat-sablonu',
                      component: () =>
                        import(
                          '@/views/pages/dashboard/projects/project/department/templates/experiments/AddTemplatePageExperiment.vue'
                        )
                    },
                    {
                      name: 'ExperimentPreviewTemplate',
                      path: 'experimenty/nahled-sablony/:experiment_template_id',
                      component: () =>
                        import(
                          '@/views/pages/dashboard/projects/project/department/templates/experiments/PreviewTemplatePageExperiment.vue'
                        )
                    },
                    {
                      name: 'ExperimentEditTemplate',
                      path: 'experimenty/uprava-sablony/:experiment_template_id',
                      component: () =>
                        import(
                          '@/views/pages/dashboard/projects/project/department/templates/experiments/EditTemplatePageExperiment.vue'
                        )
                    },
                    {
                      name: 'AttemptTemplates',
                      path: 'pokusy',
                      component: () =>
                        import(
                          '@/views/pages/dashboard/projects/project/department/templates/attempts/ListOfTemplatesPageAttempt.vue'
                        )
                    },
                    {
                      name: 'AttemptNewTemplate',
                      path: 'pokusy/pridat-sablonu',
                      component: () =>
                        import(
                          '@/views/pages/dashboard/projects/project/department/templates/attempts/AddTemplatePageAttempt.vue'
                        )
                    },
                    {
                      name: 'AttemptPreviewTemplate',
                      path: 'pokusy/nahled-sablony/:attempt_template_id',
                      component: () =>
                        import(
                          '@/views/pages/dashboard/projects/project/department/templates/attempts/PreviewTemplatePageAttempt.vue'
                        )
                    },
                    {
                      name: 'AttemptEditTemplate',
                      path: 'pokusy/uprava-sablony/:attempt_template_id',
                      component: () =>
                        import(
                          '@/views/pages/dashboard/projects/project/department/templates/attempts/EditTemplatePageAttempt.vue'
                        )
                    },
                    {
                      name: 'InvestigationTemplates',
                      path: 'setreni',
                      component: () =>
                        import(
                          '@/views/pages/dashboard/projects/project/department/templates/attempts/NewListOfTemplatesPageForInvestigation.vue'
                        )
                    },
                    {
                      name: 'InvestigationNewTemplate',
                      path: 'setreni/pridat-sablonu',
                      component: () =>
                        import(
                          '@/views/pages/dashboard/projects/project/department/templates/investigations/AddTemplatePageInvestigation.vue'
                        )
                    },
                    {
                      name: 'InvestigationPreviewTemplate',
                      path: 'setreni/nahled-sablony/:investigation_template_id',
                      component: () =>
                        import(
                          '@/views/pages/dashboard/projects/project/department/templates/investigations/PreviewTemplatePageInvestigation.vue'
                        )
                    },
                    {
                      name: 'InvestigationEditTemplate',
                      path: 'setreni/uprava-sablony/:investigation_template_id',
                      component: () =>
                        import(
                          '@/views/pages/dashboard/projects/project/department/templates/investigations/EditTemplatePageInvestigation.vue'
                        )
                    }
                  ]
                }
              ]
            },

            {
              name: 'ListOfProjectChemicals',
              path: 'chemikalie',
              component: () =>
                import(
                  '@/views/pages/dashboard/projects/project/chemicals/ListOfProjectChemicalsPage.vue'
                )
            }
          ]
        }
      ]
    },
    {
      name: 'SamplesList',
      path: '/vzorky/:type',
      children: [
        {
          name: 'Samples',
          path: '',
          component: () => import('@/views/pages/dashboard/samples/ListOfSamplesPage.vue')
        },
        {
          name: 'NewSampleAnalyticalRequest',
          path: 'analyza/vytvoreni',
          component: () =>
            import('@/views/pages/dashboard/samples/sample/requests/NewSampleAnalyticalRequest.vue')
        },
        {
          name: 'SampleDetail',
          path: 'vzorek/:sample_id',
          children: [
            {
              name: 'SampleEdit',
              path: '',
              component: () => import('@/views/pages/dashboard/samples/sample/EditSamplePage.vue')
            }
          ]
        }
      ]
    },
    {
      name: 'AnalyticalInstruments',
      path: '/analyticke-pristroje',
      meta: {
        isAllowed: ['view_analytical_instruments'],
        title: 'Analytické přístroje'
      },
      component: () => import('@/views/pages/dashboard/instruments/ListOfInstrumentsPage.vue')
    },
    {
      name: 'SeznamKolon',
      path: '/seznam-kolon',
      meta: {
        isAllowed: ['view_columns'],
        title: 'Kolony'
      },
      component: () => import('@/views/pages/dashboard/columns/ListOfColumnsPage.vue')
    },
    {
      name: 'UsersManagement',
      path: '/sprava-uzivatelu',
      meta: {
        isAllowed: ['view_users'],
        title: 'Správa uživatelů'
      },
      component: () => import('@/views/pages/dashboard/management/users/ListOfUsersPage.vue')
    },
    {
      name: 'StandardsManagement',
      path: '/sprava-standardu',
      meta: {
        isAllowed: ['view_standards'],
        title: 'Standardy'
      },
      component: () => import('@/views/pages/dashboard/management/ListOfStandardsPage.vue')
    },
    {
      name: 'AnalyticalTechniquesManagement',
      path: '/sprava-analytickych-technik',
      meta: {
        isAllowed: ['view_techniques'],
        title: 'Interní techniky'
      },
      component: () =>
        import('@/views/pages/dashboard/management/ListOfAnalyticalTechniquesPage.vue')
    },
    {
      name: 'ExternalTechniquesManagement',
      path: '/sprava-externich-technik',
      meta: {
        isAllowed: ['view_techniques'],
        title: 'Externí techniky'
      },
      component: () =>
        import('@/views/pages/dashboard/management/ListOfExternalAnalyticalTechniquesPage.vue')
    },
    {
      name: 'MySettings',
      path: '/nastaveni',
      meta: {
        title: 'Nastavení'
      },
      component: () => import('@/views/pages/dashboard/settings/MySettings.vue')
    }
  ]
};

export default MainRoutes;
