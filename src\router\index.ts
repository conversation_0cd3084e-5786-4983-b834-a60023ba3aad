import { useAuthStore } from '@/stores/auth';
import { useUIStore } from '@/stores/ui';
import { isAllowed, missingPermissions } from '@/utils/directive/isAllowed';
import { notification } from 'ant-design-vue';
import { createRouter, createWebHistory } from 'vue-router';
import AuthRoutes from './AuthRoutes';
import MainRoutes from './MainRoutes';
import { buildDynamicTitle } from '@/utils/title';

export const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/:pathMatch(.*)*',
      component: () => import('@/views/pages/maintenance/error/Error404Page.vue')
    },
    MainRoutes,
    AuthRoutes
  ]
});

router.beforeEach(async (to, from, next) => {
  const publicPages = ['/auth/login'];
  const authRequired = !publicPages.includes(to.path);
  const authStore = useAuthStore();
  if (authRequired) {
    const token = authStore.getAccessToken();

    if (authStore.user === null || token === null) {
      authStore.returnUrl = to.fullPath;
      return next('/auth/login');
    }

    if (to.name === 'Samples') {
      const type = to.params.type;
      if (type === 'rd') {
        to.meta.isAllowed = ['view_rd_samples'];
      } else if (type === 'qc-a-vt') {
        to.meta.isAllowed = ['view_qc_vt_samples'];
      }
    }
    if (to.name === 'NewSampleAnalyticalRequest') {
      const type = to.params.type;
      if (type === 'rd') {
        to.meta.isAllowed = ['create_rd_analysis_requests'];
      } else if (type === 'qc-a-vt') {
        to.meta.isAllowed = ['create_qc_vt_analysis_requests'];
      }
    }

    if (to.matched.some((record) => record.meta.requiresAuth)) {
      const permissions: string[] = to.meta.isAllowed ? (to.meta.isAllowed as string[]) : [];
      if (!isAllowed(permissions)) {
        const missingPerms = missingPermissions(permissions);
        notification.error({
          message: 'Nedostatečné oprávnění',
          description: 'Nemáte oprávnění pro zobrazení této stránky: ' + missingPerms.join(', ')
        });

        if (missingPerms.includes('view_projects')) {
          return next('/auth/login');
        } else {
          return next('/projekty');
        }
      }
    }
  }

  next();
});

router.beforeEach(() => {
  const uiStore = useUIStore();
  uiStore.isLoading = true;
});

router.afterEach(() => {
  const uiStore = useUIStore();
  uiStore.isLoading = false;
});

router.afterEach((to) => {
  const lastMatched = to.matched[to.matched.length - 1];
  if (lastMatched && lastMatched.meta && lastMatched.meta.title) {
    document.title = lastMatched.meta.title as string;
  } else {
    document.title = buildDynamicTitle(to);
  }
});
