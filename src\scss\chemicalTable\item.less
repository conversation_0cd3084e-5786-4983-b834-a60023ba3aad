@xs: 600px;
@sm: 960px;
@md: 1280px;
@lg: 1920px;

.items {
  --gap: 1rem;

  min-width: 750px;
  position: relative;
  overflow-x: auto;

  .status {
    grid-area: status;
    text-align: left;

    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: flex-start;
  }

  .name {
    grid-area: name;
    text-align: left;
    min-width: 0;
    display: flex;
    justify-content: center;
    flex-direction: column;
  }

  .density {
    grid-area: density;
    text-align: left;
    min-width: 0;
    display: flex;
    justify-content: center;
    flex-direction: column;
  }

  .molar_mass {
    grid-area: molar_mass;
    text-align: left;
    min-width: 0;
    display: flex;
    justify-content: center;
    flex-direction: column;
  }

  .notes {
    grid-area: notes;
    text-align: left;
    min-width: 0;
    display: flex;
    justify-content: center;
    flex-direction: column;
  }

  .grams {
    grid-area: grams;
    text-align: left;
    min-width: 0;
    display: flex;
    justify-content: center;
    flex-direction: column;
  }

  .moles {
    grid-area: moles;
    text-align: left;
    min-width: 0;
    display: flex;
    justify-content: center;
    flex-direction: column;
  }

  .equivalent {
    grid-area: equivalent;
    text-align: left;
    min-width: 0;
    display: flex;
    justify-content: center;
    flex-direction: column;
  }

  .volume_ml {
    grid-area: volume_ml;
    text-align: left;
    min-width: 0;
    display: flex;
    justify-content: center;
    flex-direction: column;
  }

  .concentration {
    grid-area: concentration;
    text-align: left;
    min-width: 0;
    display: flex;
    justify-content: center;
    flex-direction: column;
  }

  .action {
    grid-area: action;
    min-width: 120px;
    width: 120px;
    text-align: left;
    display: flex;
    justify-content: center;
    flex-direction: column;
  }

  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: auto;

  li {
    display: grid;
    gap: calc(2 * var(--gap) / 6);

    padding: 0.2rem 0;
    border-bottom: 1px solid var(--v-theme-primary);
    list-style-type: none;

    &.header {
      //   display: none;
      font-size: 13px;
      font-weight: bold;
      border-top: 0 !important;

      //   @media @md {
      display: grid;
      //   }

      & > div:first-child {
        padding-left: 0px;
      }

      & > div:last-child {
        padding-left: 12px;
      }
    }

    &.result {
      background-color: #ffddddcc;
      border-bottom: 0;
      //   margin-top: 25px;
    }

    &.first_chemical {
      background-color: #fff1ddcc;
      //   margin-bottom: 25px;
    }

    &.divider {
      display: grid !important;
      grid-template-columns: 1fr !important;
      gap: 0 !important;

      & > hr {
        border-color: #00000017;
      }
    }
  }

  &.normal {
    // @media @sm {
    //   grid-template-columns: 1fr 1fr 1fr;
    // }

    // @media @md {
    //   grid-template-columns: repeat(8, auto);
    // }

    // @media @lg {
    grid-template-columns: repeat(9, minmax(140px, 1fr)) 90px;
    // }

    li {
      //   grid-column: span 1;
      grid-template-columns: subgrid;
      //   grid-template-areas:
      //     'name'
      //     'molar_mass'
      //     'density'
      //     'notes'
      //     'grams'
      //     'moles'
      //     'equivalent'
      //     'volume_ml'
      //     'concentration'
      //     'action';

      //   @media @sm {
      //     grid-column: span 3;
      //     grid-template-areas:
      //       'name molar_mass density'
      //       'name notes notes'
      //       'name grams moles'
      //       'name equivalent equivalent'
      //       'name volume_ml volume_ml'
      //       'name concentration action';
      //   }

      //   @media @md {
      grid-column: span 10;
      grid-template-areas: 'name concentration density molar_mass grams moles volume_ml equivalent notes action';
      //   }

      &.is-preview {
        grid-column: span 9;
        grid-template-areas: 'name density molar_mass  notes grams moles equivalent volume_ml concentration';
      }

      & .icon-wrapper {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        & > i {
          font-size: 38px;
          &.same {
            color: var(--v-theme-secondary) !important;
          }

          &.se {
            color: rgb(42, 161, 175) !important;
          }

          &.le {
            color: rgb(42, 161, 175) !important;
          }
        }
      }
    }
  }
}

.image-container {
  position: relative;
  background-color: #f6f6f6cc;
  border-radius: 6px;
}

.image {
  display: block;
  width: 100%;
}

.floating-button {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
}

.v-tabs-window {
  overflow-y: auto;
}
