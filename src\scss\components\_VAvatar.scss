.with-badge {
  overflow: unset;
  .badge-number {
    min-height: 16px;
    top: -5px;
    right: -5px;
    min-width: 16px;
  }
  .status {
    position: absolute;
    min-width: 8px;
    height: 8px;
    border-radius: 4px;
    &-top {
      right: 2px;
      top: 1px;
    }
    &-bottom {
      right: -3px;
      bottom: 5px;
      img {
        border: 2px solid rgb(var(--v-theme-containerBg));
      }
    }
  }
  &.rounded-0 {
    .status {
      &-top {
        top: -2px;
        right: -2px;
      }
    }
  }
}
.v-avatar.v-avatar--variant-outlined {
  &.dashed {
    border-style: dashed;
  }
}
.v-avatar--variant-tonal {
  &.with-border {
    @each $color, $value in $theme-colors {
      &.text-#{$color} {
        border: 1px solid rgb(#{$value});
      }
    }
  }
}

.preview-upload {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  opacity: 0;
  border-radius: 100%;
}
