.v-breadcrumbs-item--link {
  color: rgb(var(--v-theme-lightText));
}
.v-breadcrumbs {
  flex-wrap: wrap;

  .v-breadcrumbs-item--disabled {
    --v-disabled-opacity: 1;
    .v-breadcrumbs-item--link {
      color: rgb(var(--v-theme-darkText));
    }
  }
  .v-breadcrumbs-divider {
    color: rgb(var(--v-theme-lightText));
  }
}

.breadcrumb-with-title {
  .v-toolbar__content {
    height: unset !important;
    padding: 20px 0;
  }
  .v-breadcrumbs__prepend {
    svg {
      vertical-align: -3px;
    }
  }
}

.breadcrumb-height {
  .v-toolbar__content {
    height: unset !important;
  }
}
