.v-input--density-default:not(.v-autocomplete--multiple),
.v-field--variant-solo,
.v-field--variant-filled {
  --v-input-control-height: 39px;
  --v-input-padding-top: 2px;
  input.v-field__input {
    padding-bottom: 2px;
  }
  .v-field__input {
    padding-bottom: 2px;
  }
  textarea {
    padding-top: 11px;
  }
}
.v-input--density-default {
  .v-field__input {
    min-height: 41px;
  }
}
.v-field--variant-outlined {
  &.v-field--focused {
    .v-field__outline {
      --v-field-border-width: 1px;
    }
  }
}
.remove-details {
  .v-input__details {
    display: none;
  }
}
.v-input {
  .v-input__details {
    padding-inline: 0;
  }
}

.v-input--density-comfortable {
  --v-input-control-height: 56px;
  --v-input-padding-top: 17px;
}
.v-label {
  font-size: 0.875rem;
  --v-medium-emphasis-opacity: 0.8;
}
.v-switch .v-label,
.v-checkbox .v-label {
  opacity: 1;
}

.todo-list {
  .v-selection-control.v-selection-control--dirty {
    .v-label {
      text-decoration: line-through;
    }
  }
}

.wrap-label {
  .v-selection-control {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: calc(100% - 15px);
  }
}

textarea.v-field__input {
  font-size: 14px;
}

.textarea-input {
  .v-label {
    top: 15px;
  }
}

.hidden-label {
  position: absolute;
  overflow: hidden;
  white-space: nowrap;
  margin: 0;
  padding: 0;
  height: 1px;
  width: 1px;
  clip: rect(0 0 0 0);
  clip-path: inset(100%);
}
