.horizontaTopbar {
  .v-toolbar__content {
    max-width: 1270px;
    margin: 0 auto;
  }
}
.mobile-menu {
  .v-navigation-drawer {
    margin-top: -60px !important;
    height: 100vh !important;
    z-index: 2000 !important;
  }
}
@media (max-width: 1440px) {
  .horizontalMenu {
    .v-container {
      max-width: 100%;
    }
  }
}
@media (min-width: 960px) {
  .horizontalMenu {
    border-top: 1px solid rgba(var(--v-theme-borderLight), 0.8);
    border-bottom: 1px solid rgba(var(--v-theme-borderLight), 0.8);
    margin-top: 60px;
    margin-bottom: -60px;
    background-color: rgb(var(--v-theme-surface));
    position: relative;
    &.maxWidth {
      .horizontal-navbar {
        max-width: 1250px;
      }
    }
  }
  .horizontal-navbar {
    padding: 8px 15px;
    margin: 0px auto;
    align-items: center;
    display: flex;
    z-index: 11;
    font-size: 0.875rem;
    position: relative;
    ul {
      padding: 0px;
      margin: 0px;
    }
    li {
      list-style: none;
      a {
        color: inherit;
        text-decoration: none;
        display: flex;
        padding: 10px 13px;
        height: 44px;
        align-items: center;
        .navIcon {
          margin-right: 10px;
          .icon-tabler {
            width: 4px;
            height: 4px;
            vertical-align: 3px;
          }
        }
        .ddIcon {
          margin-top: 2px;
          opacity: 0.5;
        }
        &:hover {
          opacity: 1;
          color: rgb(var(--v-theme-secondary));
        }
      }
    }
    .navItem {
      position: relative;
    }
    .ddMenu {
      position: absolute;
      width: 230px;
      display: none;
      top: 42px;
      padding: 10px;
      z-index: 1;
      background-color: rgb(var(--v-theme-surface));
      box-shadow: $box-shadow;
      border-radius: $border-radius-root;
      li {
        margin-bottom: 3px;
      }
    }
    .ddLevel-2,
    .ddLevel-3 {
      top: -5px;
      left: 230px;
    }
    .navItem:hover {
      background-color: rgb(var(--v-theme-lightsecondary));
      border-radius: $border-radius-root;
      > a > .ddIcon {
        transform: rotate(90deg);
        margin-top: 1px;
      }
      > .ddMenu {
        display: block;
      }
    }
    > li:hover {
      background-color: rgb(var(--v-theme-lightsecondary));
      border-radius: $border-radius-root;
      > .navItemLink {
        color: rgb(var(--v-theme-secondary));
        opacity: 1;
      }
    }
    .router-link-exact-active {
      color: rgb(var(--v-theme-secondary));
      font-weight: 500;
      background-color: rgb(var(--v-theme-lightsecondary));
      border-radius: $border-radius-root;
    }
  }
}
@media (max-width: 1240px) and (min-width: 960px) {
  .horizontal-navbar {
    > .navItem {
      &:nth-last-child(-n + 2) {
        .ddLevel-2,
        .ddLevel-3 {
          left: unset;
          right: 230px;
        }
      }
      .ddLevel-3 {
        left: unset;
        right: 230px;
      }
    }
  }
}
@media (min-width: 768px) {
  .horizontalMenu {
    .v-container {
      padding-inline: 40px;
    }
  }
}
