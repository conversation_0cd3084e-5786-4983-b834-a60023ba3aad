.rounded-square {
  width: 20px;
  height: 20px;
}

.icon-scale {
  position: absolute;
  left: -17px;
  bottom: -27px;
  transform: rotate(25deg);
}

.tabBtn {
  .v-tab--selected {
    .v-tab__slider {
      opacity: 0;
    }
  }
  .v-btn--variant-outlined:not(.v-tab--selected) {
    border: none;
    color: rgb(var(--v-theme-lightText));
  }
}

.headerWithBtn {
  .v-card-item {
    padding: 14px;
  }
}

.groupingAvatar {
  .v-avatar {
    cursor: pointer;
  }
}

.widget-gradient {
  position: relative;
  &::before,
  &::after {
    content: '';
    width: 100%;
    height: 100%;
    position: absolute;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0.0001) 22.07%,
      rgba(255, 255, 255, 0.15) 83.21%
    );
    transform: matrix(0.9, 0.44, -0.44, 0.9, 0, 0);
  }
  &:after {
    top: 50%;
    right: -20px;
  }
  &::before {
    right: -70px;
    bottom: 80%;
  }
}
.hover-icon-card {
  .hover-icon {
    transition: 0.5s;
  }
  &:hover {
    .hover-icon {
      opacity: 1;
      transform: scale(1.2);
      transition: 0.5s;
    }
  }
}
.widget-progress {
  .v-progress-linear {
    .v-progress-linear__background {
      background-color: rgb(var(--v-theme-lightsuccess)) !important;
      opacity: 1;
    }
  }
}
.readMedia {
  position: absolute;
  bottom: -7px;
  right: 0;
  width: 182px;
  height: 144px;
}
