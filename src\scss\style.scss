@import './variables';
@import 'vuetify/styles/main.sass';
@import './override';
@import './layout/container';
@import './layout/sidebar';
@import './layout/footer';
@import './layout/topbar';
@import './layout/horizontal';

@import './components/VAlert';
@import './components/VAvatar';
@import './components/VList';
@import './components/VButtons';
@import './components/VCard';
@import './components/VChip';
@import './components/VBadge';
@import './components/VBreadcrumb';
@import './components/VPagination';
@import './components/VCarousel';
@import './components/VField';
@import './components/VInput';
@import './components/VNavigationDrawer';
@import './components/VShadow';
@import './components/VTextField';
@import './components/VTextarea';
@import './components/VTabs';
@import './components/VTimeline';

@import './pages/datatable';
@import './pages/dashboards';
@import './pages/editor';

.DarkDefaultTheme,
.DarkTheme1,
.DarkTheme2,
.DarkTheme3,
.DarkTheme4,
.DarkTheme5,
.DarkTheme6,
.DarkTheme7,
.DarkTheme8 {
  @import './layout/dark';
}

.customListCard .v-card-item {
  padding: 5px !important;
}

.customListCard .v-card-title,
.customListCard.v-list-item-title {
  font-size: 90%;
}

.customer-modal {
  width: calc(100% - 48px);
  min-width: 340px;
  max-width: 880px;
  // max-height: calc(100vh - 148px) !important;
  // & > .v-overlay__content {
  //   overflow: auto;
  //   max-height: calc(100vh - 148px) !important;
  // }
}
.instrument-modal {
  width: calc(100% - 48px);
  min-width: 340px;
  max-width: 1440px;
}
.close-modal {
  width: calc(100% - 48px);
  min-width: 340px;
  max-width: 850px;
}

// .autocomplete .v-input__control .v-field .v-field__field .v-field__input {
//   padding-top: 0 !important;
//   padding-bottom: 0 !important;
// }

@media screen and (max-width: 570px) {
  .v-overlay.v-dialog.customer-modal {
    margin: 0;
    width: 100%;

    .v-overlay__content {
      margin: 5px;
      width: calc(100% - 10px);
      max-height: 100%;
      max-width: calc(100% - 10px);
    }
  }

  .v-overlay.v-dialog.instrument-modal {
    margin: 0;
    width: 100%;

    .v-overlay__content {
      margin: 5px;
      width: calc(100% - 10px);
      max-height: 100%;
      max-width: calc(100% - 10px);
    }
  }

  .vue3-easy-data-table__body td,
  .vue3-easy-data-table__header th {
    padding: calc(var(--easy-table-body-item-padding) / 2) !important;
  }
}

.easy-data-table__rows-selector ul.select-items.show {
  z-index: 99999999 !important;
  top: -64px !important;
}

.easy-data-table__rows-selector ul.select-items .vue3-easy-data-table__body .expand {
  padding-right: 0px !important;
}

.capitalize-first-letter::first-letter {
  text-transform: capitalize;
}
