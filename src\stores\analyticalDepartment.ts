import type { BaseResponseI, BasePaginatorResponseI } from '@/utils/axios';
import BaseConstructor from '@/utils/BaseConstructor';
import { fetchWrapper } from '@/utils/helpers/fetch-wrapper';
import { notification } from 'ant-design-vue';
import { defineStore } from 'pinia';
import { Technique, type TechniqueDto, type TechniqueType } from './techniques';
import { Method, type MethodDto } from './method/methods';
import {
  AnalyticalRequest,
  type AnalyticalRequestDto
} from './analyticalRequests/analyticalRequests';

const baseUrl = `${import.meta.env.VITE_API_URL}`;

export enum MethodDevelopmentStatus {
  ACTIVE = 'active',
  DELETED = 'deleted'
}

export interface MethodDevelopmentDto {
  analytical_technique_id: number;
  name: string;
  shortcut: string;
  status: MethodDevelopmentStatus;
  type: TechniqueType;
  using_column: boolean;
  created_at: string;
  updated_at: string;
  parent_technique_id: number;
  sub_analytical_techniques: TechniqueDto[];
}

export interface MethodDevelopmentI {
  analytical_technique_id: number;
  name: string;
  shortcut: string;
  status: MethodDevelopmentStatus;
  type: TechniqueType;
  using_column: boolean;
  created_at: Date;
  updated_at: Date;
  parent_technique_id: number;
  sub_analytical_techniques: Technique[];
}

export class MethodDevelopment
  extends BaseConstructor<MethodDevelopmentI>()
  implements MethodDevelopmentI
{
  constructor(data: MethodDevelopmentDto) {
    super(data as unknown as MethodDevelopmentI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    this.sub_analytical_techniques = data.sub_analytical_techniques.map(
      (subTechnique) => new Technique(subTechnique)
    );
  }
}

interface StateI {
  loading: boolean;
  project_id: number | undefined;
  analyticalRequestsGlobal: AnalyticalRequest[] | null;
  analyticalRequestsTotalItems: number;
  options: {
    page: number;
    itemsPerPage: number;
  };
}

export const useAnalyticalDepartmentStore = defineStore({
  id: 'analyticalDepartment',
  state: () =>
    ({
      loading: false,
      project_id: undefined,
      analyticalRequestsGlobal: null,
      analyticalRequestsTotalItems: 0,
      options: {
        page: 1,
        itemsPerPage: 10
      }
    }) as StateI,
  actions: {
    checkProjectIsDefined(): boolean {
      if (!this.project_id) {
        notification.error({ message: 'Není vybrán projekt' });
        return false;
      }

      return true;
    },

    async getProjectMethodDevelopment() {
      if (!this.checkProjectIsDefined()) return undefined;

      this.loading = true;
      return fetchWrapper
        .get(`${baseUrl}/analytical-department/projects/${this.project_id}/method_development`)
        .then((res: BaseResponseI<TechniqueDto[]>) => {
          this.loading = false;

          if (res.status_code === 200) {
            return res.data.map((technique) => new Technique(technique));
          }

          return undefined;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Nepodařilo se získat data', description: res.message });
          } else {
            notification.error({ message: 'Nepodařilo se získat data' });
          }

          return undefined;
        });
    },

    async getProjectMethodDevelopmentTechniqueDetail(technique_id: number) {
      if (!this.checkProjectIsDefined()) return undefined;

      this.loading = true;
      return fetchWrapper
        .get(
          `${baseUrl}/analytical-department/projects/${this.project_id}/method_development/technique/${technique_id}`
        )
        .then((res: BaseResponseI<MethodDto[]>) => {
          this.loading = false;

          if (res.status_code === 200) {
            return res.data.map((method) => new Method(method));
          }

          return undefined;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Nepodařilo se získat data', description: res.message });
          } else {
            notification.error({ message: 'Nepodařilo se získat data' });
          }

          return undefined;
        });
    },

    async getProjectRdSamples() {
      if (!this.checkProjectIsDefined()) return undefined;

      this.loading = true;

      const params = new URLSearchParams({
        current_page: this.options.page.toString(),
        items_per_page: this.options.itemsPerPage.toString()
      });
      return fetchWrapper
        .get(`${baseUrl}/analytical-department/projects/${this.project_id}/rd_samples?${params}`)
        .then((res: BasePaginatorResponseI<AnalyticalRequestDto>) => {
          this.loading = false;

          if (res.status_code === 200) {
            this.analyticalRequestsTotalItems = res.data.total_items;
            this.analyticalRequestsGlobal = res.data.items.map(
              (analyticalRequest) => new AnalyticalRequest(analyticalRequest)
            );
            return this.analyticalRequestsGlobal;
          }

          return undefined;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Nepodařilo se získat data', description: res.message });
          } else {
            notification.error({ message: 'Nepodařilo se získat data' });
          }

          return undefined;
        });
    },

    async getProjectQcVtSamples() {
      if (!this.checkProjectIsDefined()) return undefined;

      this.loading = true;

      const params = new URLSearchParams({
        current_page: this.options.page.toString(),
        items_per_page: this.options.itemsPerPage.toString()
      });

      return fetchWrapper
        .get(`${baseUrl}/analytical-department/projects/${this.project_id}/qc_vt_samples?${params}`)
        .then((res: BasePaginatorResponseI<AnalyticalRequestDto>) => {
          this.loading = false;

          if (res.status_code === 200) {
            this.analyticalRequestsTotalItems = res.data.total_items;
            this.analyticalRequestsGlobal = res.data.items.map(
              (analyticalRequest) => new AnalyticalRequest(analyticalRequest)
            );
            return this.analyticalRequestsGlobal;
          }

          return undefined;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Nepodařilo se získat data', description: res.message });
          } else {
            notification.error({ message: 'Nepodařilo se získat data' });
          }

          return undefined;
        });
    },

    async getProjectStandards() {
      if (!this.checkProjectIsDefined()) return undefined;

      this.loading = true;
      return fetchWrapper
        .get(`${baseUrl}/analytical-department/projects/${this.project_id}/standards`)
        .then((res: BaseResponseI<AnalyticalRequestDto[]>) => {
          this.loading = false;

          if (res.status_code === 200) {
            this.analyticalRequestsTotalItems = res.data.length;
            this.analyticalRequestsGlobal = res.data.map(
              (analyticalRequest) => new AnalyticalRequest(analyticalRequest)
            );
            return res.data.map((analyticalRequest) => new AnalyticalRequest(analyticalRequest));
          }

          return undefined;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Nepodařilo se získat data', description: res.message });
          } else {
            notification.error({ message: 'Nepodařilo se získat data' });
          }

          return undefined;
        });
    }
  }
});
