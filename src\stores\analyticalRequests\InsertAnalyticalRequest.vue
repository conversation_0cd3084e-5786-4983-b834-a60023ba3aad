<script setup lang="ts">
  import type { BreadcrumbI } from '@/components/shared/BaseBreadcrumb.vue';
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import FileUploader from '@/components/shared/file/FileUploader.vue';
  import TopPageBreadcrumb from '@/components/shared/TopPageBreadcrumb.vue';
  import UiParentCard from '@/components/shared/UiParentCard.vue';
  import LoaderWrapper from '@/layouts/dashboard/LoaderWrapper.vue';
  import {
    NewAnalyticalRequestType,
    useAnalyticalRequestsStore,
    type AnalyticalRequestModalNewPostDataI
  } from '@/stores/analyticalRequests/analyticalRequests';
  import { Attempt } from '@/stores/attempts';
  import { Experiment } from '@/stores/experiments';
  import { FormType, useFormsStore, type InsertAnalysisReanalyzeDto } from '@/stores/forms';
  import { Investigation } from '@/stores/investigations';
  import { Message } from '@/stores/messages';
  import { useProjectsStore } from '@/stores/projects';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { storeToRefs } from 'pinia';
  import { computed, onMounted, ref, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import InsertAnalysisParametersFormTable from './components/InsertAnalyticalRequestParamsTable.vue';
  import {
    useSamplesStore,
    type SampleDto,
    type SampleI,
    type SampleModalUpdatePostDataI
  } from '@/stores/sample/samples';
  import { AnalysisStatus } from '@/stores/forms';
  import { useRedirectStore } from '@/stores/redirect';
  import FileSection from '@/components/shared/file/FileSection.vue';
  import { useFilesStore, type File } from '@/stores/files';
  import { setPageTitle } from '@/utils/title';
  const filesStore = useFilesStore();

  const redirectStore = useRedirectStore();
  const samplesStore = useSamplesStore();
  const { sample } = storeToRefs(samplesStore);
  const ConfirmRef = ref<undefined | typeof ConfirmDlg>(undefined);

  const route = useRoute();
  const router = useRouter();
  const formsStore = useFormsStore();
  const projectsStore = useProjectsStore();
  const analyticalRequestsStore = useAnalyticalRequestsStore();
  const { project, department, chapter } = storeToRefs(projectsStore);

  const baseDataLoaded = ref(false);
  const newGenerateAnalyticalRequestData = ref<InsertAnalysisReanalyzeDto | null>(null);
  const form = ref<Experiment | Message | Investigation | Attempt | undefined>(undefined);

  onMounted(async () => {
    loadExecute();
  });

  const chapterSearch = ref<string | undefined>(undefined);

  const project_id = computed(() => route.params.project_id as string);
  const projectId = computed(() => parseInt(project_id.value));

  const project_department_id = computed(() => route.params.project_department_id as string);
  const projectDepartmentId = computed(() => parseInt(project_department_id.value));

  const chapter_id = computed(() => route.params.chapter_id as string);
  const chapterId = computed(() => parseInt(chapter_id.value));

  const sample_id = computed(() => route.params.sample_id as string | undefined);
  const sampleId = computed(() => (sample_id.value ? parseInt(sample_id.value) : null));

  const loadExecute = async () => {
    baseDataLoaded.value = false;
    chapterSearch.value = undefined;

    if (projectId.value) {
      if (project.value) {
        if (project.value.project_id !== projectId.value) {
          await projectsStore.getProjectById(projectId.value);
        }
      } else {
        await projectsStore.getProjectById(projectId.value);
      }
    }

    if (projectDepartmentId.value) {
      if (department.value) {
        if (department.value.project_department_id !== projectDepartmentId.value) {
          await projectsStore.getDepartmentById(projectDepartmentId.value);
        }
      } else {
        await projectsStore.getDepartmentById(projectDepartmentId.value);
      }
    }

    if (chapterId.value) {
      if (chapter.value) {
        if (chapter.value.chapter_id !== chapterId.value) {
          await projectsStore.getChapterById(chapterId.value);
        }
      } else {
        await projectsStore.getChapterById(chapterId.value);
      }
    }
    if (sampleId.value) {
      await samplesStore.getSample(sampleId.value);
      if (sample.value?.batch_number) {
        setPageTitle(sample.value?.batch_number);
      }
    }
    if (
      department.value &&
      project.value &&
      chapter.value &&
      sample.value &&
      project.value.departments.find(
        (d) =>
          department.value && d.project_department_id === department.value.project_department_id
      ) &&
      department.value.chapters.find(
        (c) => chapter.value && c.chapter_id === chapter.value.chapter_id
      )
    ) {
      const department_id =
        typeof chapter.value.department_id === 'number'
          ? chapter.value.department_id
          : chapter.value.department_id.project_department_id;
      newGenerateAnalyticalRequestData.value = {
        name: sample.value.analytical_request?.name || '',
        sample_number: sample.value.sample_number,
        kolona_id: sample.value.kolona_id || null,
        instrument_id: sample.value.instrument_id || null,
        sequence_name: sample.value.sequence_name || '',
        method_name: sample.value.method_name || '',
        analysis_status: sample.value.analysis_status || '',
        note_or_specification: sample.value.note_or_specification || '',
        result: sample.value.result || '',
        files_ids: sample.value.files.map((f) => f.file_id),
        files: sample.value.files,
        sample_id: sample.value.sample_id,
        samples: buildSamples(sample.value)
      };
      baseDataLoaded.value = true;
    } else {
      if (project.value && department.value && chapter.value) {
        router.push({
          name: 'ChapterDetail',
          params: {
            project_id: project.value.project_id.toString(),
            chapter_id: chapter.value.chapter_id.toString(),
            project_department_id: department.value.project_department_id.toString()
          }
        });
      } else if (project.value && department.value) {
        router.push({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value.project_id.toString(),
            project_department_id: department.value.project_department_id.toString()
          }
        });
      } else if (project.value) {
        router.push({
          name: 'ProjectDetail',
          params: { project_id: project.value.project_id.toString() }
        });
      } else {
        router.push({ name: 'ListOfProjects' });
      }
    }
  };

  const buildSamples = (sample: SampleI) => {
    return [
      {
        technique_id: sample.technique_id,
        note_or_specification: sample.note_or_specification,
        result: sample.result,
        analysis_status: sample.analysis_status
      }
    ];
  };

  watch([project_id, project_department_id, chapter_id], () => {
    loadExecute();
  });

  const breadcrumbItems = computed<BreadcrumbI[]>(() => {
    return [
      {
        title: 'Projekty',
        disabled: false,
        href: router.resolve({ name: 'Projects' }).href
      },
      {
        title: project.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'ProjectDetail',
          params: { project_id: project.value?.project_id }
        }).href
      },
      {
        title: department.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id
          }
        }).href
      },
      {
        title: chapter.value?.chapter_title ?? '',
        disabled: false,
        href: router.resolve({
          name: 'ChapterDetail',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id,
            chapter_id: chapter.value?.chapter_id
          }
        }).href
      },
      {
        title: 'Vložit analýzu',
        disabled: true,
        href: '#'
      }
    ];
  });

  const goBack = () => {
    if (redirectStore.previousRoute) {
      router.go(-1);
      redirectStore.clearPreviousRoute();
    }
  };

  const CreateSyntheticTemplateForm = ref();
  async function submitFormToValidate() {
    if (CreateSyntheticTemplateForm.value.isValid && newGenerateAnalyticalRequestData.value) {
      newGenerateAnalyticalRequestData.value.files_ids =
        newGenerateAnalyticalRequestData.value.files.map((f) => f.file_id);
      const data = {
        instrument_id: newGenerateAnalyticalRequestData.value.instrument_id ?? null,
        kolona_id: newGenerateAnalyticalRequestData.value.kolona_id ?? null,
        sequence_name: newGenerateAnalyticalRequestData.value.sequence_name ?? '',
        method_name: newGenerateAnalyticalRequestData.value.method_name ?? '',
        analysis_status: newGenerateAnalyticalRequestData.value.samples[0].analysis_status ?? '',
        note_or_specification:
          newGenerateAnalyticalRequestData.value.samples[0].note_or_specification ?? '',
        result: newGenerateAnalyticalRequestData.value.samples[0].result ?? '',
        parameters: [],
        files_ids: newGenerateAnalyticalRequestData.value.files_ids ?? [],
        version_id: null,
        preparation_of_standard_and_sample: ''
      } as SampleModalUpdatePostDataI;

      const res = await samplesStore.updateSampleReAnalyze(
        newGenerateAnalyticalRequestData.value.sample_id,
        data
      );
      if (res) {
        goBack();
      }
    }
  }

  watch(
    () => newGenerateAnalyticalRequestData.value?.files,
    () => {
      if (newGenerateAnalyticalRequestData.value) {
        newGenerateAnalyticalRequestData.value.files_ids =
          newGenerateAnalyticalRequestData.value.files.map((f) => f.file_id);
      }
    }
  );

  const getAllFiles = async () => {
    const files = await formsStore.getAllFiles();
    if (newGenerateAnalyticalRequestData.value && files !== false) {
      newGenerateAnalyticalRequestData.value.files = files;
    }
  };
</script>
<template>
  <LoaderWrapper
    v-if="!project || !department || !chapter || newGenerateAnalyticalRequestData === null"
  />
  <template v-else>
    <TopPageBreadcrumb title="Vložit analýzu" :_breadcrumbs="breadcrumbItems" />
    <v-row class="justify-content-end">
      <v-col cols="12">
        <UiParentCard class="pa-0" :loading="!baseDataLoaded">
          <template #action>
            <v-row justify="space-between" class="align-center">
              <v-col cols="12">
                <div class="d-flex gap-2 justify-end flex-wrap">
                  <v-btn
                    v-if="project?.project_id"
                    variant="flat"
                    color="error"
                    :disabled="!baseDataLoaded"
                    @click.prevent="goBack()"
                  >
                    Zpět
                  </v-btn>
                  <v-btn
                    v-if="project?.project_id"
                    variant="flat"
                    color="primary"
                    :disabled="!baseDataLoaded"
                    type="submit"
                    form="form-edit-form"
                  >
                    Uložit analýzu
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </template>

          <v-form
            id="form-edit-form"
            ref="CreateSyntheticTemplateForm"
            @submit.prevent="submitFormToValidate"
          >
            <v-row>
              <v-col cols="12" md="6">
                <v-label class="mb-2">Název vzorku</v-label>
                <v-text-field
                  v-model="newGenerateAnalyticalRequestData.name"
                  :rules="itemRequiredRule"
                  single-line
                  placeholder="Zadejte název"
                  hide-details="auto"
                  variant="outlined"
                  rounded="sm"
                  :readonly="true"
                ></v-text-field>
              </v-col>

              <v-col cols="12" md="6">
                <v-label class="mb-2">Číslo vzorku</v-label>
                <v-text-field
                  v-model="newGenerateAnalyticalRequestData.sample_number"
                  :rules="itemRequiredRule"
                  single-line
                  placeholder="Zadejte číslo vzorku"
                  hide-details="auto"
                  variant="outlined"
                  rounded="sm"
                  :readonly="true"
                ></v-text-field>
              </v-col>

              <v-col cols="12">
                <v-label class="mb-2">Zvolte techniky</v-label>
                <InsertAnalysisParametersFormTable
                  v-model:="newGenerateAnalyticalRequestData.samples"
                  :project_id="department.project_id"
                ></InsertAnalysisParametersFormTable>
              </v-col>

              <v-col cols="12">
                <v-label class="mb-2">Přílohy</v-label>
                <FileUploader
                  v-model="newGenerateAnalyticalRequestData.files"
                  :disabled="true"
                  :process-save-file="formsStore.addFileToForm"
                  :process-remove-file="formsStore.deleteFileFromForm"
                  :uppy-options="{
                    height: 250,
                    restrictions: {
                      maxNumberOfFiles: 10,
                      minNumberOfFiles: 0,
                      allowedFileTypes: null
                    }
                  }"
                />
                <FileSection
                  :files="newGenerateAnalyticalRequestData.files"
                  :custom-remove-file="true"
                  @reload="getAllFiles"
                  @file-remove="
                    async (file_id: number) => {
                      const res = await filesStore.deleteFile(file_id);
                      if (res) {
                        getAllFiles();
                      }
                    }
                  "
                />
              </v-col>
            </v-row>
          </v-form>
        </UiParentCard>
      </v-col>

      <ConfirmDlg ref="ConfirmRef" />
    </v-row>
  </template>
</template>
