import BaseConstructor from '@/utils/BaseConstructor';
import {
  loadParamsFailedReloadNotification,
  reloadWithoutParams,
  revertServerOptionsFromLocation,
  stringifyServerOptions,
  prepareJsonParams,
  type PaginatorData,
  type BasePaginatorResponseI,
  type BaseResponseI
} from '@/utils/axios';
import { fetchWrapper } from '@/utils/helpers/fetch-wrapper';
import { notification } from 'ant-design-vue';
import { defineStore } from 'pinia';
import type { ServerOptions } from 'vue3-easy-data-table';
import { File, type FileDto } from '../files';
import { type GetAllOptions } from '../projects';
import {
  Sample,
  type NewSampleDataI,
  type SampleDto,
  type SimpleSampleDto,
  SimpleSample
} from '../sample/samples';
import { type ChapterSearchDto, ChapterSearch } from '../chapters';
import {
  type FormForSearchSimpleDto,
  type FormForSearchSimpleI,
  FormForSearchSimple
} from '../forms';
import type { InsertAnalysisParameterI } from '@/views/pages/dashboard/projects/project/department/chapter/experiment/request/components/InsertAnalysisParametersFormTable.vue';
import type { PaginatorRequestDataI } from '../projects';
export enum AnalyticalRequestStatus {
  CREATED = 'created',
  IN_PROGRESS = 'in_progress',
  DONE = 'done',
  NA = 'na',
  CANCELED = 'canceled'
}

export enum AnalyticalRequestType {
  INTERNAL = 'internal',
  EXTERNAL = 'external'
}

export enum NewAnalyticalRequestType {
  NORMAL = 'normal',
  STANDARD_QC = 'standard_qc',
  STANDARD_RD = 'standard_rd'
}

export interface BatchNumbersDto {
  batch_number_id: number;
  batch_number: string;
  project_department_id: number;
  created_at: string;
  updated_at: string;
  project_department?: DepartmentDto;
}

export interface BatchNumbersI {
  batch_number_id: number;
  batch_number: string;
  project_department_id: number;
  created_at: Date;
  updated_at: Date;
  project_department?: Department;
}

export class BatchNumbers extends BaseConstructor<BatchNumbersI>() implements BatchNumbersI {
  constructor(data: BatchNumbersDto) {
    super(data as unknown as BatchNumbersI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    if (data.project_department) {
      this.project_department = new Department(data.project_department);
    }
  }
}

export interface AnalyticalRequestDto {
  analytical_request_id: number;
  batch_number_id: number;
  sample_number: string;
  name: string;
  status: string;
  request_type: string;
  type: string;
  created_at: string;
  updated_at: string;
  samples: SampleDto[];
  files: FileDto[];
  batch_number?: BatchNumbersDto;
}

export interface AnalyticalRequestI {
  analytical_request_id: number;
  batch_number_id: number;
  sample_number: string;
  name: string;
  status: AnalyticalRequestStatus;
  request_type: AnalyticalRequestType;
  type: string;
  created_at: Date;
  updated_at: Date;
  samples: Sample[];
  files: File[];
  batch_number?: BatchNumbers;
}

export class AnalyticalRequest
  extends BaseConstructor<AnalyticalRequestI>()
  implements AnalyticalRequestI
{
  constructor(data: AnalyticalRequestDto) {
    super(data as unknown as AnalyticalRequestI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    this.files = data.files.map((file) => new File(file));
    this.samples = data.samples.map((sample) => new Sample(sample));
    if (data.batch_number) {
      this.batch_number = new BatchNumbers(data.batch_number);
    }
  }

  get statusColor(): string {
    switch (this.status) {
      case AnalyticalRequestStatus.CREATED:
        return 'blue';
      case AnalyticalRequestStatus.IN_PROGRESS:
        return 'orange';
      case AnalyticalRequestStatus.DONE:
        return 'green';
      case AnalyticalRequestStatus.NA:
        return 'gray';
      case AnalyticalRequestStatus.CANCELED:
        return 'red';
      default:
        return 'blue';
    }
  }
}

interface AnalyticalRequestModalOptionsI {
  isEditing: boolean;
  isCreating: boolean;
  baseData: AnalyticalRequest | undefined;
  newData: AnalyticalRequestModalNewDataI | undefined;
}

interface AnalyticalRequestModalNewDataI {
  project_id: number | undefined;
  type: AnalyticalRequestType;
  project_department_id: number | undefined;
  batch_number: string | undefined;
  sample_number: string | undefined;
  name: string | undefined;
  samples: NewSampleDataI[];
  files_ids: number[];
  confirm: boolean | false;
}

export interface AnalyticalRequestModalNewPostDataI {
  project_id: number;
  type: NewAnalyticalRequestType;
  project_department_id: number;
  batch_number: string;
  sample_number: string;
  name: string;
  samples: NewSampleDataI[] | InsertAnalysisParameterI[];
  files_ids: number[];
  analytical_department_request_type?: string;
  user_id?: number;
}

export enum DepartmentEnum {
  SYNTHETIC = 'synthetic',
  ANALYTICAL = 'analytical',
  TECHNICAL = 'technical'
}

export interface DepartmentDto {
  project_department_id: number;
  project_id: number;
  shortcut: string;
  number: string;
  status: string;
  type: DepartmentEnum;
  created_at: string;
  updated_at: string;
  project?: {
    user_id: number;
    name: string;
    status: string;
    created_at: string;
    updated_at: string;
    user: {
      user_id: number;
      created_at: string;
      updated_at: string;
      first_name: string;
      last_name: string;
      name_shortcut: string;
      user_email: string;
      status: string;
    };
  };
}
export interface DepartmentI {
  project_department_id: number;
  project_id: number;
  shortcut: string;
  number: string;
  status: string;
  type: DepartmentEnum;
  created_at: Date;
  updated_at: Date;
  project?: {
    user_id: number;
    name: string;
    status: string;
    created_at: Date;
    updated_at: Date;
    user: {
      user_id: number;
      created_at: Date;
      updated_at: Date;
      first_name: string;
      last_name: string;
      name_shortcut: string;
      user_email: string;
      status: string;
    };
  };
}

export class Department extends BaseConstructor<DepartmentI>() implements DepartmentI {
  constructor(data: DepartmentDto) {
    super(data as unknown as DepartmentI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    if (data.project) {
      this.project = {
        user_id: data.project.user_id,
        name: data.project.name,
        status: data.project.status,
        created_at: new Date(data.project.created_at),
        updated_at: new Date(data.project.updated_at),
        user: {
          user_id: data.project.user.user_id,
          created_at: new Date(data.project.user.created_at),
          updated_at: new Date(data.project.user.updated_at),
          first_name: data.project.user.first_name,
          last_name: data.project.user.last_name,
          name_shortcut: data.project.user.name_shortcut,
          user_email: data.project.user.user_email,
          status: data.project.user.status
        }
      };
    }
  }

  get getName() {
    switch (this.type) {
      case DepartmentEnum.SYNTHETIC:
        return 'Syntetické oddělení';
      case DepartmentEnum.ANALYTICAL:
        return 'Analytické oddělení';
      case DepartmentEnum.TECHNICAL:
        return 'Technologické oddělení';
    }
  }

  isSynthetic() {
    return this.type === DepartmentEnum.SYNTHETIC;
  }

  isAnalytical() {
    return this.type === DepartmentEnum.ANALYTICAL;
  }

  isTechnical() {
    return this.type === DepartmentEnum.TECHNICAL;
  }
}

export interface AnalyticalRequestForSampleDto {
  analytical_request_id: number;
  batch_number_id: number;
  sample_number: string;
  name: string;
  status: string;
  request_type: string;
  type: string;
  created_at: string;
  updated_at: string;
  batch_number: BatchNumbersDto | null;
  project_department?: DepartmentDto;
}

export interface AnalyticalRequestForSampleI {
  analytical_request_id: number;
  batch_number_id: number;
  sample_number: string;
  name: string;
  status: AnalyticalRequestStatus;
  request_type: AnalyticalRequestType;
  type: string;
  created_at: Date;
  updated_at: Date;
  batch_number: BatchNumbersI | null;
  project_department?: DepartmentI;
}

export class AnalyticalRequestForSample
  extends BaseConstructor<AnalyticalRequestForSampleI>()
  implements AnalyticalRequestForSampleI
{
  constructor(data: AnalyticalRequestForSampleDto) {
    super(data as unknown as AnalyticalRequestForSampleI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);
    if (data.batch_number) {
      this.batch_number = new BatchNumbers(data.batch_number);
    }
    if (data.project_department) {
      this.project_department = new Department(data.project_department);
    }
  }

  get statusColor(): string {
    switch (this.status) {
      case AnalyticalRequestStatus.CREATED:
        return 'blue';
      case AnalyticalRequestStatus.IN_PROGRESS:
        return 'orange';
      case AnalyticalRequestStatus.DONE:
        return 'green';
      case AnalyticalRequestStatus.NA:
        return 'gray';
      case AnalyticalRequestStatus.CANCELED:
        return 'red';
      default:
        return 'blue';
    }
  }
}

export interface AnalyticalRequestForSearchDto {
  analytical_request_id: number;
  batch_number_id: number;
  sample_number: string;
  name: string;
  status: string;
  request_type: string;
  type: string;
  created_at: string;
  updated_at: string;
  project_department_id: number;
  batch_number: BatchNumbersDto | null;
  project_department?: DepartmentDto | null;
  samples?: SimpleSampleDto[];
  form?: FormForSearchSimpleDto | null;
  chapter_id?: number;
  project_name?: string;
  project_id?: number;
  chapter?: ChapterSearchDto;
  has_completed_samples?: boolean;
}

export interface AnalyticalRequestForSearchI {
  analytical_request_id: number;
  batch_number_id: number;
  sample_number: string;
  name: string;
  status: AnalyticalRequestStatus;
  request_type: AnalyticalRequestType;
  type: NewAnalyticalRequestType;
  created_at: Date;
  updated_at: Date;
  batch_number: BatchNumbersI | null;
  project_department_id: number;
  project_department?: DepartmentI | null;
  samples?: SimpleSample[];
  form?: FormForSearchSimple | null;
  chapter_id?: number;
  project_name?: string;
  project_id?: number;
  chapter?: ChapterSearch;
  has_completed_samples?: boolean;
}

export class AnalyticalRequestForSearch
  extends BaseConstructor<AnalyticalRequestForSearchI>()
  implements AnalyticalRequestForSearchI
{
  constructor(data: AnalyticalRequestForSearchDto) {
    super(data as unknown as AnalyticalRequestForSearchI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);
    if (data.batch_number) {
      this.batch_number = new BatchNumbers(data.batch_number);
    }
    if (data.project_department) {
      this.project_department = new Department(data.project_department);
    }
    if (data.samples) {
      this.samples = data.samples.map((sample) => new SimpleSample(sample));
    }
    if (data.form) {
      this.form = new FormForSearchSimple(data.form);
    }
  }
}

interface AnalyticalRequestsStateI {
  analyticalRequests: AnalyticalRequest[];
  analyticalRequest: AnalyticalRequest | null;
  loading: boolean;
  loadingSamples: boolean;
  loadingBatches: boolean;
  showAnalyticalRequestModal: boolean;
  modalOptions: AnalyticalRequestModalOptionsI | undefined;

  analyticalRequestsGlobal: AnalyticalRequest[] | null;

  totalItems?: number;
  options: ServerOptions;
  search: string | undefined;
  search_type?: 'AND' | 'OR';
  fixedFilterOptions: null | Array<{
    column: string;
    value: null | number | string | string[] | number[] | object | object[];
  }>;
  batches: BatchNumbers[];
  batchesOptions: PaginatorRequestDataI<BatchNumbers>;
}

export const useAnalyticalRequestsStore = defineStore({
  id: 'analyticalRequests',
  state: () =>
    ({
      analyticalRequest: null,
      analyticalRequests: [],
      loading: false,
      loadingBatches: false,
      loadingSamples: false,
      batches: [],
      showAnalyticalRequestModal: false,
      modalOptions: undefined,
      analyticalRequestsGlobal: null,

      search: undefined,
      search_type: undefined,
      totalItems: undefined,
      batchesOptions: {
        loading: false,
        results: [],

        search: undefined,
        search_type: 'OR',

        search_columns: [],
        totalItems: 0,
        options: {
          page: 1,
          rowsPerPage: 25,
          sortBy: [],
          sortType: []
        },
        filterOptions: null
      },
      options: {
        page: 1,
        rowsPerPage: 25,
        sortBy: [],
        sortType: ['desc', 'asc']
      },
      fixedFilterOptions: null
    }) as AnalyticalRequestsStateI,

  actions: {
    async getAnalyticalRequests(
      setData: boolean = true,
      options: GetAllOptions | undefined = {
        search_columns: ['name', 'sample_number']
      },
      search_type: 'AND' | 'OR' = 'OR'
    ): Promise<{
      data: AnalyticalRequest[];
      totalItems: number;
    }> {
      this.loading = true;
      this.loadingSamples = true;
      const URL =
        `${import.meta.env.VITE_API_URL}/analytical-requests/` +
        '?' +
        stringifyServerOptions(
          options && options.options ? options.options : this.options,
          options && options.search ? options.search : (this.search ?? null),
          options && options.search_columns ? options.search_columns : [],
          options && options.fixedFilterOptions ? options.fixedFilterOptions : null,
          this.search_type ?? search_type
        );

      return fetchWrapper
        .get(URL)
        .then((res: BasePaginatorResponseI<AnalyticalRequestDto>) => {
          this.loading = false;
          this.loadingSamples = false;
          if (res.status_code === 200) {
            this.totalItems = res.data.total_items;
            if (setData) {
              this.analyticalRequests = res.data.items.map(
                (analyticalRequest) => new AnalyticalRequest(analyticalRequest)
              );
            }

            return {
              data: res.data.items.map(
                (analyticalRequest) => new AnalyticalRequest(analyticalRequest)
              ),
              totalItems: res.data.total_items
            };
          }

          return {
            data: [],
            totalItems: 0
          };
        })
        .catch((res) => {
          this.loading = false;
          this.loadingSamples = false;
          if (res.status_code === 400) {
            loadParamsFailedReloadNotification('Načtení žádostí selhalo', res.error, () => {
              const data = reloadWithoutParams();
              this.options = data.options;
              this.search = data.term ?? undefined;
            });
          }

          return {
            data: [],
            totalItems: 0
          };
        });
    },

    setParamsFromLocation(): void {
      const data = revertServerOptionsFromLocation();
      this.options = data.options;
      this.search = data.term ?? undefined;
    },

    newAnalyticalRequest() {
      this.modalOptions = {
        isEditing: false,
        isCreating: true,
        baseData: undefined,
        newData: {
          project_id: undefined,
          type: AnalyticalRequestType.INTERNAL,
          project_department_id: undefined,
          batch_number: undefined,
          sample_number: undefined,
          name: undefined,
          samples: [],
          files_ids: [],
          confirm: false
        }
      };
    },

    createAnalyticalRequest(_data: AnalyticalRequestModalNewPostDataI | undefined = undefined) {
      if (!_data) {
        if (!this.modalOptions?.newData || this.modalOptions?.newData?.confirm === false) {
          notification.error({ message: 'Musíte potvrdit souhlas s vytvořením žádosti' });
          return false;
        }
      }

      this.loading = true;
      const data = {
        project_id: _data?.project_id ?? this.modalOptions?.newData?.project_id ?? 0,
        type: _data?.type ?? this.modalOptions?.newData?.type,
        project_department_id:
          _data?.project_department_id ?? this.modalOptions?.newData?.project_department_id ?? 0,
        batch_number: _data?.batch_number ?? this.modalOptions?.newData?.batch_number ?? null,
        sample_number: _data?.sample_number ?? this.modalOptions?.newData?.sample_number ?? null,
        name: _data?.name ?? this.modalOptions?.newData?.name ?? '',
        samples: _data?.samples ?? this.modalOptions?.newData?.samples,
        files_ids: _data?.files_ids ?? this.modalOptions?.newData?.files_ids,
        analytical_department_request_type: _data?.analytical_department_request_type ?? null
      } as AnalyticalRequestModalNewPostDataI;

      if (typeof _data?.user_id === 'number') {
        data.user_id = _data.user_id;
      }

      return fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/analytical-request/`, data)
        .then((res: BaseResponseI<AnalyticalRequestDto>) => {
          this.loading = false;

          if (res.status_code === 200) {
            this.showAnalyticalRequestModal = false;

            notification.success({
              message: 'Vytvoření žádosti proběhlo v pořádku'
            });

            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Vytvoření žádosti selhalo', description: res.error });
          } else {
            notification.error({ message: 'Vytvoření žádosti selhalo' });
            this.showAnalyticalRequestModal = false;
          }

          return false;
        });
    },

    resetModal() {
      this.showAnalyticalRequestModal = false;
      if (this.modalOptions) this.modalOptions.baseData = undefined;
      if (this.modalOptions) this.modalOptions.newData = undefined;
    },

    async addFileToAnalyticalRequest(file_id: number) {
      if (!this.analyticalRequest) {
        notification.error({ message: 'Není vybran žádost' });
        return false;
      }

      if (!this.modalOptions?.newData) {
        notification.error({ message: 'Není co upravovat' });
        return;
      }
      return fetchWrapper
        .post(
          `${import.meta.env.VITE_API_URL}/analytical-request/${this.analyticalRequest.analytical_request_id}/file/${file_id}`
        )
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Soubor byl přidán k žádosti' });
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Přidání souboru k žádosti selhalo',
              description: res.error
            });
          } else {
            notification.error({ message: 'Přidání souboru k žádosti selhalo' });
          }

          return false;
        });
    },
    async addFileToAnalyticalRequestNew(file_id: number, analytical_request_id: number) {
      if (!analytical_request_id) {
        notification.error({ message: 'Není vybran žádost' });
        return false;
      }
      return fetchWrapper
        .post(
          `${import.meta.env.VITE_API_URL}/analytical-request/${analytical_request_id}/file/${file_id}`
        )
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Soubor byl přidán k žádosti' });
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Přidání souboru k žádosti selhalo',
              description: res.error
            });
          } else {
            notification.error({ message: 'Přidání souboru k žádosti selhalo' });
          }

          return false;
        });
    },

    async deleteFileFromAnalyticalRequest(file_id: number, analytical_request_id: number) {
      if (!analytical_request_id) {
        notification.error({ message: 'Není vybran žádost' });
        return false;
      }

      return fetchWrapper
        .delete(
          `${import.meta.env.VITE_API_URL}/analytical-request/${analytical_request_id}/files/${file_id}`
        )
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Soubor byl odebrán z žádosti' });
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Odebrání souboru z žádosti selhalo',
              description: res.error
            });
          } else {
            notification.error({ message: 'Odebrání souboru z žádosti selhalo' });
          }

          return false;
        });
    },

    async requestProcedureForAnalyticalRequest() {
      if (!this.analyticalRequest) {
        notification.error({ message: 'Není vybrána žádost' });
        return false;
      }

      return fetchWrapper
        .post(
          `${import.meta.env.VITE_API_URL}/analytical-request/${this.analyticalRequest.analytical_request_id}/request_procedure`
        )
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Žádost o proceduru byla odeslána' });
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Odeslání žádosti o proceduru selhalo',
              description: res.error
            });
          } else {
            notification.error({ message: 'Odeslání žádosti o proceduru selhalo' });
          }

          return false;
        });
    },
    async getBatches(setData: boolean = true): Promise<{
      data: BatchNumbers[];
      totalItems: number;
    }> {
      this.loading = true;
      this.loadingBatches = true;
      const URL =
        `${import.meta.env.VITE_API_URL}/batch-numbers/` +
        `?` +
        stringifyServerOptions(
          this.batchesOptions.options,
          this.batchesOptions.search ?? null,
          this.batchesOptions.search_columns,
          this.batchesOptions.filterOptions
        );

      return fetchWrapper
        .get(URL)
        .then((res: BasePaginatorResponseI<BatchNumbersDto>) => {
          if (res.status_code === 200) {
            this.loading = false;
            this.loadingBatches = false;
            this.batchesOptions.totalItems = res.data.total_items;
            if (setData) {
              this.batches = res.data.items.map((batch) => new BatchNumbers(batch));
            }

            return {
              data: res.data.items.map((batch) => new BatchNumbers(batch)),
              totalItems: res.data.total_items
            };
          }

          return {
            data: [],
            totalItems: 0
          };
        })
        .catch((res) => {
          this.loading = false;
          this.loadingBatches = false;
          if (res.status_code === 400) {
            loadParamsFailedReloadNotification('Načtení šarží selhalo', res.error, () => {
              const data = reloadWithoutParams();
              this.batchesOptions.options = data.options;
              this.batchesOptions.search = data.term ?? undefined;
            });
          }

          return {
            data: [],
            totalItems: 0
          };
        });
    }
  }
});
