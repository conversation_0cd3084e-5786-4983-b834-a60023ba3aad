<template>
  <v-expansion-panel>
    <v-expansion-panel-title>
      <v-row align="center">
        <v-col cols="12" sm="2">
          <span class="font-weight-bold capitalize-first-letter">
            {{ analyticalRequest.name || '/' }}
          </span>
        </v-col>
        <v-col cols="12" sm="2">
          {{ analyticalRequest?.batch_number?.batch_number || '/' }}
        </v-col>
        <v-col cols="12" sm="2">
          {{ analyticalRequest.sample_number || '/' }}
        </v-col>
        <v-col v-if="!isStandard" cols="12" sm="2"></v-col>
        <v-col v-if="isStandard" cols="12" sm="2">
          <v-menu>
            <template #activator="{ props }">
              <v-btn size="small" variant="tonal" color="primary" v-bind="props">Exportovat</v-btn>
            </template>

            <v-list>
              <v-list-item @click="samplesStore.exportSamples(analyticalRequest)">
                <v-list-item-title>Exportovat do PDF</v-list-item-title>
              </v-list-item>
              <v-list-item @click="samplesStore.exportSamplesToWord(analyticalRequest)">
                <v-list-item-title>Exportovat do Wordu</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-menu>
        </v-col>

        <v-col v-if="isStandard" cols="12" sm="2">
          <v-btn
            size="small"
            variant="tonal"
            color="primary"
            @click.prevent.stop="
              openSimpleFileModalRequest(analyticalRequest.analytical_request_id)
            "
          >
            Vložit postup
          </v-btn>
        </v-col>
        <v-col cols="12" sm="2">
          {{ toLocale(analyticalRequest.created_at) || '/' }}
        </v-col>
      </v-row>
    </v-expansion-panel-title>
    <v-expansion-panel-text>
      <v-row v-if="analyticalRequest.samples.length > 0" class="ml-4">
        <v-col cols="12">
          <v-expansion-panels>
            <v-expansion-panel
              v-for="sample in analyticalRequest.samples"
              :key="sample.sample_id"
              :hide-actions="
                sample.result.length === 0 &&
                sample.note_or_specification.length === 0 &&
                sample.files.length === 0
              "
              :readonly="
                isParentClosed ||
                (sample.result.length === 0 &&
                  sample.note_or_specification.length === 0 &&
                  sample.files.length === 0) ||
                !project_permision
              "
            >
              <v-expansion-panel-title>
                <v-row align="center">
                  <v-col cols="12" sm="4">
                    <div style="display: flex; flex-direction: row; align-items: center">
                      <router-link
                        :to="{
                          name: 'SampleDetail',
                          params: {
                            sample_id: sample.sample_id,
                            type: sample.type === SampleType.INTERNAL ? 'rd' : 'qc-a-vt'
                          }
                        }"
                        target="_blank"
                      >
                        <v-icon color="primary">mdi-open-in-new</v-icon>
                      </router-link>
                      {{ sample.technique?.name || '/' }}
                    </div>
                  </v-col>
                  <v-col cols="12" sm="2">
                    {{ sample.user?.getName || '/' }}
                  </v-col>
                  <v-col cols="12" sm="2">
                    <v-chip
                      v-if="sample.status === SampleStatus.CREATED"
                      color="primary"
                      size="small"
                      label
                    >
                      Vytvořeno
                    </v-chip>
                    <v-chip
                      v-if="sample.status === SampleStatus.REANALYSIS"
                      color="warning"
                      size="small"
                      label
                    >
                      Reanalýza
                    </v-chip>
                    <v-chip
                      v-if="sample.status === SampleStatus.CANCELLED"
                      color="error"
                      size="small"
                      label
                    >
                      Zrušeno
                    </v-chip>
                    <v-chip
                      v-if="sample.status === SampleStatus.DONE"
                      color="success"
                      size="small"
                      label
                    >
                      Dokončeno
                    </v-chip>
                    <!-- Link/Unlink buttons for all standard samples - positioned next to status chips -->
                    <v-btn
                      v-if="
                        isStandard &&
                        sample.status === SampleStatus.CREATED &&
                        !sample.linked_sample_id
                      "
                      size="small"
                      variant="tonal"
                      color="success"
                      class="ml-2"
                      @click.prevent.stop="openSampleLinkModalForSample(sample)"
                    >
                      Napárovat
                    </v-btn>
                    <v-btn
                      v-if="isStandard && sample.linked_sample_id"
                      size="small"
                      variant="tonal"
                      color="warning"
                      class="ml-2"
                      @click.prevent.stop="unlinkSampleById(sample)"
                    >
                      Odpojit
                    </v-btn>
                  </v-col>
                  <v-col
                    v-if="
                      sample.technique?.type === TechniqueType.EXTERNAL &&
                      extractTextFromHtml(sample.analysis_status) === AnalysisStatus.NA
                    "
                    cols="12"
                    sm="2"
                  >
                    <v-btn
                      size="small"
                      variant="tonal"
                      color="primary"
                      @click.prevent.stop="saveCurrentRoute(sample.sample_id)"
                    >
                      Vložit analýzu
                    </v-btn>
                  </v-col>

                  <template
                    v-else-if="
                      sample.type === SampleType.INTERNAL ||
                      (sample.type === SampleType.EXTERNAL &&
                        (extractTextFromHtml(sample.analysis_status) === AnalysisStatus.COMPLIANT ||
                          extractTextFromHtml(sample.analysis_status) ===
                            AnalysisStatus.NON_COMPLIANT))
                    "
                  >
                    <v-col cols="12" sm="2">
                      <v-menu>
                        <template #activator="{ props }">
                          <v-btn size="small" variant="tonal" color="primary" v-bind="props">
                            Exportovat
                          </v-btn>
                        </template>

                        <v-list>
                          <v-list-item
                            @click="
                              samplesStore.exportSample(sample.sample_id, sample.sample_number)
                            "
                          >
                            <v-list-item-title>Exportovat do PDF</v-list-item-title>
                          </v-list-item>
                          <v-list-item
                            @click="
                              samplesStore.exportSampleToWord(
                                sample.sample_id,
                                sample.sample_number
                              )
                            "
                          >
                            <v-list-item-title>Exportovat do Wordu</v-list-item-title>
                          </v-list-item>
                        </v-list>
                      </v-menu>
                    </v-col>
                  </template>

                  <v-col cols="12" sm="2">
                    {{ toLocale(sample.created_at) || '/' }}
                  </v-col>
                  <v-col v-if="sample.linked_sample_id" cols="12" sm="2">
                    <v-chip color="info" size="small" label>
                      <v-icon size="small" class="mr-1">mdi-link</v-icon>
                      Napárované
                    </v-chip>
                  </v-col>
                </v-row>
              </v-expansion-panel-title>
              <v-expansion-panel-text>
                <v-row no-gutters>
                  <v-col class="text--secondary" cols="12">
                    <!-- Linked Sample Information -->
                    <v-alert
                      v-if="sample.linked_sample_id && sample.linked_sample"
                      type="info"
                      variant="tonal"
                      class="mb-4"
                    >
                      <template #prepend>
                        <v-icon>mdi-link</v-icon>
                      </template>
                      <div>
                        <strong>
                          Napárovaný vzorek: {{ sample.linked_sample.sample_number || '/' }}
                        </strong>
                        <div class="mt-1">
                          <span class="text-caption text-medium-emphasis">
                            Název:
                            {{ sample.linked_sample.analytical_request?.name || '/' }}
                          </span>
                        </div>
                        <div class="mt-1">
                          <span class="text-caption text-medium-emphasis">
                            Šarže:
                            {{
                              sample.linked_sample.analytical_request?.batch_number?.batch_number ||
                              '/'
                            }}
                            | Technika:
                            {{ sample.linked_sample.technique?.name || '/' }}
                          </span>
                        </div>
                      </div>
                    </v-alert>
                    <v-fade-transition leave-absolute>
                      <v-row style="width: 100%" no-gutters>
                        <v-col
                          v-if="
                            sample.result.length > 0 ||
                            sample.note_or_specification.length > 0 ||
                            sample.files.length > 0
                          "
                          cols="12"
                        >
                          <template
                            v-if="
                              sample.analysis_status.length > 0 &&
                              sample.type === SampleType.EXTERNAL
                            "
                          >
                            <v-label class="mb-2">Výsledek</v-label>
                            <v-col style="margin-left: -1em">
                              <v-chip
                                v-if="
                                  extractTextFromHtml(sample.analysis_status) === AnalysisStatus.NA
                                "
                                color="warning"
                                size="small"
                                label
                              >
                                N/A
                              </v-chip>
                              <v-chip
                                v-if="
                                  extractTextFromHtml(sample.analysis_status) ===
                                  AnalysisStatus.COMPLIANT
                                "
                                color="success"
                                size="small"
                                label
                              >
                                Vyhovuje
                              </v-chip>
                              <v-chip
                                v-if="
                                  extractTextFromHtml(sample.analysis_status) ===
                                  AnalysisStatus.NON_COMPLIANT
                                "
                                color="error"
                                size="small"
                                label
                              >
                                Nevyhovuje
                              </v-chip>
                            </v-col>
                          </template>
                          <template v-if="sample.result.length > 0">
                            <v-label class="mb-2">Závěr</v-label>
                            <EditorTextarea
                              v-model="sample.result"
                              :disabled="true"
                              :config="
                                {
                                  statusbar: true,
                                  resize: true,
                                  width: '100%',
                                  height: 200,
                                  toolbar: false
                                } as EditorManager & RawEditorOptions
                              "
                            />
                          </template>
                          <template
                            v-if="
                              sample.note_or_specification.length > 0 &&
                              sample.type === SampleType.EXTERNAL
                            "
                          >
                            <v-label class="my-2">Poznámka nebo specifikace</v-label>
                            <EditorTextarea
                              v-model="sample.note_or_specification"
                              :disabled="true"
                              :config="
                                {
                                  statusbar: true,
                                  resize: true,
                                  width: '100%',
                                  height: 200,
                                  toolbar: false
                                } as EditorManager & RawEditorOptions
                              "
                            />
                          </template>
                          <v-col cols="12" sm="2">
                            <v-btn
                              size="small"
                              variant="tonal"
                              color="primary"
                              @click.prevent.stop="openSimpleFileModal(sample.sample_id)"
                            >
                              Přiložit soubor
                            </v-btn>
                          </v-col>
                          <v-label class="my-2">Soubory</v-label>
                          <FileSection
                            :minimal="true"
                            :is-read-only="true"
                            :files="sample.files"
                            @reload="$emit('reload')"
                          />
                        </v-col>
                      </v-row>
                    </v-fade-transition>
                  </v-col>
                </v-row>
              </v-expansion-panel-text>
            </v-expansion-panel>
            <v-row>
              <v-col cols="12">
                <v-fade-transition leave-absolute>
                  <v-row no-gutters>
                    <v-col cols="12">
                      <FileSection
                        :files="analyticalRequest.files"
                        :custom-remove-file="true"
                        @reload="$emit('reload')"
                        @file-remove="
                          async (file_id: number) => {
                            const res =
                              await analyticalRequestsStore.deleteFileFromAnalyticalRequest(
                                file_id,
                                analyticalRequest.analytical_request_id
                              );
                            if (res) {
                              $emit('reload');
                            }
                          }
                        "
                      />
                    </v-col>
                  </v-row>
                </v-fade-transition>
              </v-col>
            </v-row>
          </v-expansion-panels>
        </v-col>
      </v-row>
      <NotFoundItem v-else>
        <template #notFound>Všechny vzorky byli zrušeny nebo dokončeny</template>
      </NotFoundItem>
    </v-expansion-panel-text>
  </v-expansion-panel>
  <FileModal
    v-if="simpleModal && selectedSample"
    v-model:show="simpleModal"
    :selected-sample="selectedSample"
    @reload="$emit('reload')"
  />
  <FileModalRequest
    v-if="simpleModalRequest && selectedRequest"
    v-model:show="simpleModalRequest"
    :selected-request="selectedRequest"
    @reload="$emit('reload')"
  />
  <SampleLinkModal
    v-if="sampleLinkModal && currentSampleForLinking"
    v-model:show="sampleLinkModal"
    :technique-id="currentSampleForLinking.technique_id"
    :batch-number="currentSampleForLinking.batch_number"
    :current-sample-id="currentSampleForLinking.sample_id"
    @reload="$emit('reload')"
  />
</template>

<script lang="ts" setup>
  import NotFoundItem from '@/components/shared/NotFoundItem.vue';
  import FileModal from '@/components/shared/file/SimpleFileModal.vue';
  import FileModalRequest from '@/components/shared/file/SimpleFileModalRequest.vue';
  import EditorTextarea from '@/components/shared/EditorTextarea.vue';
  import FileSection from '@/components/shared/file/FileSection.vue';
  import SampleLinkModal from './SampleLinkModal.vue';
  import { AnalysisStatus } from '@/stores/forms';
  import type { BatchNumber } from '@/stores/experiments';
  import { TechniqueType } from '@/stores/techniques';
  import {
    SampleStatus,
    SampleType,
    useSamplesStore,
    type SampleDto
  } from '@/stores/sample/samples';
  import { toLocale } from '@/utils/locales';
  import type { EditorManager, RawEditorOptions } from 'tinymce';
  import { useProjectsStore } from '@/stores/projects';
  import {
    AnalyticalRequestStatus,
    useAnalyticalRequestsStore,
    type AnalyticalRequest
  } from '../analyticalRequests';
  import { ref, computed } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { storeToRefs } from 'pinia';
  import { notification } from 'ant-design-vue';
  import { useRedirectStore } from '@/stores/redirect';
  import { isAllowed } from '@/utils/directive/isAllowed';
  const menu = ref(false); // The menu visibility state

  const redirectStore = useRedirectStore();
  const projectsStore = useProjectsStore();
  const { loading, project, department, chapter, project_permision } = storeToRefs(projectsStore);
  const route = useRoute();
  const router = useRouter();
  defineEmits(['reload']);
  const props = defineProps<{
    analyticalRequest: AnalyticalRequest;
    batchNumber?: BatchNumber;
    isStandard?: boolean | undefined;
    isParentClosed?: boolean | undefined;
    searchTerm?: string | undefined;
  }>();

  const samplesStore = useSamplesStore();
  const analyticalRequestsStore = useAnalyticalRequestsStore();

  const getChipColor = (status: AnalyticalRequestStatus) => {
    switch (status) {
      case AnalyticalRequestStatus.CREATED:
        return 'primary';
      case AnalyticalRequestStatus.IN_PROGRESS:
        return 'warning';
      case AnalyticalRequestStatus.CANCELED:
      case AnalyticalRequestStatus.NA:
        return 'error';
      case AnalyticalRequestStatus.DONE:
        return 'success';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: AnalyticalRequestStatus) => {
    switch (status) {
      case AnalyticalRequestStatus.CREATED:
        return 'Vytvořeno';
      case AnalyticalRequestStatus.IN_PROGRESS:
        return 'Probíhá';
      case AnalyticalRequestStatus.CANCELED:
      case AnalyticalRequestStatus.NA:
        return 'Zrušeno';
      case AnalyticalRequestStatus.DONE:
        return 'Dokončeno';
      default:
        return '/';
    }
  };
  const extractTextFromHtml = (htmlString: string): string => {
    const div = document.createElement('div');
    div.innerHTML = htmlString;
    return div.textContent || div.innerText || '';
  };
  const simpleModal = ref(false);
  const selectedSample = ref<number>();
  const openSimpleFileModal = (sample?: number) => {
    if (sample) {
      selectedSample.value = sample;
      simpleModal.value = !simpleModal.value;
    }
  };

  const selectedRequest = ref<number>();
  const simpleModalRequest = ref(false);
  const openSimpleFileModalRequest = (request?: number) => {
    if (request) {
      selectedRequest.value = request;
      simpleModalRequest.value = !simpleModalRequest.value;
    }
  };

  // Sample linking modal
  const sampleLinkModal = ref(false);
  const currentSampleForLinking = ref<{
    sample_id: number;
    technique_id: number;
    sample_number: string;
    batch_number: string;
  } | null>(null);

  // Check if any sample in the analytical request has linked_sample_id
  const hasLinkedSample = computed(() => {
    return props.analyticalRequest.samples.some((sample: any) => sample.linked_sample_id);
  });

  const openSampleLinkModalForSample = (sample: any) => {
    currentSampleForLinking.value = {
      sample_id: sample.sample_id,
      technique_id: sample.technique_id,
      sample_number: props.analyticalRequest.sample_number,
      batch_number: props.analyticalRequest.batch_number?.batch_number || ''
    };
    sampleLinkModal.value = true;
  };

  const unlinkSampleById = async (sample: any) => {
    if (sample.linked_sample_id) {
      const success = await samplesStore.unlinkSample(sample.sample_id, sample.linked_sample_id);
      if (success) {
        // Reload the page/component
        window.location.reload();
      }
    }
  };
  const project_id = computed(() => route.params.project_id as string);
  const projectId = computed(() => parseInt(project_id.value));

  const project_department_id = computed(() => route.params.project_department_id as string);
  const projectDepartmentId = computed(() => parseInt(project_department_id.value));

  const chapter_id = computed(() => route.params.chapter_id as string);
  const chapterId = computed(() => parseInt(chapter_id.value));

  const haveAll = computed(() => projectId.value && projectDepartmentId.value && chapterId.value);

  const saveCurrentRoute = async (sample_id: number) => {
    const currentRoute = { ...route };
    await redirectStore.setPreviousRoute(currentRoute);
    await redirectToExternalAnalysis(sample_id);
  };
  const redirectToExternalAnalysis = async (sample_id: number) => {
    if (project.value && department.value && chapter.value && haveAll.value && sample_id) {
      const go = router.resolve({
        name: 'ExternalInsertAnalyticalRequest',
        params: {
          project_id: project.value.project_id,
          project_department_id: department.value.project_department_id,
          chapter_id: chapter.value.chapter_id,
          sample_id: sample_id.toString()
        }
      });
      router.push(go);
    } else {
      notification.error({ message: 'Nepodařilo se přesměrovat na nový analytický požadavek' });
    }
  };

  const checkAdminPermission = () => {
    return isAllowed(['edit_all']);
  };
  const checkAdminViewPermission = () => {
    return isAllowed(['view_all']);
  };
</script>
