<template>
  <section class="my-5">
    <span class="text-h4"><PERSON><PERSON><PERSON><PERSON></span>
  </section>
  <v-row v-if="batchNumber.analytical_requests.length > 0">
    <v-col cols="12">
      <v-expansion-panels>
        <AnalyticalRequestItem
          v-for="analyticalRequest in filteredRequests"
          :key="analyticalRequest.analytical_request_id"
          :analytical-request="analyticalRequest"
          :batch-number="batchNumber"
          :is-standard="undefined"
          @reload="$emit('reload')"
        />
      </v-expansion-panels>
    </v-col>
  </v-row>
  <NotFoundItem v-else>
    <template #notFound>Nebyly nalezeny žádné <PERSON></template>
  </NotFoundItem>
</template>

<script lang="ts" setup>
  import NotFoundItem from '@/components/shared/NotFoundItem.vue';
  import type { BatchNumber } from '@/stores/experiments';
  import AnalyticalRequestItem from './AnalyticalRequestItem.vue';
  import { computed } from 'vue';

  defineEmits(['reload']);
  const props = defineProps<{
    batchNumber: BatchNumber;
  }>();

  const filteredRequests = computed(() => {
    return [...props.batchNumber.analytical_requests].sort((a, b) => {
      const regex = /^\d+\/\d+$/;
      const aValid = regex.test(a.sample_number);
      const bValid = regex.test(b.sample_number);

      if (aValid && bValid) {
        const [aFirst, aSecond] = a.sample_number.split('/').map(Number);
        const [bFirst, bSecond] = b.sample_number.split('/').map(Number);

        if (aSecond !== bSecond) {
          return aSecond - bSecond;
        }
        return aFirst - bFirst;
      }

      if (!aValid && bValid) return 1;
      if (aValid && !bValid) return -1;

      return 0;
    });
  });
</script>
