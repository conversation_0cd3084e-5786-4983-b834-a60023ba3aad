<template>
  <section class="my-5">
    <span class="text-h4"><PERSON><PERSON><PERSON><PERSON></span>
  </section>
  <v-row v-if="filteredRequests.length > 0">
    <v-col cols="12">
      <v-expansion-panels>
        <AnalyticalRequestItem
          v-for="analyticalRequest in filteredRequests"
          :key="analyticalRequest.analytical_request_id"
          :analytical-request="analyticalRequest"
          :is-standard="isStandard"
          :is-parent-closed="isParentClosed"
          :search-term="analyticalRequestSearch"
          @reload="$emit('reload')"
        />
      </v-expansion-panels>
    </v-col>
  </v-row>
  <NotFoundItem v-else>
    <template #notFound>Nebyly nalezeny ž<PERSON>dn<PERSON></template>
  </NotFoundItem>
</template>

<script lang="ts" setup>
  import NotFoundItem from '@/components/shared/NotFoundItem.vue';
  import { AnalyticalRequest } from '../analyticalRequests';
  import AnalyticalRequestItem from './AnalyticalRequestItem.vue';
  import { computed } from 'vue';
  import { useAuthStore } from '@/stores/auth';
  import { storeToRefs } from 'pinia';
  import { isAllowed } from '@/utils/directive/isAllowed';

  defineEmits(['reload']);
  const props = defineProps<{
    analyticalRequests: AnalyticalRequest[];
    analyticalRequestSearch?: string | undefined;
    isStandard: boolean;
    isParentClosed: boolean;
    startDate?: Date | string | null;
    endDate?: Date | string | null;
  }>();

  const authStore = useAuthStore();
  const { user } = storeToRefs(authStore);

  const filteredRequests = computed(() => {
    return props.analyticalRequests
      .filter((analyticalRequest) => {
        if (props.startDate && props.endDate) {
          const requestDate = new Date(analyticalRequest.created_at);

          const startDate = new Date(props.startDate);
          startDate.setHours(0, 0, 0, 0);

          const endDate = new Date(props.endDate);
          endDate.setHours(23, 59, 59, 999);

          if (requestDate < startDate || requestDate > endDate) {
            return false;
          }
        }

        if (props.analyticalRequestSearch) {
          const search = props.analyticalRequestSearch.toUpperCase().replace(/\s+/g, '');

          if (analyticalRequest.name && analyticalRequest.name.toUpperCase().includes(search)) {
            return true;
          }

          if (
            analyticalRequest.batch_number?.batch_number &&
            analyticalRequest.batch_number.batch_number.toUpperCase().includes(search)
          ) {
            return true;
          }

          if (
            analyticalRequest.sample_number &&
            analyticalRequest.sample_number.toUpperCase().includes(search)
          ) {
            return true;
          }

          if (analyticalRequest.created_at) {
            const formattedDate =
              analyticalRequest.created_at instanceof Date
                ? analyticalRequest.created_at.toISOString().split('T')[0]
                : new Date(analyticalRequest.created_at).toISOString().split('T')[0];

            if (formattedDate.toUpperCase().includes(search)) {
              return true;
            }
          }

          if (analyticalRequest.samples && analyticalRequest.samples.length > 0) {
            return analyticalRequest.samples.some((sample) => {
              if (sample.technique?.name && sample.technique.name.toUpperCase().includes(search)) {
                return true;
              }

              if (
                sample.technique?.shortcut &&
                sample.technique.shortcut.toUpperCase().includes(search)
              ) {
                return true;
              }

              if (sample.user?.fullName && sample.user.fullName.toUpperCase().includes(search)) {
                return true;
              }

              if (
                sample.user?.name_shortcut &&
                sample.user.name_shortcut.toUpperCase().includes(search)
              ) {
                return true;
              }

              return false;
            });
          }

          return false;
        }
        return true;
      })
      .filter((analyticalRequest) => {
        if (isAllowed(['show_only_own_sample_ressults']) && user.value) {
          return analyticalRequest.samples.some(
            (sample) => sample.user?.user_id === user.value?.user_id
          );
        }
        return true;
      })
      .sort((a, b) => {
        const regex = /^\d+\/\d+$/;
        const aValid = regex.test(a.sample_number);
        const bValid = regex.test(b.sample_number);

        if (aValid && bValid) {
          const [aFirst, aSecond] = a.sample_number.split('/').map(Number);
          const [bFirst, bSecond] = b.sample_number.split('/').map(Number);

          if (aSecond !== bSecond) {
            return aSecond - bSecond;
          }
          return aFirst - bFirst;
        }

        if (!aValid && bValid) return 1;
        if (aValid && !bValid) return -1;

        return 0;
      });
  });
</script>
