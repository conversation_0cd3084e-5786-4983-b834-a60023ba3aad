<template>
  <template v-for="(field, index) in parametersState" :key="index">
    <v-row>
      <v-col sm="12" cols="12">
        <v-autocomplete
          v-model="field.technique_id"
          :clearable="false"
          hide-details
          :rules="itemRequiredRule"
          rounded="sm"
          :items="
            techniques
              .filter((technique) => technique.type === TechniqueType.EXTERNAL)
              .map((technique) => {
                return {
                  value: technique.analytical_technique_id,
                  title: `${technique.name} (${technique.type})`,
                  technique: technique
                };
              })
          "
          variant="outlined"
          color="primary"
          label="Vyberte techniku"
          single-line
          class="autocomplete"
          :no-data-text="'Žádná další políčka'"
          :slim="true"
          :readonly="true"
        >
          <template #chip>
            <v-chip
              label
              variant="tonal"
              color="primary"
              size="large"
              class="my-1 text-subtitle-1 font-weight-regular"
            ></v-chip>
          </template>

          <template #item="{ props, item }">
            <v-list-item v-bind="props" :title="''">
              <div class="player-wrapper pa-2">
                <h6 class="text-subtitle-1 mb-0">
                  {{ item.raw.technique.parent_technique_id === null ? 'Hlavní' : 'Sub' }} -
                  {{ item.raw.technique.name }} ({{ item.raw.technique.shortcut }}) -
                  <v-chip
                    v-if="item.raw.technique.status === 'active'"
                    color="success"
                    size="small"
                    label
                  >
                    Aktivní
                  </v-chip>
                  <v-chip
                    v-if="item.raw.technique.status === 'inactive'"
                    color="warning"
                    size="small"
                    label
                  >
                    Neaktivní
                  </v-chip>
                  <v-chip
                    v-if="item.raw.technique.status === 'deleted'"
                    color="error"
                    size="small"
                    label
                  >
                    Odstraněno
                  </v-chip>
                </h6>
                <small class="text-h6 text-lightText">
                  Typ: {{ translateType(item.raw.technique.type) }} | Vytvořena:
                  {{ toLocale(item.raw.technique.created_at) }}
                </small>
              </div>
            </v-list-item>
          </template>
        </v-autocomplete>
      </v-col>

      <v-col cols="12">
        <v-label class="mb-2">Specifikace</v-label>
        <EditorTextarea
          v-model="field.note_or_specification"
          :show-edit-button="false"
          :config="
            {
              statusbar: true,
              resize: true,
              min_height: 200
            } as EditorManager & RawEditorOptions
          "
        ></EditorTextarea>
      </v-col>
      <v-col cols="12">
        <v-label class="mb-2">Výsledek</v-label>
        <EditorTextarea
          v-model="field.result"
          :show-edit-button="false"
          :config="
            {
              statusbar: true,
              resize: true,
              min_height: 200
            } as EditorManager & RawEditorOptions
          "
        ></EditorTextarea>
      </v-col>
      <v-col cols="12">
        <v-radio-group v-model="field.analysis_status">
          <v-radio label="Vyhovuje" :value="AnalysisStatus.COMPLIANT"></v-radio>
          <v-radio label="Nevyhovuje" :value="AnalysisStatus.NON_COMPLIANT"></v-radio>
          <v-radio label="N/A" :value="AnalysisStatus.NA"></v-radio>
        </v-radio-group>
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="12">
        <v-divider class="my-5"></v-divider>
      </v-col>
    </v-row>
  </template>
</template>

<script setup lang="ts">
  import EditorTextarea from '@/components/shared/EditorTextarea.vue';
  import { AnalysisStatus } from '@/stores/forms';
  import { useProjectsStore } from '@/stores/projects';
  import { Technique, TechniqueType } from '@/stores/techniques';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { toLocale } from '@/utils/locales';
  import { useVModel } from '@vueuse/core';
  import type { EditorManager, RawEditorOptions } from 'tinymce';
  import { onMounted, ref } from 'vue';

  export interface InsertAnalysisParameterI {
    technique_id: number | undefined;
    note_or_specification: string;
    result: string;
    analysis_status: AnalysisStatus;
  }

  const props = defineProps<{
    modelValue: InsertAnalysisParameterI[];
    project_id: number;
  }>();

  const techniques = ref<Technique[]>([]);
  const projectsStore = useProjectsStore();
  const parametersState = useVModel(props, 'modelValue');

  onMounted(async () => {
    if (parametersState.value.length === 0) {
      addField();
    }

    techniques.value = (await projectsStore.getProjectAnalyticalTechniques(props.project_id)) ?? [];
  });

  const addField = () => {
    parametersState.value.push({
      technique_id: undefined,
      note_or_specification: '',
      result: '',
      analysis_status: AnalysisStatus.NA
    });
  };

  const translateType = (type: string): string => {
    const translations: { [key: string]: string } = {
      external: 'externí',
      internal: 'interní'
    };
    return translations[type] || type;
  };
</script>
