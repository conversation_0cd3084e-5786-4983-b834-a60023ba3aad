<template>
  <v-dialog v-model="showState" class="customer-modal" style="z-index: 1010" max-width="800px">
    <v-card>
      <v-form ref="SampleLinkForm" class="SampleLinkForm" @submit.prevent="submitForm">
        <v-card-title class="pa-5">
          <span class="text-h5">Napárovat existující analýzu</span>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text class="pa-6">
          <v-row>
            <v-col cols="12">
              <v-label class="mb-2">Vyberte vzorek na napárování</v-label>

              <v-autocomplete
                v-model="selectedSample"
                v-model:search="searchTerm"
                :items="
                  samples
                    .filter((sample) => sample.sample_id !== props.currentSampleId)
                    .map((sample) => {
                      return {
                        value: sample.sample_id,
                        title: `${sample.analytical_request?.name} - (${sample.sample_number})`,
                        sample: sample
                      };
                    })
                "
                :loading="searchLoading"
                :clearable="true"
                hide-details
                rounded="sm"
                variant="outlined"
                color="primary"
                label="Vyberte vzorek na napárování"
                single-line
                class="autocomplete"
                :no-data-text="'Žádná další políčka'"
                :slim="true"
                :rules="itemRequiredRule"
              >
                <template #chip>
                  <v-chip
                    label
                    variant="tonal"
                    color="primary"
                    size="large"
                    class="my-1 text-subtitle-1 font-weight-regular"
                  ></v-chip>
                </template>

                <template #item="{ props: sampleProps, item }">
                  <v-list-item v-bind="sampleProps" :title="''">
                    <div class="player-wrapper pa-2">
                      <h6 class="text-subtitle-1 mb-0">
                        {{ item.raw.sample.analytical_request?.name || '/' }} -
                        {{ item.raw.sample.sample_number || '/' }}
                        <v-chip
                          v-if="item.raw.sample.status === SampleStatus.CREATED"
                          color="primary"
                          size="small"
                          label
                        >
                          Vytvořeno
                        </v-chip>
                        <v-chip
                          v-if="item.raw.sample.status === SampleStatus.DONE"
                          color="success"
                          size="small"
                          label
                        >
                          Dokončeno
                        </v-chip>
                        <v-chip
                          v-if="item.raw.sample.status === SampleStatus.CANCELLED"
                          color="error"
                          size="small"
                          label
                        >
                          Zrušeno
                        </v-chip>
                        <v-chip
                          v-if="item.raw.sample.status === SampleStatus.REANALYSIS"
                          color="warning"
                          size="small"
                          label
                        >
                          Reanalýza
                        </v-chip>
                        <v-chip
                          v-if="item.raw.sample.status === SampleStatus.UPDATED"
                          color="info"
                          size="small"
                          label
                        >
                          Aktualizováno
                        </v-chip>
                      </h6>
                      <small class="text-h6 text-lightText">
                        Vytvořeno: {{ toLocale(item.raw.sample.created_at) }}
                      </small>
                    </div>
                  </v-list-item>
                </template>
              </v-autocomplete>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" variant="text" @click="showState = false">Zrušit</v-btn>
          <v-btn color="primary" variant="flat" type="submit" :loading="loading">Napárovat</v-btn>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
  import { useVModel, useDebounceFn } from '@vueuse/core';
  import { ref, onMounted, watch } from 'vue';
  import { useSamplesStore, SampleStatus } from '@/stores/sample/samples';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { toLocale } from '@/utils/locales';
  import { notification } from 'ant-design-vue';
  import { storeToRefs } from 'pinia';

  const emits = defineEmits(['update:show', 'reload']);
  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    },
    techniqueId: {
      type: Number,
      required: true
    },
    batchNumber: {
      type: String,
      required: true
    },
    currentSampleId: {
      type: Number,
      required: true
    }
  });

  const showState = useVModel(props, 'show');
  const samplesStore = useSamplesStore();
  const { samples, loading: searchLoading } = storeToRefs(samplesStore);

  const loading = ref(false);

  const selectedSample = ref<number | null>(null);
  const searchTerm = ref('');

  watch(searchTerm, async () => {
    await debouncedSearch();
  });

  const SampleLinkForm = ref();

  const debouncedSearch = useDebounceFn(async () => {
    if (
      searchTerm.value &&
      searchTerm.value !== '' &&
      searchTerm.value.length > 2 &&
      searchLoading.value === false
    ) {
      await searchSamples(searchTerm.value);
    }
  }, 300);

  const searchSamples = async (search?: string) => {
    const filterOptions = [
      {
        column: 'technique_id',
        value: props.techniqueId
      },
      {
        column: 'analytical_request__batch_number__batch_number',
        value: props.batchNumber
      }
    ];

    const originalOptions = { ...samplesStore.options };
    samplesStore.options.search = search || '';
    samplesStore.options.search_columns = [
      'sample_number',
      'analytical_request__name',
      'analytical_request__batch_number__batch_number'
    ];
    samplesStore.options.filterOptions = filterOptions;
    samplesStore.options.options.page = 1;
    samplesStore.options.options.rowsPerPage = 25;

    await samplesStore.getSamples(true);
    samplesStore.options = originalOptions;
  };

  const loadInitialSamples = async () => {
    await searchSamples();
  };

  const submitForm = async () => {
    if (!SampleLinkForm.value?.validate() || !selectedSample.value) {
      return;
    }

    loading.value = true;

    try {
      const success = await samplesStore.linkSample(props.currentSampleId, selectedSample.value);

      if (success) {
        showState.value = false;
        emits('reload');
      }
    } catch (error) {
      console.error('Error linking sample:', error);
      notification.error({ message: 'Chyba při napárování vzorku' });
    } finally {
      loading.value = false;
    }
  };

  onMounted(() => {
    if (props.show) {
      loadInitialSamples();
    }
  });
</script>

<style scoped>
  .player-wrapper {
    border-left: 3px solid var(--v-theme-primary);
  }
</style>
