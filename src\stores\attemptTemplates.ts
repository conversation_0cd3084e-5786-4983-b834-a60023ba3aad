import BaseConstructor from '@/utils/BaseConstructor';
import {
  loadParamsFailedReloadNotification,
  reloadWithoutParams,
  revertServerOptionsFromLocation,
  stringifyServerOptions,
  type BasePaginatorResponseI,
  type BaseResponseI
} from '@/utils/axios';
import { fetchWrapper } from '@/utils/helpers/fetch-wrapper';
import { notification } from 'ant-design-vue';
import { defineStore } from 'pinia';
import type { ServerOptions } from 'vue3-easy-data-table';
import {
  Chemical,
  ChemicalStatus,
  ChemicalType,
  type ChemicalDto,
  type NewChemicalDataI,
  type ChemicalI
} from './chemicals';
import { File, useFilesStore, type FileDto } from './files';
import type { GetAllOptions, PaginatorRequestDataI } from './projects';

const baseUrl = `${import.meta.env.VITE_API_URL}/attempt-templates`;

export enum AttemptTemplateStatus {
  ACTIVE = 'active',
  DELETED = 'deleted'
}

export interface AttemptTemplateDto {
  project_template_id: number;
  project_id: number;
  template_name: string;
  status: string;
  created_at: string;
  updated_at: string;
  chemicals: ChemicalDto[];
  files: FileDto[];
  attempt_template_id: number;
  reaction_scheme_file_id: number | null;
  reaction_procedure: string;
  batch_description: string;
  apparatus: string;
}

export interface AttemptTemplateI {
  project_template_id: number;
  project_id: number;
  template_name: string;
  status: AttemptTemplateStatus;
  created_at: Date;
  updated_at: Date;
  chemicals: Chemical[];
  files: File[];
  attempt_template_id: number;
  reaction_scheme_file_id: number | null;
  reaction_procedure: string;
  apparatus: string;
  batch_description: string;
}

export class AttemptTemplate
  extends BaseConstructor<AttemptTemplateI>()
  implements AttemptTemplateI
{
  constructor(data: AttemptTemplateDto) {
    super(data as unknown as AttemptTemplateI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    this.files = data.files.map((file) => new File(file));
    this.chemicals = data.chemicals.map((chemical) => new Chemical(chemical));
  }
}

export type AttemptTemplatesListItemI = {
  attempt_template_id: number;
  template_name: string;
  status: string;
};

interface AttemptTemplateModalOptionsI {
  isEditing: boolean;
  isCreating: boolean;
  baseData: AttemptTemplate | undefined;
  newData: AttemptTemplateModalNewDataI | undefined;
  updateData: AttemptTemplateModalUpdateDataI | undefined;
}

export interface AttemptTemplateModalNewDataI {
  project_id: number | undefined;
  template_name: string | undefined;
  reaction_scheme_file_id: number | null | undefined;
  files_ids: number[];
  chemicals: NewChemicalDataI[];
  reaction_procedure: string | undefined;
  batch_description: string | undefined;
  apparatus: string | undefined;
  confirm: boolean | false;
  files: File[];
}

export interface AttemptTemplateModalUpdateDataI {
  template_name: string | undefined;
  reaction_scheme_file_id: number | null | undefined;
  chemicals: ChemicalI[];
  reaction_procedure: string | undefined;
  status: AttemptTemplateStatus | undefined;
  batch_description: string | undefined;
  files_ids: number[];
  apparatus: string | undefined;
  files: File[];
  reaction_scheme_files: File[];
}

interface AttemptTemplatesStateI {
  attemptTemplates: AttemptTemplate[];
  attemptTemplate: AttemptTemplate | null;
  loading: boolean;

  showAttemptTemplateModal: boolean;
  modalOptions: AttemptTemplateModalOptionsI | undefined;
  totalItems?: number;
  options: ServerOptions;
  search: string | undefined;
}

export const useAttemptTemplates = defineStore({
  id: 'attemptTemplates',
  state: () =>
    ({
      attemptTemplate: null,
      attemptTemplates: [],
      loading: false,

      showAttemptTemplateModal: false,
      modalOptions: undefined,

      search: undefined,
      totalItems: undefined,
      options: {
        page: 1,
        rowsPerPage: 100,
        sortBy: [],
        sortType: ['desc', 'asc']
      }
    }) as AttemptTemplatesStateI,
  actions: {
    async getAttemptTemplates(
      setData: boolean = true,
      options: GetAllOptions | undefined = {
        search_columns: ['name']
      }
    ): Promise<{
      data: AttemptTemplate[];
      totalItems: number;
    }> {
      this.loading = true;
      this.options.sortBy = ['status', 'project_template_id'];
      this.options.sortType = ['asc'];
      const URL =
        baseUrl +
        '/?' +
        stringifyServerOptions(
          options && options.options ? options.options : this.options,
          options && options.search ? options.search : (this.search ?? null),
          options && options.search_columns ? options.search_columns : [],
          options && options.fixedFilterOptions ? options.fixedFilterOptions : null
        );

      return fetchWrapper
        .get(URL)
        .then((res: BasePaginatorResponseI<AttemptTemplateDto>) => {
          this.loading = false;

          if (res.status_code === 200) {
            this.totalItems = res.data.total_items;
            if (setData) {
              this.attemptTemplates = res.data.items.map(
                (attemptTemplate) => new AttemptTemplate(attemptTemplate)
              );
            }

            this.loading = false;
            return {
              data: res.data.items.map((attemptTemplate) => new AttemptTemplate(attemptTemplate)),
              totalItems: res.data.total_items
            };
          }

          return {
            data: [],
            totalItems: 0
          };
        })
        .catch((res) => {
          this.loading = false;

          if (res.status_code === 400) {
            loadParamsFailedReloadNotification('Načtení šablon selhalo', res.error, () => {
              const data = reloadWithoutParams();
              this.options = data.options;
              this.search = data.term ?? undefined;
            });
          } else {
            notification.error({ message: 'Načtení šablon selhalo' });
          }

          return {
            data: [],
            totalItems: 0
          };
        });
    },

    async getAttemptTemplatesForSelect(
      attemptTemplateSelectOptions: PaginatorRequestDataI<AttemptTemplate>,
      addNewData = true
    ) {
      attemptTemplateSelectOptions.loading = true;
      const URL =
        baseUrl +
        '/?' +
        stringifyServerOptions(
          attemptTemplateSelectOptions.options,
          attemptTemplateSelectOptions.search ?? null,
          attemptTemplateSelectOptions.search_columns,
          attemptTemplateSelectOptions.filterOptions
        );

      fetchWrapper
        .get(URL)
        .then((res: BasePaginatorResponseI<AttemptTemplateDto>) => {
          attemptTemplateSelectOptions.loading = false;

          if (res.status_code === 200) {
            attemptTemplateSelectOptions.totalItems = res.data.total_items;
            if (addNewData) {
              attemptTemplateSelectOptions.results = [
                ...attemptTemplateSelectOptions.results,
                ...res.data.items.map((attemptTemplate) => new AttemptTemplate(attemptTemplate))
              ];
              attemptTemplateSelectOptions.results = [
                ...new Map(
                  attemptTemplateSelectOptions.results.map((attemptTemplate) => [
                    attemptTemplate.attempt_template_id,
                    attemptTemplate
                  ])
                ).values()
              ];
            } else {
              attemptTemplateSelectOptions.results = Array.from(
                res.data.items.map((attemptTemplate) => new AttemptTemplate(attemptTemplate))
              );
            }

            return;
          }

          if (!addNewData) {
            attemptTemplateSelectOptions.results = [];
          }
        })
        .catch((res) => {
          attemptTemplateSelectOptions.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Načtení šablon selhalo', description: res.error });
          }

          if (!addNewData) {
            attemptTemplateSelectOptions.results = [];
          }
        });
    },

    async getAttemptTemplate(attempt_template_id: number): Promise<AttemptTemplate | undefined> {
      this.loading = true;

      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/attempt-template/${attempt_template_id}`)
        .then((res: BaseResponseI<AttemptTemplateDto>) => {
          if (res.status_code === 200) {
            this.loading = false;
            return new AttemptTemplate(res.data);
          }
          this.loading = false;
          return undefined;
        })
        .catch((res) => {
          if (res.status_code === 400) {
            loadParamsFailedReloadNotification('Načtení šablony selhalo', res.error, () => {
              const data = reloadWithoutParams();
              this.options = data.options;
              this.search = data.term ?? undefined;
            });
          }

          this.loading = false;
          return undefined;
        });
    },

    setParamsFromLocation(): void {
      const data = revertServerOptionsFromLocation();
      this.options = data.options;
      this.search = data.term ?? undefined;
    },

    showNewAttemptTemplateModal(project_id: number) {
      const getLocalStorage = localStorage.getItem('attemptTemplatePageStore');
      let parsedData;
      if (getLocalStorage) {
        parsedData = JSON.parse(getLocalStorage);
      }
      this.modalOptions = {
        isEditing: false,
        isCreating: true,
        baseData: undefined,
        updateData: undefined,
        newData: {
          project_id: project_id,
          batch_description: undefined,
          apparatus: undefined,
          template_name: undefined,
          files_ids: [],
          chemicals: [],
          files: [],
          reaction_scheme_file_id: null,
          reaction_procedure:
            '<table style="border-collapse: collapse; width: 99.9746%; height: 292.167px;" border="1"><colgroup><col style="width: 5.77076%;"><col style="width: 44.9123%;"><col style="width: 49.2935%;"></colgroup> <tbody> <tr style="height: 36.5208px;"> <td> </td> <td> </td> <td> </td> </tr> <tr style="height: 36.5208px;"> <td> </td> <td> </td> <td> </td> </tr> <tr style="height: 36.5208px;"> <td> </td> <td> </td> <td> </td> </tr> <tr style="height: 36.5208px;"> <td> </td> <td> </td> <td> </td> </tr> <tr style="height: 36.5208px;"> <td> </td> <td> </td> <td> </td> </tr> <tr style="height: 36.5208px;"> <td> </td> <td> </td> <td> </td> </tr> <tr style="height: 36.5208px;"> <td> </td> <td> </td> <td> </td> </tr> <tr style="height: 36.5208px;"> <td> </td> <td> </td> <td> </td> </tr> </tbody> </table>',
          confirm: false
        }
      };

      this.showAttemptTemplateModal = true;
    },

    async showPreviewModal(id: number) {
      const filesStore = useFilesStore();
      const _AttemptTemplate = await this.getAttemptTemplate(id);
      if (_AttemptTemplate === undefined) {
        notification.error({ message: 'Šablona nebyla nalezena' });
        return;
      }

      this.attemptTemplate = _AttemptTemplate;
      const reaction_scheme_file = _AttemptTemplate.reaction_scheme_file_id
        ? await filesStore.getFileById(_AttemptTemplate.reaction_scheme_file_id)
        : undefined;

      this.modalOptions = {
        isEditing: false,
        isCreating: false,
        baseData: _AttemptTemplate,
        newData: undefined,
        updateData: {
          files: [],
          reaction_scheme_files: reaction_scheme_file ? [reaction_scheme_file] : [],
          files_ids: [],
          reaction_procedure: _AttemptTemplate.reaction_procedure,
          template_name: _AttemptTemplate.template_name,
          reaction_scheme_file_id: _AttemptTemplate.reaction_scheme_file_id ?? undefined,
          status: _AttemptTemplate.status,
          batch_description: _AttemptTemplate.batch_description,
          apparatus: _AttemptTemplate.apparatus,
          chemicals: _AttemptTemplate.chemicals.map((chemical) => {
            return {
              chemical_id: chemical.chemical_id,
              name: chemical.name,
              density: chemical.density,
              molar_mass: chemical.molar_mass,
              notes: chemical.notes,
              grams: chemical.grams,
              moles: chemical.moles,
              equivalent: chemical.equivalent,
              volume_ml: chemical.volume_ml,
              concentration: chemical.concentration,
              type: chemical.type,
              project_template_id: chemical.project_template_id,
              status: chemical.status,
              form_id: null,
              created_at: chemical.created_at,
              updated_at: chemical.updated_at
            } as ChemicalI;
          })
        }
      };

      // this.showAttemptTemplateModal = true;
    },

    async showEditModal(id: number) {
      const filesStore = useFilesStore();
      const _AttemptTemplate = await this.getAttemptTemplate(id);
      if (_AttemptTemplate === undefined) {
        notification.error({ message: 'Šablona nebyla nalezena' });
        return;
      }

      this.attemptTemplate = _AttemptTemplate;

      this.attemptTemplate = _AttemptTemplate;
      const reaction_scheme_file = _AttemptTemplate.reaction_scheme_file_id
        ? await filesStore.getFileById(_AttemptTemplate.reaction_scheme_file_id)
        : undefined;

      this.modalOptions = {
        isEditing: true,
        isCreating: false,
        baseData: _AttemptTemplate,
        newData: undefined,
        updateData: {
          reaction_scheme_files: reaction_scheme_file ? [reaction_scheme_file] : [],
          files_ids: [],
          files: [],
          reaction_procedure: _AttemptTemplate.reaction_procedure ?? undefined,
          template_name: _AttemptTemplate.template_name,
          reaction_scheme_file_id: _AttemptTemplate.reaction_scheme_file_id ?? undefined,
          status: _AttemptTemplate.status,
          batch_description: _AttemptTemplate.batch_description,
          apparatus: _AttemptTemplate.apparatus,
          chemicals: _AttemptTemplate.chemicals.map((chemical) => {
            return {
              chemical_id: chemical.chemical_id,
              name: chemical.name,
              density: chemical.density,
              molar_mass: chemical.molar_mass,
              notes: chemical.notes,
              grams: chemical.grams,
              moles: chemical.moles,
              equivalent: chemical.equivalent,
              volume_ml: chemical.volume_ml,
              concentration: chemical.concentration,
              type: chemical.type,
              project_template_id: chemical.project_template_id,
              status: chemical.status,
              form_id: null,
              created_at: chemical.created_at,
              updated_at: chemical.updated_at
            } as ChemicalI;
          })
        }
      };

      this.showAttemptTemplateModal = true;
    },

    async deleteAttemptTemplate(attempt_template_id: number) {
      this.loading = true;

      return fetchWrapper
        .delete(`${import.meta.env.VITE_API_URL}/attempt-template/${attempt_template_id}`)
        .then((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 200) {
            notification.success({ message: 'Šablona byla úspěšně smazána' });
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Smazání šablony selhalo', description: res.error });
          }

          return false;
        });
    },

    async createAttemptTemplate(): Promise<boolean> {
      if (!this.modalOptions?.newData) {
        notification.error({ message: 'Musíte potvrdit souhlas s vytvořením šablony' });
        return false;
      }

      this.loading = true;

      const data = {
        project_id: this.modalOptions.newData.project_id,
        template_name: this.modalOptions.newData.template_name ?? '',
        reaction_scheme_file_id: this.modalOptions.newData.reaction_scheme_file_id ?? null,
        reaction_procedure: this.modalOptions.newData.reaction_procedure ?? '',
        files_ids: this.modalOptions.newData.files.map((f) => f.file_id) ?? [],
        batch_description: this.modalOptions.newData.batch_description ?? '',
        apparatus: this.modalOptions.newData.apparatus ?? '',
        chemicals:
          this.modalOptions.newData.chemicals?.map((chemical) => ({
            name: chemical.name ?? '',
            density: chemical.density ?? 0,
            molar_mass: chemical.molar_mass ?? 0,
            notes: chemical.notes ?? '',
            grams: chemical.grams ?? 0,
            moles: chemical.moles ?? 0,
            equivalent: chemical.equivalent ?? 0,
            volume_ml: chemical.volume_ml ?? 0,
            concentration: chemical.concentration ?? 0,
            status: ChemicalStatus.ACTIVE,
            type: chemical.type ?? ChemicalType.CHEMICAL
          })) ?? []
      };

      return fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/attempt-template/`, data)
        .then(async (res: BaseResponseI<AttemptTemplateDto>) => {
          this.loading = false;

          if (res.status_code === 200) {
            this.showAttemptTemplateModal = false;

            notification.success({
              message: 'Vytvoření šablony proběhlo v pořádku',
              description: 'Název: ' + res.data.template_name
            });
            localStorage.removeItem('attemptTemplatePageStore');
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Vytvoření šablony selhalo', description: res.error });
          } else {
            this.showAttemptTemplateModal = false;
          }

          this.loading = false;
          return false;
        });
    },

    async updateAttemptTemplate() {
      if (
        !this.modalOptions?.updateData ||
        this.modalOptions?.baseData?.attempt_template_id === undefined
      ) {
        notification.error({ message: 'Není co upravovat' });
        return;
      }

      this.loading = true;
      const data = {
        template_name: this.modalOptions.updateData.template_name ?? '',
        reaction_scheme_file_id: this.modalOptions.updateData.reaction_scheme_file_id ?? null,
        reaction_procedure: this.modalOptions.updateData.reaction_procedure ?? '',
        status: this.modalOptions.updateData.status ?? AttemptTemplateStatus.ACTIVE,
        apparatus: this.modalOptions.updateData.apparatus ?? '',
        batch_description: this.modalOptions.updateData.batch_description ?? '',
        chemicals: this.modalOptions.updateData.chemicals.map((chemical) => ({
          name: chemical.name ?? '',
          density: chemical.density ?? 0,
          molar_mass: chemical.molar_mass ?? 0,
          notes: chemical.notes ?? '',
          grams: chemical.grams ?? 0,
          moles: chemical.moles ?? 0,
          equivalent: chemical.equivalent ?? 0,
          volume_ml: chemical.volume_ml ?? 0,
          concentration: chemical.concentration ?? 0,
          status: ChemicalStatus.ACTIVE,
          type: chemical.type ?? ChemicalType.CHEMICAL
        }))
      };

      return fetchWrapper
        .put(
          `${import.meta.env.VITE_API_URL}/attempt-template/${this.modalOptions.baseData.attempt_template_id}`,
          data
        )
        .then((res: BaseResponseI<AttemptTemplateDto>) => {
          this.loading = false;

          if (res.status_code === 200) {
            this.showAttemptTemplateModal = false;

            notification.success({
              message: 'Úprava šablony proběhla v pořádku',
              description: 'Název: ' + res.data.template_name
            });

            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Úprava šablony selhala', description: res.error });
          } else {
            this.showAttemptTemplateModal = false;
          }

          return false;
        });
    },

    async addFileToAttemptTemplate(file_id: number) {
      if (!this.attemptTemplate) {
        notification.error({ message: 'Není vybran šablona' });
        return false;
      }

      return fetchWrapper
        .post(
          `${import.meta.env.VITE_API_URL}/attempt-template/${this.attemptTemplate.attempt_template_id}/file/${file_id}`
        )
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Soubor byl přidán k šabloně' });
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Přidání souboru k šabloně selhalo',
              description: res.error
            });
          } else {
            notification.error({ message: 'Přidání souboru k šabloně selhalo' });
          }

          return false;
        });
    },

    async deleteFileFromAttemptTemplate(file_id: number) {
      if (!this.attemptTemplate) {
        notification.error({ message: 'Není vybrána šablona' });
        return false;
      }

      return fetchWrapper
        .delete(
          `${import.meta.env.VITE_API_URL}/attempt-template/${this.attemptTemplate.attempt_template_id}/file/${file_id}`
        )
        .then((res: BaseResponseI<AttemptTemplateDto>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Soubor byl odebrán ze šablony' });
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Odebrání souboru ze šablony selhalo',
              description: res.error
            });
          } else {
            notification.error({ message: 'Odebrání souboru ze šablony selhalo' });
          }

          return false;
        });
    },

    resetModal() {
      this.showAttemptTemplateModal = false;
      if (this.modalOptions) this.modalOptions.baseData = undefined;
      if (this.modalOptions) this.modalOptions.newData = undefined;
    }
  }
});
