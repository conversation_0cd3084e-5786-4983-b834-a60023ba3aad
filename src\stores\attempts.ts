import type { TagListI } from '@/components/shared/TagModal.vue';
import { router } from '@/router';
import BaseConstructor from '@/utils/BaseConstructor';
import {
  loadParamsFailedReloadNotification,
  reloadWithoutParams,
  revertServerOptionsFromLocation,
  stringifyServerOptions,
  type BasePaginatorResponseI,
  type BaseResponseI
} from '@/utils/axios';
import { fetchWrapper } from '@/utils/helpers/fetch-wrapper';
import { notification } from 'ant-design-vue';
import { defineStore } from 'pinia';
import type { ServerOptions } from 'vue3-easy-data-table';
import { useChaptersStore } from './chapters';
import { Chemical, type ChemicalDto, type NewChemicalDataI } from './chemicals';
import { BatchNumber, Conclusion, type BatchNumberDto, type ConclusionDto } from './experiments';
import { File, type FileDto } from './files';
import { useProjectsStore, type GetAllOptions } from './projects';
import { Tag, type TagDto } from './tags';
import { UserLock, UserLockStatus, type LockTableDto, type LockTableI } from './userLock';
import { useAuthStore } from './auth';

const baseUrl = `${import.meta.env.VITE_API_URL}/attempts`;

export enum AttemptStatus {
  CREATED = 'created',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELED = 'canceled',
  SIGNED = 'signed'
}

export interface AttemptDto extends LockTableDto {
  form_id: number;
  batch_number_id: number;
  chapter_id: number;
  form_name: string;
  batch_description: string;
  status: AttemptStatus;
  created_at: string;
  updated_at: string;
  chemicals: ChemicalDto[];
  conclusions: ConclusionDto[];
  tags: TagDto[];
  files: FileDto[];
  batch_number: BatchNumberDto;
  attempt_id: number;
  reaction_procedure: string;
  product_weight: number;
  yield_percentage: number;
  content_percentage: number;
  reaction_scheme_file_id: number | null;
  apparatus: string;
  owner: OwnerDto;
  authors?: OwnerDto[];
  collaborators: collaboratorDto[];
}

export interface collaboratorDto {
  can_edit: boolean;
  can_delete: boolean;
  can_share: boolean;
  form_collaborator_id: number;
  form_id: number;
  user_id: number;
  created_at: string;
  updated_at: string;
  user: OwnerDto;
}

export interface CollaboratorI {
  can_edit: boolean;
  can_delete: boolean;
  can_share: boolean;
  form_collaborator_id: number;
  form_id: number;
  user_id: number;
  created_at: Date;
  updated_at: Date;
  user: Owner;
}

export class Collaborator extends BaseConstructor<CollaboratorI>() implements CollaboratorI {
  constructor(data: collaboratorDto) {
    super(data as unknown as CollaboratorI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    this.user = new Owner(data.user);
  }
}

export interface OwnerDto {
  user_id: number;
  first_name: string;
  last_name: string;
  name_shortcut: string;
  status: string;
  user_email: string;
  created_at: string;
  updated_at: string;
}

export interface OwnerI {
  user_id: number;
  first_name: string;
  last_name: string;
  name_shortcut: string;
  status: string;
  user_email: string;
  created_at: Date;
  updated_at: Date;
}

export class Owner extends BaseConstructor<OwnerI>() implements OwnerI {
  constructor(data: OwnerDto) {
    super(data as unknown as OwnerI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);
  }
}

export interface AttemptI extends LockTableI {
  form_id: number;
  batch_number_id: number;
  chapter_id: number;
  form_name: string;
  batch_description: string;
  status: AttemptStatus;
  created_at: Date;
  updated_at: Date;
  chemicals: Chemical[];
  conclusions: Conclusion[];
  tags: Tag[];
  files: File[];
  batch_number: BatchNumber;
  attempt_id: number;
  reaction_procedure: string;
  product_weight: number;
  yield_percentage: number;
  content_percentage: number;
  reaction_scheme_file_id: number | null;
  apparatus: string;
  tagIsLoaded: boolean;
  showTags: boolean;
  owner: Owner;
  authors?: Owner[];
  collaborators: Collaborator[];
}

export class Attempt extends BaseConstructor<AttemptI>() implements AttemptI {
  constructor(data: AttemptDto) {
    super(data as unknown as AttemptI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    this.tags = data.tags.map((tag) => new Tag(tag));
    this.files = data.files.map((file) => new File(file));
    this.batch_number = new BatchNumber(data.batch_number);
    this.chemicals = data.chemicals.map((chemical) => new Chemical(chemical));
    this.conclusions = data.conclusions.map((conclusion) => new Conclusion(conclusion));
    this.tagIsLoaded = true;
    this.showTags = false;
    if (data.edited_by) {
      this.edited_by = new UserLock(data.edited_by);
    }
    this.owner = new Owner(data.owner);
    if (data.authors) {
      this.authors = data.authors.map((author) => new Owner(author));
    }
    this.collaborators = data.collaborators.map((collaborator) => new Collaborator(collaborator));

    if (!data.reaction_procedure.length) {
      this.reaction_procedure =
        '<table style="border-collapse: collapse; width: 99.9746%; height: 292.167px;" border="1"><colgroup><col style="width: 5.77076%;"><col style="width: 44.9123%;"><col style="width: 49.2935%;"></colgroup> <tbody> <tr style="height: 36.5208px;"> <td> </td> <td> </td> <td> </td> </tr> <tr style="height: 36.5208px;"> <td> </td> <td> </td> <td> </td> </tr> <tr style="height: 36.5208px;"> <td> </td> <td> </td> <td> </td> </tr> <tr style="height: 36.5208px;"> <td> </td> <td> </td> <td> </td> </tr> <tr style="height: 36.5208px;"> <td> </td> <td> </td> <td> </td> </tr> <tr style="height: 36.5208px;"> <td> </td> <td> </td> <td> </td> </tr> <tr style="height: 36.5208px;"> <td> </td> <td> </td> <td> </td> </tr> <tr style="height: 36.5208px;"> <td> </td> <td> </td> <td> </td> </tr> </tbody> </table>';
    }
  }

  get isReadOnly(): boolean {
    return this.status === AttemptStatus.SIGNED || this.status === AttemptStatus.CANCELED;
  }
  get isCanceled(): boolean {
    return this.status === AttemptStatus.CANCELED;
  }
  get isCompleted(): boolean {
    return this.status === AttemptStatus.SIGNED || this.status === AttemptStatus.COMPLETED;
  }
  get isActive(): boolean {
    return this.status !== AttemptStatus.CANCELED;
  }

  get isLocked(): boolean {
    const authStore = useAuthStore();
    return (
      this.edited_by?.status === UserLockStatus.LOCKED_BY_OTHER_USER ||
      (this.edited_by?.status === UserLockStatus.LOCKED &&
        this.edited_by.user_id !== authStore.user?.user_id)
    );
  }

  get statusText(): string {
    switch (this.status) {
      case AttemptStatus.CREATED:
        return 'Aktivní';
      case AttemptStatus.IN_PROGRESS:
        return 'Probíhající';
      case AttemptStatus.SIGNED:
        return 'Podepsaný';
      case AttemptStatus.CANCELED:
        return 'Zrušený';
      default:
        return 'Neznámý';
    }
  }
  async getTags() {
    const chaptersStore = useChaptersStore();
    const tags = await chaptersStore.getTagsByChapter(this.chapter_id);
    this.tags = tags;

    this.tagIsLoaded = true;
  }

  get joinTags() {
    return this.tags?.map((t) => t.tag_name).join(', ') ?? '';
  }

  get tagLists() {
    return (
      this.tags?.map(
        (t) =>
          ({
            tag_id: t.tag_id,
            tag_name: t.tag_name
          }) as TagListI
      ) ?? []
    );
  }
}

export interface AttemptModalNewDataI {
  chapter_id: number | undefined;
  form_name: string | undefined;
  attempt_template_id: undefined | number | null;
  confirm: boolean | false;
}

export interface AttemptUpdateDataI {
  form_name: string | undefined;
  batch_description: string | undefined;
  chemicals: NewChemicalDataI[];
  reaction_procedure: string | undefined;
  product_weight: number | undefined;
  yield_percentage: number | undefined;
  content_percentage: number | undefined;
  conclusion_text: string | undefined;
  apparatus: string | undefined;
  reaction_scheme_file_id: number | null;
}

interface AttemptUpdatePostDataI {
  form_name: string;
  batch_description: string;
  chemicals: NewChemicalDataI[];
  reaction_procedure: string;
  product_weight: number;
  yield_percentage: number;
  content_percentage: number;
  conclusion_text: string;
  apparatus: string;
  reaction_scheme_file_id: number | null;
}

export interface SaveAttemptAsTemplateDto {
  template_name: string;
}

interface AttemptModalOptionsI {
  isEditing: boolean;
  isCreating: boolean;
  baseData: Attempt | undefined;
  newData: AttemptModalNewDataI | undefined;
  updateData: AttemptUpdateDataI | undefined;
}

interface AttemptsStateI {
  attempts: Map<number, Attempt>;
  attempt: Attempt | null;
  loading: boolean;

  showAttemptModal: boolean;
  modalOptions: AttemptModalOptionsI | undefined;

  totalItems?: number;
  options: ServerOptions;
  search: string | undefined;
}

export interface SaveAttemptAsTampleteDto {
  template_name: string;
}

export interface CopyAttemptDto {
  form_name: string;
}

export const useAttemptsStore = defineStore({
  id: 'attempts',
  state: () =>
    ({
      attempt: null,
      attempts: new Map(),
      loading: false,

      showAttemptModal: false,
      modalOptions: undefined,

      items: [],
      search: undefined,
      totalItems: undefined,
      options: {
        page: 1,
        rowsPerPage: 25,
        sortBy: [],
        sortType: ['desc', 'asc']
      }
    }) as AttemptsStateI,
  actions: {
    async getAttempts(
      setData: boolean = true,
      options: GetAllOptions | undefined = {
        search_columns: ['form_name']
      }
    ): Promise<{
      data: Attempt[];
      totalItems: number;
    }> {
      this.loading = true;

      const URL =
        baseUrl +
        '/?' +
        stringifyServerOptions(
          options && options.options ? options.options : this.options,
          options && options.search ? options.search : (this.search ?? null),
          options && options.search_columns ? options.search_columns : [],
          options && options.fixedFilterOptions ? options.fixedFilterOptions : null
        );

      return fetchWrapper
        .get(URL)
        .then((res: BasePaginatorResponseI<AttemptDto>) => {
          if (res.status_code === 200) {
            this.totalItems = res.data.total_items;
            if (setData) {
              this.attempts = new Map(
                res.data.items.map((attempt) => [attempt.form_id, new Attempt(attempt)])
              );
            }

            this.loading = false;
            return {
              data: res.data.items.map((attempt) => new Attempt(attempt)),
              totalItems: res.data.total_items
            };
          }
          this.loading = false;
          return {
            data: [],
            totalItems: 0
          };
        })
        .catch((res) => {
          if (res.status_code === 400) {
            loadParamsFailedReloadNotification('Načtení pokusu selhalo', res.error, () => {
              const data = reloadWithoutParams();
              this.options = data.options;
              this.search = data.term ?? undefined;
            });
          }

          this.loading = false;
          return {
            data: [],
            totalItems: 0
          };
        });
    },

    async getAttempt(id: number) {
      this.loading = true;

      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/attempt/${id}`)
        .then((res: BaseResponseI<AttemptDto>) => {
          this.loading = false;

          if (res.status_code === 200) {
            this.attempt = new Attempt(res.data);
            return new Attempt(res.data);
          }
          return undefined;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Načtení pokusu selhalo', description: res.message });
          } else {
            notification.error({ message: 'Načtení pokusu selhalo' });
          }

          return undefined;
        });
    },

    async getAttemptById(id: number) {
      const _Attempt = await this.getAttempt(id);

      if (_Attempt) {
        this.attempt = _Attempt;
      } else {
        notification.error({
          message: 'Chyba',
          description: 'Pokus nebyl nalezen.'
        });
        router.push({ name: 'ChapterDetail' });
      }
    },

    setParamsFromLocation(): void {
      const data = revertServerOptionsFromLocation();
      this.options = data.options;
      this.search = data.term ?? undefined;
    },

    async showNewAttemptModal(chapter_id: number) {
      const projectsStore = useProjectsStore();
      const chaptersStore = useChaptersStore();

      const _Chapter = await chaptersStore.getChapter(chapter_id);
      if (_Chapter === undefined) {
        notification.error({ message: 'Kapitola nebyla nalezena' });
        return;
      }

      this.modalOptions = {
        isEditing: false,
        isCreating: true,
        baseData: undefined,
        updateData: undefined,
        newData: {
          chapter_id: chapter_id,
          form_name: undefined,
          attempt_template_id: undefined,
          confirm: false
        }
      };

      this.showAttemptModal = true;
    },

    async createAttempt() {
      if (!this.modalOptions?.newData || this.modalOptions?.newData?.confirm === false) {
        notification.error({ message: 'Musíte potvrdit souhlas s vytvořením pokusu' });
        return false;
      }

      if (this.modalOptions.newData.chapter_id === undefined) {
        notification.error({ message: 'Není vybrána kapitola' });
        return false;
      }

      this.loading = true;

      const data = {
        chapter_id: this.modalOptions.newData.chapter_id,
        form_name: this.modalOptions.newData.form_name ?? '',
        attempt_template_id: this.modalOptions.newData.attempt_template_id ?? null
      };

      return await fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/attempt/`, data)
        .then((res: BaseResponseI<AttemptDto>) => {
          if (res.status_code === 200) {
            this.attempts.set(res.data.form_id, new Attempt(res.data));
            this.showAttemptModal = false;

            notification.success({
              message: 'Vytvoření pokusu proběhlo v pořádku',
              description: 'Název: ' + res.data.form_name
            });

            this.loading = false;
            router.push(this.redirectToAttemptDetailLink(res.data.form_id));
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Vytvoření pokusu selhalo', description: res.error });
          } else {
            notification.error({ message: 'Vytvoření pokusu selhalo' });
            this.showAttemptModal = false;
          }

          this.loading = false;
          return false;
        });
    },
    resetModal() {
      this.showAttemptModal = false;
      if (this.modalOptions) this.modalOptions.baseData = undefined;
      if (this.modalOptions) this.modalOptions.newData = undefined;
    },

    async updateAttempt(data: AttemptUpdateDataI) {
      if (!this.attempt) {
        notification.error({ message: 'Není vybrán attempt' });
        return false;
      }

      this.loading = true;

      const _data = {
        form_name: data.form_name ?? '',
        batch_description: data.batch_description ?? '',
        chemicals: data.chemicals,
        reaction_procedure: data.reaction_procedure ?? '',
        product_weight: data.product_weight ?? 0,
        yield_percentage: data.yield_percentage ?? 0,
        content_percentage: data.content_percentage ?? 0,
        conclusion_text: data.conclusion_text ?? '',
        apparatus: data.apparatus ?? '',
        reaction_scheme_file_id: data.reaction_scheme_file_id ?? null
      } as AttemptUpdatePostDataI;

      return await fetchWrapper
        .put(`${import.meta.env.VITE_API_URL}/attempt/${this.attempt.attempt_id}`, _data)
        .then((res: BaseResponseI<AttemptDto>) => {
          if (res.status_code === 200) {
            this.attempts.set(res.data.form_id, new Attempt(res.data));
            this.showAttemptModal = false;

            notification.success({
              message: 'Aktualizace pokusu proběhla v pořádku',
              description: 'Název: ' + res.data.form_name
            });

            this.loading = false;
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Aktualizace pokusu selhala', description: res.error });
          } else {
            this.showAttemptModal = false;
          }

          this.loading = false;
          return false;
        });
    },

    async saveAttemptAsTemplate(data: SaveAttemptAsTemplateDto) {
      if (!this.attempt) {
        notification.error({ message: 'Není vybrán pokus' });
        return false;
      }

      this.loading = true;

      return await fetchWrapper
        .post(
          `${import.meta.env.VITE_API_URL}/attempt/${this.attempt.attempt_id}/save_as_template`,
          data
        )
        .then((res: BaseResponseI<AttemptDto>) => {
          if (res.status_code === 200) {
            notification.success({
              message: 'Uložení pokusu jako šablony proběhlo v pořádku',
              description: 'Název: ' + res.data.form_name
            });

            this.loading = false;
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Uložení pokusu jako šablony selhalo',
              description: res.error
            });
          } else {
            notification.error({ message: 'Uložení pokusu jako šablony selhalo' });
          }

          this.loading = false;
          return false;
        });
    },
    async updateChapterTags(form_id: number, tags: number[]) {
      this.loading = true;

      return fetchWrapper
        .patch(`${import.meta.env.VITE_API_URL}/form/${form_id}/tags`, { tags: tags })
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Štítky byly úspěšně upraveny' });
            this.loading = false;
          }
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Úprava štítků selhala', description: res.error });
          }

          this.loading = false;
        });
    },

    async updateTags(experiment: Attempt, tags: TagListI[]) {
      await this.updateChapterTags(
        experiment.form_id,
        tags.map((tag) => tag.tag_id)
      );

      experiment.getTags();
      experiment.showTags = false;
    },
    closeAttempt(id: number, force: boolean) {
      if (!id) {
        notification.error({ message: 'Není co zavírat' });
        return false;
      }
      this.loading = true;
      const data = {
        status: 'canceled',
        brute_force_close: force
      };

      return fetchWrapper
        .patch(`${import.meta.env.VITE_API_URL}/form/${id}/status`, data)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            this.attempts.delete(id);
            notification.success({ message: 'Pokus byl úspěšně uzavřen' });
            this.getAttempts();
            this.loading = false;

            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Zavření pokusu selhalo', description: res.error });
          }

          this.loading = false;
          return false;
        });
    },
    reactivateAttempt(id: number) {
      if (!id) {
        notification.error({ message: 'Není co otvírat' });
        return false;
      }
      this.loading = true;
      const data = {
        status: 'created',
        brute_force_close: false
      };

      return fetchWrapper
        .patch(`${import.meta.env.VITE_API_URL}/form/${id}/status`, data)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            this.attempts.delete(id);
            notification.success({ message: 'Pokus byl úspěšně otevřen' });
            this.getAttempts();
            this.loading = false;

            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Aktivace pokusu selhala', description: res.error });
          }

          this.loading = false;
          return false;
        });
    },
    redirectToAttemptDetailLink(id: number) {
      return router.resolve({
        name: 'Attempt',
        params: { form_id: id.toString() }
      });
    },
    async copyAttempt(attempt_id: number, data: CopyAttemptDto) {
      this.loading = true;
      return await fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/attempt/${attempt_id}/copy`, data)
        .then((res: BaseResponseI<AttemptDto>) => {
          if (res.status_code === 200) {
            notification.success({
              message: 'Kopie pokusu byla úspěšně vytvořena',
              description: 'Název: ' + res.data.form_name
            });
            this.loading = false;
            router.push(this.redirectToAttemptDetailLink(res.data.form_id));
            return res.data;
          }
          this.loading = false;
          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Kopírování pokusu selhalo', description: res.error });
          } else {
            notification.error({ message: 'Kopírování pokusu selhalo' });
          }
          this.loading = false;
          return false;
        });
    }
  }
});
