import { router } from '@/router';
import { getJwtPayload, getTokenJwtPayloadWithExpirationDate } from '@/utils/Auth';
import type { BaseResponseI } from '@/utils/axios';
import BaseConstructor from '@/utils/BaseConstructor';
import { fetchWrapper } from '@/utils/helpers/fetch-wrapper';
import { notification } from 'ant-design-vue';
import { defineStore } from 'pinia';
import { Shortcut, useUsersStore } from './users';

export interface UserDto {
  user_id: number;
  created_at: string;
  updated_at: string;
  first_name: string;
  last_name: string;
  user_email: string;
  name_shortcut: string;
  status: string;
  roles?: RoleDto[];
  password_hash?: string;
  permissions?: PermissionDto[];
  user_laborants?: UserDto[];
  fromResponsible?: boolean | undefined;
}

export interface UserI {
  user_id: number;
  created_at: Date;
  updated_at: Date;
  first_name: string;
  last_name: string;
  user_email: string;
  name_shortcut: string;
  status: string;
  roles?: Role[];
  password_hash?: string;
  permissions?: Permission[];
  userLaborants?: User[];
  shortcuts?: Shortcut[];
  fromResponsible?: boolean | undefined;
}

export interface PermissionDto {
  system_permission_id: number;
  name: string;
}
export interface PermissionI {
  system_permission_id: number;
  name: string;
  translatedPermission?: string;
}

export interface RoleDto {
  system_role_id: number;
  name: string;
  permissions: PermissionDto[];
}

export interface RoleI {
  system_role_id: number;
  name: string;
  permissions: Permission[];
  newPermissions: number[];
  translatedRole?: string;
}

export class Permission extends BaseConstructor<PermissionI>() implements PermissionI {
  constructor(data: PermissionDto) {
    super(data as unknown as PermissionI);
    this.translatedPermission = permissionsTranslator(data.name);
  }
}

export class Role extends BaseConstructor<RoleI>() implements RoleI {
  constructor(data: RoleDto) {
    super(data as unknown as RoleI);
    this.permissions = data.permissions.map((permission) => new Permission(permission));
    this.newPermissions = this.permissions.map((permission) => permission.system_permission_id);
    this.translatedRole = rolesTranslator(data.name);
  }
}

export const rolesTranslator = (name: string) => {
  switch (name) {
    case 'administrator':
      return 'Administrátor';
    case 'editor':
      return 'Editor';
    case 'viewer':
      return 'Čtenář';
    case 'guest':
      return 'Host';
    case 'anonymous':
      return 'Anonymní';
    case 'laborant':
      return 'Laborant';
    case 'syntetik':
      return 'Syntetik';
    case 'synthetic':
      return 'Syntetik';
    default:
      return name;
  }
};

export const permissionsTranslator = (name: string) => {
  switch (name) {
    case 'view_rd_samples':
      return 'Zobrazit vzorky RD';
    case 'fill_rd_analyses':
      return 'Vyplnit analýzy RD';
    case 'create_rd_analysis_requests':
      return 'Vytvořit žádosti o analýzu RD';
    case 'view_qc_vt_samples':
      return 'Zobrazit vzorky QC VT';
    case 'fill_qc_vt_analyses':
      return 'Vyplnit analýzy QC VT';
    case 'create_qc_vt_analysis_requests':
      return 'Vytvořit žádosti o analýzu QC VT';
    case 'view_analytical_instruments':
      return 'Zobrazit analytické přístroje';
    case 'add_edit_analytical_instruments':
      return 'Přidat/upravit analytické přístroje';
    case 'view_instrument_statistics':
      return 'Zobrazit statistiky přístrojů';
    case 'view_columns':
      return 'Zobrazit kolony';
    case 'add_edit_columns':
      return 'Přidat/upravit kolony';
    case 'view_column_statistics':
      return 'Zobrazit statistiky kolon';
    case 'view_users':
      return 'Zobrazit uživatele';
    case 'add_edit_users':
      return 'Přidat/upravit uživatele';
    case 'add_edit_roles_permissions':
      return 'Přidat/upravit role a oprávnění';
    case 'view_standards':
      return 'Zobrazit standardy';
    case 'add_edit_standards':
      return 'Přidat/upravit standardy';
    case 'view_techniques':
      return 'Zobrazit techniky';
    case 'add_edit_techniques':
      return 'Přidat/upravit techniky';
    case 'view_projects':
      return 'Zobrazit projekty';
    case 'add_projects':
      return 'Přidat projekty';
    case 'edit_all_projects':
      return 'Upravit všechny projekty';
    case 'request_project_closure':
      return 'Požádat o uzavření projektu';
    case 'view_changelog':
      return 'Zobrazit změny';
    case 'view_analytical_code':
      return 'Zobrazit analytický kód';
    case 'view_synthetic_code':
      return 'Zobrazit syntetický kód';
    case 'view_technological_code':
      return 'Zobrazit technologický kód';
    case 'edit_project':
      return 'Upravit projekt';
    case 'manage_project_users':
      return 'Spravovat uživatele projektu';
    case 'manage_project_techniques':
      return 'Spravovat techniky projektu';
    case 'manage_project_chemicals':
      return 'Spravovat chemikálie projektu';
    case 'view_project_overviews_statistics':
      return 'Zobrazit přehledy a statistiky projektu';
    case 'sign_reopen_experiments':
      return 'Podepsat/otevřít experimenty';
    case 'view_analytical_department':
      return 'Zobrazit analytické oddělení';
    case 'edit_analytical_department':
      return 'Upravit analytické oddělení';
    case 'view_technological_department':
      return 'Zobrazit technologické oddělení';
    case 'edit_technological_department':
      return 'Upravit technologické oddělení';
    case 'view_syntetic_department':
      return 'Zobrazit syntetické oddělení';
    case 'edit_syntetic_department':
      return 'Upravit syntetické oddělení';
    case 'view_all':
      return 'Zobrazit vše';
    case 'edit_all':
      return 'Upravit vše';
    case 'deactivate_project':
      return 'Deaktivovat projekt';
    case 'sign_experiments':
      return 'Podepsat experimenty';
    case 'reopen_experiments':
      return 'Otevřít experimenty';
    case 'request_brute_force_closure':
      return 'Požádat o hrubé uzavření';
    case 'view_changelog_department':
      return 'Zobrazit změny oddělení';
    case 'view_changelog_analytical':
      return 'Zobrazit změny analytického oddělení';
    case 'view_changelog_technological':
      return 'Zobrazit změny technologického oddělení';
    case 'view_changelog_syntetic':
      return 'Zobrazit změny syntetického oddělení';
    case 'reopen_project':
      return 'Otevřít projekty';
    case 'activate_method':
      return 'Aktivovat metodu';
    case 'close_method':
      return 'Uzavřít metodu';
    case 'access_analytical_methods':
      return 'Přístup k analytickým metodám';
    case 'access_analytical_samples_rd':
      return 'Přístup k analytickým vzorkům RD';
    case 'access_analytical_samples_qc_vt':
      return 'Přístup k analytickým vzorkům QC VT';
    case 'access_analytical_standards':
      return 'Přístup ke standardům';
    case 'show_only_own_sample_results':
      return 'Zobrazit pouze výsledky vlastních vzorků';
    case 'access_analytical_sections':
      return 'Přístup k analytickým sekcím';
    case 'edit_finished_sample':
      return 'Upravit dokončené vzorky';
    case 'show_only_own_sample_ressults':
      return 'Zobrazit pouze výsledky vlastních vzorků';
    default:
      return name;
  }
};

export class User extends BaseConstructor<UserI>() implements UserI {
  constructor(data: UserDto) {
    super(data as unknown as UserI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    if (data.roles) {
      this.roles = data.roles.map((role) => new Role(role));
    }

    if (data.permissions) {
      this.permissions = data.permissions.map((permission) => new Permission(permission));
    }

    if (data.user_laborants) {
      this.userLaborants = data.user_laborants.map((user) => new User(user));
    }
  }

  get isActive() {
    return this.status === 'active';
  }

  get fullName() {
    return `${this.first_name.charAt(0).toUpperCase()}${this.first_name.slice(1)} ${this.last_name.charAt(0).toUpperCase()}${this.last_name.slice(1)}`;
  }

  hasRole(roleName: string) {
    return this.roles?.some((role) => role.name === roleName);
  }
  hasPermission(permissionId: number) {
    return this.roles?.some((role) =>
      role.permissions.some((permission) => permission.system_permission_id === permissionId)
    );
  }

  get getStatusTitle() {
    return this.status === 'active' ? 'Aktivní' : 'Neaktivní';
  }

  get getName() {
    return this.fullName;
  }

  async init() {
    const usersStore = useUsersStore();
    this.shortcuts = await usersStore.getUserShortcuts(this.user_id);
    this.shortcuts.sort((a, b) => a.order - b.order);
  }
}

interface AuthStore {
  user: User | null;
  token: AuthResponseI | null;
  returnUrl: string | null;
}

export interface AuthResponseI {
  access_token: string;
  refresh_token: string;
  token_type: string;
}

export const useAuthStore = defineStore({
  id: 'auth',
  state: () =>
    ({
      user: null,
      token: null,
      returnUrl: null
    }) as AuthStore,
  actions: {
    async login(username: string, password: string) {
      localStorage.removeItem('columns');
      const formData = new FormData();
      formData.append('username', username);
      formData.append('password', password);

      try {
        this.token = (await fetchWrapper.post(
          `${import.meta.env.VITE_API_URL}/auth/login_user/`,
          formData
        )) as unknown as AuthResponseI;
        const data = getJwtPayload(this.token.access_token);

        if (data) {
          await this.refreshUserData(data.user_id);

          localStorage.setItem('token', JSON.stringify(this.token));
          router.push(this.returnUrl || '/');
        }
      } catch (res) {
        const errorData = res as BaseResponseI<null>;
        notification.error({
          message: 'Chyba při přihlášení',
          description: errorData.error ?? 'Zkontrolujte prosím vaše přihlašovací údaje'
        });
      }
    },
    logout() {
      this.user = null;

      localStorage.removeItem('user');
      localStorage.removeItem('token');
      Object.keys(localStorage).forEach((key) => {
        if (key.endsWith('Store')) {
          localStorage.removeItem(key);
        }
      });

      router.push('/auth/login');
    },
    init() {
      const userData = localStorage.getItem('user');
      const tokenData = localStorage.getItem('token');

      try {
        this.user = userData ? JSON.parse(userData) : null;
        this.token = tokenData ? JSON.parse(tokenData) : null;
      } catch (e) {
        this.user = null;
        this.token = null;
      }

      this.getRefreshToken();
    },
    getAccessToken() {
      if (this.token) {
        return this.token.access_token;
      } else {
        this.logout();
      }
    },
    async getRefreshToken() {
      if (this.token) {
        try {
          this.token = (await fetchWrapper.post(
            `${import.meta.env.VITE_API_URL}/auth/refresh_token`
          )) as unknown as AuthResponseI;
          localStorage.setItem('token', JSON.stringify(this.token));
          await this.refreshUserData();

          return this.token.access_token;
        } catch (error) {
          this.logout();
        }
      } else {
        this.logout();
      }
    },

    async refreshUserData(user_id: number | null = null) {
      const usersStore = useUsersStore();
      if (this.user || user_id) {
        const User = user_id
          ? await usersStore.getUser(user_id)
          : this.user?.user_id
            ? await usersStore.getUser(this.user.user_id)
            : undefined;

        if (User) {
          this.user = User;
          await this.user.init();
          localStorage.setItem('user', JSON.stringify(this.user));
        }
      }
    }
  }
});
