import type { TagListI } from '@/components/shared/TagModal.vue';
import BaseConstructor from '@/utils/BaseConstructor';
import {
  loadParamsFailedReloadNotification,
  reloadWithoutParams,
  revertServerOptionsFromLocation,
  stringifyServerOptions,
  type BasePaginatorResponseI,
  type BaseResponseI
} from '@/utils/axios';
import { fetchWrapper } from '@/utils/helpers/fetch-wrapper';
import { notification } from 'ant-design-vue';
import { defineStore } from 'pinia';
import type { ServerOptions } from 'vue3-easy-data-table';
import { Experiment, type ExperimentDto } from './experiments';
import { File, type FileDto } from './files';
import {
  useProjectsStore,
  Department,
  type DepartmentDto,
  type GetAllOptions,
  type SimpleProjectForSearchDto,
  SimpleProjectForSearch
} from './projects';
import { Tag, type TagDto } from './tags';
import type { Attempt } from './attempts';
import type { Investigation } from './investigations';
import type { Message } from './messages';
import { type newFormDto, newForm } from './forms';
import type { AnalyticalRequest } from '@/stores/analyticalRequests/analyticalRequests.ts';
const baseUrl = `${import.meta.env.VITE_API_URL}/chapters`;

export enum ChapterSpecificType {
  VYVOJ_METODY = 'Vyvoj metody',
  VZORKY_RD = 'Vzorky RD',
  VZORKY_QC_VT = 'Vzorky QC a VT',
  STANDARDY = 'Standardy',
  SPECIFIKACE = 'Specifikace',
  CERTIFIKACE = 'Certifikace',
  TEST = 'Test'
}

export enum ChapterStatus {
  ACTIVE = 'active',
  DELETED = 'deleted',
  CLOSED = 'closed'
}

export interface ChapterForSearchDto {
  chapter_id: number;
  chapter_title: string;
  department_id: number;
  parent_chapter_id: number | null;
  status: string;
  editable: boolean;
  created_at: string;
  updated_at: string;
  files: FileDto[];
  tags: TagDto[];
}

export interface ChapterForSearchI {
  chapter_id: number;
  chapter_title: string;
  department_id: number;
  parent_chapter_id: number | null;
  status: string;
  editable: boolean;
  created_at: Date;
  updated_at: Date;
  files: File[];
  tags: Tag[];
}

export class ChapterForSearch
  extends BaseConstructor<ChapterForSearchI>()
  implements ChapterForSearchI
{
  constructor(data: ChapterForSearchDto) {
    super(data as unknown as ChapterForSearchI);
    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    this.files = data.files.map((file) => new File(file));
    this.tags = data.tags.map((tag) => new Tag(tag));
  }
}

export interface ChapterDto {
  chapter_id: number;
  department_id: number;
  parent_chapter_id: number | null;
  chapter_title: string;
  status: string;
  editable: boolean;
  created_at: string;
  updated_at: string;

  sub_chapters: ChapterDto[];
  forms: ExperimentDto[];
  tags: TagDto[];
  files: FileDto[];
}

export interface ChapterI {
  chapter_id: number;
  department_id: Department | number;
  parent_chapter_id: Chapter | number | null;
  chapter_title: string;
  status: ChapterStatus;
  editable: boolean;
  created_at: Date;
  updated_at: Date;
  sub_chapters: Chapter[];

  tagIsLoaded: boolean;
  showTags: boolean;

  forms: (Experiment | Attempt | Investigation | Message)[];
  tags: Tag[];
  files: File[];
}

export class Chapter extends BaseConstructor<ChapterI>() implements ChapterI {
  constructor(data: ChapterDto) {
    super(data as unknown as ChapterI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    this.department_id = data.department_id;
    this.parent_chapter_id = data.parent_chapter_id;

    this.sub_chapters = data.sub_chapters
      ? data.sub_chapters.map((chapter) => new Chapter(chapter))
      : [];
    this.forms = data.forms ? data.forms.map((form) => new Experiment(form)) : [];
    this.files = data.files ? data.files.map((file) => new File(file)) : [];
    this.tags = data.tags ? data.tags.map((tag) => new Tag(tag)) : [];

    this.tagIsLoaded = true;
    this.showTags = false;
  }

  async init() {
    const projectsStore = useProjectsStore();
    const chaptersStore = useChaptersStore();

    const Department =
      typeof this.department_id === 'number'
        ? await projectsStore.getDepartment(this.department_id)
        : this.department_id;
    this.department_id = Department ? Department : this.department_id;

    const ParentChapter =
      typeof this.parent_chapter_id === 'number'
        ? await chaptersStore.getChapter(this.parent_chapter_id)
        : this.parent_chapter_id;
    this.parent_chapter_id = ParentChapter ? ParentChapter : this.parent_chapter_id;

    this.getTags();
  }

  async getTags() {
    const chaptersStore = useChaptersStore();
    const tags = await chaptersStore.getTagsByChapter(this.chapter_id);
    this.tags = tags;

    this.tagIsLoaded = true;
  }

  get joinTags() {
    return this.tags?.map((t) => t.tag_name).join(', ') ?? '';
  }

  get tagLists() {
    return (
      this.tags?.map(
        (t) =>
          ({
            tag_id: t.tag_id,
            tag_name: t.tag_name
          }) as TagListI
      ) ?? []
    );
  }

  get isActive() {
    return this.status === ChapterStatus.ACTIVE;
  }
}

export interface SimpleDepartmentForSearchDto {
  chapter_id: number;
  project_department_id: number;
  project_id: number;
  shortcut: string;
  number: string;
  type: string;
  status: string;
  created_at: string;
  updated_at: string;
  project: SimpleProjectForSearchDto;
}

export interface SimpleDepartmentForSearchI {
  project_department_id: number;
  project_id: number;
  shortcut: string;
  number: string;
  type: string;
  status: string;
  created_at: Date;
  updated_at: Date;
  project: SimpleProjectForSearch;
}

export class SimpleDepartmentForSearch
  extends BaseConstructor<SimpleDepartmentForSearchI>()
  implements SimpleDepartmentForSearchI
{
  constructor(data: SimpleDepartmentForSearchDto) {
    super(data as unknown as SimpleDepartmentForSearchI);
    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);
    this.project = new SimpleProjectForSearch(data.project);
  }
}

export interface ChapterSearchDto {
  chapter_id: number;
  chapter_title: string;
  department_id: number;
  parent_chapter_id: number | null;
  status: string;
  editable: boolean;
  updated_at: string;
  created_at: string;
  project_department: SimpleDepartmentForSearchDto;
}

export interface ChapterSearchI {
  chapter_id: number;
  chapter_title: string;
  department_id: number;
  parent_chapter_id: number | null;
  status: string;
  editable: boolean;
  updated_at: Date;
  created_at: Date;
  project_department: SimpleDepartmentForSearch;
}
export class ChapterSearch extends BaseConstructor<ChapterSearchI>() implements ChapterSearchI {
  constructor(data: ChapterSearchDto) {
    super(data as unknown as ChapterSearchI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    this.project_department = new SimpleDepartmentForSearch(data.project_department);
  }
}

export interface DependenciesDto {
  chapters?: ChapterDto[];
  forms?: newFormDto[];
  departments?: DepartmentDto[];
}
export interface DependenciesI {
  chapters?: Chapter[];
  forms?: newForm[];
  departments?: Department[];
}
export class Dependencies extends BaseConstructor<DependenciesI>() implements DependenciesI {
  constructor(data: DependenciesDto) {
    super(data as unknown as DependenciesI);
    if (data.chapters) {
      this.chapters = data.chapters.map((chapter) => new Chapter(chapter));
    }
    if (data.forms) {
      this.forms = data.forms.map((form) => new newForm(form));
    }
    if (data.departments) {
      this.departments = data.departments.map((department) => new Department(department));
    }
  }
}

export interface canCloseDto {
  count_of_missing_dependencies: number;
  missing_dependencies?: DependenciesDto | undefined;
}

export interface canCloseI {
  count_of_missing_dependencies: number;
  missing_dependencies?: Dependencies | undefined;
}

export class canClose extends BaseConstructor<canCloseI>() implements canCloseI {
  constructor(data: canCloseDto) {
    super(data as unknown as canCloseI);
    if (data.missing_dependencies) {
      this.missing_dependencies = new Dependencies(data.missing_dependencies);
    }
  }
}

export type ChaptersListItemI = {
  chapter_id: number;
  department_id: number;
  parent_chapter_id: number | null;
  chapter_title: string;
  editable: boolean;

  status: string;
};

interface ChapterModalOptionsI {
  isEditing: boolean;
  isCreating: boolean;
  baseData: Chapter | undefined;
  newData: NewChapterModalDataI | undefined;
}

interface NewChapterModalDataI {
  department_id: number | undefined;
  parent_chapter_id: number | null;
  chapter_title: string | undefined;
  confirm: boolean | false;
}

interface ChaptersStateI {
  chapters: Map<number, Chapter>;
  chapter: Chapter | null;
  loading: boolean;

  showChapterModal: boolean;
  showTagModal: boolean;
  modalOptions: ChapterModalOptionsI | undefined;
  closeDependencies: canClose | undefined;
  totalItems?: number;
  options: ServerOptions;
  search: string | undefined;
}

export const useChaptersStore = defineStore({
  id: 'chapters',
  state: () =>
    ({
      chapter: null,
      chapters: new Map(),
      loading: false,

      showChapterModal: false,
      showTagModal: false,
      modalOptions: undefined,
      closeDependencies: undefined,
      search: undefined,
      totalItems: undefined,
      options: {
        page: 1,
        rowsPerPage: 25,
        sortBy: [],
        sortType: ['desc', 'asc']
      }
    }) as ChaptersStateI,
  actions: {
    setChapters(chapters: ChapterDto[]) {
      this.chapters = new Map(
        chapters.map((chapter) => [chapter.chapter_id, new Chapter(chapter)])
      );
    },

    async getAll(
      setData: boolean = true,
      options: GetAllOptions | undefined = {
        search_columns: ['chapter_shortcut']
      }
    ): Promise<{
      data: Chapter[];
      totalItems: number;
    }> {
      this.loading = true;

      const URL =
        baseUrl +
        '/?' +
        stringifyServerOptions(
          options && options.options ? options.options : this.options,
          options && options.search ? options.search : (this.search ?? null),
          options && options.search_columns ? options.search_columns : [],
          options && options.fixedFilterOptions ? options.fixedFilterOptions : null
        );

      return fetchWrapper
        .get<BasePaginatorResponseI<ChapterDto>>(URL)
        .then((res) => {
          if (res.status_code === 200) {
            this.totalItems = res.data.total_items;
            if (setData) {
              this.chapters = new Map(
                res.data.items.map((chapter) => [chapter.chapter_id, new Chapter(chapter)])
              );
              return {
                data: res.data.items.map((chapter) => new Chapter(chapter)),
                totalItems: res.data.total_items
              };
            }

            this.loading = false;
            return {
              data: res.data.items.map((chapter) => new Chapter(chapter)),
              totalItems: res.data.total_items
            };
          }
          this.loading = false;
          return {
            data: [],
            totalItems: 0
          };
        })
        .catch((res) => {
          if (res.status_code === 400) {
            loadParamsFailedReloadNotification('Načtení kapitol selhalo', res.error, () => {
              const data = reloadWithoutParams();
              this.options = data.options;
              this.search = data.term ?? undefined;
            });
          }

          this.loading = false;
          return {
            data: [],
            totalItems: 0
          };
        });
    },

    async getChapter(id: number) {
      this.loading = true;

      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/chapter/${id}`)
        .then((res: BaseResponseI<ChapterDto>) => {
          if (res.status_code === 200) {
            this.loading = false;
            return new Chapter(res.data);
          }

          return undefined;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Načtení kapitoly selhalo', description: res.error });
          }

          this.loading = false;
          return undefined;
        });
    },

    setParamsFromLocation(): void {
      const data = revertServerOptionsFromLocation();
      this.options = data.options;
      this.search = data.term ?? undefined;
    },

    showNewChapterModal(department_id: number | undefined, parent_chapter_id: number | null) {
      this.modalOptions = {
        isEditing: false,
        isCreating: true,
        baseData: undefined,
        newData: {
          department_id: department_id,
          parent_chapter_id: parent_chapter_id,
          chapter_title: '',
          confirm: false
        }
      };

      this.showChapterModal = true;
    },

    showPreviewModal(id: number) {
      this.chapter = this.chapters.get(id) ?? null;
      if (this.chapter === undefined) {
        notification.error({ message: 'Kapitola nebyla nalezena' });
        return;
      }

      this.modalOptions = {
        isEditing: false,
        isCreating: false,
        baseData: this.chapter ?? undefined,
        newData: {
          department_id:
            typeof this.chapter?.department_id !== 'number'
              ? this.chapter?.department_id.project_department_id
              : this.chapter?.department_id,
          parent_chapter_id:
            typeof this.chapter?.parent_chapter_id !== 'number'
              ? (this.chapter?.parent_chapter_id?.chapter_id ?? null)
              : this.chapter?.parent_chapter_id,
          chapter_title: this.chapter?.chapter_title ?? '',
          confirm: false
        }
      };

      this.showChapterModal = true;
    },

    async showEditModal(id: number) {
      const _Chapter = await this.getChapter(id);
      if (!_Chapter) {
        notification.error({ message: 'Kapitola nebyla nalezena' });
        return;
      }

      this.chapter = _Chapter;

      this.modalOptions = {
        isEditing: true,
        isCreating: false,
        baseData: _Chapter,
        newData: {
          department_id:
            typeof _Chapter.department_id !== 'number'
              ? _Chapter.department_id.project_department_id
              : _Chapter.department_id,
          parent_chapter_id:
            typeof _Chapter.parent_chapter_id !== 'number'
              ? (_Chapter.parent_chapter_id?.chapter_id ?? null)
              : _Chapter.parent_chapter_id,
          chapter_title: _Chapter.chapter_title ?? '',
          confirm: false
        }
      };

      this.showChapterModal = true;
    },
    async checkIfCanCloseChapter(chapter: number): Promise<canClose | false> {
      if (!chapter) {
        notification.error({ message: 'Není co zavírat' });
        return false;
      }
      this.loading = true;
      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/chapter/${chapter}/can_be_closed`)
        .then((res: BaseResponseI<canCloseDto>) => {
          if (res.status_code === 200) {
            this.closeDependencies = new canClose(res.data);
            this.loading = false;
            return this.closeDependencies;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Zjištění selhalo', description: res.error });
          }

          this.loading = false;
          return false;
        });
    },
    async exportChapter(chapter_id: number, form_type: string) {
      this.loading = true;
      return fetchWrapper
        .blob(`${import.meta.env.VITE_API_URL}/chapter/${chapter_id}/export?form_type=${form_type}`)
        .then((res) => {
          this.loading = false;

          const url = window.URL.createObjectURL(new Blob([res]));
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute(
            'download',
            `${form_type}_forms_for_${chapter_id}.pdf`
          );
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          return true;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Analýza nešla exportovat', description: res.message });
          } else {
            notification.error({ message: 'Analýza nešla exportovat' });
          }

          return false;
        });
    },
    async exportChapterToWord(chapter_id: number, form_type: string) {
      this.loading = true;
      return fetchWrapper
        .blob(`${import.meta.env.VITE_API_URL}/chapter/${chapter_id}/export_to_word?form_type=${form_type}`)
        .then((res) => {
          this.loading = false;

          const url = window.URL.createObjectURL(new Blob([res]));
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute(
            'download',
            `${form_type}_forms_for_${chapter_id}.docx`
          );
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          return true;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Analýza nešla exportovat', description: res.message });
          } else {
            notification.error({ message: 'Analýza nešla exportovat' });
          }

          return false;
        });
    },
    async closeChapter(id: number, force: boolean) {
      if (!id) {
        notification.error({ message: 'Není co zavírat' });
        return false;
      }
      this.loading = true;
      const data = {
        status: 'closed',
        brute_force_close: force
      };

      return fetchWrapper
        .patch(`${import.meta.env.VITE_API_URL}/chapter/${id}/status`, data)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            this.chapters.delete(id);
            notification.success({ message: 'Kapitola byl úspěšně uzavřen' });
            this.getAll();
            this.loading = false;

            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Zavření kapitoli selhalo', description: res.error });
          }

          this.loading = false;
          return false;
        });
    },
    reactivateChapter(id: number) {
      if (!id) {
        notification.error({ message: 'Není co otvírat' });
        return false;
      }
      this.loading = true;
      const data = {
        status: 'active',
        brute_force_close: false
      };

      return fetchWrapper
        .patch(`${import.meta.env.VITE_API_URL}/chapter/${id}/status`, data)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            this.chapters.delete(id);
            notification.success({ message: 'Kapitola byla úspěšně otevřena' });
            this.getAll();
            this.loading = false;

            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Aktivace kapitoly selhala', description: res.error });
          }

          this.loading = false;
          return false;
        });
    },

    async createChapter() {
      if (!this.modalOptions?.newData || this.modalOptions?.newData?.confirm === false) {
        notification.error({ message: 'Musíte potvrdit souhlas s vytvořením kapitoly' });
        return false;
      }

      this.loading = true;

      const data = {
        department_id: this.modalOptions.newData.department_id,
        parent_chapter_id: this.modalOptions.newData.parent_chapter_id,
        chapter_title: this.modalOptions.newData.chapter_title
      };

      return await fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/chapter/`, data)
        .then((res: BaseResponseI<ChapterDto>) => {
          if (res.status_code === 200) {
            this.chapters.set(res.data.chapter_id, new Chapter(res.data));
            this.showChapterModal = false;

            notification.success({
              message: 'Vytvoření kapitoly proběhlo v pořádku',
              description: 'Název: ' + res.data.chapter_title
            });

            this.loading = false;
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Vytvoření kapitoly selhalo', description: res.error });
          } else {
            this.showChapterModal = false;
          }

          this.loading = false;
          return false;
        });
    },

    async updateChapter() {
      if (!this.modalOptions?.newData) {
        notification.error({ message: 'Není co upravovat' });
        return false;
      }

      this.loading = true;

      const data = {
        department_id: this.modalOptions.newData.department_id,
        parent_chapter_id: this.modalOptions.newData.parent_chapter_id ?? null,
        chapter_title: this.modalOptions.newData.chapter_title,
        status: this.modalOptions.baseData?.status ?? ChapterStatus.ACTIVE
      };

      return fetchWrapper
        .put(`${import.meta.env.VITE_API_URL}/chapter/${this.chapter?.chapter_id}`, data)
        .then((res: BaseResponseI<ChapterDto>) => {
          if (res.status_code === 200) {
            this.chapters.set(res.data.chapter_id, new Chapter(res.data));
            this.showChapterModal = false;

            notification.success({
              message: 'Úprava kapitoly proběhla v pořádku',
              description: 'Název: ' + res.data.chapter_title
            });
            this.getAll();
            this.loading = false;
            return true;
          }

          this.loading = false;
          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Úprava kapitoly selhala', description: res.error });
          } else {
            this.showChapterModal = false;
          }

          this.loading = false;
          return false;
        });
    },

    async getTagsByChapter(id: number): Promise<Tag[]> {
      this.loading = true;

      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/chapter/${id}/tags`)
        .then((res: BaseResponseI<TagDto[]>) => {
          if (res.status_code === 200) {
            this.loading = false;
            return res.data;
          }

          this.loading = false;
          return [];
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Načtení štítků selhalo', description: res.error });
          }

          this.loading = false;
          return [];
        });
    },

    async updateChapterTags(chapter_id: number, tags: number[]) {
      this.loading = true;

      return fetchWrapper
        .patch(`${import.meta.env.VITE_API_URL}/chapter/${chapter_id}/tags`, { tags: tags })
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Štítky byly úspěšně upraveny' });
            this.loading = false;
          }
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Úprava štítků selhala', description: res.error });
          }

          this.loading = false;
        });
    },

    resetModal() {
      this.showChapterModal = false;
      if (this.modalOptions) this.modalOptions.baseData = undefined;
      if (this.modalOptions) this.modalOptions.newData = undefined;
    },

    async updateTags(chapter: Chapter, tags: TagListI[]) {
      await this.updateChapterTags(
        chapter.chapter_id,
        tags.map((tag) => tag.tag_id)
      );

      chapter.getTags();
      chapter.showTags = false;
    },

    async addFileToChapter(chapter_id: number, file_id: number) {
      this.loading = true;

      return fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/chapter/${chapter_id}/file/${file_id}`)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Soubor byl úspěšně přidán' });
            this.loading = false;
          }
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Přidání souboru selhalo', description: res.error });
          }

          this.loading = false;
        });
    },

    async processMultipleFiles(chapter_id: number | null | undefined, files: number[]) {
      if (chapter_id === null || chapter_id === undefined) {
        notification.error({ message: 'Není vybrána kapitola' });
        return;
      }

      files.forEach(async (file_id) => await this.addFileToChapter(chapter_id, file_id));
    },

    async deleteFileFromChapter(chapter_id: number, file_id: number): Promise<boolean> {
      this.loading = true;

      return await fetchWrapper
        .delete(`${import.meta.env.VITE_API_URL}/chapter/${chapter_id}/file/${file_id}`)
        .then((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 200) {
            notification.success({ message: 'Soubor byl úspěšně odebrán' });
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Odebrání souboru selhalo', description: res.error });
          }

          return false;
        });
    },
    async requestReopenChapter(chapter_id: number) {
      if (!chapter_id) {
        notification.error({ message: 'Není zvolena kapitola' });
        return false;
      }
      this.loading = true;

      const URL = `${import.meta.env.VITE_API_URL}/chapter/${chapter_id}/request_reopen`;

      return fetchWrapper
        .post(URL)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Žádost byla úspešně odeslána' });
            this.loading = false;
          }
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Odeslání žádosti selhalo', description: res.error });
          }

          this.loading = false;
        });
    }
  }
});
