import BaseConstructor from '@/utils/BaseConstructor';

export enum ChemicalStatus {
  ACTIVE = 'active',
  DELETED = 'deleted'
}

export enum ChemicalType {
  CHEMICAL = 'chemical',
  RESULT = 'result',
  FIRST_CHEMICAL = 'first_chemical'
}

export interface NewChemicalDataI {
  name: string | undefined;
  density: number | undefined;
  molar_mass: number | undefined;
  notes: string | undefined;
  grams: number | undefined;
  moles: number | undefined;
  equivalent: number | undefined;
  volume_ml: number | undefined;
  concentration: number | undefined | string;
  type: ChemicalType | undefined;
}

export interface ChemicalDto {
  chemical_id: number;
  form_id: number | null;
  project_template_id: number | null;
  name: string;
  density: number;
  molar_mass: number;
  notes: string;
  grams: number;
  moles: number;
  equivalent: number;
  volume_ml: number;
  concentration: number;
  status: string;
  type: string;
  created_at: string;
  updated_at: string;
}

export interface ChemicalI {
  chemical_id: number;
  form_id: number | null;
  project_template_id: number | null;
  name: string;
  density: number;
  molar_mass: number;
  notes: string;
  grams: number;
  moles: number;
  equivalent: number;
  volume_ml: number;
  concentration: number;

  status: ChemicalStatus;
  type: ChemicalType;

  created_at: Date;
  updated_at: Date;
}

export class Chemical extends BaseConstructor<ChemicalI>() implements ChemicalI {
  constructor(data: ChemicalDto) {
    super(data as unknown as ChemicalI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    this.status = data.status as ChemicalStatus;
    this.type = data.type as ChemicalType;
  }
}

// interface ChemicalModalOptionsI {
//   isEditing: boolean;
//   isCreating: boolean;
//   baseData: Chemical | undefined;
//   newData: NewChemicalModalDataI | undefined;
// }

// export interface NewChemicalDataI {
//   name: string | undefined;
//   density: number | undefined;
//   molar_mass: number | undefined;
//   notes: string | undefined;
//   grams: number | undefined;
//   moles: number | undefined;
//   equivalent: number | undefined;
//   volume_ml: number | undefined;
//   concentration: number | undefined;
//   type: ChemicalType | undefined;
// }

// interface NewChemicalModalDataI extends NewChemicalDataI {
//   form_id: number | null | undefined;
//   experiment_template_id: number | null | undefined;
//   status: string | ChemicalStatus | undefined;
//   confirm: boolean | false;
// }

// interface ChemicalsStateI {
//   chemicals: Map<number, Chemical>;
//   chemical: Chemical | null;
//   loading: boolean;

//   showChemicalModal: boolean;
//   modalOptions: ChemicalModalOptionsI | undefined;

//   totalItems?: number;
//   options: ServerOptions;
//   search: string | undefined;
// }

// export const useChemicalsStore = defineStore({
//   id: 'chemicals',
//   state: () =>
//     ({
//       chemical: null,
//       chemicals: new Map(),
//       loading: false,

//       showChemicalModal: false,
//       modalOptions: undefined,

//       items: [],
//       search: undefined,
//       totalItems: undefined,
//       options: {
//         page: 1,
//         rowsPerPage: 25,
//         sortBy: [],
//         sortType: ['desc', 'asc']
//       }
//     }) as ChemicalsStateI,
//   actions: {
//     setChemicals(chemicals: ChemicalDto[]) {
//       this.chemicals = new Map(chemicals.map((chemical) => [chemical.chemical_id, new Chemical(chemical)]));
//     },

//     async getAll(
//       setData: boolean = true,
//       options: GetAllOptions | undefined = {
//         search_columns: ['name']
//       }
//     ): Promise<{
//       data: Chemical[];
//       totalItems: number;
//     }> {
//       this.loading = true;

//       const URL =
//         baseUrl +
//         '/?' +
//         stringifyServerOptions(
//           options && options.options ? options.options : this.options,
//           options && options.search ? options.search : (this.search ?? null),
//           options && options.search_columns ? options.search_columns : [],
//           options && options.fixedFilterOptions ? options.fixedFilterOptions : null
//         );

//       return fetchWrapper
//         .get(URL)
//         .then((res: BasePaginatorResponseI<ChemicalDto>) => {
//           if (res.status_code === 200) {
//             this.totalItems = res.data.total_items;
//             if (setData) {
//               this.chemicals = new Map(res.data.items.map((chemical) => [chemical.chemical_id, new Chemical(chemical)]));
//             }

//             this.loading = false;
//             return {
//               data: res.data.items.map((chemical) => new Chemical(chemical)),
//               totalItems: res.data.total_items
//             };
//           }
//           this.loading = false;
//           return {
//             data: [],
//             totalItems: 0
//           };
//         })
//         .catch((res) => {
//           if (res.status_code === 400) {
//             loadParamsFailedReloadNotification('Načtení chemikálie selhalo', res.error, () => {
//               const data = reloadWithoutParams();
//               this.options = data.options;
//               this.search = data.term ?? undefined;
//             });
//           }

//           this.loading = false;
//           return {
//             data: [],
//             totalItems: 0
//           };
//         });
//     },

//     async getChemical(id: number) {
//       this.loading = true;

//       return fetchWrapper
//         .get(`${import.meta.env.VITE_API_URL}/chemical/${id}`)
//         .then((res: BaseResponseI<ChemicalDto>) => {
//           if (res.status_code === 200) {
//             this.loading = false;
//             return new Chemical(res.data);
//           }

//           this.loading = false;
//           return undefined;
//         })
//         .catch((res: BaseResponseI<null>) => {
//           if (res.status_code === 400) {
//             notification.error({ message: 'Načtení chemikálie selhalo', description: res.error });
//           }

//           this.loading = false;
//           return undefined;
//         });
//     },

//     setParamsFromLocation(): void {
//       const data = revertServerOptionsFromLocation();
//       this.options = data.options;
//       this.search = data.term ?? undefined;
//     },

//     showNewChemicalModal() {
//       this.modalOptions = {
//         isEditing: false,
//         isCreating: true,
//         baseData: undefined,
//         newData: {
//           form_id: undefined,
//           experiment_template_id: undefined,
//           name: undefined,
//           density: undefined,
//           molar_mass: undefined,
//           notes: undefined,
//           grams: undefined,
//           moles: undefined,
//           equivalent: undefined,
//           volume_ml: undefined,
//           concentration: undefined,
//           status: ChemicalStatus.ACTIVE,
//           type: ChemicalType.CHEMICAL,
//           confirm: false
//         }
//       };

//       this.showChemicalModal = true;
//     },

//     async showPreviewModal(id: number) {
//       const Chemical = await this.getChemical(id);
//       if (Chemical === undefined) {
//         notification.error({ message: 'Chemikálie nebyla nalezena' });
//         return;
//       }

//       this.modalOptions = {
//         isEditing: false,
//         isCreating: false,
//         baseData: Chemical,
//         newData: {
//           form_id: Chemical.form_id,
//           experiment_template_id: Chemical.experiment_template_id,
//           name: Chemical.name,
//           density: Chemical.density,
//           molar_mass: Chemical.molar_mass,
//           notes: Chemical.notes,
//           grams: Chemical.grams,
//           moles: Chemical.moles,
//           equivalent: Chemical.equivalent,
//           volume_ml: Chemical.volume_ml,
//           concentration: Chemical.concentration,
//           status: Chemical.status,
//           type: Chemical.type,
//           confirm: false
//         }
//       };

//       this.showChemicalModal = true;
//     },

//     async showEditModal(id: number) {
//       const Chemical = await this.getChemical(id);
//       if (Chemical === undefined) {
//         notification.error({ message: 'Chemikálie nebyla nalezena' });
//         return;
//       }

//       this.modalOptions = {
//         isEditing: true,
//         isCreating: false,
//         baseData: this.chemical ?? undefined,
//         newData: {
//           form_id: Chemical.form_id,
//           experiment_template_id: Chemical.experiment_template_id,
//           name: Chemical.name,
//           density: Chemical.density,
//           molar_mass: Chemical.molar_mass,
//           notes: Chemical.notes,
//           grams: Chemical.grams,
//           moles: Chemical.moles,
//           equivalent: Chemical.equivalent,
//           volume_ml: Chemical.volume_ml,
//           concentration: Chemical.concentration,
//           status: Chemical.status,
//           type: Chemical.type,
//           confirm: false
//         }
//       };

//       this.showChemicalModal = true;
//     },

//     deleteChemical(id: number) {
//       this.loading = true;

//       return fetchWrapper
//         .delete(`${import.meta.env.VITE_API_URL}/chemical/${id}`)
//         .then((res: BaseResponseI<null>) => {
//           if (res.status_code === 200) {
//             this.chemicals.delete(id);
//             notification.success({ message: 'Chemikálie byla úspěšně smazána' });
//             this.getAll();
//             this.loading = false;
//           }
//         })
//         .catch((res: BaseResponseI<null>) => {
//           if (res.status_code === 400) {
//             notification.error({ message: 'Smazání přístroje selhalo', description: res.error });
//           }

//           this.loading = false;
//         });
//     },

//     async createChemical() {
//       if (!this.modalOptions?.newData || this.modalOptions?.newData?.confirm === false) {
//         notification.error({ message: 'Musíte potvrdit souhlas s vytvořením chemikálie' });
//         return false;
//       }

//       this.loading = true;

//       const data = {
//         form_id: this.modalOptions?.newData?.form_id,
//         name: this.modalOptions?.newData?.name,
//         density: this.modalOptions?.newData?.density,
//         molar_mass: this.modalOptions?.newData?.molar_mass,
//         notes: this.modalOptions?.newData?.notes,
//         grams: this.modalOptions?.newData?.grams,
//         moles: this.modalOptions?.newData?.moles,
//         equivalent: this.modalOptions?.newData?.equivalent,
//         volume_ml: this.modalOptions?.newData?.volume_ml,
//         concentration: this.modalOptions?.newData?.concentration,
//         status: this.modalOptions?.newData?.status,
//         type: this.modalOptions?.newData?.type
//       };

//       return await fetchWrapper
//         .post(`${import.meta.env.VITE_API_URL}/chemical/`, data)
//         .then((res: BaseResponseI<ChemicalDto>) => {
//           if (res.status_code === 200) {
//             this.chemicals.set(res.data.chemical_id, new Chemical(res.data));
//             this.showChemicalModal = false;

//             notification.success({
//               message: 'Vytvoření chemikálie proběhlo v pořádku',
//               description: 'Název: ' + res.data.name
//             });

//             this.loading = false;
//             return true;
//           }

//           return false;
//         })
//         .catch((res: BaseResponseI<null>) => {
//           if (res.status_code === 400) {
//             notification.error({ message: 'Vytvoření chemikálie selhalo', description: res.error });
//           } else {
//             this.showChemicalModal = false;
//           }

//           this.loading = false;
//           return false;
//         });
//     },

//     updateChemical() {
//       if (!this.modalOptions?.newData) {
//         notification.error({ message: 'Není co upravovat' });
//         return;
//       }

//       this.loading = true;

//       const data = {};

//       return fetchWrapper
//         .put(`${import.meta.env.VITE_API_URL}/chemical/${this.chemical?.chemical_id}`, data)
//         .then((res: BaseResponseI<ChemicalDto>) => {
//           if (res.status_code === 200) {
//             this.chemicals.set(res.data.chemical_id, new Chemical(res.data));
//             this.showChemicalModal = false;

//             notification.success({
//               message: 'Úprava chemikálie proběhla v pořádku',
//               description: 'Název: ' + res.data.name
//             });
//             this.getAll();
//             this.loading = false;
//           }
//         })
//         .catch((res: BaseResponseI<null>) => {
//           if (res.status_code === 400) {
//             notification.error({ message: 'Úprava chemikálie selhala', description: res.error });
//           } else {
//             this.showChemicalModal = false;
//           }

//           this.loading = false;
//         });
//     },
//     resetModal() {
//       this.showChemicalModal = false;
//       if (this.modalOptions) this.modalOptions.baseData = undefined;
//       if (this.modalOptions) this.modalOptions.newData = undefined;
//     }
//   }
// });
