import BaseConstructor from '@/utils/BaseConstructor';
import {
  loadParamsFailedReloadNotification,
  reloadWithoutParams,
  revertServerOptionsFromLocation,
  stringifyServerOptions,
  type BasePaginatorResponseI,
  type BaseResponseI
} from '@/utils/axios';
import { fetchWrapper } from '@/utils/helpers/fetch-wrapper';
import { formatDate } from '@/utils/locales';
import { notification } from 'ant-design-vue';
import { defineStore } from 'pinia';
import type { GetAllOptions } from './projects';
import type { ServerOptions } from 'vue3-easy-data-table';
import { Project } from './projects';
import { Technique, type TechniqueDto } from './techniques';
import {
  AnalyticalRequestForSample,
  type AnalyticalRequestForSampleDto
} from './analyticalRequests/analyticalRequests';
import { Instrument, type InstrumentDto } from './instruments';
import { SampleParameters, type SampleParametersDto } from './sample/samples';
import { File, type FileDto } from './files';
import { en } from 'vuetify/locale';
import { watch } from 'vue';
import { User, type UserDto } from './auth';
import { useStorage } from '@vueuse/core';

const baseUrl = `${import.meta.env.VITE_API_URL}/kolonas`;

export const roundTableData = (value: number, decimals: number = 5): number => {
  return Number(Math.round(Number(value + 'e' + decimals)) + 'e-' + decimals);
};

export enum ColumnHistoryStatus {
  CREATED = 'created',
  REANALYS = 'reanalysis',
  DONE = 'done',
  CANCELED = 'canceled'
}

export enum ColumnHistoryType {
  INTERNAL = 'internal',
  EXTERNAL = 'external'
}

export enum ColumnHistoryAnalysisStatus {
  NA = 'na',
  COMPLIANT = 'compliant',
  NON_COMPLIANT = 'non_compliant'
}

export interface ColumnHistoryDto {
  sample_id: number;
  user_id: number;
  analytical_request_id: number;
  technique_id: number;
  instrument_id: number | null;
  kolona_id: number | null;
  project_id: number;
  sample_number: string;
  sequence_name: string;
  method_name: string;
  preparation_of_standard_and_sample: string;
  note_or_specification: string;
  technique_notes: string;
  result: string;
  analysis_status: string;
  status: string;
  type: string;
  reanalyse_at: string | null;
  created_at: string;
  updated_at: string;
  analytical_request?: AnalyticalRequestForSampleDto | null;
  user?: UserDto | null;
  technique?: TechniqueDto | null;
  kolona?: ColumnDto | null;
  instrument?: InstrumentDto | null;
  sub_tech?: TechniqueDto | null;
  parameters?: SampleParametersDto[];
  files?: FileDto[];
}

export interface ColumnHistoryI {
  sample_id: number;
  user_id: number;
  analytical_request_id: number;
  technique_id: number;
  instrument_id: number | null;
  kolona_id: number | null;
  project_id: number;
  sample_number: string;
  sequence_name: string;
  method_name: string;
  preparation_of_standard_and_sample: string;
  note_or_specification: string;
  technique_notes: string;
  result: string;
  analysis_status: ColumnHistoryAnalysisStatus;
  status: ColumnHistoryStatus;
  type: ColumnHistoryType;
  reanalyse_at: Date | null;
  created_at: Date;
  updated_at: Date;
  analytical_request?: AnalyticalRequestForSample | null;
  user?: User | null;
  technique?: Technique | null;
  kolona?: Column | null;
  instrument?: Instrument | null;
  sub_tech?: Technique | null;
  parameters?: SampleParameters[];
  files?: File[];
}

export class ColumnHistory extends BaseConstructor<ColumnHistoryI>() implements ColumnHistoryI {
  constructor(data: ColumnHistoryDto) {
    super(data as unknown as ColumnHistoryI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);
    this.reanalyse_at = data.reanalyse_at ? new Date(data.reanalyse_at) : null;

    if (data.analytical_request)
      this.analytical_request = new AnalyticalRequestForSample(data.analytical_request);
    if (data.user) this.user = new User(data.user);
    if (data.technique) this.technique = new Technique(data.technique);
    if (data.kolona) this.kolona = new Column(data.kolona);
    if (data.instrument) this.instrument = new Instrument(data.instrument);
    if (data.sub_tech) this.sub_tech = new Technique(data.sub_tech);
    if (data.parameters)
      this.parameters = data.parameters.map((param) => new SampleParameters(param));
    if (data.files) this.files = data.files.map((file) => new File(file));
  }

  get getInstrumentName() {
    return this.instrument_id ? 'N/A' : 'N/A';
  }
}

export interface ColumnUsageHistoryDto {
  samples: ColumnHistoryDto[];
  versions: [];
}

export interface ColumnUsageHistoryI {
  samples: ColumnHistory[];
  versions: [];
}

export class ColumnUsageHistory
  extends BaseConstructor<ColumnUsageHistoryI>()
  implements ColumnUsageHistoryI
{
  constructor(data: ColumnUsageHistoryDto) {
    super(data as unknown as ColumnUsageHistoryI);
    this.samples = data.samples.map((sample) => new ColumnHistory(sample));
  }
}

export interface ColumnDto {
  kolona_id: number;
  technique_id?: number;
  technique?: number | TechniqueDto;
  techniques?: TechniqueDto[];
  name: string;
  chemistry: string;
  diameter: number;
  length: number;
  particles: string;
  serial_number: string;
  catalog_number: string;
  manufacturer: string;
  used_since: string;
  note: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface ColumnI {
  kolona_id: number;
  technique_id?: number;
  technique?: Technique | number;
  techniques?: Technique[];
  name: string;
  chemistry: string;
  diameter: number;
  length: number;
  particles: number;
  serial_number: string;
  catalog_number: string;
  manufacturer: string;
  used_since: Date;
  note: string;
  status: string;
  created_at: Date;
  updated_at: Date;
}

export class Column extends BaseConstructor<ColumnI>() implements ColumnI {
  constructor(data: ColumnDto) {
    super(data as unknown as ColumnI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);
    this.used_since = new Date(data.used_since);

    this.particles = typeof data.particles === 'number' ? data.particles : Number(data.particles);

    if (data.techniques) {
      this.techniques = data.techniques.map((technique) => new Technique(technique));
    }

    if (data.technique) {
      this.technique =
        typeof data.technique === 'number' ? data.technique : new Technique(data.technique);
    }
  }
}

export type ColumnsListItemI = {
  kolona_id: number;
  technique: Technique | number | Technique[];
  techniques?: Technique[];
  technique_id: number;
  name: string;
  chemistry: string;
  diameter: number;
  length: number;
  particles: number;
  serial_number: string;
  catalog_number: string;
  manufacturer: string;
  used_since: Date;
  note: string;
  status: string;
};

interface ColumnModalOptionsI {
  isEditing: boolean;
  isCreating: boolean;
  baseData: Column | undefined;
  newData: NewColumnModalDataI | undefined;
  defaultTab?: string | undefined;
}

interface NewColumnModalDataI {
  technique_id: number | undefined;
  technique_ids: number[] | undefined;
  name: string | undefined;
  chemistry: string | undefined;
  diameter: number | undefined;
  length: number | undefined;
  particles: number | undefined;
  serial_number: string | undefined;
  catalog_number: string | undefined;
  manufacturer: string | undefined;
  used_since: Date | undefined;
  note: string | undefined;
  status: string | undefined;
  confirm: boolean | false;
}

interface ColumnsStateI {
  columns: Map<number, Column>;
  column: Column | null;
  loading: boolean;

  showColumnModal: boolean;
  modalOptions: ColumnModalOptionsI | undefined;

  items: ColumnsListItemI[];
  totalItems?: number;
  historyLength?: number;
  historyItemsColumns: Map<number, ColumnHistory>;
  historyItems: ColumnHistoryI[];
  options: ServerOptions;
  search: string | undefined;
  search_type?: 'AND' | 'OR';
  fixedFilterOptions: null | Array<{
    column: string;
    value: null | number | string | string[] | number[] | object | object[];
  }>;
}

export const useColumnsStore = defineStore({
  id: 'columns',
  state: () =>
    ({
      column: null,
      columns: new Map(),
      loading: false,
      historyItemsColumns: new Map(),
      showColumnModal: false,
      modalOptions: {
        isEditing: false,
        isCreating: false,
        baseData: undefined,
        newData: {
          technique_id: undefined,
          technique_ids: undefined,
          name: undefined,
          chemistry: undefined,
          diameter: undefined,
          length: undefined,
          particles: undefined,
          serial_number: undefined,
          catalog_number: undefined,
          manufacturer: undefined,
          used_since: undefined,
          note: undefined,
          status: 'active',
          confirm: false
        }
      },
      items: [],
      historyItems: [],
      search: undefined,
      totalItems: undefined,
      historyLength: undefined,
      options: {
        page: 1,
        rowsPerPage: 25,
        sortBy: [],
        sortType: ['desc', 'asc']
      },
      fixedFilterOptions: null,
      search_type: undefined
    }) as ColumnsStateI,
  actions: {
    setColumns(columns: ColumnDto[]) {
      this.columns = new Map(columns.map((column) => [column.kolona_id, new Column(column)]));
    },

    async getAll() {
      this.loading = true;
      this.options.sortBy = ['status', 'kolona_id'];
      this.options.sortType = ['asc'];
      const URL =
        baseUrl +
        '/?' +
        stringifyServerOptions(
          this.options,
          this.search ?? null,
          ['name', 'kolona_id', 'chemistry', 'serial_number'],
          null,
          'OR'
        );

      fetchWrapper
        .get(URL)
        .then(async (res: BasePaginatorResponseI<ColumnDto>) => {
          if (res.status_code === 200) {
            this.totalItems = res.data.total_items;
            this.columns = new Map(
              res.data.items.map((column) => [column.kolona_id, new Column(column)])
            );
            this.items = [...this.columns.values()].map((column) => {
              return {
                kolona_id: column.kolona_id,
                name: column.name,
                chemistry: column.chemistry,
                diameter: column.diameter,
                length: column.length,
                particles: column.particles,
                serial_number: column.serial_number,
                catalog_number: column.catalog_number,
                technique: column.technique,
                techniques: column.techniques,
                manufacturer: column.manufacturer,
                used_since: column.used_since,
                note: column.note,
                status: column.status,
                technique_id: column.technique_id
              } as ColumnsListItemI;
            });
          }
          this.loading = false;
        })
        .catch((res) => {
          if (res.status_code === 400) {
            loadParamsFailedReloadNotification('Načtení kolon selhalo', res.error, () => {
              const data = reloadWithoutParams();
              this.options = data.options;
              this.search = data.term ?? undefined;
            });
          }

          this.loading = false;
        });
    },
    async getColumns(
      options: GetAllOptions | undefined = {
        search_columns: ['name']
      },
      search_type: 'AND' | 'OR' = 'OR'
    ): Promise<{
      data: ColumnDto[];
      totalItems: number;
    }> {
      this.loading = true;

      const URL =
        `${import.meta.env.VITE_API_URL}/kolonas/` +
        '?' +
        stringifyServerOptions(
          options && options.options ? options.options : this.options,
          options && options.search ? options.search : (this.search ?? null),
          options && options.search_columns ? options.search_columns : [],
          options && options.fixedFilterOptions ? options.fixedFilterOptions : null,
          this.search_type ?? search_type
        );

      return fetchWrapper
        .get(URL)
        .then((res: BasePaginatorResponseI<ColumnDto>) => {
          if (res.status_code === 200) {
            this.totalItems = res.data.total_items;
            this.columns = new Map(
              res.data.items.map((column) => [column.kolona_id, new Column(column)])
            );

            this.items = [...this.columns.values()].map((column) => ({
              kolona_id: column.kolona_id,
              name: column.name,
              chemistry: column.chemistry,
              diameter: column.diameter,
              length: column.length,
              particles: column.particles,
              serial_number: column.serial_number,
              catalog_number: column.catalog_number,
              technique: column.technique,
              manufacturer: column.manufacturer,
              used_since: column.used_since,
              note: column.note,
              status: column.status,
              technique_id: column.technique_id
            })) as ColumnsListItemI[];
          }

          this.loading = false;
          return {
            data: [],
            totalItems: 0
          };
        })
        .catch((res) => {
          if (res.status_code === 400) {
            loadParamsFailedReloadNotification('Načtení kolon selhalo', res.error, () => {
              const data = reloadWithoutParams();
              this.options = data.options;
              this.search = data.term ?? undefined;
            });
          }
          this.loading = false;
          return {
            data: [],
            totalItems: 0
          };
        });
    },
    setParamsFromLocation(): void {
      const data = revertServerOptionsFromLocation();
      this.options = data.options;
      this.search = data.term ?? undefined;
    },

    showNewColumnModal() {
      const getLocalStorage = localStorage.getItem('columnsStore');
      let parsedData;
      if (getLocalStorage) {
        parsedData = JSON.parse(getLocalStorage);
      }

      this.modalOptions = {
        isEditing: false,
        isCreating: true,
        baseData: undefined,
        newData: {
          technique_id: undefined,
          technique_ids: undefined,
          name: undefined,
          chemistry: undefined,
          diameter: undefined,
          length: undefined,
          particles: undefined,
          serial_number: undefined,
          catalog_number: undefined,
          manufacturer: undefined,
          used_since: undefined,
          note: undefined,
          status: 'active',
          confirm: false
        }
      };

      this.showColumnModal = true;
    },

    showPreviewModal(id: number, currentTab: string) {
      this.column = this.columns.get(id) ?? null;
      if (this.column === undefined) {
        notification.error({ message: 'Přístroj nebyl nalezen' });
        return;
      }
      //this.getChapterHistoryOfUsage(id);

      this.modalOptions = {
        isEditing: false,
        isCreating: false,
        baseData: this.column ?? undefined,
        defaultTab: currentTab || 'one',
        newData: {
          technique_id: undefined,
          technique_ids: (() => {
            try {
              if (!this.column?.techniques || !Array.isArray(this.column.techniques)) {
                return undefined;
              }

              const ids = [];
              for (const technique of this.column.techniques) {
                if (technique && typeof technique === 'object') {
                  // Try different possible property names
                  const id =
                    technique.analytical_technique_id ||
                    (technique as any).id ||
                    (technique as any).technique_id;
                  if (typeof id === 'number') {
                    ids.push(id);
                  }
                }
              }
              return ids.length > 0 ? ids : undefined;
            } catch (error) {
              console.error('Error processing technique_ids in preview:', error);
              return undefined;
            }
          })(),
          name: this.column?.name,
          chemistry: this.column?.chemistry,
          diameter: this.column?.diameter,
          length: this.column?.length,
          particles: this.column?.particles,
          serial_number: this.column?.serial_number,
          catalog_number: this.column?.catalog_number,
          manufacturer: this.column?.manufacturer,
          used_since: this.column?.used_since,
          note: this.column?.note,
          status: this.column?.status,
          confirm: false
        }
      };
      this.showColumnModal = true;
    },

    showEditModal(id: number) {
      this.column = this.columns.get(id) ?? null;
      if (this.column === undefined) {
        notification.error({ message: 'Přístroj nebyl nalezen' });
        return;
      }
      console.log(this.column);
      this.modalOptions = {
        isEditing: true,
        isCreating: false,
        baseData: this.column ?? undefined,
        newData: {
          technique_id: undefined,
          technique_ids: (() => {
            try {
              if (!this.column?.techniques || !Array.isArray(this.column.techniques)) {
                return undefined;
              }

              const ids = [];
              for (const technique of this.column.techniques) {
                if (technique && typeof technique === 'object') {
                  // Try different possible property names
                  const id =
                    technique.analytical_technique_id ||
                    (technique as any).id ||
                    (technique as any).technique_id;
                  if (typeof id === 'number') {
                    ids.push(id);
                  }
                }
              }
              return ids.length > 0 ? ids : undefined;
            } catch (error) {
              console.error('Error processing technique_ids:', error);
              return undefined;
            }
          })(),
          name: this.column?.name ?? '',
          chemistry: this.column?.chemistry ?? '',
          diameter: this.column?.diameter ?? 0,
          length: this.column?.length ?? 0,
          particles: this.column?.particles ?? 0,
          serial_number: this.column?.serial_number ?? '',
          catalog_number: this.column?.catalog_number ?? '',
          manufacturer: this.column?.manufacturer ?? 'N/A',
          used_since: this.column?.used_since ?? new Date(),
          note: this.column?.note ?? '',
          status: this.column?.status ?? 'active',
          confirm: false
        }
      };
      this.showColumnModal = true;
    },

    async getColumn(id: number | undefined | null) {
      if (!id) {
        notification.error({ message: 'Kolona nebyla nalezena' });
        return;
      }
      this.loading = true;

      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/kolona/${id}`)
        .then((res: BaseResponseI<ColumnDto>) => {
          if (res.status_code === 200) {
            this.loading = false;
            return new Column(res.data);
          }
          return null;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Načtení kolony selhalo', description: res.error });
          }
          this.loading = false;
          return null;
        });
    },

    deleteColumn(id: number) {
      this.loading = true;

      return fetchWrapper
        .delete(`${import.meta.env.VITE_API_URL}/kolona/${id}`)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            this.columns.delete(id);
            notification.success({ message: 'Kolona byla úspěšně smazána' });
            this.getAll();
            this.loading = false;
          }
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Smazání kolony selhalo', description: res.error });
          }

          this.loading = false;
        });
    },

    createColumn() {
      if (!this.modalOptions?.newData || this.modalOptions?.newData?.confirm === false) {
        notification.error({ message: 'Musíte potvrdit souhlas s vytvořením kolony' });
        return;
      }

      this.loading = true;

      const data = {
        technique_ids: this.modalOptions.newData.technique_ids ?? [],
        name: this.modalOptions.newData.name ?? '',
        chemistry: this.modalOptions.newData.chemistry ?? '',
        diameter: this.modalOptions.newData.diameter ?? 0,
        length: this.modalOptions.newData.length ?? 0,
        particles: this.modalOptions.newData.particles ?? 0,
        serial_number: this.modalOptions.newData.serial_number ?? '',
        catalog_number: this.modalOptions.newData.catalog_number ?? '',
        manufacturer: this.modalOptions.newData.manufacturer ?? 'N/A',
        used_since: formatDate(this.modalOptions.newData.used_since) || null,
        note: this.modalOptions.newData.note ?? '',
        status: this.modalOptions.newData.status ?? 'active'
      };

      return fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/kolona/`, data)
        .then((res: BaseResponseI<ColumnDto>) => {
          if (res.status_code === 200) {
            this.columns.set(res.data.kolona_id, new Column(res.data));
            this.showColumnModal = false;

            notification.success({
              message: 'Vytvoření kolony proběhlo v pořádku',
              description: 'Název: ' + res.data.name
            });
            this.getAll();
            localStorage.removeItem('usersStore');
            this.loading = false;
          }
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Vytvoření kolony selhalo', description: res.error });
          } else {
            this.showColumnModal = false;
          }

          this.loading = false;
        });
    },

    updateColumn() {
      if (!this.modalOptions?.newData) {
        notification.error({ message: 'Není co upravovat' });
        return;
      }

      this.loading = true;

      const data = {
        technique_ids: this.modalOptions.newData.technique_ids ?? [],
        name: this.modalOptions.newData.name ?? '',
        chemistry: this.modalOptions.newData.chemistry ?? '',
        diameter: this.modalOptions.newData.diameter ?? 0,
        length: this.modalOptions.newData.length ?? 0,
        particles: this.modalOptions.newData.particles ?? 0,
        serial_number: this.modalOptions.newData.serial_number ?? '',
        catalog_number: this.modalOptions.newData.catalog_number ?? '',
        manufacturer: this.modalOptions.newData.manufacturer ?? 'N/A',
        used_since: formatDate(this.modalOptions?.newData?.used_since) || null,
        note: this.modalOptions.newData.note ?? '',
        status: this.modalOptions.newData.status ?? 'active'
      };

      return fetchWrapper
        .put(`${import.meta.env.VITE_API_URL}/kolona/${this.column?.kolona_id}`, data)
        .then((res: BaseResponseI<ColumnDto>) => {
          if (res.status_code === 200) {
            this.columns.set(res.data.kolona_id, new Column(res.data));
            this.showColumnModal = false;

            notification.success({
              message: 'Úprava kolony proběhla v pořádku',
              description: 'Název: ' + res.data.name
            });
            this.getAll();
            this.loading = false;
          }
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Úprava kolony selhala', description: res.error });
          } else {
            this.showColumnModal = false;
          }

          this.loading = false;
        });
    },

    resetModal() {
      this.showColumnModal = false;
      if (this.modalOptions) this.modalOptions.baseData = undefined;
      if (this.modalOptions) this.modalOptions.newData = undefined;
    },

    async getChapterHistoryOfUsage() {
      this.loading = true;
      if (!this.column) {
        notification.error({ message: 'Kolona nebyla nalezena' });
        return;
      }
      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/kolona/${this.column.kolona_id}/history_of_usage`)
        .then((res: BaseResponseI<ColumnUsageHistoryDto>) => {
          this.loading = false;
          if (res.status_code === 200) {
            const history = new ColumnUsageHistory(res.data);
            this.historyItems = history.samples;
            this.historyLength = history.samples.length;
            // notification.success({ message: 'Historie použití byla úspěšně načtena' });
            return history;
          }
          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Načtení historie použití selhalo',
              description: res.error
            });
          }
          this.loading = false;
          return false;
        });
    }
  }
});
