import BaseConstructor from '@/utils/BaseConstructor';
import {
  loadParamsFailedReloadNotification,
  reloadWithoutParams,
  revertServerOptionsFromLocation,
  stringifyServerOptions,
  type BasePaginatorResponseI,
  type BaseResponseI
} from '@/utils/axios';
import { fetchWrapper } from '@/utils/helpers/fetch-wrapper';
import { notification } from 'ant-design-vue';
import { defineStore } from 'pinia';
import type { ServerOptions } from 'vue3-easy-data-table';
import {
  Chemical,
  ChemicalStatus,
  ChemicalType,
  type ChemicalDto,
  type NewChemicalDataI
} from './chemicals';
import { File, type FileDto } from './files';
import type { GetAllOptions } from './projects';

const baseUrl = `${import.meta.env.VITE_API_URL}/experiment-templates`;

export enum ExperimentTemplateStatus {
  ACTIVE = 'active',
  DELETED = 'deleted'
}

export interface ExperimentTemplateDto {
  project_template_id: number;
  project_id: number;
  template_name: string;
  status: string;
  created_at: string;
  updated_at: string;
  chemicals: ChemicalDto[];
  files: FileDto[];
  experiment_template_id: number;
  reaction_scheme_file_id: number | null;
  reaction_procedure: string;
}

export interface ExperimentTemplateI {
  project_template_id: number;
  project_id: number;
  template_name: string;
  status: ExperimentTemplateStatus;
  created_at: Date;
  updated_at: Date;
  chemicals: Chemical[];
  files: File[];
  experiment_template_id: number;
  reaction_scheme_file_id: number | null;
  reaction_procedure: string;
}

export class ExperimentTemplate
  extends BaseConstructor<ExperimentTemplateI>()
  implements ExperimentTemplateI
{
  constructor(data: ExperimentTemplateDto) {
    super(data as unknown as ExperimentTemplateI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    this.files = data.files.map((file) => new File(file));
    this.chemicals = data.chemicals.map((chemical) => new Chemical(chemical));
  }
}

export type ExperimentTemplatesListItemI = {
  experiment_template_id: number;
  template_name: string;
  status: string;
};

interface ExperimentTemplateModalOptionsI {
  isEditing: boolean;
  isCreating: boolean;
  baseData: ExperimentTemplate | undefined;
  newData: ExperimentTemplateModalNewDataI | undefined;
  updateData: ExperimentTemplateModalUpdateDataI | undefined;
}

export interface ExperimentTemplateModalNewDataI {
  project_id: number | undefined;
  template_name: string | undefined;
  reaction_scheme_file_id: number | null | undefined;
  files_ids: number[];
  files: File[];
  chemicals: NewChemicalDataI[];
  reaction_procedure: string | undefined;
  confirm: boolean | false;
}

export interface ExperimentTemplateModalUpdateDataI {
  template_name: string | undefined;
  reaction_scheme_file_id: number | null | undefined;
  chemicals: NewChemicalDataI[];
  reaction_procedure: string | undefined;
  status: ExperimentTemplateStatus | undefined;
}

interface ExperimentTemplatesStateI {
  experimentTemplates: ExperimentTemplate[];
  experimentTemplate: ExperimentTemplate | null;
  loading: boolean;

  showExperimentTemplateModal: boolean;
  modalOptions: ExperimentTemplateModalOptionsI | undefined;
  totalItems?: number;
  options: ServerOptions;
  search: string | undefined;
}

export const useExperimentTemplates = defineStore({
  id: 'experimentTemplates',
  state: () =>
    ({
      experimentTemplate: null,
      experimentTemplates: [],
      loading: false,

      showExperimentTemplateModal: false,
      modalOptions: undefined,

      search: undefined,
      totalItems: undefined,
      options: {
        page: 1,
        rowsPerPage: 100,
        sortBy: [],
        sortType: ['desc', 'asc']
      }
    }) as ExperimentTemplatesStateI,
  actions: {
    async getExperimentTemplates(
      setData: boolean = true,
      options: GetAllOptions | undefined = {
        search_columns: []
      }
    ): Promise<{
      data: ExperimentTemplate[];
      totalItems: number;
    }> {
      this.loading = true;
      this.options.sortBy = ['status', 'project_template_id'];
      this.options.sortType = ['asc'];
      const URL =
        baseUrl +
        '/?' +
        stringifyServerOptions(
          options && options.options ? options.options : this.options,
          options && options.search ? options.search : (this.search ?? null),
          options && options.search_columns ? options.search_columns : [],
          options && options.fixedFilterOptions ? options.fixedFilterOptions : null
        );

      return fetchWrapper
        .get(URL)
        .then((res: BasePaginatorResponseI<ExperimentTemplateDto>) => {
          this.loading = false;

          if (res.status_code === 200) {
            this.totalItems = res.data.total_items;
            if (setData) {
              this.experimentTemplates = res.data.items.map(
                (experimentTemplate) => new ExperimentTemplate(experimentTemplate)
              );
            }

            this.loading = false;
            return {
              data: res.data.items.map(
                (experimentTemplate) => new ExperimentTemplate(experimentTemplate)
              ),
              totalItems: res.data.total_items
            };
          }

          return {
            data: [],
            totalItems: 0
          };
        })
        .catch((res) => {
          this.loading = false;

          if (res.status_code === 400) {
            loadParamsFailedReloadNotification('Načtení šablon selhalo', res.error, () => {
              const data = reloadWithoutParams();
              this.options = data.options;
              this.search = data.term ?? undefined;
            });
          } else {
            notification.error({ message: 'Načtení šablon selhalo' });
          }

          return {
            data: [],
            totalItems: 0
          };
        });
    },

    async getExperimentTemplate(
      experiment_template_id: number
    ): Promise<ExperimentTemplate | undefined> {
      this.loading = true;

      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/experiment-template/${experiment_template_id}`)
        .then((res: BaseResponseI<ExperimentTemplateDto>) => {
          if (res.status_code === 200) {
            this.loading = false;
            return new ExperimentTemplate(res.data);
          }
          this.loading = false;
          return undefined;
        })
        .catch((res) => {
          if (res.status_code === 400) {
            loadParamsFailedReloadNotification('Načtení šablony selhalo', res.error, () => {
              const data = reloadWithoutParams();
              this.options = data.options;
              this.search = data.term ?? undefined;
            });
          }

          this.loading = false;
          return undefined;
        });
    },

    setParamsFromLocation(): void {
      const data = revertServerOptionsFromLocation();
      this.options = data.options;
      this.search = data.term ?? undefined;
    },

    showNewExperimentTemplateModal(project_id: number) {
      const getLocalStorage = localStorage.getItem('experimentTemplatePageStore');
      let parsedData;
      if (getLocalStorage) {
        parsedData = JSON.parse(getLocalStorage);
      }
      this.modalOptions = {
        isEditing: false,
        isCreating: true,
        baseData: undefined,
        updateData: undefined,
        newData: {
          project_id: project_id,
          template_name: undefined,
          files_ids: [],
          chemicals: [],
          files: parsedData?.files ?? [],
          reaction_scheme_file_id: null,
          reaction_procedure: undefined,
          confirm: false
        }
      };

      // this.showExperimentTemplateModal = true;
    },

    async showPreviewModal(id: number) {
      const _ExperimentTemplate = await this.getExperimentTemplate(id);
      if (_ExperimentTemplate === undefined) {
        notification.error({ message: 'Šablona nebyla nalezena' });
        return;
      }

      this.experimentTemplate = _ExperimentTemplate;

      this.modalOptions = {
        isEditing: false,
        isCreating: false,
        baseData: _ExperimentTemplate,
        newData: undefined,
        updateData: {
          reaction_procedure: _ExperimentTemplate.reaction_procedure ?? undefined,
          template_name: _ExperimentTemplate.template_name,
          reaction_scheme_file_id: _ExperimentTemplate.reaction_scheme_file_id ?? undefined,
          status: _ExperimentTemplate.status,
          chemicals: _ExperimentTemplate.chemicals.map((chemical) => {
            return {
              chemical_id: chemical.chemical_id,
              name: chemical.name,
              density: chemical.density,
              molar_mass: chemical.molar_mass,
              notes: chemical.notes,
              grams: chemical.grams,
              moles: chemical.moles,
              equivalent: chemical.equivalent,
              volume_ml: chemical.volume_ml,
              concentration: chemical.concentration,
              type: chemical.type
            } as NewChemicalDataI;
          })
        }
      };

      // this.showExperimentTemplateModal = true;
    },

    async showEditModal(id: number) {
      const _ExperimentTemplate = await this.getExperimentTemplate(id);
      if (_ExperimentTemplate === undefined) {
        notification.error({ message: 'Šablona nebyla nalezena' });
        return;
      }

      this.experimentTemplate = _ExperimentTemplate;

      this.modalOptions = {
        isEditing: true,
        isCreating: false,
        baseData: _ExperimentTemplate,
        newData: undefined,
        updateData: {
          reaction_procedure: _ExperimentTemplate.reaction_procedure ?? undefined,
          template_name: _ExperimentTemplate.template_name,
          reaction_scheme_file_id: _ExperimentTemplate.reaction_scheme_file_id ?? undefined,
          status: _ExperimentTemplate.status,
          chemicals: _ExperimentTemplate.chemicals.map((chemical) => {
            return {
              chemical_id: chemical.chemical_id,
              name: chemical.name,
              density: chemical.density,
              molar_mass: chemical.molar_mass,
              notes: chemical.notes,
              grams: chemical.grams,
              moles: chemical.moles,
              equivalent: chemical.equivalent,
              volume_ml: chemical.volume_ml,
              concentration: chemical.concentration,
              type: chemical.type
            } as NewChemicalDataI;
          })
        }
      };

      // this.showExperimentTemplateModal = true;
    },

    async deleteExperimentTemplate(experiment_template_id: number) {
      this.loading = true;

      return fetchWrapper
        .delete(`${import.meta.env.VITE_API_URL}/experiment-template/${experiment_template_id}`)
        .then((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 200) {
            notification.success({ message: 'Šablona byla úspěšně smazána' });
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Smazání šablony selhalo', description: res.error });
          }

          return false;
        });
    },

    async createExperimentTemplate(): Promise<boolean> {
      if (!this.modalOptions?.newData) {
        notification.error({ message: 'Musíte potvrdit souhlas s vytvořením šablony' });
        return false;
      }

      if (!this.modalOptions.newData.reaction_scheme_file_id) {
        notification.error({ message: 'Musíte vybrat jeden soubor s reakční schématem' });
        this.loading = false;
        return false;
      }

      this.loading = true;

      const data = {
        project_id: this.modalOptions.newData.project_id,
        template_name: this.modalOptions.newData.template_name ?? '',
        reaction_scheme_file_id: this.modalOptions.newData.reaction_scheme_file_id ?? null,
        reaction_procedure: this.modalOptions.newData.reaction_procedure ?? '',
        files_ids: this.modalOptions.newData.files_ids ?? [],
        chemicals: this.modalOptions.newData.chemicals
      };

      return fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/experiment-template/`, data)
        .then(async (res: BaseResponseI<ExperimentTemplateDto>) => {
          this.loading = false;

          if (res.status_code === 200) {
            this.showExperimentTemplateModal = false;

            notification.success({
              message: 'Vytvoření šablony proběhlo v pořádku',
              description: 'Název: ' + res.data.template_name
            });
            localStorage.removeItem('experimentTemplatePageStore');
            return true;
          }

          this.loading = false;
          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Vytvoření šablony selhalo', description: res.error });
          } else {
            this.showExperimentTemplateModal = false;
          }

          this.loading = false;
          return false;
        });
    },

    async updateExperimentTemplate() {
      if (
        !this.modalOptions?.updateData ||
        this.modalOptions?.baseData?.experiment_template_id === undefined
      ) {
        notification.error({ message: 'Není co upravovat' });
        return;
      }

      if (!this.modalOptions.updateData.reaction_scheme_file_id) {
        notification.error({ message: 'Musíte vybrat jeden soubor s reakční schématem' });
        this.loading = false;
        return false;
      }

      this.loading = true;

      const data = {
        template_name: this.modalOptions.updateData.template_name ?? '',
        reaction_scheme_file_id: this.modalOptions.updateData.reaction_scheme_file_id ?? null,
        reaction_procedure: this.modalOptions.updateData.reaction_procedure ?? '',
        status: this.modalOptions.updateData.status ?? ExperimentTemplateStatus.ACTIVE,
        chemicals: this.modalOptions.updateData.chemicals.map((chemical) => ({
          name: chemical.name ?? '',
          density: chemical.density ?? 0,
          molar_mass: chemical.molar_mass ?? 0,
          notes: chemical.notes ?? '',
          grams: chemical.grams ?? 0,
          moles: chemical.moles ?? 0,
          equivalent: chemical.equivalent ?? 0,
          volume_ml: chemical.volume_ml ?? 0,
          concentration: chemical.concentration ?? 0,
          status: ChemicalStatus.ACTIVE,
          type: chemical.type ?? ChemicalType.CHEMICAL
        }))
      } as ExperimentTemplateModalUpdateDataI;

      return fetchWrapper
        .put(
          `${import.meta.env.VITE_API_URL}/experiment-template/${this.modalOptions.baseData.experiment_template_id}`,
          data
        )
        .then((res: BaseResponseI<ExperimentTemplateDto>) => {
          this.loading = false;

          if (res.status_code === 200) {
            this.showExperimentTemplateModal = false;

            notification.success({
              message: 'Úprava šablony proběhla v pořádku',
              description: 'Název: ' + res.data.template_name
            });

            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Úprava šablony selhala', description: res.error });
          } else {
            this.showExperimentTemplateModal = false;
          }

          return false;
        });
    },

    async addFileToExperimentTemplate(file_id: number) {
      if (!this.experimentTemplate) {
        notification.error({ message: 'Není vybran šablona' });
        return false;
      }

      return fetchWrapper
        .post(
          `${import.meta.env.VITE_API_URL}/experiment-template/${this.experimentTemplate.experiment_template_id}/file/${file_id}`
        )
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Soubor byl přidán k šabloně' });
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Přidání souboru k šabloně selhalo',
              description: res.error
            });
          } else {
            notification.error({ message: 'Přidání souboru k šabloně selhalo' });
          }

          return false;
        });
    },

    async deleteFileFromExperimentTemplate(file_id: number) {
      if (!this.experimentTemplate) {
        notification.error({ message: 'Není vybran šablona' });
        return false;
      }

      return fetchWrapper
        .delete(
          `${import.meta.env.VITE_API_URL}/experiment-template/${this.experimentTemplate.experiment_template_id}/file/${file_id}`
        )
        .then((res: BaseResponseI<ExperimentTemplateDto>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Soubor byl odebrán ze šablony' });
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Odebrání souboru ze šablony selhalo',
              description: res.error
            });
          } else {
            notification.error({ message: 'Odebrání souboru ze šablony selhalo' });
          }

          return false;
        });
    },

    resetModal() {
      this.showExperimentTemplateModal = false;
      if (this.modalOptions) this.modalOptions.baseData = undefined;
      if (this.modalOptions) this.modalOptions.newData = undefined;
    }
  }
});
