import type { TagListI } from '@/components/shared/TagModal.vue';
import { router } from '@/router';
import BaseConstructor from '@/utils/BaseConstructor';
import {
  loadParamsFailedReloadNotification,
  reloadWithoutParams,
  revertServerOptionsFromLocation,
  stringifyServerOptions,
  type BasePaginatorResponseI,
  type BaseResponseI
} from '@/utils/axios';
import { fetchWrapper } from '@/utils/helpers/fetch-wrapper';
import { notification } from 'ant-design-vue';
import { defineStore } from 'pinia';
import type { ServerOptions } from 'vue3-easy-data-table';
import { useChaptersStore } from './chapters';
import { Chemical, type ChemicalDto, type NewChemicalDataI } from './chemicals';
import { File, type FileDto } from './files';
import { useProjectsStore, type GetAllOptions } from './projects';
import { Tag, type TagDto } from './tags';
import { UserLock, UserLockStatus, type LockTableDto, type LockTableI } from './userLock';
import { useAuthStore } from './auth';
import {
  AnalyticalRequest,
  type AnalyticalRequestDto
} from './analyticalRequests/analyticalRequests';

const baseUrl = `${import.meta.env.VITE_API_URL}/experiments`;

export enum ExperimentStatus {
  CREATED = 'created',
  IN_PROGRESS = 'in_progress',
  SIGNED = 'signed',
  CANCELED = 'canceled'
}

export interface ExperimentDto extends LockTableDto {
  form_id: number;
  batch_number_id: number;
  chapter_id: number;
  form_name: string;
  batch_description: string;
  status: ExperimentStatus;
  created_at: string;
  updated_at: string;
  chemicals: ChemicalDto[];
  conclusions: ConclusionDto[];
  tags: TagDto[];
  files: FileDto[];
  batch_number: BatchNumberDto;
  experiment_id: number;
  reaction_procedure: string;
  product_weight: number;
  yield_percentage: number;
  content_percentage: number;
  reaction_scheme_file_id: number | null;
  owner: OwnerDto;
  collaborators: collaboratorDto[];
  authors?: OwnerDto[];
}

export interface collaboratorDto {
  can_edit: boolean;
  can_delete: boolean;
  can_share: boolean;
  form_collaborator_id: number;
  form_id: number;
  user_id: number;
  created_at: string;
  updated_at: string;
  user: OwnerDto;
}

export interface CollaboratorI {
  can_edit: boolean;
  can_delete: boolean;
  can_share: boolean;
  form_collaborator_id: number;
  form_id: number;
  user_id: number;
  created_at: Date;
  updated_at: Date;
  user: Owner;
}

export class Collaborator extends BaseConstructor<CollaboratorI>() implements CollaboratorI {
  constructor(data: collaboratorDto) {
    super(data as unknown as CollaboratorI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    this.user = new Owner(data.user);
  }
}

export interface OwnerDto {
  user_id: number;
  first_name: string;
  last_name: string;
  name_shortcut: string;
  status: string;
  user_email: string;
  created_at: string;
  updated_at: string;
}

export interface OwnerI {
  user_id: number;
  first_name: string;
  last_name: string;
  name_shortcut: string;
  status: string;
  user_email: string;
  created_at: Date;
  updated_at: Date;
}

export class Owner extends BaseConstructor<OwnerI>() implements OwnerI {
  constructor(data: OwnerDto) {
    super(data as unknown as OwnerI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);
  }
}

export enum ConclusionType {
  MAIN = 'main',
  ADDITIONAL = 'additional'
}

export interface ConclusionDto {
  conclusion_id: number;
  form_id: number;
  user_id: number;
  type: ConclusionType;
  conclusion_text: string;
  created_at: string;
  updated_at: string;
}

export interface ConclusionI {
  conclusion_id: number;
  form_id: number;
  user_id: number;
  type: ConclusionType;
  conclusion_text: string;
  created_at: Date;
  updated_at: Date;
}

export class Conclusion extends BaseConstructor<ConclusionI>() implements ConclusionI {
  constructor(data: ConclusionDto) {
    super(data as unknown as ConclusionI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);
  }
}

export interface BatchNumberDto {
  batch_number_id: number;
  batch_number: string;
  project_department_id: number;
  created_at: string;
  updated_at: string;
  analytical_requests: AnalyticalRequestDto[];
  project_department?: {
    project_department_id: number;
    project_id: number;
    shortcut: string;
    number: string;
    type: string;
    status: string;
    created_at: string;
    updated_at: string;
    project: {
      project_id: number;
      user_id: number;
      name: string;
      status: string;
      updated_at: string;
      created_at: string;
      user: {
        user_id: number;
        created_at: string;
        updated_at: string;
        first_name: string;
        last_name: string;
        name_shortcut: string;
        user_email: string;
        status: string;
      };
    };
  };
}

export interface BatchNumberI {
  batch_number_id: number;
  batch_number: string;
  project_department_id: number;
  created_at: Date;
  updated_at: Date;
  analytical_requests: AnalyticalRequest[];
  project_department?: {
    project_department_id: number;
    project_id: number;
    shortcut: string;
    number: string;
    type: string;
    status: string;
    created_at: Date;
    updated_at: Date;
    project: {
      project_id: number;
      user_id: number;
      name: string;
      status: string;
      updated_at: Date;
      created_at: Date;
      user: {
        user_id: number;
        created_at: Date;
        updated_at: Date;
        first_name: string;
        last_name: string;
        name_shortcut: string;
        user_email: string;
        status: string;
      };
    };
  };
}

export class BatchNumber extends BaseConstructor<BatchNumberI>() implements BatchNumberI {
  constructor(data: BatchNumberDto) {
    super(data as unknown as BatchNumberI);
    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    this.analytical_requests = data.analytical_requests.map(
      (analyticalRequest) => new AnalyticalRequest(analyticalRequest)
    );
    this.project_department = data.project_department
      ? {
          project_department_id: data.project_department.project_department_id,
          project_id: data.project_department.project_id,
          shortcut: data.project_department.shortcut,
          number: data.project_department.number,
          type: data.project_department.type,
          status: data.project_department.status,
          created_at: new Date(data.project_department.created_at),
          updated_at: new Date(data.project_department.updated_at),
          project: {
            project_id: data.project_department.project.project_id,
            user_id: data.project_department.project.user_id,
            name: data.project_department.project.name,
            status: data.project_department.project.status,
            created_at: new Date(data.project_department.project.created_at),
            updated_at: new Date(data.project_department.project.updated_at),
            user: {
              user_id: data.project_department.project.user.user_id,
              created_at: new Date(data.project_department.project.user.created_at),
              updated_at: new Date(data.project_department.project.user.updated_at),
              first_name: data.project_department.project.user.first_name,
              last_name: data.project_department.project.user.last_name,
              name_shortcut: data.project_department.project.user.name_shortcut,
              user_email: data.project_department.project.user.user_email,
              status: data.project_department.project.user.status
            }
          }
        }
      : undefined;
  }
}

export interface ExperimentI extends LockTableI {
  form_id: number;
  batch_number_id: number;
  chapter_id: number;
  form_name: string;
  batch_description: string;
  status: ExperimentStatus;
  created_at: Date;
  updated_at: Date;
  chemicals: Chemical[];
  conclusions: Conclusion[];
  tags: Tag[];
  files: File[];
  batch_number: BatchNumber;
  experiment_id: number;
  tagIsLoaded: boolean;
  showTags: boolean;
  reaction_procedure: string;
  product_weight: number;
  yield_percentage: number;
  content_percentage: number;
  reaction_scheme_file_id: number | null;
  owner: Owner;
  collaborators: Collaborator[];
  authors?: Owner[];
}

export class Experiment extends BaseConstructor<ExperimentI>() implements ExperimentI {
  constructor(data: ExperimentDto) {
    super(data as unknown as ExperimentI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    this.tags = data.tags.map((tag) => new Tag(tag));
    this.files = data.files.map((file) => new File(file));
    this.batch_number = new BatchNumber(data.batch_number);
    this.chemicals = data.chemicals.map((chemical) => new Chemical(chemical));
    this.conclusions = data.conclusions.map((conclusion) => new Conclusion(conclusion));
    this.tagIsLoaded = true;
    this.showTags = false;
    if (data.edited_by) {
      this.edited_by = new UserLock(data.edited_by);
    }
    this.owner = new Owner(data.owner);
    if (data.authors) {
      this.authors = data.authors.map((author) => new Owner(author));
    }
    this.collaborators = data.collaborators.map((collaborator) => new Collaborator(collaborator));
  }

  get statusText(): string {
    switch (this.status) {
      case ExperimentStatus.CREATED:
        return 'Aktivní';
      case ExperimentStatus.IN_PROGRESS:
        return 'Probíhající';
      case ExperimentStatus.SIGNED:
        return 'Podepsaný';
      case ExperimentStatus.CANCELED:
        return 'Zrušený';
      default:
        return 'Neznámý';
    }
  }

  get isActive(): boolean {
    return this.status === ExperimentStatus.CREATED;
  }

  get isReadOnly(): boolean {
    return this.status === ExperimentStatus.SIGNED || this.status === ExperimentStatus.CANCELED;
  }

  get isCompleted(): boolean {
    return this.status === ExperimentStatus.SIGNED;
  }

  get isCanceled(): boolean {
    return this.status === ExperimentStatus.CANCELED;
  }

  get isInProgress(): boolean {
    return this.status === ExperimentStatus.IN_PROGRESS;
  }

  get isLocked(): boolean {
    const authStore = useAuthStore();
    return (
      this.edited_by?.status === UserLockStatus.LOCKED_BY_OTHER_USER ||
      (this.edited_by?.status === UserLockStatus.LOCKED &&
        this.edited_by.user_id !== authStore.user?.user_id)
    );
  }
  async getTags() {
    const chaptersStore = useChaptersStore();
    const tags = await chaptersStore.getTagsByChapter(this.chapter_id);
    this.tags = tags;

    this.tagIsLoaded = true;
  }

  get joinTags() {
    return this.tags?.map((t) => t.tag_name).join(', ') ?? '';
  }

  get tagLists() {
    return (
      this.tags?.map(
        (t) =>
          ({
            tag_id: t.tag_id,
            tag_name: t.tag_name
          }) as TagListI
      ) ?? []
    );
  }
}

interface ExperimentModalNewDataI {
  chapter_id: number | undefined;
  form_name: string | undefined;
  experiment_template_id: null | number;
  confirm: boolean | false;
}

export interface ExperimentUpdateDataI {
  form_name: string | undefined;
  batch_description: string | undefined;
  chemicals: NewChemicalDataI[];
  reaction_procedure: string | undefined;
  product_weight: number | undefined;
  yield_percentage: number | undefined;
  content_percentage: number | undefined;
  conclusion_text: string | undefined;
  reaction_scheme_file_id: number | null;
}

interface ExperimentModalOptionsI {
  isEditing: boolean;
  isCreating: boolean;
  baseData: Experiment | undefined;
  newData: ExperimentModalNewDataI | undefined;
  updateData: ExperimentUpdateDataI | undefined;
}

interface ExperimentsStateI {
  experiments: Map<number, Experiment>;
  experiment: Experiment | null;
  loading: boolean;

  showExperimentModal: boolean;
  modalOptions: ExperimentModalOptionsI | undefined;

  totalItems?: number;
  options: ServerOptions;
  search: string | undefined;
}

export interface SaveExperimentAsTemplateDto {
  template_name: string;
}

export interface CopyExperimentDto {
  form_name: string;
}

export const useExperimentsStore = defineStore({
  id: 'experiments',
  state: () =>
    ({
      experiment: null,
      experiments: new Map(),
      loading: false,

      showExperimentModal: false,
      modalOptions: undefined,

      items: [],
      search: undefined,
      totalItems: undefined,
      options: {
        page: 1,
        rowsPerPage: 25,
        sortBy: [],
        sortType: ['desc', 'asc']
      }
    }) as ExperimentsStateI,
  actions: {
    async getExperiments(
      setData: boolean = true,
      options: GetAllOptions | undefined = {
        search_columns: ['form_name']
      }
    ): Promise<{
      data: Experiment[];
      totalItems: number;
    }> {
      this.loading = true;

      const URL =
        baseUrl +
        '/?' +
        stringifyServerOptions(
          options && options.options ? options.options : this.options,
          options && options.search ? options.search : (this.search ?? null),
          options && options.search_columns ? options.search_columns : [],
          options && options.fixedFilterOptions ? options.fixedFilterOptions : null
        );

      return fetchWrapper
        .get(URL)
        .then((res: BasePaginatorResponseI<ExperimentDto>) => {
          if (res.status_code === 200) {
            this.totalItems = res.data.total_items;
            if (setData) {
              this.experiments = new Map(
                res.data.items.map((experiment) => [experiment.form_id, new Experiment(experiment)])
              );
            }

            this.loading = false;
            return {
              data: res.data.items.map((experiment) => new Experiment(experiment)),
              totalItems: res.data.total_items
            };
          }
          this.loading = false;
          return {
            data: [],
            totalItems: 0
          };
        })
        .catch((res) => {
          if (res.status_code === 400) {
            loadParamsFailedReloadNotification('Načtení experimentů selhalo', res.error, () => {
              const data = reloadWithoutParams();
              this.options = data.options;
              this.search = data.term ?? undefined;
            });
          }

          this.loading = false;
          return {
            data: [],
            totalItems: 0
          };
        });
    },

    async getExperiment(id: number) {
      this.loading = true;

      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/experiment/${id}`)
        .then((res: BaseResponseI<ExperimentDto>) => {
          this.loading = false;

          if (res.status_code === 200) {
            return new Experiment(res.data);
          }

          return undefined;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({
              message: 'Načtení experimentu selhalo',
              description: res.message
            });
          } else {
            notification.error({ message: 'Načtení experimentu selhalo' });
          }

          return undefined;
        });
    },

    async getExperimentById(id: number) {
      const _Experiment = await this.getExperiment(id);

      if (_Experiment) {
        this.experiment = _Experiment;
      } else {
        notification.error({
          message: 'Chyba',
          description: 'Experiment nebyl nalezen nebo nemáte oprávnění k zobrazení.'
        });
        router.push({ name: 'ChapterDetail' });
      }
    },

    setParamsFromLocation(): void {
      const data = revertServerOptionsFromLocation();
      this.options = data.options;
      this.search = data.term ?? undefined;
    },

    async showNewExperimentModal(chapter_id: number) {
      const projectsStore = useProjectsStore();
      const chaptersStore = useChaptersStore();

      const _Chapter = await chaptersStore.getChapter(chapter_id);
      if (_Chapter === undefined) {
        notification.error({ message: 'Kapitola nebyla nalezena' });
        return;
      }

      this.modalOptions = {
        isEditing: false,
        isCreating: true,
        baseData: undefined,
        updateData: undefined,
        newData: {
          chapter_id: chapter_id,
          form_name: undefined,
          experiment_template_id: null,
          confirm: false
        }
      };

      this.showExperimentModal = true;
    },

    async createExperiment() {
      if (!this.modalOptions?.newData || this.modalOptions?.newData?.confirm === false) {
        notification.error({ message: 'Musíte potvrdit souhlas s vytvořením experimentu' });
        return false;
      }

      if (this.modalOptions.newData.chapter_id === undefined) {
        notification.error({ message: 'Není vybrána kapitola' });
        return false;
      }

      this.loading = true;

      const data = {
        chapter_id: this.modalOptions.newData.chapter_id,
        form_name: this.modalOptions.newData.form_name ?? '',
        experiment_template_id: this.modalOptions.newData.experiment_template_id
      };

      return await fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/experiment/`, data)
        .then((res: BaseResponseI<ExperimentDto>) => {
          if (res.status_code === 200) {
            this.experiments.set(res.data.form_id, new Experiment(res.data));
            this.showExperimentModal = false;

            notification.success({
              message: 'Vytvoření experimentu proběhlo v pořádku',
              description: 'Název: ' + res.data.form_name
            });

            this.loading = false;
            router.push(this.redirectToExperimentDetailLink(res.data.form_id));

            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Vytvoření experimentu selhalo',
              description: res.error
            });
          } else {
            notification.error({ message: 'Vytvoření experimentu selhalo' });
            this.showExperimentModal = false;
          }

          this.loading = false;
          return false;
        });
    },
    resetModal() {
      this.showExperimentModal = false;
      if (this.modalOptions) this.modalOptions.baseData = undefined;
      if (this.modalOptions) this.modalOptions.newData = undefined;
    },

    async updateExperiment(data: ExperimentUpdateDataI) {
      if (!this.experiment) {
        notification.error({ message: 'Není vybrán experiment' });
        return false;
      }

      this.loading = true;

      return await fetchWrapper
        .put(`${import.meta.env.VITE_API_URL}/experiment/${this.experiment.experiment_id}`, data)
        .then((res: BaseResponseI<ExperimentDto>) => {
          if (res.status_code === 200) {
            this.experiments.set(res.data.form_id, new Experiment(res.data));
            this.showExperimentModal = false;

            notification.success({
              message: 'Aktualizace experimentu proběhla v pořádku',
              description: 'Název: ' + res.data.form_name
            });

            this.loading = false;
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Aktualizace experimentu selhala',
              description: res.error
            });
          } else {
            this.showExperimentModal = false;
          }

          this.loading = false;
          return false;
        });
    },

    async saveExperimentAsTemplate(data: SaveExperimentAsTemplateDto) {
      if (!this.experiment) {
        notification.error({ message: 'Není vybrán experiment' });
        return false;
      }

      this.loading = true;

      return await fetchWrapper
        .post(
          `${import.meta.env.VITE_API_URL}/experiment/${this.experiment.experiment_id}/save_as_template`,
          data
        )
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({
              message: 'Uložení experimentu jako šablony proběhlo v pořádku',
              description: 'Název: ' + data.template_name
            });

            this.loading = false;
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Uložení experimentu jako šablony selhalo',
              description: res.error
            });
          } else {
            notification.error({ message: 'Uložení experimentu jako šablony selhalo' });
          }

          this.loading = false;
          return false;
        });
    },

    async updateChapterTags(form_id: number, tags: number[]) {
      this.loading = true;

      return fetchWrapper
        .patch(`${import.meta.env.VITE_API_URL}/form/${form_id}/tags`, { tags: tags })
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Štítky byly úspěšně upraveny' });
            this.loading = false;
          }
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Úprava štítků selhala', description: res.error });
          }

          this.loading = false;
        });
    },

    async updateTags(experiment: Experiment, tags: TagListI[]) {
      await this.updateChapterTags(
        experiment.form_id,
        tags.map((tag) => tag.tag_id)
      );

      experiment.getTags();
      experiment.showTags = false;
    },
    closeExperiment(id: number, force: boolean) {
      if (!id) {
        notification.error({ message: 'Není co zavírat' });
        return false;
      }
      this.loading = true;
      const data = {
        status: 'canceled',
        brute_force_close: force
      };

      return fetchWrapper
        .patch(`${import.meta.env.VITE_API_URL}/form/${id}/status`, data)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            this.experiments.delete(id);
            notification.success({ message: 'Experiment byl úspěšně uzavřen' });
            this.getExperiments();
            this.loading = false;

            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Zavření experimentu selhalo', description: res.error });
          }

          this.loading = false;
          return false;
        });
    },
    reactivateExperiment(id: number) {
      if (!id) {
        notification.error({ message: 'Není co otvírat' });
        return false;
      }
      this.loading = true;
      const data = {
        status: 'created',
        brute_force_close: false
      };

      return fetchWrapper
        .patch(`${import.meta.env.VITE_API_URL}/form/${id}/status`, data)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            this.experiments.delete(id);
            notification.success({ message: 'Experiment byl úspěšně otevřen' });
            this.getExperiments();
            this.loading = false;

            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Aktivace experimentu selhala', description: res.error });
          }

          this.loading = false;
          return false;
        });
    },
    redirectToExperimentDetailLink(id: number) {
      return router.resolve({
        name: 'Experiment',
        params: { form_id: id.toString() }
      });
    },
    async copyExperiment(experiment_id: number, data: CopyExperimentDto) {
      this.loading = true;
      return await fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/experiment/${experiment_id}/copy`, data)
        .then((res: BaseResponseI<ExperimentDto>) => {
          if (res.status_code === 200) {
            notification.success({
              message: 'Kopie experimentu byla úspěšně vytvořena',
              description: 'Název: ' + res.data.form_name
            });
            this.loading = false;
            router.push(this.redirectToExperimentDetailLink(res.data.form_id));
            return res.data;
          }
          this.loading = false;
          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Kopírování experimentu selhalo',
              description: res.error
            });
          } else {
            notification.error({ message: 'Kopírování experimentu selhalo' });
          }
          this.loading = false;
          return false;
        });
    }
  }
});
