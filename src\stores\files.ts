import BaseConstructor from '@/utils/BaseConstructor';
import type { BaseResponseI } from '@/utils/axios';
import { fetchWrapper } from '@/utils/helpers/fetch-wrapper';
import { notification } from 'ant-design-vue';
import { defineStore } from 'pinia';

export interface FileDto {
  file_id: number;
  file_name: string;
  file_type: string;
  file_path: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface FileI {
  file_id: number;
  file_name: string;
  file_type: string;
  file_path: string;
  status: string;
  created_at: Date;
  updated_at: Date;
}

export interface FilePreviewI {
  file_id: number;
  file_name: string;
  file_type: string;
  file_path: string;
  status: string;
  file_url: string;
}

export class File extends BaseConstructor<FileI>() implements FileI {
  constructor(data: FileDto) {
    super(data as unknown as FileI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);
  }
}

export interface FileSimpleDto {
  file_id: number;
  file_name: string;
  file_path: string;
  FilePreview?: FilePreviewI;
}

export interface BrowserFileDataI extends FileSimpleDto {
  isRemote: boolean;
}

interface FileStoreStateI {
  file: null | File;
  loading: boolean;
  error: string | undefined;

  showUpdateFileNameModal: boolean;
  showFileUploaderModal: boolean;
}

export const useFilesStore = defineStore({
  id: 'files',
  state: () =>
    ({
      file: null,
      loading: false,
      error: undefined,
      showFileUploaderModal: false,
      showUpdateFileNameModal: false
    }) as FileStoreStateI,
  actions: {
    async processUploadResponse(res: string) {
      this.loading = false;
      const parsedRes = JSON.parse(res) as BaseResponseI<FileDto>;
      if (parsedRes.status_code === 200) {
        this.file = new File(parsedRes.data);
        return this.file as File;
      } else {
        notification.error({
          message: 'Chyba při nahrávání souboru',
          description: parsedRes.message
        });
      }

      return undefined;
    },

    async deleteFile(file_id: number) {
      this.loading = true;

      return fetchWrapper
        .delete(`${import.meta.env.VITE_API_URL}/file/${file_id}`)
        .then(() => {
          this.loading = false;
          return true;
        })
        .catch(() => {
          return false;
        });
    },

    async getFileById(file_id: number, setData = true) {
      this.loading = true;

      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/file/${file_id}`)
        .then(async (res: BaseResponseI<FileDto>) => {
          if (res.status_code === 200) {
            if (setData) {
              this.file = new File(res.data);
            }

            this.loading = false;
            return new File(res.data);
          }

          this.loading = false;
          return undefined;
        })
        .catch(() => {
          return undefined;
        });
    },

    async getFilePreview(file_id: number) {
      this.loading = true;

      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/file/preview/${file_id}`)
        .then((res: BaseResponseI<FilePreviewI>) => {
          if (res.status_code === 200) {
            this.loading = false;
            return res.data;
          }

          this.loading = false;
          return undefined;
        })
        .catch(() => {
          return undefined;
        });
    },

    async updateFileName(file_id: number, file_name: string) {
      this.loading = true;

      return fetchWrapper
        .put(`${import.meta.env.VITE_API_URL}/file/${file_id}`, { file_name })
        .then((res: BaseResponseI<FileDto>) => {
          if (res.status_code === 200) {
            notification.success({
              message: 'Úspěšně upraveno',
              description: 'Název souboru byl úspěšně upraven.'
            });

            this.loading = false;
            return true;
          }

          this.loading = false;
          return false;
        })
        .catch(() => {
          return false;
        });
    },

    async showFileUpdateModal(file_id: number) {
      const isFileLoaded = await this.getFileById(file_id);
      if (isFileLoaded) {
        this.showUpdateFileNameModal = true;
      }
    },

    async getFileUrl(file_id: number) {
      this.loading = true;

      const filePreview = await this.getFilePreview(file_id);
      return filePreview?.file_url ?? undefined;
    }
  }
});
