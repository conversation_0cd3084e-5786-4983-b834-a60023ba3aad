import type { BaseResponseI } from '@/utils/axios';
import { fetchWrapper } from '@/utils/helpers/fetch-wrapper';
import type { InsertAnalysisParameterI } from '@/views/pages/dashboard/projects/project/department/chapter/experiment/request/components/InsertAnalysisParametersFormTable.vue';
import type { AnalyticalRequestParameterI } from '@/views/pages/dashboard/projects/project/department/chapter/experiment/request/components/ParametersFormTable.vue';
import { notification } from 'ant-design-vue';
import { defineStore } from 'pinia';
import { Tag, type TagDto } from './tags';
import { NewAnalyticalRequestType } from './analyticalRequests/analyticalRequests';
import { Attempt, type AttemptDto, type AttemptModalNewDataI } from './attempts';
import type { Chapter } from './chapters';
import {
  Experiment,
  type BatchNumberDto,
  type ExperimentDto,
  type ConclusionDto,
  Conclusion,
  BatchNumber,
  type BatchNumberI
} from './experiments';
import { File, useFilesStore, type FileDto } from './files';
import {
  Investigation,
  type InvestigationDto,
  type InvestigationModalNewDataI
} from './investigations';
import { Message, type MessageDto, type MessageModalNewDataI } from './messages';
import type { Department, Project } from './projects';
import { Chemical, type ChemicalDto } from './chemicals';
import {
  AnalyticalRequest,
  type AnalyticalRequestDto
} from './analyticalRequests/analyticalRequests';
import BaseConstructor from '@/utils/BaseConstructor';
const baseUrl = `${import.meta.env.VITE_API_URL}/form`;

export enum FormType {
  ATTEMPT = 'attempt',
  INVESTIGATION = 'investigation',
  MESSAGE = 'message',
  EXPERIMENT = 'experiment'
}

export enum AnalysisStatus {
  NA = 'na',
  COMPLIANT = 'compliant',
  NON_COMPLIANT = 'non_compliant'
}
export interface NewGenerateAnalyticalRequestDto {
  name: string;
  samples: AnalyticalRequestParameterI[];
  files_ids: number[];
  files: File[];
}

export enum InsertAnalysisSampleStatus {
  NA = 'na',
  COMPLIANT = 'compliant',
  NON_COMPLIANT = 'non_compliant'
}

export interface newFormDto {
  form_id: number;
  batch_number_id: number;
  chapter_id: number;
  form_name: string;
  form_type: string;
  batch_description: string;
  status: string;
  created_at: string;
  updated_at: string;
  chemicals: ChemicalDto[];
  conclusions: ConclusionDto[];
  tags: TagDto[];
  files: FileDto[];
  batch_number: BatchNumberDto;
  edited_by: {
    status: string;
    user_id: number | null;
    table_name: string | null;
    primary_key_value: number | null;
    lock_time: string | null;
    lock_duration: number | null;
    user: string | null;
  };
}
export interface newFormI {
  form_id: number;
  batch_number_id: number;
  chapter_id: number;
  form_name: string;
  form_type: string;
  batch_description: string;
  status: string;
  created_at: Date;
  updated_at: Date;
  chemicals: Chemical[];
  conclusions: Conclusion[];
  tags: Tag[];
  files: File[];
  batch_number: BatchNumber;
  edited_by: {
    status: string;
    user_id: number | null;
    table_name: string | null;
    primary_key_value: number | null;
    lock_time: Date | null;
    lock_duration: string | null;
    user: string | null;
  };
}

export class newForm extends BaseConstructor<newFormI>() implements newFormI {
  constructor(data: newFormDto) {
    super(data as unknown as newFormI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    this.tags = data.tags.map((tag) => new Tag(tag));
    this.files = data.files.map((file) => new File(file));
    this.batch_number = new BatchNumber(data.batch_number);
    this.chemicals = data.chemicals.map((chemical) => new Chemical(chemical));
    this.conclusions = data.conclusions.map((conclusion) => new Conclusion(conclusion));
  }
}

interface NewChapterDto {
  chapter_id: number;
  chapter_title: string;
  status: string;
  editable: boolean;
  created_at: string;
  updated_at: string;
  department_id: number;
  parent_chapter_id: number | null;
  project_department: NewProjectDepartmentDto;
}

interface NewChapterI {
  chapter_id: number;
  chapter_title: string;
  status: string;
  editable: boolean;
  created_at: Date;
  updated_at: Date;
  project_department: NewProjectDepartment;
}

export class NewChapter extends BaseConstructor<NewChapterI>() implements NewChapterI {
  constructor(data: NewChapterDto) {
    super(data as unknown as NewChapterI);
    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);
    this.project_department = new NewProjectDepartment(data.project_department);
  }
}
interface NewProjectDepartmentDto {
  project_department_id: number;
  project_id: number;
  shortcut: string;
  number: string;
  status: string;
  type: string;
  created_at: string;
  updated_at: string;
  project: NewProjectDto;
}

interface NewProjectDepartmentI {
  project_department_id: number;
  project_id: number;
  shortcut: string;
  number: string;
  status: string;
  created_at: Date;
  updated_at: Date;
  project: NewProject;
}

export class NewProjectDepartment
  extends BaseConstructor<NewProjectDepartmentI>()
  implements NewProjectDepartmentI
{
  constructor(data: NewProjectDepartmentDto) {
    super(data as unknown as NewProjectDepartmentI);
    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);
    this.project = new NewProject(data.project);
  }
}

interface NewProjectDto {
  project_id: number;
  user_id: number;
  name: string;
  status: string;
  created_at: string;
  updated_at: string;
  user: NewUserDto;
}

interface NewProjectI {
  project_id: number;
  user_id: number;
  name: string;
  status: string;
  created_at: Date;
  updated_at: Date;
  user: NewUser;
}

export class NewProject extends BaseConstructor<NewProjectI>() implements NewProjectI {
  constructor(data: NewProjectDto) {
    super(data as unknown as NewProjectI);
    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);
    this.user = new NewUser(data.user);
  }
}

interface NewUserDto {
  user_id: number;
  created_at: string;
  updated_at: string;
  name_shortcut: string;
  user_email: string;
  status: string;
  first_name: string;
  last_name: string;
}
interface NewUserI {
  user_id: number;
  created_at: Date;
  updated_at: Date;
  name_shortcut: string;
  user_email: string;
  status: string;
  first_name: string;
  last_name: string;
}

export class NewUser extends BaseConstructor<NewUserI>() implements NewUserI {
  constructor(data: NewUserDto) {
    super(data as unknown as NewUserI);
    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);
  }
}

export interface NewFormForSearchDto {
  batch_description: string;
  batch_number_id: number;
  chapter_id: number;
  form_id: number;
  form_name: string;
  form_type: string;
  status: string;
  created_at: string;
  updated_at: string;
  chapter: NewChapterDto;
  batch_number?: BatchNumberDto;
  affected_batch_number?: string | null;
}
export interface NewFormForSearchI {
  form_id: number;
  batch_number_id: number;
  chapter_id: number;
  form_name: string;
  form_type: string;
  status: string;
  created_at: Date;
  updated_at: Date;
  batch_description: string;
  chapter: NewChapter;
  batch_number?: BatchNumber;
  affected_batch_number?: string | null;
}

export class NewFormForSearch
  extends BaseConstructor<NewFormForSearchI>()
  implements NewFormForSearchI
{
  constructor(data: NewFormForSearchDto) {
    super(data as unknown as NewFormForSearchI);
    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);
    this.chapter = new NewChapter(data.chapter);
    this.batch_number = data.batch_number ? new BatchNumber(data.batch_number) : undefined;
  }
}

export interface OwnerDto {
  user_id: number;
  first_name: string;
  last_name: string;
  name_shortcut: string;
  status: string;
  user_email: string;
  created_at: string;
  updated_at: string;
}

export interface OwnerI {
  user_id: number;
  first_name: string;
  last_name: string;
  name_shortcut: string;
  status: string;
  user_email: string;
  created_at: Date;
  updated_at: Date;
}

export class Owner extends BaseConstructor<OwnerI>() implements OwnerI {
  constructor(data: OwnerDto) {
    super(data as unknown as OwnerI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);
  }
}
export interface FormForSearchDto {
  form_id: number;
  batch_number_id: number;
  chapter_id: number;
  form_name: string;
  form_type: string;
  batch_description: string;
  status: string;
  created_at: string;
  updated_at: string;
  chemicals: ChemicalDto[];
  conclusions: ConclusionDto[];
  tags: TagDto[];
  files: FileDto[];
  batch_number: {
    batch_number_id: number;
    batch_number: string;
    project_department_id: number;
    created_at: string;
    updated_at: string;
    project_department: {
      project_department_id: number;
      project_id: number;
      shortcut: string;
      number: string;
      type: string;
      status: string;
      created_at: string;
      updated_at: string;
      project: {
        project_id: number;
        user_id: number;
        name: string;
        status: string;
        created_at: string;
        updated_at: string;
        user: {
          user_id: number;
          created_at: string;
          updated_at: string;
          first_name: string;
          last_name: string;
          name_shortcut: string;
          user_email: string;
          status: string;
        };
      };
    };
    analytical_requests: AnalyticalRequestDto[];
  };
  edited_by: {
    status: string;
    user_id: number | null;
    table_name: string | null;
    primary_key_value: number | null;
    lock_time: string | null;
    lock_duration: number | null;
    user: string | null;
  };
  owner: OwnerDto;
}
export interface FormForSearchI {
  form_id: number;
  batch_number_id: number;
  chapter_id: number;
  form_name: string;
  form_type: string;
  batch_description: string;
  status: string;
  created_at: Date;
  updated_at: Date;
  chemicals: Chemical[];
  conclusions: Conclusion[];
  tags: Tag[];
  files: File[];
  batch_number: {
    batch_number_id: number;
    batch_number: string;
    project_department_id: number;
    created_at: Date;
    updated_at: Date;
    project_department: {
      project_department_id: number;
      project_id: number;
      shortcut: string;
      number: string;
      type: string;
      status: string;
      created_at: Date;
      updated_at: Date;
      project: {
        project_id: number;
        user_id: number;
        name: string;
        status: string;
        created_at: Date;
        updated_at: Date;
        user: {
          user_id: number;
          created_at: Date;
          updated_at: Date;
          first_name: string;
          last_name: string;
          name_shortcut: string;
          user_email: string;
          status: string;
        };
      };
    };
    analytical_requests: AnalyticalRequest[];
  };
  edited_by: {
    status: string;
    user_id: number | null;
    table_name: string | null;
    primary_key_value: number | null;
    lock_time: Date | null;
    lock_duration: string | null;
    user: string | null;
  };
  owner: Owner;
}

export class FormForSearch extends BaseConstructor<FormForSearchI>() implements FormForSearchI {
  constructor(data: FormForSearchDto) {
    super(data as unknown as FormForSearchI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    this.tags = data.tags.map((tag) => new Tag(tag));
    this.files = data.files.map((file) => new File(file));
    this.batch_number = {
      batch_number_id: data.batch_number.batch_number_id,
      batch_number: data.batch_number.batch_number,
      project_department_id: data.batch_number.project_department_id,
      created_at: new Date(data.batch_number.created_at),
      updated_at: new Date(data.batch_number.updated_at),
      project_department: {
        project_department_id: data.batch_number.project_department.project_department_id,
        project_id: data.batch_number.project_department.project_id,
        shortcut: data.batch_number.project_department.shortcut,
        number: data.batch_number.project_department.number,
        type: data.batch_number.project_department.type,
        status: data.batch_number.project_department.status,
        created_at: new Date(data.batch_number.project_department.created_at),
        updated_at: new Date(data.batch_number.project_department.updated_at),
        project: {
          project_id: data.batch_number.project_department.project.project_id,
          user_id: data.batch_number.project_department.project.user_id,
          name: data.batch_number.project_department.project.name,
          status: data.batch_number.project_department.project.status,
          created_at: new Date(data.batch_number.project_department.project.created_at),
          updated_at: new Date(data.batch_number.project_department.project.updated_at),
          user: {
            user_id: data.batch_number.project_department.project.user.user_id,
            created_at: new Date(data.batch_number.project_department.project.user.created_at),
            updated_at: new Date(data.batch_number.project_department.project.user.updated_at),
            first_name: data.batch_number.project_department.project.user.first_name,
            last_name: data.batch_number.project_department.project.user.last_name,
            name_shortcut: data.batch_number.project_department.project.user.name_shortcut,
            user_email: data.batch_number.project_department.project.user.user_email,
            status: data.batch_number.project_department.project.user.status
          }
        }
      },
      analytical_requests: data.batch_number.analytical_requests.map(
        (request) => new AnalyticalRequest(request)
      )
    };
    this.chemicals = data.chemicals.map((chemical) => new Chemical(chemical));
    this.conclusions = data.conclusions.map((conclusion) => new Conclusion(conclusion));
  }
}

export interface FormForSearchSimpleDto {
  form_id: number;
  batch_number_id: number;
  chapter_id: number;
  form_name: string;
  form_type: string;
  batch_description: string;
  status: string;
  created_at: string;
  updated_at: string;
  chapter: {
    chapter_id: number;
    department_id: number;
    parent_chapter_id: number | null;
    chapter_title: string;
    status: string;
    editable: boolean;
    created_at: string;
    updated_at: string;
    project_department: {
      project_department_id: number;
      project_id: number;
      shortcut: string;
      number: string;
      type: string;
      status: string;
      created_at: string;
      updated_at: string;
      project: {
        project_id: number;
        user_id: number;
        name: string;
        status: string;
        created_at: string;
        updated_at: string;
        user: {
          user_id: number;
          created_at: string;
          updated_at: string;
          first_name: string;
          last_name: string;
          name_shortcut: string;
          user_email: string;
          status: string;
        };
      };
    };
  };
}

export interface FormForSearchSimpleI {
  form_id: number;
  batch_number_id: number;
  chapter_id: number;
  form_name: string;
  form_type: string;
  batch_description: string;
  status: string;
  created_at: Date;
  updated_at: Date;
  chapter: {
    chapter_id: number;
    department_id: number;
    parent_chapter_id: number | null;
    chapter_title: string;
    status: string;
    editable: boolean;
    created_at: Date;
    updated_at: Date;
    project_department: {
      project_department_id: number;
      project_id: number;
      shortcut: string;
      number: string;
      type: string;
      status: string;
      created_at: Date;
      updated_at: Date;
      project: {
        project_id: number;
        user_id: number;
        name: string;
        status: string;
        created_at: Date;
        updated_at: Date;
        user: {
          user_id: number;
          created_at: Date;
          updated_at: Date;
          first_name: string;
          last_name: string;
          name_shortcut: string;
          user_email: string;
          status: string;
        };
      };
    };
  };
}

export class FormForSearchSimple
  extends BaseConstructor<FormForSearchSimpleI>()
  implements FormForSearchSimpleI
{
  constructor(data: FormForSearchSimpleDto) {
    super(data as unknown as FormForSearchSimpleI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    this.chapter = {
      ...data.chapter,
      created_at: new Date(data.chapter.created_at),
      updated_at: new Date(data.chapter.updated_at),
      project_department: {
        ...data.chapter.project_department,
        created_at: new Date(data.chapter.project_department.created_at),
        updated_at: new Date(data.chapter.project_department.updated_at),
        project: {
          ...data.chapter.project_department.project,
          created_at: new Date(data.chapter.project_department.project.created_at),
          updated_at: new Date(data.chapter.project_department.project.updated_at),
          user: {
            ...data.chapter.project_department.project.user,
            created_at: new Date(data.chapter.project_department.project.user.created_at),
            updated_at: new Date(data.chapter.project_department.project.user.updated_at)
          }
        }
      }
    };
  }
}

export interface InsertAnalysisDto {
  name: string;
  sample_number: string | null;
  samples: Array<InsertAnalysisParameterI>;
  files_ids: number[];
  files: File[];
}

export interface InsertNewAnalysisDto {
  name: string;
  sample_number: string | null;
  samples: AnalyticalRequestParameterI[];
  files_ids: number[];
  files: File[];
  batch_number: string | null;
}

export interface InsertAnalysisReanalyzeDto {
  name: string;
  sample_number: string;
  samples: Array<InsertAnalysisParameterI>;
  files_ids: number[];
  files: File[];
  instrument_id: number | null;
  kolona_id: number | null;
  sequence_name: string;
  method_name: string;
  preparation_of_standard_and_sample?: string;
  note_or_specification: string;
  result: string;
  sample_id: number;
  analysis_status?: AnalysisStatus;
}

export enum NewChemicalType {
  CHEMICAL = 'chemical',
  FIRST_CHEMICAL = 'first_chemical',
  RESULT = 'result'
}

export interface NewChemicalDto {
  name: string;
  density: number;
  molar_mass: number;
  notes: string;
  grams: number;
  moles: number;
  equivalent: number;
  volume_ml: number;
  concentration: number;
  type: NewChemicalType;
}

export interface UpdateChemicalDto {
  name: string;
  density: number;
  molar_mass: number;
  notes: string;
  grams: number;
  moles: number;
  equivalent: number;
  volume_ml: number;
  concentration: number;
}

export interface AddConclusionDto {
  conclusion_text: string;
}

export interface UpdateFormTagsDto {
  tags: number[];
}

interface NewFormModalWithSelectFormTypeOptionsI {
  baseData: {
    department: Department;
    chapter: Chapter;
    project: Project;
  };
  newData: {
    attempt: AttemptModalNewDataI | undefined;
    investigation: InvestigationModalNewDataI | undefined;
    message: MessageModalNewDataI | undefined;
  };
}

interface FormStateI {
  loading: boolean;
  form_id: number | undefined;
  showInviteModal: boolean;
  showAddConclusionModal: boolean;
  completeBatches: BatchNumber | null;
  modalOptions: NewFormModalWithSelectFormTypeOptionsI | undefined;
  showNewFormModalWithSelectFormType: boolean;
}

export const useFormsStore = defineStore({
  id: 'forms',
  state: () =>
    ({
      loading: false,
      form_id: undefined,
      showInviteModal: false,
      showAddConclusionModal: false,
      modalOptions: undefined,
      showNewFormModalWithSelectFormType: false,
      completeBatches: null
    }) as FormStateI,
  actions: {
    checkFormIsDefined(): boolean {
      if (!this.form_id) {
        return false;
      }

      return true;
    },

    async showNewFormModalWithSelectFormTypeForm(
      department: Department,
      chapter: Chapter,
      project: Project
    ) {
      this.modalOptions = {
        baseData: { department, chapter, project },
        newData: {
          attempt: undefined,
          investigation: undefined,
          message: undefined
        }
      };

      this.showNewFormModalWithSelectFormType = true;
    },

    async resetNewFormModalWithSelectFormTypeForm() {
      this.modalOptions = undefined;
      this.showNewFormModalWithSelectFormType = false;
    },

    async generateAnalyticalRequest(
      data: NewGenerateAnalyticalRequestDto,
      type: NewAnalyticalRequestType
    ) {
      if (!this.checkFormIsDefined()) return false;
      this.loading = true;

      const samples = data.samples.map((sample) => {
        return {
          technique_id: sample.technique_id ?? 0,
          notes: sample.notes ?? ''
        };
      });

      const _data = {
        type: type,
        name: data.name,
        samples: samples,
        files_ids: data.files_ids
      };

      return fetchWrapper
        .post(`${baseUrl}/${this.form_id}/generate_analytical_request`, _data)
        .then((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 200) {
            notification.success({ message: 'Analytická žádost byla vygenerována' });
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({
              message: 'Nelze vygenerovat analytickou žádost',
              description: res.message
            });
          } else {
            notification.error({ message: 'Nelze vygenerovat analytickou žádost' });
          }

          return false;
        });
    },

    async exportFormAsPdf(form_name?: string) {
      if (!this.checkFormIsDefined()) return false;
      this.loading = true;
      return fetchWrapper
        .blob(`${baseUrl}/${this.form_id}/export`)
        .then((res) => {
          this.loading = false;

          const url = window.URL.createObjectURL(new Blob([res]));
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', `${form_name ?? 'form'}.pdf`);
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          return true;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Formulář nešlo exportovat', description: res.message });
          } else {
            notification.error({ message: 'Formulář nešlo exportovat' });
          }

          return false;
        });
    },
    async exportFormAsWord(form_name?: string) {
      if (!this.checkFormIsDefined()) return false;
      this.loading = true;
      return fetchWrapper
        .blob(`${baseUrl}/${this.form_id}/export_to_word`)
        .then((res) => {
          this.loading = false;

          const url = window.URL.createObjectURL(new Blob([res]));
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', `${form_name ?? 'form'}.docx`);
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          return true;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Formulář nešlo exportovat', description: res.message });
          } else {
            notification.error({ message: 'Formulář nešlo exportovat' });
          }

          return false;
        });
    },

    async signAndCloseForm() {
      if (!this.checkFormIsDefined()) return false;
      this.loading = true;
      return fetchWrapper
        .post(`${baseUrl}/${this.form_id}/sign_and_close`)
        .then((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 200) {
            notification.success({ message: 'Formulář byl podepsán a uzavřen' });
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Nelze uzavřít formulář', description: res.message });
          } else {
            notification.error({ message: 'Nelze uzavřít formulář' });
          }

          return false;
        });
    },

    async insertAnalysis(data: InsertAnalysisDto) {
      if (!this.checkFormIsDefined()) return false;
      this.loading = true;
      return fetchWrapper
        .post(`${baseUrl}/${this.form_id}/insert_analysis`, data)
        .then((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 200) {
            notification.success({ message: 'Analýza byla vložena' });
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Nelze vložit analýzu', description: res.message });
          } else {
            notification.error({ message: 'Nelze vložit analýzu' });
          }
          return false;
        });
    },

    async addUserToForm(user_id: number) {
      if (!this.checkFormIsDefined()) return false;
      this.loading = true;
      //todo: add interface
      const data = {
        can_edit: true,
        can_delete: true,
        can_share: true
      };
      return fetchWrapper
        .post(`${baseUrl}/${this.form_id}/collaborators/${user_id}`, data)
        .then((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 200) {
            notification.success({ message: 'Uživatel byl přidán do formuláře' });
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({
              message: 'Nelze přidat uživatele do formuláře',
              description: res.message
            });
          } else {
            notification.error({ message: 'Nelze přidat uživatele do formuláře' });
          }

          return false;
        });
    },
    async removeUserFromForm(user_id: number) {
      if (!this.checkFormIsDefined()) return false;
      this.loading = true;
      return fetchWrapper
        .delete(`${baseUrl}/${this.form_id}/collaborators/${user_id}`)
        .then((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 200) {
            notification.success({ message: 'Uživatel byl odstraněn z formuláře' });
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({
              message: 'Nelze smazat uživatele z formuláře',
              description: res.message
            });
          } else {
            notification.error({ message: 'Nelze smazat uživatele z formuláře' });
          }

          return false;
        });
    },

    async createNewChemical(data: NewChemicalDto) {
      if (!this.checkFormIsDefined()) return false;
      this.loading = true;
      return fetchWrapper
        .post(`${baseUrl}/${this.form_id}/chemical/`, data)
        .then((res: BaseResponseI<{ chemicals: ChemicalDto[] }>) => {
          this.loading = false;

          if (res.status_code === 200) {
            return (
              res.data.chemicals
                .map((chemical) => new Chemical(chemical))
                .sort((a, b) => {
                  const createdAtA =
                    typeof a.created_at === 'number'
                      ? a.created_at
                      : new Date(a.created_at).getTime();
                  const createdAtB =
                    typeof b.created_at === 'number'
                      ? b.created_at
                      : new Date(b.created_at).getTime();
                  return createdAtB - createdAtA;
                })[0] ?? false
            );
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({
              message: 'Nelze vytvořit novou chemickou látku',
              description: res.message
            });
          } else {
            notification.error({ message: 'Nelze vytvořit novou chemickou látku' });
          }

          return false;
        });
    },

    async updateChemical(chemical_id: number, data: UpdateChemicalDto) {
      if (!this.checkFormIsDefined()) return false;
      this.loading = true;
      return fetchWrapper
        .put(`${baseUrl}/${this.form_id}/chemical/${chemical_id}`, data)
        .then((res: BaseResponseI<{ chemicals: ChemicalDto[] }>) => {
          this.loading = false;
          if (res.status_code === 200) {
            return (
              res.data.chemicals
                .map((chemical) => new Chemical(chemical))
                .find((chemical) => chemical.chemical_id === chemical_id) ?? false
            );
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({
              message: 'Nelze upravit chemickou látku',
              description: res.message
            });
          } else {
            notification.error({ message: 'Nelze upravit chemickou látku' });
          }

          return false;
        });
    },

    async deleteChemical(chemical_id: number) {
      if (!this.checkFormIsDefined()) return false;
      this.loading = true;
      return fetchWrapper
        .delete(`${baseUrl}/${this.form_id}/chemical/${chemical_id}`)
        .then((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 200) {
            notification.success({ message: 'Chemická látka byla smazána' });
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({
              message: 'Nelze smazat chemickou látku',
              description: res.message
            });
          } else {
            notification.error({ message: 'Nelze smazat chemickou látku' });
          }

          return false;
        });
    },

    async saveNewChemicalToChemicalTable(chemical_id: number) {
      if (!this.checkFormIsDefined()) return false;
      this.loading = true;
      return fetchWrapper
        .post(`${baseUrl}/${this.form_id}/chemical/${chemical_id}/save_to_chemical_table`)
        .then((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 200) {
            notification.success({ message: 'Chemická látka byla uložena do chemické tabulky' });
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({
              message: 'Nelze uložit chemickou látku do chemické tabulky',
              description: res.message
            });
          } else {
            notification.error({ message: 'Nelze uložit chemickou látku do chemické tabulky' });
          }

          return false;
        });
    },

    async recalculateChemicalForm() {
      if (!this.checkFormIsDefined()) return false;
      this.loading = true;
      return fetchWrapper
        .post(`${baseUrl}/${this.form_id}/calculate_chemical/`)
        .then((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 200) {
            notification.success({ message: 'Chemikálie ve formuláři úspěšně přepočítány' });
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({
              message: 'Nelze přepočítat chemikálie',
              description: res.message
            });
          } else {
            notification.error({ message: 'Nelze přepočítat chemikálie' });
          }

          return false;
        });
    },

    async addFileToForm(file_id: number) {
      if (!this.checkFormIsDefined()) return false;
      this.loading = true;

      return fetchWrapper
        .post(`${baseUrl}/${this.form_id}/file/${file_id}`)
        .then((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 200) {
            notification.success({ message: 'Soubor byl přidán do formuláře' });
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({
              message: 'Nelze přidat soubor do formuláře',
              description: res.message
            });
          } else {
            notification.error({ message: 'Nelze přidat soubor do formuláře' });
          }

          return false;
        });
    },

    async deleteFileFromForm(file_id: number) {
      if (!this.checkFormIsDefined()) return false;
      this.loading = true;
      return fetchWrapper
        .delete(`${baseUrl}/${this.form_id}/file/${file_id}`)
        .then((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 200) {
            notification.success({ message: 'Soubor byl smazán z formuláře' });
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({
              message: 'Nelze smazat soubor z formuláře',
              description: res.message
            });
          } else {
            notification.error({ message: 'Nelze smazat soubor z formuláře' });
          }

          return false;
        });
    },

    async addConclusionToForm(data: AddConclusionDto) {
      if (!this.checkFormIsDefined()) return false;
      this.loading = true;
      return fetchWrapper
        .post(`${baseUrl}/${this.form_id}/add_conclusion`, data)
        .then((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 200) {
            notification.success({ message: 'Závěr byl přidán do formuláře' });
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({
              message: 'Nelze přidat závěr do formuláře',
              description: res.message
            });
          } else {
            notification.error({ message: 'Nelze přidat závěr do formuláře' });
          }

          return false;
        });
    },

    async getAllFiles(): Promise<false | File[]> {
      if (!this.checkFormIsDefined()) return false;

      this.loading = true;
      return fetchWrapper
        .get(`${baseUrl}/${this.form_id}/all_files`)
        .then((res: BaseResponseI<FileDto[]>) => {
          this.loading = false;

          if (res.status_code === 200) {
            return res.data.map((file) => new File(file));
          }

          return false;
        })
        .catch(() => {
          this.loading = false;
          return false;
        });
    },

    async updateFormTags(data: UpdateFormTagsDto) {
      if (!this.checkFormIsDefined()) return false;
      this.loading = true;
      return fetchWrapper
        .put(`${baseUrl}/${this.form_id}/tags`, data)
        .then((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 200) {
            notification.success({ message: 'Štítky byly upraveny' });
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Štítky nelze upravit', description: res.message });
          } else {
            notification.error({ message: 'Štítky nelze upravit' });
          }

          return false;
        });
    },

    async getForm(
      form_id: number
    ): Promise<false | Experiment | Message | Investigation | Attempt> {
      this.loading = true;
      return fetchWrapper
        .get(`${baseUrl}/${form_id}`)
        .then((res: BaseResponseI<ExperimentDto | MessageDto | InvestigationDto | AttemptDto>) => {
          this.loading = false;

          if (res.status_code === 200) {
            this.form_id = form_id;
            switch (res.data.form_type) {
              case FormType.EXPERIMENT:
                this.completeBatches = new BatchNumber(res.data.batch_number);
                return new Experiment(res.data as ExperimentDto);
              case FormType.MESSAGE:
                this.completeBatches = new BatchNumber(res.data.batch_number);
                return new Message(res.data as MessageDto);
              case FormType.INVESTIGATION:
                this.completeBatches = new BatchNumber(res.data.batch_number);
                return new Investigation(res.data as InvestigationDto);
              case FormType.ATTEMPT:
                this.completeBatches = new BatchNumber(res.data.batch_number);
                return new Attempt(res.data as AttemptDto);
              default:
                return false;
            }
          }

          return false;
        })
        .catch(() => {
          this.loading = false;
          return false;
        });
    },
    async requestReopenForm(form_id: number) {
      if (!form_id) {
        notification.error({ message: 'Není zvolen projekt' });
        return false;
      }
      this.loading = true;

      const URL = `${import.meta.env.VITE_API_URL}/form/${form_id}/request_reopen`;

      return fetchWrapper
        .post(URL)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Žádost byla úspešně odeslána' });
            this.loading = false;
          }
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Odeslání žádosti selhalo', description: res.error });
          }

          this.loading = false;
        });
    },
    async moveToDifferentChapter(chapter_id: number) {
      if (!this.checkFormIsDefined()) return false;
      this.loading = true;
      const URL = `${import.meta.env.VITE_API_URL}/form/move_to_chapter`;
      const data = {
        form_id: this.form_id,
        chapter_id
      };
      return fetchWrapper
        .post(URL, data)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Formulář byl úspěšně přesunut' });
            this.loading = false;
            return true;
          }
          this.loading = false;
          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Při přesouvání formuláře nastala chyba',
              description: res.error
            });
          }
          this.loading = false;
          return false;
        });
    }
  }
});

// export enum FormDtoType {
//   MESSAGE_FORM = 'message_form',
//   ATTEMPT_FORM = 'attempt_form',
//   INVESTIGATION_FORM = 'investigation_form',
//   EXPERIMENT_FORM = 'experiment_form'
// }

export interface FormDto {
  form_type: FormType;
}
