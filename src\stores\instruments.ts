import BaseConstructor from '@/utils/BaseConstructor';
import {
  loadParamsFailedReloadNotification,
  reloadWithoutParams,
  revertServerOptionsFromLocation,
  stringifyServerOptions,
  type BasePaginatorResponseI,
  type BaseResponseI
} from '@/utils/axios';
import { fetchWrapper } from '@/utils/helpers/fetch-wrapper';
import { defineStore } from 'pinia';
import type { GetAllOptions } from './projects';
import type { ServerOptions } from 'vue3-easy-data-table';
import { Technique, useTechniquesStore, type TechniqueDto } from './techniques';
import { Column, type ColumnDto } from './columns';
import { User, type UserDto } from './auth';
import {
  AnalyticalRequestForSample,
  type AnalyticalRequestForSampleDto
} from './analyticalRequests/analyticalRequests';
import { notification } from 'ant-design-vue';

const baseUrl = `${import.meta.env.VITE_API_URL}/instruments`;

export interface InstrumentDto {
  instrument_id: number;
  technique_id: number;
  instrument_shortcut: string;
  status: string;
  created_at?: string;
  updated_at?: string;
}

export interface InstrumentI {
  instrument_id: number;
  technique: Technique | number;
  instrument_shortcut: string;
  status: string;
  technique_id: number;
  created_at?: Date;
  updated_at?: Date;
}

export class Instrument extends BaseConstructor<InstrumentI>() implements InstrumentI {
  constructor(data: InstrumentDto) {
    super(data as unknown as InstrumentI);

    if (data.created_at) this.created_at = new Date(data.created_at);
    if (data.updated_at) this.updated_at = new Date(data.updated_at);

    if (data.technique_id) {
      const techniquesStore = useTechniquesStore();
      const technique = techniquesStore.techniques.get(data.technique_id);
      this.technique = technique ? technique : data.technique_id;
    }
  }

  async init() {
    const techniquesStore = useTechniquesStore();

    const Technique =
      typeof this.technique === 'number'
        ? await techniquesStore.getTechnique(this.technique)
        : this.technique;
    this.technique = Technique ? Technique : this.technique;
  }
}

export type InstrumentsListItemI = {
  instrument_id: number;
  technique_id: Technique | number;
  newTechnique_id: number;
  instrument_shortcut: string;
  status: string;
};

interface InstrumentModalOptionsI {
  isEditing: boolean;
  isCreating: boolean;
  baseData: Instrument | undefined;
  newData: NewInstrumentModalDataI | undefined;
}

interface NewInstrumentModalDataI {
  technique_id: number | undefined;
  instrument_shortcut: string | undefined;
  status: string | undefined;
  confirm: boolean | false;
  from_datetime: Date | undefined;
  to_datetime: Date | undefined;
}

interface InstrumentsStateI {
  instruments: Map<number, Instrument>;
  instrument: Instrument | null;
  loading: boolean;

  showInstrumentModal: boolean;
  modalOptions: InstrumentModalOptionsI | undefined;

  items: InstrumentsListItemI[];
  historyItemsInstruments: Map<number, InstrumentUsageHistory>;
  historyItems: InstrumentUsageHistoryProjectI[];
  historyLength?: number;
  totalItems?: number;
  options: ServerOptions;
  search: string | undefined;
  search_type?: 'AND' | 'OR';
  fixedFilterOptions: null | Array<{
    column: string;
    value: null | number | string | string[] | number[] | object | object[];
  }>;
}

export interface WeeklyOverviewWeekDto {
  count: number;
  week_number: number;
}

export interface WeeklyOverviewDto {
  year: number;
  weekly_overviews: WeeklyOverviewWeekDto[];
}

export interface InstrumentUsageHistoryProjectSampleDto {
  sample_id: number;
  user_id: number;
  technique_id: number;
  instrument_id: number;
  kolona_id: number;
  sample_number: string;
  sequence_name: string;
  method_name: string;
  preparation_of_standard_and_sample: string;
  note_or_specification: string;
  technique_notes: string;
  result: string;
  analysis_status: string;
  status: string;
  type: string;
  reanalyse_at: string;
  created_at: string;
  updated_at: string;
  user?: UserDto | null;
  technique?: TechniqueDto | null;
  instrument?: InstrumentDto | null;
  kolona?: ColumnDto | null;
  sub_technique?: TechniqueDto | null;
  analytical_request?: AnalyticalRequestForSampleDto | null;
  unread_notification?: boolean;
}

export interface InstrumentUsageHistoryProjectDto {
  project_id: number;
  user_id: number;
  name: string;
  status: string;
  created_at: string;
  updated_at: string;
  samples: InstrumentUsageHistoryProjectSampleDto[];
}

export interface InstrumentUsageHistoryDto {
  projects: InstrumentUsageHistoryProjectDto[];
}

export interface InstrumentUsageHistoryI {
  projects: InstrumentUsageHistoryProject[];
}

export interface InstrumentUsageHistoryProjectSampleI {
  sample_id: number;
  user_id: number;
  technique_id: number;
  instrument_id: number;
  kolona_id: number;
  sample_number: string;
  sequence_name: string;
  method_name: string;
  preparation_of_standard_and_sample: string;
  note_or_specification: string;
  technique_notes: string;
  result: string;
  reanalyse_at: Date;
  analysis_status: string;
  status: string;
  type: string;
  created_at: Date;
  updated_at: Date;
  user?: User | null;
  technique?: Technique | null;
  instrument?: Instrument | null;
  kolona?: Column | null;
  sub_technique?: Technique | null;
  analytical_request?: AnalyticalRequestForSample | null;
  unread_notification?: boolean;
}

export interface InstrumentUsageHistoryProjectI {
  project_id: number;
  user_id: number;
  name: string;
  status: string;
  created_at: Date;
  updated_at: Date;
  samples: InstrumentUsageHistoryProjectSampleI[];
}

export class InstrumentUsageHistoryProjectSample
  extends BaseConstructor<InstrumentUsageHistoryProjectSampleI>()
  implements InstrumentUsageHistoryProjectSampleI
{
  constructor(data: InstrumentUsageHistoryProjectSampleDto) {
    super(data as unknown as InstrumentUsageHistoryProjectSampleI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);
    this.reanalyse_at = new Date(data.reanalyse_at);

    if (data.user) this.user = new User(data.user);

    if (data.technique) this.technique = new Technique(data.technique);

    if (data.instrument) this.instrument = new Instrument(data.instrument);

    if (data.kolona) this.kolona = new Column(data.kolona);

    if (data.sub_technique) this.sub_technique = new Technique(data.sub_technique);

    if (data.analytical_request)
      this.analytical_request = new AnalyticalRequestForSample(data.analytical_request);
  }
}

export class InstrumentUsageHistoryProject
  extends BaseConstructor<InstrumentUsageHistoryProjectI>()
  implements InstrumentUsageHistoryProjectI
{
  constructor(data: InstrumentUsageHistoryProjectDto) {
    super(data as unknown as InstrumentUsageHistoryProjectI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);
    this.samples = data.samples.map((sample) => new InstrumentUsageHistoryProjectSample(sample));
  }
}

export class InstrumentUsageHistory
  extends BaseConstructor<InstrumentUsageHistoryI>()
  implements InstrumentUsageHistoryI
{
  constructor(data: InstrumentUsageHistoryDto) {
    super(data as unknown as InstrumentUsageHistoryI);

    this.projects = data.projects.map((project) => new InstrumentUsageHistoryProject(project));
  }
}

export const useInstrumentsStore = defineStore({
  id: 'instruments',
  state: () =>
    ({
      instrument: null,
      instruments: new Map(),
      loading: false,

      showInstrumentModal: false,
      modalOptions: undefined,
      historyItemsInstruments: new Map(),
      historyItems: [],
      items: [],
      search: undefined,
      totalItems: undefined,
      historyLength: undefined,
      options: {
        page: 1,
        rowsPerPage: 25,
        sortBy: [],
        sortType: ['desc', 'asc']
      },
      fixedFilterOptions: null,
      search_type: undefined
    }) as InstrumentsStateI,
  actions: {
    setInstruments(instruments: InstrumentDto[]) {
      this.instruments = new Map(
        instruments.map((instrument) => [instrument.instrument_id, new Instrument(instrument)])
      );
    },

    async getAll() {
      this.loading = true;
      this.options.sortBy = ['status', 'technique__status', 'instrument_id'];
      this.options.sortType = ['asc'];
      const URL =
        baseUrl +
        '/?' +
        stringifyServerOptions(this.options, this.search ?? null, ['instrument_shortcut']);

      fetchWrapper
        .get(URL)
        .then(async (res: BasePaginatorResponseI<InstrumentDto>) => {
          if (res.status_code === 200) {
            this.totalItems = res.data.total_items;

            const instruments: Instrument[] = [];
            await Promise.all(
              res.data.items.map(async (instrument) => {
                const c = new Instrument(instrument);
                await c.init();

                instruments.push(c);
              })
            );

            this.instruments = new Map(
              instruments.map((instrument) => [instrument.instrument_id, instrument])
            );

            this.items = [...this.instruments.values()].map((instrument) => {
              return {
                instrument_id: instrument.instrument_id,
                technique_id:
                  typeof instrument.technique !== 'number'
                    ? instrument.technique
                    : instrument.technique,
                newTechnique_id:
                  typeof instrument.technique !== 'number'
                    ? instrument.technique.analytical_technique_id
                    : instrument.technique,
                instrument_shortcut: instrument.instrument_shortcut,
                status: instrument.status
              } as InstrumentsListItemI;
            });
          }
          this.loading = false;
        })
        .catch((res) => {
          if (res.status_code === 400) {
            loadParamsFailedReloadNotification('Načtení přístrojů selhalo', res.error, () => {
              const data = reloadWithoutParams();
              this.options = data.options;
              this.search = data.term ?? undefined;
            });
          }

          this.loading = false;
        });
    },
    async getAllNew() {
      this.loading = true;

      const URL =
        baseUrl +
        '/?' +
        stringifyServerOptions(this.options, this.search ?? null, ['instrument_shortcut']);

      fetchWrapper
        .get(URL)
        .then(async (res: BasePaginatorResponseI<InstrumentDto>) => {
          if (res.status_code === 200) {
            this.totalItems = res.data.total_items;

            const instruments: Instrument[] = [];
            await Promise.all(
              res.data.items.map(async (instrument) => {
                const c = new Instrument(instrument);
                instruments.push(c);
              })
            );

            this.instruments = new Map(
              instruments.map((instrument) => [instrument.instrument_id, instrument])
            );

            this.items = [...this.instruments.values()].map((instrument) => {
              return {
                instrument_id: instrument.instrument_id,
                technique_id:
                  typeof instrument.technique !== 'number'
                    ? instrument.technique
                    : instrument.technique,
                newTechnique_id:
                  typeof instrument.technique !== 'number'
                    ? instrument.technique.analytical_technique_id
                    : instrument.technique,
                instrument_shortcut: instrument.instrument_shortcut,
                status: instrument.status
              } as InstrumentsListItemI;
            });
          }
          this.loading = false;
        })
        .catch((res) => {
          if (res.status_code === 400) {
            loadParamsFailedReloadNotification('Načtení přístrojů selhalo', res.error, () => {
              const data = reloadWithoutParams();
              this.options = data.options;
              this.search = data.term ?? undefined;
            });
          }

          this.loading = false;
        });
    },
    async getInstruments(
      options: GetAllOptions | undefined = {
        search_columns: ['instrument_shortcut']
      },
      search_type: 'AND' | 'OR' = 'OR'
    ): Promise<{
      data: InstrumentDto[];
      totalItems: number;
    }> {
      this.loading = true;

      const URL =
        `${import.meta.env.VITE_API_URL}/instruments/` +
        '?' +
        stringifyServerOptions(
          options && options.options ? options.options : this.options,
          options && options.search ? options.search : (this.search ?? null),
          options && options.search_columns ? options.search_columns : [],
          options && options.fixedFilterOptions ? options.fixedFilterOptions : null,
          this.search_type ?? search_type
        );

      return fetchWrapper
        .get(URL)
        .then(async (res: BasePaginatorResponseI<InstrumentDto>) => {
          if (res.status_code === 200) {
            this.totalItems = res.data.total_items;

            const instruments: Instrument[] = [];
            await Promise.all(
              res.data.items.map(async (instrument) => {
                const c = new Instrument(instrument);
                instruments.push(c);
              })
            );

            this.instruments = new Map(
              instruments.map((instrument) => [instrument.instrument_id, instrument])
            );

            this.items = [...this.instruments.values()].map((instrument) => {
              return {
                instrument_id: instrument.instrument_id,
                technique_id:
                  typeof instrument.technique !== 'number'
                    ? instrument.technique
                    : instrument.technique,
                newTechnique_id:
                  typeof instrument.technique !== 'number'
                    ? instrument.technique.analytical_technique_id
                    : instrument.technique,
                instrument_shortcut: instrument.instrument_shortcut,
                status: instrument.status
              } as InstrumentsListItemI;
            });
          }
          this.loading = false;
          return {
            data: [],
            totalItems: 0
          };
        })
        .catch((res) => {
          if (res.status_code === 400) {
            loadParamsFailedReloadNotification('Načtení přístrojů selhalo', res.error, () => {
              const data = reloadWithoutParams();
              this.options = data.options;
              this.search = data.term ?? undefined;
            });
          }
          this.loading = false;
          return {
            data: [],
            totalItems: 0
          };
        });
    },
    setParamsFromLocation(): void {
      const data = revertServerOptionsFromLocation();
      this.options = data.options;
      this.search = data.term ?? undefined;
    },

    showNewInstrumentModal() {
      const getLocalStorage = localStorage.getItem('instrumentStore');
      let parsedData;
      if (getLocalStorage) {
        parsedData = JSON.parse(getLocalStorage);
      }

      this.modalOptions = {
        isEditing: false,
        isCreating: true,
        baseData: undefined,
        newData: {
          technique_id: undefined,
          instrument_shortcut: undefined,
          status: 'active',
          confirm: false,
          from_datetime: new Date() as Date,
          to_datetime: new Date() as Date
        }
      };

      this.showInstrumentModal = true;
    },

    showPreviewModal(id: number) {
      this.instrument = this.instruments.get(id) ?? null;
      if (this.instrument === undefined) {
        notification.error({ message: 'Přístroj nebyl nalezen' });
        return;
      }

      this.modalOptions = {
        isEditing: false,
        isCreating: false,
        baseData: this.instrument ?? undefined,
        newData: {
          technique_id:
            typeof this.instrument?.technique !== 'number'
              ? this.instrument?.technique.analytical_technique_id
              : this.instrument?.technique,
          instrument_shortcut: this.instrument?.instrument_shortcut ?? '',
          status: this.instrument?.status ?? 'active',
          confirm: false,
          from_datetime: new Date() as Date,
          to_datetime: new Date() as Date
        }
      };

      this.showInstrumentModal = true;
    },

    showEditModal(id: number) {
      this.instrument = this.instruments.get(id) ?? null;
      if (this.instrument === undefined) {
        notification.error({ message: 'Přístroj nebyl nalezen' });
        return;
      }

      this.modalOptions = {
        isEditing: true,
        isCreating: false,
        baseData: this.instrument ?? undefined,
        newData: {
          technique_id:
            typeof this.instrument?.technique !== 'number'
              ? this.instrument?.technique.analytical_technique_id
              : this.instrument?.technique,
          instrument_shortcut: this.instrument?.instrument_shortcut ?? '',
          status: this.instrument?.status ?? 'active',
          confirm: false,
          from_datetime: new Date() as Date,
          to_datetime: new Date() as Date
        }
      };

      this.showInstrumentModal = true;
    },

    async getInstrument(id: number | undefined | null) {
      if (!id) {
        notification.error({ message: 'Přístroj nebyl nalezen' });
        return;
      }

      this.loading = true;

      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/instrument/${id}`)
        .then((res: BaseResponseI<InstrumentDto>) => {
          if (res.status_code === 200) {
            this.loading = false;
            return new Instrument(res.data);
          }
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Načtení přístroje selhalo', description: res.error });
          }

          this.loading = false;
          return null;
        });
    },

    deleteInstrument(id: number) {
      this.loading = true;

      return fetchWrapper
        .delete(`${import.meta.env.VITE_API_URL}/instrument/${id}`)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            this.instruments.delete(id);
            notification.success({ message: 'Přístroj byl úspěšně smazán' });
            this.getAll();
            this.loading = false;
          }
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Smazání přístroje selhalo', description: res.error });
          }

          this.loading = false;
        });
    },

    createInstrument() {
      if (!this.modalOptions?.newData || this.modalOptions?.newData?.confirm === false) {
        notification.error({ message: 'Musíte potvrdit souhlas s vytvořením přístroje' });
        return;
      }

      this.loading = true;

      const data = {
        technique_id: this.modalOptions.newData.technique_id,
        instrument_shortcut: this.modalOptions.newData.instrument_shortcut ?? '',
        status: this.modalOptions.newData.status ?? 'active'
      };

      return fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/instrument/`, data)
        .then((res: BaseResponseI<InstrumentDto>) => {
          if (res.status_code === 200) {
            this.instruments.set(res.data.instrument_id, new Instrument(res.data));
            this.showInstrumentModal = false;

            notification.success({
              message: 'Vytvoření přístroje proběhlo v pořádku',
              description: 'Zkratka: ' + res.data.instrument_shortcut
            });
            this.getAll();
            localStorage.removeItem('instrumentStore');
            this.loading = false;
          }
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Vytvoření přístroje selhalo', description: res.error });
          } else {
            this.showInstrumentModal = false;
          }

          this.loading = false;
        });
    },

    updateInstrument() {
      if (!this.modalOptions?.newData) {
        notification.error({ message: 'Není co upravovat' });
        return;
      }

      this.loading = true;

      const data = {
        technique_id: this.modalOptions.newData.technique_id,
        instrument_shortcut: this.modalOptions.newData.instrument_shortcut ?? '',
        status: this.modalOptions.newData.status ?? 'active'
      };

      return fetchWrapper
        .put(`${import.meta.env.VITE_API_URL}/instrument/${this.instrument?.instrument_id}`, data)
        .then((res: BaseResponseI<InstrumentDto>) => {
          if (res.status_code === 200) {
            this.instruments.set(res.data.instrument_id, new Instrument(res.data));
            this.showInstrumentModal = false;

            notification.success({
              message: 'Úprava přístroje proběhla v pořádku',
              description: 'Zkratka: ' + res.data.instrument_shortcut
            });
            this.getAll();
            this.loading = false;
          }
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Úprava přístroje selhala', description: res.error });
          } else {
            this.showInstrumentModal = false;
          }

          this.loading = false;
        });
    },
    resetModal() {
      this.showInstrumentModal = false;
      if (this.modalOptions) this.modalOptions.baseData = undefined;
      if (this.modalOptions) this.modalOptions.newData = undefined;
    },

    async getWeeklyOverviewOfInstrument(
      year: number | undefined | null = new Date().getFullYear()
    ) {
      if (!this.instrument) {
        notification.error({ message: 'Přístroj nebyl vybrán' });
        return false;
      }

      this.loading = true;
      const URL = `${import.meta.env.VITE_API_URL}/instrument/${this.instrument.instrument_id}/weekly_overview?year=${year}`;

      return fetchWrapper
        .get(URL)
        .then((res: BaseResponseI<WeeklyOverviewDto>) => {
          this.loading = false;

          if (res.status_code === 200) {
            notification.success({ message: 'Týdenní přehled byl úspěšně načten' });
            return res.data;
          }
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({
              message: 'Načtení týdenního přehledu selhalo',
              description: res.error
            });
          }

          return null;
        });
    },

    async getHistoryOfInstrumentUsage(from_datetime: Date, to_datetime: Date) {
      if (!this.instrument) {
        notification.error({ message: 'Přístroj nebyl vybrán' });
        return { projects: [] } as unknown as InstrumentUsageHistoryI;
      }

      this.loading = true;

      const URL = `${import.meta.env.VITE_API_URL}/instrument/${this.instrument.instrument_id}/history_of_usage?from_datetime=${from_datetime.toISOString()}&to_datetime=${to_datetime.toISOString()}`;

      return fetchWrapper
        .get(URL)
        .then((res: BaseResponseI<InstrumentUsageHistoryDto>) => {
          this.loading = false;
          if (res.status_code === 200) {
            const history = new InstrumentUsageHistory(res.data);
            this.historyItems = history.projects;
            this.historyLength = history.projects.length;
            // notification.success({ message: 'Historie použití byla úspěšně načtena' });
            return history;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Načtení historie použití selhalo',
              description: res.error
            });
          }
          this.loading = false;
          return false;
        });
    }
  }
});
