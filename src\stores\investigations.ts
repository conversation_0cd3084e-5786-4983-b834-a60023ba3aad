import type { TagListI } from '@/components/shared/TagModal.vue';
import { router } from '@/router';
import BaseConstructor from '@/utils/BaseConstructor';
import {
  loadParamsFailedReloadNotification,
  reloadWithoutParams,
  revertServerOptionsFromLocation,
  stringifyServerOptions,
  type BasePaginatorResponseI,
  type BaseResponseI
} from '@/utils/axios';
import { fetchWrapper } from '@/utils/helpers/fetch-wrapper';
import { notification } from 'ant-design-vue';
import { defineStore } from 'pinia';
import type { ServerOptions } from 'vue3-easy-data-table';
import { useChaptersStore } from './chapters';
import { Chemical, type ChemicalDto, type NewChemicalDataI } from './chemicals';
import { File, type FileDto } from './files';
import { useProjectsStore, type GetAllOptions } from './projects';
import { Tag, type TagDto } from './tags';
import { BatchNumber, Conclusion, type BatchNumberDto, type ConclusionDto } from './experiments';
import { UserLock, UserLockStatus, type LockTableDto, type LockTableI } from './userLock';
import { useAuthStore } from './auth';

const baseUrl = `${import.meta.env.VITE_API_URL}/investigations`;

export enum InvestigationStatus {
  CREATED = 'created',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  SIGNED = 'signed',
  CANCELED = 'canceled'
}

export interface InvestigationDto extends LockTableDto {
  form_id: number;
  batch_number_id: number;
  chapter_id: number;
  form_name: string;
  batch_description: string;
  affected_batch_number: string;
  status: InvestigationStatus;
  created_at: string;
  updated_at: string;
  chemicals: ChemicalDto[];
  conclusions: ConclusionDto[];
  tags: TagDto[];
  files: FileDto[];
  batch_number: BatchNumberDto;
  investigation_id: number;
  impact_on_quality: boolean;
  impact_on_yield: boolean;
  problem_description: string;
  investigation_in_production: string;
  investigation_in_laboratory: string;
  recommendations: string;
  owner: OwnerDto;
  collaborators: collaboratorDto[];
  authors?: OwnerDto[];
}

export interface collaboratorDto {
  can_edit: boolean;
  can_delete: boolean;
  can_share: boolean;
  form_collaborator_id: number;
  form_id: number;
  user_id: number;
  created_at: string;
  updated_at: string;
  user: OwnerDto;
}

export interface CollaboratorI {
  can_edit: boolean;
  can_delete: boolean;
  can_share: boolean;
  form_collaborator_id: number;
  form_id: number;
  user_id: number;
  created_at: Date;
  updated_at: Date;
  user: Owner;
}

export class Collaborator extends BaseConstructor<CollaboratorI>() implements CollaboratorI {
  constructor(data: collaboratorDto) {
    super(data as unknown as CollaboratorI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    this.user = new Owner(data.user);
  }
}

export interface OwnerDto {
  user_id: number;
  first_name: string;
  last_name: string;
  name_shortcut: string;
  status: string;
  user_email: string;
  created_at: string;
  updated_at: string;
}

export interface OwnerI {
  user_id: number;
  first_name: string;
  last_name: string;
  name_shortcut: string;
  status: string;
  user_email: string;
  created_at: Date;
  updated_at: Date;
}

export class Owner extends BaseConstructor<OwnerI>() implements OwnerI {
  constructor(data: OwnerDto) {
    super(data as unknown as OwnerI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);
  }
}

export interface InvestigationI extends LockTableI {
  form_id: number;
  batch_number_id: number;
  chapter_id: number;
  form_name: string;
  batch_description: string;
  affected_batch_number: string;
  status: InvestigationStatus;
  created_at: Date;
  updated_at: Date;
  chemicals: Chemical[];
  conclusions: Conclusion[];
  tags: Tag[];
  files: File[];
  batch_number: BatchNumber;
  investigation_id: number;
  impact_on_quality: boolean;
  impact_on_yield: boolean;
  problem_description: string;
  investigation_in_production: string;
  investigation_in_laboratory: string;
  recommendations: string;
  tagIsLoaded: boolean;
  showTags: boolean;
  owner: Owner;
  collaborators: Collaborator[];
  authors?: Owner[];
}

export class Investigation extends BaseConstructor<InvestigationI>() implements InvestigationI {
  constructor(data: InvestigationDto) {
    super(data as unknown as InvestigationI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    this.tags = data.tags.map((tag) => new Tag(tag));
    this.files = data.files.map((file) => new File(file));
    this.batch_number = new BatchNumber(data.batch_number);
    this.chemicals = data.chemicals.map((chemical) => new Chemical(chemical));
    this.conclusions = data.conclusions.map((conclusion) => new Conclusion(conclusion));
    this.tagIsLoaded = true;
    this.showTags = false;
    if (data.edited_by) {
      this.edited_by = new UserLock(data.edited_by);
    }

    this.owner = new Owner(data.owner);
    if (data.authors) {
      this.authors = data.authors.map((author) => new Owner(author));
    }
    this.collaborators = data.collaborators.map((collaborator) => new Collaborator(collaborator));
  }

  get isActive(): boolean {
    return this.status !== InvestigationStatus.CANCELED;
  }

  get isReadOnly(): boolean {
    return (
      this.status === InvestigationStatus.SIGNED || this.status === InvestigationStatus.CANCELED
    );
  }

  get isLocked(): boolean {
    const authStore = useAuthStore();
    return (
      this.edited_by?.status === UserLockStatus.LOCKED_BY_OTHER_USER ||
      (this.edited_by?.status === UserLockStatus.LOCKED &&
        this.edited_by.user_id !== authStore.user?.user_id)
    );
  }

  get statusText(): string {
    switch (this.status) {
      case InvestigationStatus.CREATED:
        return 'Aktivní';
      case InvestigationStatus.IN_PROGRESS:
        return 'Probíhající';
      case InvestigationStatus.SIGNED:
        return 'Podepsaný';
      case InvestigationStatus.CANCELED:
        return 'Zrušený';
      default:
        return 'Neznámý';
    }
  }
  async getTags() {
    const chaptersStore = useChaptersStore();
    const tags = await chaptersStore.getTagsByChapter(this.chapter_id);
    this.tags = tags;

    this.tagIsLoaded = true;
  }

  get joinTags() {
    return this.tags?.map((t) => t.tag_name).join(', ') ?? '';
  }

  get tagLists() {
    return (
      this.tags?.map(
        (t) =>
          ({
            tag_id: t.tag_id,
            tag_name: t.tag_name
          }) as TagListI
      ) ?? []
    );
  }
}

export interface InvestigationModalNewDataI {
  chapter_id: number | undefined;
  form_name: string | undefined;
  investigation_template_id: null | number;
  confirm: boolean | false;
}

export interface InvestigationUpdateDataI {
  form_name: string | undefined;
  batch_description: string | undefined;
  affected_batch_number: string | undefined;
  chemicals: NewChemicalDataI[];
  conclusion_text: string | undefined;
  impact_on_quality: boolean | undefined;
  impact_on_yield: boolean | undefined;
  problem_description: string | undefined;
  investigation_in_production: string | undefined;
  investigation_in_laboratory: string | undefined;
  recommendations: string | undefined;
  batch_number: string | undefined;
}

interface InvestigationUpdatePostDataI {
  form_name: string;
  batch_description: string;
  affected_batch_number: string;
  chemicals: NewChemicalDataI[];
  conclusion_text: string;
  impact_on_quality: boolean;
  impact_on_yield: boolean;
  problem_description: string;
  investigation_in_production: string;
  investigation_in_laboratory: string;
  recommendations: string;
}

interface InvestigationModalOptionsI {
  isEditing: boolean;
  isCreating: boolean;
  baseData: Investigation | undefined;
  newData: InvestigationModalNewDataI | undefined;
  updateData: InvestigationUpdateDataI | undefined;
}

interface InvestigationsStateI {
  investigations: Map<number, Investigation>;
  investigation: Investigation | null;
  loading: boolean;

  showInvestigationModal: boolean;
  modalOptions: InvestigationModalOptionsI | undefined;

  totalItems?: number;
  options: ServerOptions;
  search: string | undefined;
}

export interface SaveInvestigationAsTemplateDto {
  template_name: string;
}

export const useInvestigationsStore = defineStore({
  id: 'investigations',
  state: () =>
    ({
      investigation: null,
      investigations: new Map(),
      loading: false,

      showInvestigationModal: false,
      modalOptions: undefined,

      items: [],
      search: undefined,
      totalItems: undefined,
      options: {
        page: 1,
        rowsPerPage: 25,
        sortBy: [],
        sortType: ['desc', 'asc']
      }
    }) as InvestigationsStateI,
  actions: {
    async getInvestigations(
      setData: boolean = true,
      options: GetAllOptions | undefined = {
        search_columns: ['form_name']
      }
    ): Promise<{
      data: Investigation[];
      totalItems: number;
    }> {
      this.loading = true;

      const URL =
        baseUrl +
        '/?' +
        stringifyServerOptions(
          options && options.options ? options.options : this.options,
          options && options.search ? options.search : (this.search ?? null),
          options && options.search_columns ? options.search_columns : [],
          options && options.fixedFilterOptions ? options.fixedFilterOptions : null
        );

      return fetchWrapper
        .get(URL)
        .then((res: BasePaginatorResponseI<InvestigationDto>) => {
          if (res.status_code === 200) {
            this.totalItems = res.data.total_items;
            if (setData) {
              this.investigations = new Map(
                res.data.items.map((investigation) => [
                  investigation.form_id,
                  new Investigation(investigation)
                ])
              );
            }

            this.loading = false;
            return {
              data: res.data.items.map((investigation) => new Investigation(investigation)),
              totalItems: res.data.total_items
            };
          }
          this.loading = false;
          return {
            data: [],
            totalItems: 0
          };
        })
        .catch((res) => {
          if (res.status_code === 400) {
            loadParamsFailedReloadNotification('Načtení výzkumů selhalo', res.error, () => {
              const data = reloadWithoutParams();
              this.options = data.options;
              this.search = data.term ?? undefined;
            });
          }

          this.loading = false;
          return {
            data: [],
            totalItems: 0
          };
        });
    },

    async getInvestigation(id: number) {
      this.loading = true;

      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/investigation/${id}`)
        .then((res: BaseResponseI<InvestigationDto>) => {
          this.loading = false;

          if (res.status_code === 200) {
            return new Investigation(res.data);
          }

          return undefined;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Načtení výzkumu selhalo', description: res.message });
          } else {
            notification.error({ message: 'Načtení výzkumu selhalo' });
          }

          return undefined;
        });
    },

    async getInvestigationById(id: number) {
      const _Investigation = await this.getInvestigation(id);

      if (_Investigation) {
        this.investigation = _Investigation;
      } else {
        notification.error({
          message: 'Chyba',
          description: 'Výzkum nebyl nalezen nebo nemáte oprávnění k zobrazení.'
        });
        router.push({ name: 'ChapterDetail' });
      }
    },

    setParamsFromLocation(): void {
      const data = revertServerOptionsFromLocation();
      this.options = data.options;
      this.search = data.term ?? undefined;
    },

    async showNewInvestigationModal(chapter_id: number) {
      const projectsStore = useProjectsStore();
      const chaptersStore = useChaptersStore();

      const _Chapter = await chaptersStore.getChapter(chapter_id);
      if (_Chapter === undefined) {
        notification.error({ message: 'Kapitola nebyla nalezena' });
        return;
      }

      this.modalOptions = {
        isEditing: false,
        isCreating: true,
        baseData: undefined,
        updateData: undefined,
        newData: {
          chapter_id: chapter_id,
          form_name: undefined,
          investigation_template_id: null,
          confirm: false
        }
      };

      this.showInvestigationModal = true;
    },

    async createInvestigation() {
      if (!this.modalOptions?.newData || this.modalOptions?.newData?.confirm === false) {
        notification.error({ message: 'Musíte potvrdit souhlas s vytvořením výzkumu' });
        return false;
      }

      if (this.modalOptions.newData.chapter_id === undefined) {
        notification.error({ message: 'Není vybrána kapitola' });
        return false;
      }

      this.loading = true;

      const data = {
        chapter_id: this.modalOptions.newData.chapter_id,
        form_name: this.modalOptions.newData.form_name ?? '',
        investigation_template_id: this.modalOptions.newData.investigation_template_id ?? null
      };

      return await fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/investigation/`, data)
        .then((res: BaseResponseI<InvestigationDto>) => {
          if (res.status_code === 200) {
            this.investigations.set(res.data.form_id, new Investigation(res.data));
            this.showInvestigationModal = false;

            notification.success({
              message: 'Vytvoření výzkumu proběhlo v pořádku',
              description: 'Název: ' + res.data.form_name
            });

            this.loading = false;
            router.push(this.redirectToInvestigationDetailLink(res.data.form_id));
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Vytvoření výzkumu selhalo', description: res.error });
          } else {
            notification.error({ message: 'Vytvoření výzkumu selhalo' });
            this.showInvestigationModal = false;
          }

          this.loading = false;
          return false;
        });
    },
    resetModal() {
      this.showInvestigationModal = false;
      if (this.modalOptions) this.modalOptions.baseData = undefined;
      if (this.modalOptions) this.modalOptions.newData = undefined;
    },

    async updateInvestigation(data: InvestigationUpdateDataI) {
      if (!this.investigation) {
        notification.error({ message: 'Není vybrán výzkum' });
        return false;
      }
      this.loading = true;

      const _data = {
        form_name: data.form_name ?? '',
        batch_description: data.batch_description ?? '',
        affected_batch_number: data.affected_batch_number ?? '',
        chemicals: data.chemicals,
        conclusion_text: data.conclusion_text ?? '',
        impact_on_quality: data.impact_on_quality ?? false,
        impact_on_yield: data.impact_on_yield ?? false,
        problem_description: data.problem_description ?? '',
        investigation_in_production: data.investigation_in_production ?? '',
        investigation_in_laboratory: data.investigation_in_laboratory ?? '',
        recommendations: data.recommendations ?? '',
        batch_number: {
          batch_number: data.batch_number ?? ''
        }
      } as InvestigationUpdatePostDataI;

      return await fetchWrapper
        .put(
          `${import.meta.env.VITE_API_URL}/investigation/${this.investigation.investigation_id}`,
          _data
        )
        .then((res: BaseResponseI<InvestigationDto>) => {
          if (res.status_code === 200) {
            this.investigations.set(res.data.form_id, new Investigation(res.data));
            this.showInvestigationModal = false;

            notification.success({
              message: 'Aktualizace výzkumu proběhla v pořádku',
              description: 'Název: ' + res.data.form_name
            });

            this.loading = false;
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Aktualizace výzkumu selhala', description: res.error });
          } else {
            this.showInvestigationModal = false;
          }

          this.loading = false;
          return false;
        });
    },

    async saveInvestigationAsTemplate(data: SaveInvestigationAsTemplateDto) {
      if (!this.investigation) {
        notification.error({ message: 'Není vybrán výzkum' });
        return false;
      }

      this.loading = true;

      return await fetchWrapper
        .post(
          `${import.meta.env.VITE_API_URL}/investigation/${this.investigation.investigation_id}/save_as_template`,
          data
        )
        .then((res: BaseResponseI<InvestigationDto>) => {
          if (res.status_code === 200) {
            notification.success({
              message: 'Uložení výzkumu jako šablony proběhlo v pořádku',
              description: 'Název: ' + res.data.form_name
            });

            this.loading = false;
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Uložení výzkumu jako šablony selhalo',
              description: res.error
            });
          } else {
            notification.error({ message: 'Uložení výzkumu jako šablony selhalo' });
          }

          this.loading = false;
          return false;
        });
    },
    async updateChapterTags(form_id: number, tags: number[]) {
      this.loading = true;

      return fetchWrapper
        .patch(`${import.meta.env.VITE_API_URL}/form/${form_id}/tags`, { tags: tags })
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Štítky byly úspěšně upraveny' });
            this.loading = false;
          }
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Úprava štítků selhala', description: res.error });
          }

          this.loading = false;
        });
    },

    async updateTags(experiment: Investigation, tags: TagListI[]) {
      await this.updateChapterTags(
        experiment.form_id,
        tags.map((tag) => tag.tag_id)
      );

      experiment.getTags();
      experiment.showTags = false;
    },
    closeInvestigation(id: number, force: boolean) {
      if (!id) {
        notification.error({ message: 'Není co zavírat' });
        return false;
      }
      this.loading = true;
      const data = {
        status: 'canceled',
        brute_force_close: force
      };

      return fetchWrapper
        .patch(`${import.meta.env.VITE_API_URL}/form/${id}/status`, data)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            this.investigations.delete(id);
            notification.success({ message: 'Šetření bylo úspěšně uzavřeno' });
            this.getInvestigations();
            this.loading = false;

            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Zavření šetření selhalo', description: res.error });
          }

          this.loading = false;
          return false;
        });
    },
    reactivateInvestigation(id: number) {
      if (!id) {
        notification.error({ message: 'Není co otvírat' });
        return false;
      }
      this.loading = true;
      const data = {
        status: 'created',
        brute_force_close: false
      };

      return fetchWrapper
        .patch(`${import.meta.env.VITE_API_URL}/form/${id}/status`, data)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            this.investigations.delete(id);
            notification.success({ message: 'Šetření bylo úspěšně otevřeně' });
            this.getInvestigations();
            this.loading = false;

            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Aktivace šetření selhala', description: res.error });
          }

          this.loading = false;
          return false;
        });
    },
    redirectToInvestigationDetailLink(id: number) {
      return router.resolve({
        name: 'Investigation',
        params: { form_id: id.toString() }
      });
    }
  }
});
