import BaseConstructor from '@/utils/BaseConstructor';
import {
  loadParamsFailedReloadNotification,
  reloadWithoutParams,
  revertServerOptionsFromLocation,
  stringifyServerOptions,
  type BasePaginatorResponseI,
  type BaseResponseI
} from '@/utils/axios';
import { fetchWrapper } from '@/utils/helpers/fetch-wrapper';
import { notification } from 'ant-design-vue';
import { defineStore } from 'pinia';
import type { ServerOptions } from 'vue3-easy-data-table';
import {
  Chemical,
  ChemicalStatus,
  ChemicalType,
  type ChemicalDto,
  type NewChemicalDataI,
  type ChemicalI
} from './chemicals';
import { File, useFilesStore, type FileDto } from './files';
import type { GetAllOptions, PaginatorRequestDataI } from './projects';

const baseUrl = `${import.meta.env.VITE_API_URL}/investigation-templates`;

export enum InvestigationTemplateStatus {
  ACTIVE = 'active',
  DELETED = 'deleted'
}

export interface InvestigationTemplateDto {
  project_template_id: number;
  project_id: number;
  template_name: string;
  status: string;
  created_at: string;
  updated_at: string;
  chemicals: ChemicalDto[];
  files: FileDto[];
  investigation_template_id: number;
  impact_on_quality: boolean;
  impact_on_yield: boolean;
  problem_description: string;
  investigation_in_production: string;
  investigation_in_laboratory: string;
}

export interface InvestigationTemplateI {
  project_template_id: number;
  project_id: number;
  template_name: string;
  status: InvestigationTemplateStatus;
  created_at: Date;
  updated_at: Date;
  chemicals: Chemical[];
  files: File[];
  investigation_template_id: number;
  impact_on_quality: boolean;
  impact_on_yield: boolean;
  problem_description: string;
  investigation_in_production: string;
  investigation_in_laboratory: string;
}

export class InvestigationTemplate
  extends BaseConstructor<InvestigationTemplateI>()
  implements InvestigationTemplateI
{
  constructor(data: InvestigationTemplateDto) {
    super(data as unknown as InvestigationTemplateI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    this.files = data.files.map((file) => new File(file));
    this.chemicals = data.chemicals.map((chemical) => new Chemical(chemical));
  }
}

interface InvestigationTemplateModalOptionsI {
  isEditing: boolean;
  isCreating: boolean;
  baseData: InvestigationTemplate | undefined;
  newData: InvestigationTemplateModalNewDataI | undefined;
  updateData: InvestigationTemplateModalUpdateDataI | undefined;
}

export interface InvestigationTemplateModalNewDataI {
  project_id: number | undefined;
  template_name: string | undefined;
  files_ids: number[];
  chemicals: NewChemicalDataI[];
  impact_on_quality: boolean | undefined;
  impact_on_yield: boolean | undefined;
  problem_description: string | undefined;
  investigation_in_production: string | undefined;
  investigation_in_laboratory: string | undefined;
  confirm: boolean | false;
  files: File[];
}

export interface InvestigationTemplateModalUpdateDataI {
  template_name: string | undefined;
  chemicals: ChemicalI[];
  status: InvestigationTemplateStatus | undefined;
  files_ids: number[];
  files: File[];
  impact_on_quality: boolean | undefined;
  impact_on_yield: boolean | undefined;
  problem_description: string | undefined;
  investigation_in_production: string | undefined;
  investigation_in_laboratory: string | undefined;
}

interface InvestigationTemplatesStateI {
  investigationTemplates: InvestigationTemplate[];
  investigationTemplate: InvestigationTemplate | null;
  loading: boolean;

  showInvestigationTemplateModal: boolean;
  modalOptions: InvestigationTemplateModalOptionsI | undefined;
  totalItems?: number;
  options: ServerOptions;
  search: string | undefined;
}

export const useInvestigationTemplates = defineStore({
  id: 'investigationTemplates',
  state: () =>
    ({
      investigationTemplate: null,
      investigationTemplates: [],
      loading: false,

      showInvestigationTemplateModal: false,
      modalOptions: undefined,

      search: undefined,
      totalItems: undefined,
      options: {
        page: 1,
        rowsPerPage: 100,
        sortBy: [],
        sortType: ['desc', 'asc']
      }
    }) as InvestigationTemplatesStateI,
  actions: {
    async getInvestigationTemplates(
      setData: boolean = true,
      options: GetAllOptions | undefined = {
        search_columns: ['name']
      }
    ): Promise<{
      data: InvestigationTemplate[];
      totalItems: number;
    }> {
      this.loading = true;
      this.options.sortBy = ['status', 'project_template_id'];
      this.options.sortType = ['asc'];
      const URL =
        baseUrl +
        '/?' +
        stringifyServerOptions(
          options && options.options ? options.options : this.options,
          options && options.search ? options.search : (this.search ?? null),
          options && options.search_columns ? options.search_columns : [],
          options && options.fixedFilterOptions ? options.fixedFilterOptions : null
        );

      return fetchWrapper
        .get(URL)
        .then((res: BasePaginatorResponseI<InvestigationTemplateDto>) => {
          this.loading = false;

          if (res.status_code === 200) {
            this.totalItems = res.data.total_items;
            if (setData) {
              this.investigationTemplates = res.data.items.map(
                (investigationTemplate) => new InvestigationTemplate(investigationTemplate)
              );
            }

            this.loading = false;
            return {
              data: res.data.items.map(
                (investigationTemplate) => new InvestigationTemplate(investigationTemplate)
              ),
              totalItems: res.data.total_items
            };
          }

          return {
            data: [],
            totalItems: 0
          };
        })
        .catch((res) => {
          this.loading = false;

          if (res.status_code === 400) {
            loadParamsFailedReloadNotification('Načtení šablon selhalo', res.error, () => {
              const data = reloadWithoutParams();
              this.options = data.options;
              this.search = data.term ?? undefined;
            });
          } else {
            notification.error({ message: 'Načtení šablon selhalo' });
          }

          return {
            data: [],
            totalItems: 0
          };
        });
    },

    async getInvestigationTemplatesForSelect(
      investigationTemplateSelectOptions: PaginatorRequestDataI<InvestigationTemplate>,
      addNewData = true
    ) {
      investigationTemplateSelectOptions.loading = true;
      const URL =
        baseUrl +
        '/?' +
        stringifyServerOptions(
          investigationTemplateSelectOptions.options,
          investigationTemplateSelectOptions.search ?? null,
          investigationTemplateSelectOptions.search_columns,
          investigationTemplateSelectOptions.filterOptions
        );

      fetchWrapper
        .get(URL)
        .then((res: BasePaginatorResponseI<InvestigationTemplateDto>) => {
          investigationTemplateSelectOptions.loading = false;

          if (res.status_code === 200) {
            investigationTemplateSelectOptions.totalItems = res.data.total_items;
            if (addNewData) {
              investigationTemplateSelectOptions.results = [
                ...investigationTemplateSelectOptions.results,
                ...res.data.items.map(
                  (investigationTemplate) => new InvestigationTemplate(investigationTemplate)
                )
              ];
              investigationTemplateSelectOptions.results = [
                ...new Map(
                  investigationTemplateSelectOptions.results.map((investigationTemplate) => [
                    investigationTemplate.investigation_template_id,
                    investigationTemplate
                  ])
                ).values()
              ];
            } else {
              investigationTemplateSelectOptions.results = Array.from(
                res.data.items.map(
                  (investigationTemplate) => new InvestigationTemplate(investigationTemplate)
                )
              );
            }

            return;
          }

          if (!addNewData) {
            investigationTemplateSelectOptions.results = [];
          }
        })
        .catch((res) => {
          investigationTemplateSelectOptions.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Načtení šablon selhalo', description: res.error });
          }

          if (!addNewData) {
            investigationTemplateSelectOptions.results = [];
          }
        });
    },

    async getInvestigationTemplate(
      investigation_template_id: number
    ): Promise<InvestigationTemplate | undefined> {
      this.loading = true;

      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/investigation-template/${investigation_template_id}`)
        .then((res: BaseResponseI<InvestigationTemplateDto>) => {
          if (res.status_code === 200) {
            this.loading = false;
            return new InvestigationTemplate(res.data);
          }
          this.loading = false;
          return undefined;
        })
        .catch((res) => {
          if (res.status_code === 400) {
            loadParamsFailedReloadNotification('Načtení šablony selhalo', res.error, () => {
              const data = reloadWithoutParams();
              this.options = data.options;
              this.search = data.term ?? undefined;
            });
          }

          this.loading = false;
          return undefined;
        });
    },

    setParamsFromLocation(): void {
      const data = revertServerOptionsFromLocation();
      this.options = data.options;
      this.search = data.term ?? undefined;
    },

    showNewInvestigationTemplateModal(project_id: number) {
      const getLocalStorage = localStorage.getItem('investigationTemplatePageStore');
      let parsedData;
      if (getLocalStorage) {
        parsedData = JSON.parse(getLocalStorage);
      }
      this.modalOptions = {
        isEditing: false,
        isCreating: true,
        baseData: undefined,
        updateData: undefined,
        newData: {
          project_id: project_id,
          template_name: undefined,
          files_ids: [],
          chemicals: [],
          files: [],
          confirm: false,
          impact_on_quality: undefined,
          impact_on_yield: undefined,
          problem_description: undefined,
          investigation_in_production: undefined,
          investigation_in_laboratory: undefined
        }
      };

      this.showInvestigationTemplateModal = true;
    },

    async showPreviewModal(id: number) {
      const filesStore = useFilesStore();
      const _InvestigationTemplate = await this.getInvestigationTemplate(id);
      if (_InvestigationTemplate === undefined) {
        notification.error({ message: 'Šablona nebyla nalezena' });
        return;
      }
      this.modalOptions = {
        isEditing: false,
        isCreating: false,
        baseData: _InvestigationTemplate,
        newData: undefined,
        updateData: {
          files: [],
          files_ids: [],
          template_name: _InvestigationTemplate.template_name,
          status: _InvestigationTemplate.status,
          impact_on_quality: _InvestigationTemplate.impact_on_quality,
          impact_on_yield: _InvestigationTemplate.impact_on_yield,
          problem_description: _InvestigationTemplate.problem_description,
          investigation_in_production: _InvestigationTemplate.investigation_in_production,
          investigation_in_laboratory: _InvestigationTemplate.investigation_in_laboratory,
          chemicals: _InvestigationTemplate.chemicals.map((chemical) => {
            return {
              chemical_id: chemical.chemical_id,
              name: chemical.name,
              density: chemical.density,
              molar_mass: chemical.molar_mass,
              notes: chemical.notes,
              grams: chemical.grams,
              moles: chemical.moles,
              equivalent: chemical.equivalent,
              volume_ml: chemical.volume_ml,
              concentration: chemical.concentration,
              type: chemical.type,
              project_template_id: chemical.project_template_id,
              status: chemical.status,
              form_id: null,
              created_at: chemical.created_at,
              updated_at: chemical.updated_at
            } as ChemicalI;
          })
        }
      };

      // this.showInvestigationTemplateModal = true;
    },

    async showEditModal(id: number) {
      const filesStore = useFilesStore();
      const _InvestigationTemplate = await this.getInvestigationTemplate(id);
      if (_InvestigationTemplate === undefined) {
        notification.error({ message: 'Šablona nebyla nalezena' });
        return;
      }

      this.investigationTemplate = _InvestigationTemplate;

      this.modalOptions = {
        isEditing: true,
        isCreating: false,
        baseData: _InvestigationTemplate,
        newData: undefined,
        updateData: {
          files_ids: [],
          files: [],
          template_name: _InvestigationTemplate.template_name,
          status: _InvestigationTemplate.status,
          impact_on_quality: _InvestigationTemplate.impact_on_quality,
          impact_on_yield: _InvestigationTemplate.impact_on_yield,
          problem_description: _InvestigationTemplate.problem_description,
          investigation_in_production: _InvestigationTemplate.investigation_in_production,
          investigation_in_laboratory: _InvestigationTemplate.investigation_in_laboratory,
          chemicals: _InvestigationTemplate.chemicals.map((chemical) => {
            return {
              chemical_id: chemical.chemical_id,
              name: chemical.name,
              density: chemical.density,
              molar_mass: chemical.molar_mass,
              notes: chemical.notes,
              grams: chemical.grams,
              moles: chemical.moles,
              equivalent: chemical.equivalent,
              volume_ml: chemical.volume_ml,
              concentration: chemical.concentration,
              type: chemical.type,
              project_template_id: chemical.project_template_id,
              status: chemical.status,
              form_id: null,
              created_at: chemical.created_at,
              updated_at: chemical.updated_at
            } as ChemicalI;
          })
        }
      };

      //this.showInvestigationTemplateModal = true;
    },

    async deleteInvestigationTemplate(investigation_template_id: number) {
      this.loading = true;

      return fetchWrapper
        .delete(
          `${import.meta.env.VITE_API_URL}/investigation-template/${investigation_template_id}`
        )
        .then((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 200) {
            notification.success({ message: 'Šablona byla úspěšně smazána' });
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Smazání šablony selhalo', description: res.error });
          }

          return false;
        });
    },

    async createInvestigationTemplate(): Promise<boolean> {
      const filesStore = useFilesStore();
      if (!this.modalOptions?.newData) {
        notification.error({ message: 'Musíte potvrdit souhlas s vytvořením šablony' });
        return false;
      }

      this.loading = true;
      const file_id: number | undefined = this.modalOptions.newData.files_ids[0] ?? undefined;

      const data = {
        project_id: this.modalOptions.newData.project_id,
        template_name: this.modalOptions.newData.template_name ?? '',
        files_ids: this.modalOptions.newData.files.map((f) => f.file_id) ?? [],
        impact_on_quality: this.modalOptions.newData.impact_on_quality ?? false,
        impact_on_yield: this.modalOptions.newData.impact_on_yield ?? false,
        problem_description: this.modalOptions.newData.problem_description ?? '',
        investigation_in_production: this.modalOptions.newData.investigation_in_production ?? '',
        investigation_in_laboratory: this.modalOptions.newData.investigation_in_laboratory ?? '',
        chemicals:
          this.modalOptions.newData.chemicals?.map((chemical) => ({
            name: chemical.name ?? '',
            density: chemical.density ?? 0,
            molar_mass: chemical.molar_mass ?? 0,
            notes: chemical.notes ?? '',
            grams: chemical.grams ?? 0,
            moles: chemical.moles ?? 0,
            equivalent: chemical.equivalent ?? 0,
            volume_ml: chemical.volume_ml ?? 0,
            concentration: chemical.concentration ?? 0,
            status: ChemicalStatus.ACTIVE,
            type: chemical.type ?? ChemicalType.CHEMICAL
          })) ?? []
      };

      return fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/investigation-template/`, data)
        .then(async (res: BaseResponseI<InvestigationTemplateDto>) => {
          this.loading = false;

          if (res.status_code === 200) {
            this.showInvestigationTemplateModal = false;

            notification.success({
              message: 'Vytvoření šablony proběhlo v pořádku',
              description: 'Název: ' + res.data.template_name
            });
            localStorage.removeItem('investigationTemplatePageStore');
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Vytvoření šablony selhalo', description: res.error });
          } else {
            this.showInvestigationTemplateModal = false;
          }

          this.loading = false;
          return false;
        });
    },

    async updateInvestigationTemplate() {
      if (
        !this.modalOptions?.updateData ||
        this.modalOptions?.baseData?.investigation_template_id === undefined
      ) {
        notification.error({ message: 'Není co upravovat' });
        return;
      }

      this.loading = true;

      const data = {
        template_name: this.modalOptions.updateData.template_name ?? '',
        status: this.modalOptions.updateData.status ?? InvestigationTemplateStatus.ACTIVE,
        impact_on_quality: this.modalOptions.updateData.impact_on_quality ?? false,
        impact_on_yield: this.modalOptions.updateData.impact_on_yield ?? false,
        problem_description: this.modalOptions.updateData.problem_description ?? '',
        investigation_in_production: this.modalOptions.updateData.investigation_in_production ?? '',
        investigation_in_laboratory: this.modalOptions.updateData.investigation_in_laboratory ?? '',
        chemicals: this.modalOptions.updateData.chemicals.map((chemical) => ({
          name: chemical.name ?? '',
          density: chemical.density ?? 0,
          molar_mass: chemical.molar_mass ?? 0,
          notes: chemical.notes ?? '',
          grams: chemical.grams ?? 0,
          moles: chemical.moles ?? 0,
          equivalent: chemical.equivalent ?? 0,
          volume_ml: chemical.volume_ml ?? 0,
          concentration: chemical.concentration ?? 0,
          status: ChemicalStatus.ACTIVE,
          type: chemical.type ?? ChemicalType.CHEMICAL
        }))
      };

      return fetchWrapper
        .put(
          `${import.meta.env.VITE_API_URL}/investigation-template/${this.modalOptions.baseData.investigation_template_id}`,
          data
        )
        .then((res: BaseResponseI<InvestigationTemplateDto>) => {
          this.loading = false;

          if (res.status_code === 200) {
            this.showInvestigationTemplateModal = false;

            notification.success({
              message: 'Úprava šablony proběhla v pořádku',
              description: 'Název: ' + res.data.template_name
            });

            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Úprava šablony selhala', description: res.error });
          } else {
            this.showInvestigationTemplateModal = false;
          }

          return false;
        });
    },

    async addFileToInvestigationTemplate(file_id: number) {
      if (!this.investigationTemplate) {
        notification.error({ message: 'Není vybran šablona' });
        return false;
      }

      return fetchWrapper
        .post(
          `${import.meta.env.VITE_API_URL}/investigation-template/${this.investigationTemplate.investigation_template_id}/file/${file_id}`
        )
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Soubor byl přidán k šabloně' });
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Přidání souboru k šabloně selhalo',
              description: res.error
            });
          } else {
            notification.error({ message: 'Přidání souboru k šabloně selhalo' });
          }

          return false;
        });
    },

    async deleteFileFromInvestigationTemplate(file_id: number) {
      if (!this.investigationTemplate) {
        notification.error({ message: 'Není vybrána šablona' });
        return false;
      }

      return fetchWrapper
        .delete(
          `${import.meta.env.VITE_API_URL}/investigation-template/${this.investigationTemplate.investigation_template_id}/file/${file_id}`
        )
        .then((res: BaseResponseI<InvestigationTemplateDto>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Soubor byl odebrán ze šablony' });
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Odebrání souboru ze šablony selhalo',
              description: res.error
            });
          } else {
            notification.error({ message: 'Odebrání souboru ze šablony selhalo' });
          }

          return false;
        });
    },

    resetModal() {
      this.showInvestigationTemplateModal = false;
      if (this.modalOptions) this.modalOptions.baseData = undefined;
      if (this.modalOptions) this.modalOptions.newData = undefined;
    }
  }
});
