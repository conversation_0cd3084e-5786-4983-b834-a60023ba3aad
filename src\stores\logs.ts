import BaseConstructor from '@/utils/BaseConstructor';
import { stringifyServerOptions, type BasePaginatorResponseI } from '@/utils/axios';
import { fetchWrapper } from '@/utils/helpers/fetch-wrapper';
import { notification } from 'ant-design-vue';
import { defineStore } from 'pinia';
import { User, type UserDto } from './auth';
import type { PaginatorRequestDataI } from './projects';
import { useUsersStore } from './users';
import { columns } from '@/views/pages/dashboard/projects/project/chemicals/components/shared.ts';

const baseUrl = `${import.meta.env.VITE_API_URL}/logs/`;

export interface LogDto {
  log_id: number;
  timestamp: string;
  table_name: string;
  user_id: number;
  user: UserDto;
  table_primary_key: number;
  old_data: Record<string, unknown>;
  new_data: Record<string, unknown>;
}

export interface LogI {
  log_id: number;
  timestamp: Date;
  table_name: string;
  user?: User | undefined;
  user_id: number;
  table_primary_key: number;
  old_data: Record<string, unknown>;
  new_data: Record<string, unknown>;
}

export interface ChangelogI {
  logName: string;
  description: string;
  changes: ChangelogItemI[];
}

export enum ChangelogState {
  EDIT = 'EDIT',
  DELETE = 'DELETE',
  CREATE = 'CREATE'
}

export interface ChangelogItemI {
  data: string;
  state: ChangelogState;
}

export class Log extends BaseConstructor<LogI>() implements LogI {
  constructor(data: LogDto) {
    super(data as unknown as LogI);

    this.user = data.user ? new User(data.user) : undefined;
    this.timestamp = new Date(data.timestamp);
  }

  async init() {
    if (!this.user) {
      const usersStore = useUsersStore();
      this.user = await usersStore.getUser(this.user_id);
    }
  }
}

export interface LogsPaginatorRequestDataI<T = unknown> extends PaginatorRequestDataI<T> {
  filterOptions: Array<{
    column: string;
    value: string | number | (string | number)[] | Array<{
      table_name: (string | number)[];
      table_primary_key: (string | number)[]
    } | object>;
  }>;
}
interface ProjectsStateI {
  loading: boolean;
  logs: Log[];
  showDrawer: boolean;
  options: LogsPaginatorRequestDataI<Log>;
}

export const useLogsStore = defineStore({
  id: 'logs',
  state: () =>
    ({
      loading: false,
      logs: [],
      showDrawer: false,
      options: {
        loading: false,
        results: [],

        search: undefined,
        search_type: 'AND',

        search_columns: [],
        totalItems: 0,
        options: {
          page: 1,
          rowsPerPage: 25,
          sortBy: ['log_id'],
          sortType: ['desc']
        },
        filterOptions: []
      }
    }) as ProjectsStateI,
  actions: {
    setMultiColumnsAsActiveFilter(
      data: Array<{ table_name: (string | number)[]; table_primary_key: (string | number)[] } | object>
    ) {
      if (!data) return;

      this.options.filterOptions = [
        {column: 'multi_columns', value: data}
      ];
    },
    setProjectTableAsActiveFilter(
      project_id: number | number[] | null = null,
      add_to_existing_filter: boolean = false
    ) {
      this.setOrAddFilterParam('project', project_id, add_to_existing_filter);
    },

    setProjectChemicalAsActiveFilter(
      project_chemical_id: number | number[] | null = null,
      add_to_existing_filter: boolean = false
    ) {
      this.setOrAddFilterParam('project_chemical', project_chemical_id, add_to_existing_filter);
    },

    setProjectExperimentTemplateAsActiveFilter(
      experiment_template_id: number | number[] | null = null,
      add_to_existing_filter: boolean = false
    ) {
      this.setOrAddFilterParam(
        'experiment_template',
        experiment_template_id,
        add_to_existing_filter
      );
    },

    setProjectDepartmentAsActiveFilter(
      project_department_id: number | number[] | null = null,
      add_to_existing_filter: boolean = false
    ) {
      this.setOrAddFilterParam('project_department', project_department_id, add_to_existing_filter);
    },

    setChapterTableAsActiveFilter(
      chapter_id: number | number[] | null = null,
      add_to_existing_filter: boolean = false
    ) {
      this.setOrAddFilterParam('chapter', chapter_id, add_to_existing_filter);
    },

    setExperimentTableAsActiveFilter(
      experiment_id: number | number[] | null = null,
      add_to_existing_filter: boolean = false
    ) {
      this.setOrAddFilterParam('experiment', experiment_id, add_to_existing_filter);
    },
    setFormStatusAsActiveFilter(
      form_status_id: number | number[] | null = null,
      add_to_existing_filter: boolean = false
    ) {
      this.setOrAddFilterParam('form', form_status_id, add_to_existing_filter);
    },

    setProjectAttemptTemplateAsActiveFilter(
      attempt_template_id: number | number[] | null = null,
      add_to_existing_filter: boolean = false
    ) {
      this.setOrAddFilterParam('attempt_template', attempt_template_id, add_to_existing_filter);
    },

    setProjectInvestigationTemplateAsActiveFilter(
      investigation_template_id: number | number[] | null = null,
      add_to_existing_filter: boolean = false
    ) {
      this.setOrAddFilterParam(
        'investigation_template',
        investigation_template_id,
        add_to_existing_filter
      );
    },
    setSampleTableAsActiveFilter(
      sample_id: number | number[] | null = null,
      add_to_existing_filter: boolean = false
    ) {
      this.setOrAddFilterParam('sample', sample_id, add_to_existing_filter);
    },

    setFileTableAsActiveFilter(
      file_id: number | number[] | null = null,
      add_to_existing_filter: boolean = false
    ) {
      this.setOrAddFilterParam('file', file_id, add_to_existing_filter);
    },

    setInvestigationTableAsActiveFilter(
      investigation_id: number | number[] | null = null,
      add_to_existing_filter: boolean = false
    ) {
      this.setOrAddFilterParam('investigation', investigation_id, add_to_existing_filter);
    },

    setAttemptTableAsActiveFilter(
      attempt_id: number | number[] | null = null,
      add_to_existing_filter: boolean = false
    ) {
      this.setOrAddFilterParam('attempt', attempt_id, add_to_existing_filter);
    },

    setMessageTableAsActiveFilter(
      message_id: number | number[] | null = null,
      add_to_existing_filter: boolean = false
    ) {
      this.setOrAddFilterParam('message', message_id, add_to_existing_filter);
    },

    setOrAddFilterParam(
      table_name: string,
      data: number | number[] | null = null,
      add_to_existing_filter: boolean = false
    ) {
      if (!data) return;
      if (!this.options.filterOptions) {
        this.options.filterOptions = [];
      }

      const updateFilterOptions = (
        column: string,
        newValue: string | number | (string | number)[] | Array<any>
      ) => {
        const existingOption = this.options.filterOptions.find(
          (option) => option.column === column
        );
        if (existingOption) {
          existingOption.value = newValue;
        } else {
          this.options.filterOptions.push({ column, value: newValue });
        }
      };

      if (add_to_existing_filter) {
        const table_name_option = this.options.filterOptions.find(
          (option) => option.column === 'table_name'
        );
        const table_primary_key_option = this.options.filterOptions.find(
          (option) => option.column === 'table_primary_key'
        );

        const newTableNameValue = table_name_option
          ? this.getExpandedArray(table_name_option.value).includes(table_name)
            ? table_name_option.value
            : [...this.getExpandedArray(table_name_option.value), table_name]
          : table_name;

        updateFilterOptions('table_name', newTableNameValue);

        const existingPrimaryKeys = table_primary_key_option
          ? this.getExpandedArray(table_primary_key_option.value)
          : [];
        const newPrimaryKeys = [
          ...new Set([...existingPrimaryKeys, ...this.getExpandedArray(data)])
        ];

        updateFilterOptions('table_primary_key', newPrimaryKeys);
      } else {
        this.options.filterOptions = [
          { column: 'table_name', value: table_name },
          { column: 'table_primary_key', value: data }
        ];
      }
    },

    getExpandedArray(
      data: string | number | (string | number)[] | Array<any>
    ): (number | string)[] {
      return typeof data === 'string' || typeof data === 'number' ? [data] : data;
    },

    async getLogs() {
      this.loading = true;

      const URL =
        baseUrl +
        '?' +
        stringifyServerOptions(
          this.options.options,
          this.options.search ?? null,
          this.options.search_columns,
          this.options.filterOptions
        );
      return fetchWrapper
        .get(URL)
        .then(async (res: BasePaginatorResponseI<LogDto>) => {
          if (res.status_code === 200) {
            const logs: Log[] = [];
            await Promise.all(
              res.data.items.map(async (log) => {
                const l = new Log(log);
                await l.init();

                logs.push(l);
              })
            );

            this.options.totalItems = res.data.total_items;
            this.logs = logs;
            this.options.results = this.logs;
          }

          this.loading = false;
        })
        .catch((res) => {
          this.loading = false;
          if (res.status_code === 400) {
            notification.error({ message: 'Načtení logů selhalo', description: res.error });
          }
        });
    }
  }
});
