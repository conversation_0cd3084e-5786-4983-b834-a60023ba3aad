<template>
  <v-card
    variant="outlined"
    :color="
      isParentClosed ? 'error' : method.isClosed ? 'warning' : method.isActive ? 'primary' : 'error'
    "
    :class="'card-hover-border bg-containerBg'"
    link
    :subtitle="`Stav: ${method.status}`"
    :title="formattedTitle"
    :disabled="!hasEditPermission && !hasViewPermission"
    @click.prevent="openMethodPage(method.method_id)"
  >
    <template #prepend>
      <FileMarkdownOutlined :style="{ fontSize: '28px' }" />
    </template>
    <div v-if="!isTabletOrSmaller">
      <div v-if="method.technique.parent_technique_id" class="absolute-center">
        <p class="v-card-title">{{ method.technique.name }}</p>
      </div>
    </div>
    <template #append>
      <v-menu>
        <template #activator="{ props }">
          <v-btn
            size="x-small"
            v-bind="props"
            variant="text"
            style="height: auto"
            @click.prevent.stop="props.isActive = true"
          >
            <EllipsisOutlined :style="{ fontSize: '28px' }" />
          </v-btn>
        </template>

        <v-list elevation="24" density="compact" class="py-0">
          <v-list-item
            v-if="method.isActive && !isParentClosed"
            :value="method.method_id + '_close'"
          >
            <v-list-item-title
              @click.prevent="handleClick(() => closeMethod(method.method_id), 'close')"
            >
              Uzavřít metodu
            </v-list-item-title>
          </v-list-item>

          <v-list-item
            v-if="!method.isActive && !isParentClosed"
            :value="method.method_id + '_reopen'"
          >
            <v-list-item-title
              @click.prevent="handleClick(() => reopenMethod(method.method_id), 'reopen')"
            >
              Otevřít metodu
            </v-list-item-title>
          </v-list-item>
          <!-- isParentClosed
          <v-list-item :value="method.method_id + '_update_method'">
            <v-list-item-title @click="openMethodPage(method.method_id)">Upravit</v-list-item-title>
          </v-list-item>

          <v-list-item :value="method.method_id + '_download'" v-if="!isReadOnly">
            <v-list-item-title @click="deleteMethod(method.method_id)" >Archivovat</v-list-item-title>
          </v-list-item>

          <v-list-item :value="method.method_id + '_download'">
            <v-list-item-title @click="reopenMethod(method.method_id)">Otevřít</v-list-item-title>
          </v-list-item>
-->
        </v-list>
      </v-menu>
    </template>
    <ConfirmDlg ref="ConfirmRef" />
  </v-card>
</template>
<script lang="ts" setup>
  import { EllipsisOutlined, FileMarkdownOutlined } from '@ant-design/icons-vue';
  import { useMethodsStore, type Method } from '../methods';
  import { useRoute, useRouter } from 'vue-router';
  import { useAnalyticalDepartmentStore } from '@/stores/analyticalDepartment';
  import { useProjectsStore } from '@/stores/projects';
  import { TechniqueType, useTechniquesStore, type Technique } from '@/stores/techniques';
  import { notification } from 'ant-design-vue';
  import { storeToRefs } from 'pinia';
  import { computed, ref, onMounted } from 'vue';
  import { isAllowed } from '@/utils/directive/isAllowed';
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';

  const ConfirmRef = ref<InstanceType<typeof ConfirmDlg> | null>(null);
  const route = useRoute();
  const router = useRouter();
  const baseDataLoaded = ref(false);
  const methodsStore = useMethodsStore();
  const projectsStore = useProjectsStore();
  const techniquesStore = useTechniquesStore();
  const analyticalDepartmentStore = useAnalyticalDepartmentStore();
  const technique = ref<Technique | undefined>(undefined);
  const methods = ref<Method[] | undefined>([]);
  const { project, department, chapter, project_permision } = storeToRefs(projectsStore);
  const emits = defineEmits(['reload']);
  const props = defineProps<{
    method: Method;
    isParentClosed: boolean;
  }>();

  const openMethodPage = async (methodId: number) => {
    await loadExecute(methodId);
  };

  const isTabletOrSmaller = ref(false);

  const updateIsTabletOrSmaller = () => {
    isTabletOrSmaller.value = window.matchMedia('(max-width: 768px)').matches;
  };

  onMounted(async () => {
    updateIsTabletOrSmaller();
    updateLenght();
    await getMissingPermissions();
  });
  const chapterSearch = ref<string | undefined>(undefined);

  const project_id = computed(() => route.params.project_id as string);
  const projectId = computed(() => parseInt(project_id.value));

  const project_department_id = computed(() => route.params.project_department_id as string);
  const projectDepartmentId = computed(() => parseInt(project_department_id.value));

  const chapter_id = computed(() => route.params.chapter_id as string);
  const chapterId = computed(() => parseInt(chapter_id.value));

  const analytical_technique_id = computed(() => route.params.analytical_technique_id as string);
  const analyticalTechniqueUd = computed(() => parseInt(analytical_technique_id.value));

  const loadExecute = async (method_id: number) => {
    baseDataLoaded.value = false;
    chapterSearch.value = undefined;

    if (projectId.value) {
      if (project.value) {
        if (project.value.project_id !== projectId.value) {
          await projectsStore.getProjectById(projectId.value);
        }
      } else {
        await projectsStore.getProjectById(projectId.value);
      }
    }

    if (projectDepartmentId.value) {
      if (department.value) {
        if (department.value.project_department_id !== projectDepartmentId.value) {
          await projectsStore.getDepartmentById(projectDepartmentId.value);
        }
      } else {
        await projectsStore.getDepartmentById(projectDepartmentId.value);
      }
    }

    if (chapterId.value) {
      await projectsStore.getChapterById(chapterId.value);
    }

    if (analyticalTechniqueUd.value) {
      analyticalDepartmentStore.project_id = projectId.value;
      technique.value = await techniquesStore.getTechnique(analyticalTechniqueUd.value);
      methods.value = await analyticalDepartmentStore.getProjectMethodDevelopmentTechniqueDetail(
        analyticalTechniqueUd.value
      );
    }
    if (
      technique.value &&
      technique.value.type === TechniqueType.INTERNAL &&
      department.value &&
      project.value &&
      chapter.value &&
      project.value.departments.find(
        (d) =>
          department.value && d.project_department_id === department.value.project_department_id
      ) &&
      department.value.chapters.find(
        (c) => chapter.value && c.chapter_id === chapter.value.chapter_id
      ) &&
      analyticalTechniqueUd.value
    ) {
      baseDataLoaded.value = true;
      router.push({ name: 'ListOfMethodForTechniqueUpdate', params: { method_id: method_id } });
    } else {
      notification.error({
        message: 'Chyba',
        description: 'Nepodařilo se načíst data nebo nebyla vybrána metoda'
      });
      if (chapter.value) {
        router.push({
          name: 'ChapterDetail',
          params: {
            project_id: project.value?.project_id.toString(),
            project_department_id: department.value?.project_department_id.toString(),
            chapter_id: chapter.value?.chapter_id.toString()
          }
        });
      } else if (project.value && department.value) {
        router.push({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value.project_id.toString(),
            project_department_id: department.value.project_department_id.toString()
          }
        });
      } else if (project.value) {
        router.push({
          name: 'ProjectDetail',
          params: { project_id: project.value.project_id.toString() }
        });
      } else {
        router.push({ name: 'ListOfProjects' });
      }
    }
  };
  const reopenMethod = async (methodId: number) => {
    const res = await methodsStore.reOpenMethod(methodId);
    if (res) {
      emits('reload');
    }
  };

  const closeMethod = async (method_id: number) => {
    if (
      await ConfirmRef.value?.open('Opravdu chcete uzavřít metodu?', '', {
        color: 'error',
        notclosable: true,
        zIndex: 2400
      })
    ) {
      const res = await methodsStore.closeMethod(method_id, false);
      if (res) emits('reload');
    }
  };

  const checkAdminPermission = () => {
    return isAllowed(['edit_all']);
  };

  const handleClick = (action: () => void, actionType: 'close' | 'reopen') => {
    const dynamicPermission = actionType === 'close' ? 'close_method' : 'activate_method';
    if (checkAdminPermission()) {
      action();
      return;
    } else if (!project_permision.value) {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nejste přiřazený k danému projektu.'
      });
      return;
    } else if (!isAllowed([dynamicPermission])) {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nemáte oprávnění pro tuto akci.'
      });
      return;
    } else if (checkProjectTypePermisionsForEdit()) {
      action();
    }
  };

  defineExpose({
    closeMethod,
    reopenMethod
  });

  const havePermision = ref<boolean>(true);
  const missingPermison = ref<string>();
  const checkProjectTypePermisionsForEdit = () => {
    if (isAllowed(['edit_analytical_department']) || isAllowed(['edit_all'])) {
      havePermision.value = true;
      return true;
    } else {
      missingPermison.value = 'edit_analytical_department';
      havePermision.value = false;
    }

    if (!havePermision.value) {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nemáte oprávnění pro editaci metody: ' + missingPermison.value + '.'
      });
    }
    return false;
  };

  const hasEditPermission = ref(false);
  const hasViewPermission = ref(false);

  const getMissingPermissions = async () => {
    const requiredPermissions = ['view_analytical_department', 'edit_analytical_department'];
    const missingPermissions = requiredPermissions.filter((permission) => !isAllowed([permission]));
    hasEditPermission.value = !missingPermissions.includes('edit_analytical_department');
    hasViewPermission.value = !missingPermissions.includes('view_analytical_department');
    return missingPermissions;
  };

  const maxLegth = ref(40);

  const formattedTitle = computed(() => {
    if (!props.method.method_name) return '';
    return props.method.method_name.length > maxLegth.value
      ? `${props.method.method_name.slice(0, maxLegth.value)}...`
      : props.method.method_name;
  });

  const updateLenght = () => {
    if (window.innerWidth < 1465) {
      maxLegth.value = 20;
    } else {
      maxLegth.value = 40;
    }
  };
</script>
<style scoped>
  .absolute-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }
  .absolute-center-2 {
    position: absolute;
    top: 75%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }
</style>
