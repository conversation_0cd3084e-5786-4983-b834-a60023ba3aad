<template>
  <section class="my-5">
    <span class="text-h4">Metody</span>
  </section>
  <v-row v-if="methods.length > 0">
    <v-col
      v-for="method in methods
        .filter((method) =>
          methodSearch
            ? method.method_name
                .toUpperCase()
                .includes(methodSearch.toUpperCase().replace(/\s+/g, ''))
            : true
        )
        .sort((a, b) => {
          if (a.status === 'active' && b.status === 'closed') return -1;
          if (a.status === 'closed' && b.status === 'active') return 1;
          return a.created_at > b.created_at ? -1 : 1;
        })"
      :key="method.method_id"
      cols="12"
    >
      <MethodItem :method="method" :is-parent-closed="isParentClosed" @reload="$emit('reload')" />
    </v-col>
  </v-row>
  <NotFoundItem v-else>
    <template #notFound>Nebyly nalezeny žádné metody</template>
  </NotFoundItem>
</template>

<script lang="ts" setup>
  import NotFoundItem from '@/components/shared/NotFoundItem.vue';
  import type { Method } from '../methods';
  import MethodItem from './MethodItem.vue';

  defineEmits(['reload']);
  defineProps<{
    methods: Method[];
    methodSearch: string | undefined;
    isParentClosed: boolean;
  }>();
</script>
