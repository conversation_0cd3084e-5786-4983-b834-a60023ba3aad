<template>
  <v-row v-for="(field, index) in parametersState" :key="index">
    <v-col cols="5">
      <v-text-field
        v-model="field.parameter"
        :rules="field.value && field.value.length > 0 ? itemRequiredRule : undefined"
        single-line
        label="Parameter"
        hide-details="auto"
        variant="outlined"
        rounded="sm"
      ></v-text-field>
    </v-col>
    <v-col cols="5">
      <v-text-field
        v-model="field.value"
        :rules="field.parameter && field.parameter.length > 0 ? itemRequiredRule : undefined"
        single-line
        label="Hodnota"
        hide-details="auto"
        variant="outlined"
        rounded="sm"
      ></v-text-field>
    </v-col>
    <v-col cols="2">
      <div class="d-flex justify-end align-center gap-2 h-100">
        <v-btn
          variant="flat"
          color="error"
          density="compact"
          icon="mdi-close"
          @click="parametersState.splice(index, 1)"
        />
      </div>
    </v-col>
  </v-row>
  <v-row>
    <v-col cols="12">
      <v-btn @click="addField">+ Parameter a Metoda</v-btn>
    </v-col>
  </v-row>
</template>

<script setup lang="ts">
  import { itemRequiredRule } from '@/utils/formValidation';
  import { useVModel } from '@vueuse/core';
  import { onMounted } from 'vue';

  export interface Field {
    parameter: string | undefined;
    value: string | undefined;
  }

  const props = defineProps<{
    modelValue: Field[];
  }>();

  const parametersState = useVModel(props, 'modelValue');

  onMounted(() => {
    if (parametersState.value.length === 0) {
      addField();
      addField();
    }
  });

  const addField = () => {
    parametersState.value.push({ parameter: undefined, value: undefined });
  };
</script>
