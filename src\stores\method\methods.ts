import BaseConstructor from '@/utils/BaseConstructor';
import {
  loadParamsFailedReloadNotification,
  reloadWithoutParams,
  revertServerOptionsFromLocation,
  stringifyServerOptions,
  type BasePaginatorResponseI,
  type BaseResponseI
} from '@/utils/axios';
import { fetchWrapper } from '@/utils/helpers/fetch-wrapper';
import { notification } from 'ant-design-vue';
import { defineStore } from 'pinia';
import type { ServerOptions } from 'vue3-easy-data-table';
import type { GetAllOptions } from '../projects';
import type { Field } from './components/ParametersFormTable.vue';
import { SampleParameters, type SampleParametersDto } from '../sample/samples';
import { Tag, type TagDto } from '../tags';
import { File, useFilesStore, type FileDto } from '../files';
import { Technique, type TechniqueDto } from '../techniques';
import { th } from 'vuetify/locale';
import { Instrument, type InstrumentDto } from '../instruments';
import { Column, type ColumnDto } from '../columns';
import { router } from '@/router';
export enum MethodChapterTypes {
  VYVOJ_METODY = 'Vyvoj metody'
}

export enum MethodStatus {
  ACTIVE = 'active',
  CLOSED = 'closed',
  DELETED = 'deleted'
}
export enum MethodSaveType {
  NEWBRANCH = 'save_into_new_branch',
  NEWMETHOD = 'save_as_new_method',
  CLOSEMETHOD = 'save_and_close_method',
  SPECBRANCH = 'save_into_spec_branch',
  NEWBRANCHMAIN = 'save_into_main_branch'
}
export enum ProjectStatus {
  ACTIVE = 'active',
  CLOSED = 'inactive',
  DELETED = 'deleted'
}
export interface BranchDto {
  branch_id: number;
  name: string;
  created_at: string;
  updated_at: string;
}
export interface BranchI {
  branch_id: number;
  name: string;
  created_at: Date;
  updated_at: Date;
}

export interface SimpleVersionChange {
  branch_name: string;
  description: string;
}

export class Branch extends BaseConstructor<BranchI>() implements BranchI {
  constructor(data: BranchDto) {
    super(data as unknown as BranchI);
    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);
  }
}

export interface ProjectDto {
  project_id: number;
  user_id: number;
  name: string;
  status: string;
  created_at: string;
  updated_at: string;
}
export interface ProjectI {
  project_id: number;
  user_id: number;
  name: string;
  status: ProjectStatus;
  created_at: Date;
  updated_at: Date;
}

export class Project extends BaseConstructor<ProjectI>() implements ProjectI {
  constructor(data: ProjectDto) {
    super(data as unknown as ProjectI);
    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);
  }
}

export interface CurentVersionDto {
  version_id: number;
  method_id: number;
  parent_version_id: number | null;
  is_default_for_prefill?: boolean;
  branch_id: number;
  description: string;
  data: MethodGetDto;
  created_at: string;
  parent_version?: CurentVersionDto | null;
  child_versions?: CurentVersionDto[] | null;
  branch: BranchDto;
}

export interface CurentVersionI {
  version_id: number;
  method_id: number;
  parent_version_id: number | null;
  is_default_for_prefill?: boolean;
  branch_id: number;
  description: string;
  data: MethodGetI;
  created_at: Date;
  parent_version?: CurentVersion | null;
  child_versions?: CurentVersion[] | null;
  branch: BranchI;
}

export class CurentVersion extends BaseConstructor<CurentVersionI>() implements CurentVersionI {
  constructor(data: CurentVersionDto) {
    super(data as unknown as CurentVersionI);
    this.created_at = new Date(data.created_at);
    this.data = new MethodGet(data.data);
    if (data.parent_version) {
      this.parent_version = new CurentVersion(data.parent_version);
    }
    if (data.child_versions) {
      this.child_versions = data.child_versions.map((version) => new CurentVersion(version));
    }
    this.branch = new Branch(data.branch);
  }
}

export interface MethodGetDto {
  method_id: number;
  project_id: number;
  technique_id: number;
  instrument_id: number;
  kolona_id: number | null;
  method_name: string;
  sequence_name: string;
  preparation_of_standard_and_sample: string;
  note: string;
  result: string;
  status: string;
  created_at: string;
  updated_at: string;
  parameters?: SampleParametersDto[];
  tags?: TagDto[];
  files: FileDto[];
  project: ProjectDto;
  technique: TechniqueDto;
  instrument: InstrumentDto;
  kolona: ColumnDto;
  current_version: CurentVersionDto;
  gradient: string;
}

export interface MethodGetI {
  method_id: number;
  project_id: number;
  technique_id: number;
  instrument_id: number;
  kolona_id: number | null;
  method_name: string;
  sequence_name: string;
  preparation_of_standard_and_sample: string;
  note: string;
  result: string;
  status: string;
  created_at: Date;
  updated_at: Date;
  parameters?: SampleParameters[];
  tags?: Tag[];
  files: File[];
  project: Project;
  technique: Technique;
  instrument: Instrument;
  kolona: Column;
  current_version: CurentVersion;
  gradient: string;
}

export class MethodGet extends BaseConstructor<MethodGetI>() implements MethodGetI {
  constructor(data: MethodGetDto) {
    super(data as unknown as MethodGetI);
    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);
    if (data.parameters) {
      this.parameters = data.parameters?.map((param) => new SampleParameters(param)) ?? [];
    }
    if (data.tags) {
      this.tags = data.tags?.map((tag) => new Tag(tag)) ?? [];
    }
    if (data.files) {
      this.files = data.files?.map((file) => new File(file)) ?? [];
    }
    if (data.project) {
      this.project = new Project(data.project);
    }
    if (data.technique) {
      this.technique = new Technique(data.technique);
    }
    if (data.instrument) {
      this.instrument = new Instrument(data.instrument);
    }
    if (data.kolona) {
      this.kolona = new Column(data.kolona);
    }
    if (data.current_version) {
      this.current_version = new CurentVersion(data.current_version);
    }
  }
  get isActive() {
    return this.status === MethodStatus.ACTIVE;
  }
  get isClosed() {
    return this.status === MethodStatus.CLOSED;
  }
}

export interface MethodUpdateDto {
  method_name: string;
  instrument_id: number | null;
  kolona_id: number | null;
  sequence_name: string;
  preparation_of_standard_and_sample: string;
  note: string;
  result: string;
  status: string;
  parameters: [] | null;
  description_of_the_change: string;
  save_type: MethodSaveType.NEWBRANCH;
  files_ids: number[];
  save_branch_id: number | null;
  new_branch_or_method_name: string | null;
  gradient: string;
}

export interface MethodDto {
  method_id: number;
  project_id: number;
  branch_id: number;
  technique_id: number;
  instrument_id: number | null;
  kolona_id: number | null;
  save_branch_id: number;
  new_branch_or_method_name: string;
  save_type: string;
  method_name: string;
  sequence_name: string;
  preparation_of_standard_and_sample: string;
  note: string;
  result: string;
  status: string;
  created_at: string;
  updated_at: string;
  parameters?: Array<SampleParametersDto>;
  tags?: TagDto[];
  histories?: MethodDto[] | any;
  history_id?: number;
  description_of_the_change?: string;
  files?: FileDto[];
  technique: TechniqueDto;
  branch: BranchDto;
  project: ProjectDto;
  instrument?: InstrumentDto | null;
  kolona?: ColumnDto | null;
  gradient: string;
}
export interface MethodI {
  method_id: number;
  project_id: number;
  branch_id: number;
  technique_id: number;
  instrument_id: number | null;
  kolona_id: number | null;
  description_of_the_change: string;
  new_branch_or_method_name: string;
  save_branch_id: number;
  save_type: MethodSaveType;
  method_name: string;
  sequence_name: string;
  preparation_of_standard_and_sample: string;
  note: string;
  result: string;
  status: MethodStatus;
  created_at: Date;
  updated_at: Date;
  parameters?: Array<SampleParameters>;
  tags?: Tag[];
  histories: Method[] | any;
  history_id?: number;
  files?: File[];
  technique: Technique;
  branch: Branch;
  project: Project;
  instrument?: Instrument | null;
  kolona?: Column | null;
  gradient: string;
}

export class Method extends BaseConstructor<MethodI>() implements MethodI {
  constructor(data: MethodDto) {
    super(data as unknown as MethodI);
    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    if (data.parameters) {
      this.parameters = data.parameters.map((param) => new SampleParameters(param));
    }
    if (data.files) {
      this.files = data.files.map((file) => new File(file));
    }
    if (data.tags) {
      this.tags = data.tags.map((tag) => new Tag(tag));
    }
    if (data.histories) {
      this.histories = data.histories?.map((history: MethodDto) => new Method(history)) ?? [];
    }
    if (data.project) {
      this.project = new Project(data.project);
    }
    if (data.branch) {
      this.branch = new Branch(data.branch);
    }
    if (data.technique) {
      this.technique = new Technique(data.technique);
    }
    if (data.instrument) {
      this.instrument = new Instrument(data.instrument);
    }
    if (data.kolona) {
      this.kolona = new Column(data.kolona);
    }
  }

  get isActive() {
    return this.status === MethodStatus.ACTIVE;
  }
  get isClosed() {
    return this.status === MethodStatus.CLOSED;
  }
}

export interface SimpleMethodDto {
  department_id: number;
  chapter_id: number;
  created_at: string;
  gradient: string;
  instrument_id: number | null;
  kolona_id: number | null;
  method_id: number;
  method_name: string;
  note: string;
  preparation_of_standard_and_sample: string;
  project_id: number;
  result: string;
  sequence_name: string;
  status: string;
  technique_id: number;
  updated_at: string;
  files?: FileDto[];
  parameters?: SampleParametersDto[];
  tags?: TagDto[];
  technique?: TechniqueDto;
  project?: {
    project_id: number;
    name: string;
    status: string;
    created_at: string;
    updated_at: string;
    user_id: number;
  };
}
export interface SimpleMethodI {
  department_id: number;
  chapter_id: number;
  created_at: Date;
  gradient: string;
  instrument_id: number | null;
  kolona_id: number | null;
  method_id: number;
  method_name: string;
  note: string;
  preparation_of_standard_and_sample: string;
  project_id: number;
  result: string;
  sequence_name: string;
  status: MethodStatus;
  technique_id: number;
  updated_at: Date;
  files?: File[];
  parameters?: SampleParameters[];
  tags?: Tag[];
  technique?: Technique;
  project?: {
    project_id: number;
    name: string;
    status: string;
    created_at: Date;
    updated_at: Date;
    user_id: number;
  };
}

export class SimpleMethod extends BaseConstructor<SimpleMethodI>() implements SimpleMethodI {
  constructor(data: SimpleMethodDto) {
    super(data as unknown as SimpleMethodI);
    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);
    if (data.files) {
      this.files = data.files.map((file) => new File(file));
    }
    if (data.parameters) {
      this.parameters = data.parameters.map((param) => new SampleParameters(param));
    }
    if (data.tags) {
      this.tags = data.tags.map((tag) => new Tag(tag));
    }
    if (data.technique) {
      this.technique = new Technique(data.technique);
    }
    if (data.project) {
      this.project = new Project(data.project);
    }
    if (data.project) {
      this.project = {
        project_id: data.project.project_id,
        name: data.project.name,
        status: data.project.status,
        created_at: new Date(data.project.created_at),
        updated_at: new Date(data.project.updated_at),
        user_id: data.project.user_id
      };
    }
  }
}

export interface MethodVersionsDataDto {
  method_id: number;
  project_id: number;
  technique_id: number;
  instrument_id: number | null;
  kolona_id: number | null;
  method_name: string;
  sequence_name: string;
  preparation_of_standard_and_sample: string;
  note: string;
  result: string;
  status: string;
  created_at: string;
  updated_at: string;
  parameters?: Array<SampleParametersDto>;
  tags?: TagDto[];
  files?: FileDto[];
  gradient: string;
}
export interface MethodVersionsDataI {
  method_id: number;
  project_id: number;
  technique_id: number;
  instrument_id: number | null;
  kolona_id: number | null;
  method_name: string;
  sequence_name: string;
  preparation_of_standard_and_sample: string;
  note: string;
  result: string;
  status: MethodStatus;
  created_at: Date;
  updated_at: Date;
  parameters?: Array<SampleParameters>;
  tags?: Tag[];
  files?: File[];
  gradient: string;
}

export class MethodVersionsData
  extends BaseConstructor<MethodVersionsDataI>()
  implements MethodVersionsDataI
{
  constructor(data: MethodVersionsDataDto) {
    super(data as unknown as MethodI);
    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    if (data.parameters) {
      this.parameters = data.parameters.map((param) => new SampleParameters(param));
    }
    if (data.files) {
      this.files = data.files.map((file) => new File(file));
    }
    if (data.tags) {
      this.tags = data.tags.map((tag) => new Tag(tag));
    }
  }
}

export interface MethodVersionsDto {
  version_id: number;
  method_id: number;
  parent_version_id: number;
  branch_id: number;
  description: string;
  data: MethodVersionsDataDto;
  created_at: string;
  parent_version?: MethodVersionsDto | null;
  child_versions?: MethodVersionsDto[] | null;
  branch: BranchDto;
  is_default_for_prefill?: boolean;
  method?: MethodDto;
  gradient?: string | null;
}
export interface MethodVersionsI {
  version_id: number;
  method_id: number;
  parent_version_id: number;
  branch_id: number;
  is_default_for_prefill?: boolean;
  description: string;
  data: MethodVersionsData;
  created_at: Date;
  parent_version?: CurentVersion | null;
  child_versions?: CurentVersion[] | null;
  branch: Branch;
  method?: Method;
  gradient?: string | null;
}

export class MethodVersions extends BaseConstructor<MethodVersionsI>() implements MethodVersionsI {
  constructor(data: MethodVersionsDto) {
    super(data as unknown as MethodVersionsI);

    if (data.branch) {
      this.branch = new Branch(data.branch);
    }
    if (data.data) {
      this.data = new MethodVersionsData(data.data);
    }
    if (data.method) {
      this.method = new Method(data.method);
    }
  }
}

export interface VersionComparisonDto {
  differences: any;
  version_a: MethodVersionsDto;
  version_b: MethodVersionsDto;
}

export interface VersionComparisonI {
  differences: any;
  version_a: MethodVersions;
  version_b: MethodVersions;
}

export class VersionComparison
  extends BaseConstructor<VersionComparisonI>()
  implements VersionComparisonI
{
  constructor(data: VersionComparisonDto) {
    super(data as unknown as VersionComparisonI);
    this.version_a = new MethodVersions(data.version_a);
    this.version_b = new MethodVersions(data.version_b);
  }
}

interface MethodModalOptionsI {
  isEditing: boolean;
  isCreating: boolean;
  baseData: Method | undefined;
  newData: NewMethodModalDataI | undefined;
  updateData: UpdateMethodModalDataI | undefined;
}

interface NewMethodModalDataI {
  project_id?: number | undefined;
  technique_id?: number | undefined;
  method_name?: string | undefined;
  confirm: boolean | false;
}

interface UpdateMethodModalDataI {
  method_name?: string | undefined;
  instrument_id?: number | null | undefined;
  kolona_id?: number | null | undefined;
  sequence_name?: string | undefined;
  preparation_of_standard_and_sample?: string | undefined;
  note?: string | undefined;
  result?: string | undefined;
  status?: MethodStatus | undefined;
  parameters: Field[];
  description_of_the_change?: string | undefined;
  save_type?: MethodSaveType | undefined;
  files_ids: number[];
  save_branch_id?: number | null;
  new_branch_or_method_name?: string | null;
  gradient?: string | undefined;
}

interface MethodsStateI {
  external_methods_last_update: Date | null;
  methods: Method[];
  method: Method | null;
  methodGet: MethodGet | null;
  currentVersion: CurentVersion | null;
  methodGets: MethodGet[];
  methodVersions: MethodVersions[] | null;
  specificVersion: MethodVersions | null;
  loading: boolean;
  loadingMethod: boolean;
  versionA: MethodVersions | undefined;
  versionB: MethodVersions | undefined;
  differences: any;
  showMethodModal: boolean;
  modalOptions: MethodModalOptionsI | undefined;

  totalItems?: number;
  options: ServerOptions;
  search: string | undefined;
  search_type?: 'AND' | 'OR';
  fixedFilterOptions: null | Array<{
    column: string;
    value: null | number | string | string[] | number[] | object | object[];
  }>;
}

export const useMethodsStore = defineStore({
  id: 'methods',
  state: () =>
    ({
      external_methods_last_update: null,
      method: null,
      methods: [],
      methodGet: null,
      methodGets: [],
      currentVersion: null,
      methodVersions: [],
      specificVersion: null,
      loading: false,
      loadingMethod: false,
      versionA: undefined,
      versionB: undefined,
      differences: undefined,
      showMethodModal: false,
      modalOptions: undefined,

      search: undefined,
      search_type: undefined,
      totalItems: undefined,
      options: {
        page: 1,
        rowsPerPage: 25,
        sortBy: [],
        sortType: ['desc', 'asc']
      },
      fixedFilterOptions: null
    }) as MethodsStateI,
  actions: {
    async getMethods(
      setData: boolean = true,
      options: GetAllOptions | undefined = {
        search_columns: ['method_name']
      },
      search_type: 'AND' | 'OR' = 'OR'
    ): Promise<{
      data: MethodGet[];
      totalItems: number;
    }> {
      this.loading = true;

      const URL =
        `${import.meta.env.VITE_API_URL}/methods/` +
        '?' +
        stringifyServerOptions(
          options && options.options ? options.options : this.options,
          options && options.search ? options.search : (this.search ?? null),
          options && options.search_columns ? options.search_columns : [],
          options && options.fixedFilterOptions ? options.fixedFilterOptions : null,
          this.search_type ?? search_type
        );
      return fetchWrapper
        .get(URL)
        .then((res: BasePaginatorResponseI<MethodGetDto>) => {
          if (res.status_code === 200) {
            this.totalItems = res.data.total_items;
            if (setData) {
              this.methodGets = res.data.items.map((method) => new MethodGet(method));
            }
            this.loading = false;
            return {
              data: res.data.items.map((method) => new MethodGet(method)),
              totalItems: res.data.total_items
            };
          }
          this.loading = false;
          return {
            data: [],
            totalItems: 0
          };
        })
        .catch((res) => {
          if (res.status_code === 400) {
            loadParamsFailedReloadNotification('Načtení metod selhalo', res.error, () => {
              const data = reloadWithoutParams();
              this.options = data.options;
              this.search = data.term ?? undefined;
            });
          }

          this.loading = false;
          return {
            data: [],
            totalItems: 0
          };
        });
    },
    async getMethodVersionsForSamples(
      setData: boolean = true,
      options: GetAllOptions | undefined = {
        search_columns: ['method_name']
      },
      search_type: 'AND' | 'OR' = 'OR'
    ) {
      const URL =
        `${import.meta.env.VITE_API_URL}/versions/` +
        '?' +
        stringifyServerOptions(
          options && options.options ? options.options : this.options,
          options && options.search ? options.search : (this.search ?? null),
          options && options.search_columns ? options.search_columns : [],
          options && options.fixedFilterOptions ? options.fixedFilterOptions : null,
          this.search_type ?? search_type
        );
      this.loadingMethod = true;
      return fetchWrapper
        .get(URL)
        .then((res: BasePaginatorResponseI<MethodVersionsDto>) => {
          this.loadingMethod = false;
          if (res.status_code === 200) {
            this.methodVersions = res.data.items.map((version) => new MethodVersions(version));
            return this.methodVersions;
          }
        })
        .catch((res) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Načtení metod selhalo', description: res.error });
          }
          this.loadingMethod = false;
          return false;
        });
    },
    async getMethod(method_id: number) {
      this.loadingMethod = true;
      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/method/${method_id}`)
        .then((res: BaseResponseI<MethodDto>) => {
          this.loadingMethod = false;

          if (res.status_code === 200) {
            return new Method(res.data);
          }
        })
        .catch((res) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Načtení metod selhalo', description: res.error });
          }

          this.loadingMethod = false;
          return undefined;
        });
    },
    async getMethodHistory(method_id: number) {
      this.loadingMethod = true;
      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/method/${method_id}`)
        .then((res: BaseResponseI<MethodGetDto>) => {
          this.loadingMethod = false;

          if (res.status_code === 200) {
            this.methodGet = new MethodGet(res.data);
            this.currentVersion = new CurentVersion(res.data.current_version);
            return new MethodGet(res.data);
          }
        })
        .catch((res) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Načtení metod selhalo', description: res.error });
          }

          this.loadingMethod = false;
          return undefined;
        });
    },

    setParamsFromLocation(): void {
      const data = revertServerOptionsFromLocation();
      this.options = data.options;
      this.search = data.term ?? undefined;
    },

    showNewMethodModal(project_id: number) {
      this.modalOptions = {
        isEditing: false,
        isCreating: true,
        baseData: undefined,
        updateData: undefined,
        newData: {
          project_id: project_id,
          technique_id: undefined,
          method_name: undefined,
          confirm: false
        }
      };

      this.showMethodModal = true;
    },

    createMethod() {
      if (!this.modalOptions?.newData || this.modalOptions?.newData?.confirm === false) {
        notification.error({ message: 'Musíte potvrdit souhlas s vytvořením metody' });
        return;
      }

      if (!this.modalOptions?.newData?.project_id || !this.modalOptions?.newData?.technique_id) {
        notification.error({ message: 'Není vybrán projekt nebo technika' });
        return;
      }

      this.loading = true;

      const data = {
        project_id: this.modalOptions.newData.project_id,
        technique_id: this.modalOptions.newData.technique_id ?? undefined,
        method_name: this.modalOptions.newData.method_name ?? undefined
      };

      return fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/method/`, data)
        .then(async (res: BaseResponseI<MethodDto>) => {
          if (res.status_code === 200) {
            this.showMethodModal = false;

            notification.success({
              message: 'Vytvoření metody proběhlo v pořádku'
            });
            this.getMethods();
            this.loading = false;
            const redirectTo = await this.redirectToMethodDetailLink(res.data.method_id);
            router.push(redirectTo);
            return new Method(res.data);
          }

          this.loading = false;
          return undefined;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Vytvoření metody selhalo', description: res.error });
          } else {
            this.showMethodModal = false;
          }

          this.loading = false;
          return undefined;
        });
    },

    resetModal() {
      this.showMethodModal = false;
      if (this.modalOptions) this.modalOptions.baseData = undefined;
      if (this.modalOptions) this.modalOptions.newData = undefined;
    },

    async deleteMethod(method_id: number) {
      this.loading = true;

      return fetchWrapper
        .delete(`${import.meta.env.VITE_API_URL}/method/${method_id}`)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Metoda byla úspěšně smazána' });
            this.getMethods();
            this.loading = false;
            return true;
          }
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Smazání metody selhalo', description: res.error });
          }
          this.loading = false;
          return false;
        });
    },
    async exportMethodAsPdf(method_id: number, method_name: string | undefined, version_id: number | undefined = undefined) {
      this.loading = true;
      return fetchWrapper
        .blob(
          `${import.meta.env.VITE_API_URL}/method/${method_id}/export` + (version_id ? `?version_id=${version_id}` : '')
        )
        .then((res) => {
          this.loading = false;

          const url = window.URL.createObjectURL(new Blob([res]));
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', `${method_name ?? 'method'}.pdf`);
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          return true;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Formulář nešlo exportovat', description: res.message });
          } else {
            notification.error({ message: 'Formulář nešlo exportovat' });
          }

          return false;
        });
    },
    async exportMethodAsWord(method_id: number, method_name: string | undefined, version_id: number | undefined = undefined) {
      this.loading = true;
      return fetchWrapper
        .blob(
          `${import.meta.env.VITE_API_URL}/method/${method_id}/export_to_word` + (version_id ? `?version_id=${version_id}` : '')
        )
        .then((res) => {
          this.loading = false;

          const url = window.URL.createObjectURL(new Blob([res]));
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', `${method_name ?? 'method'}.docx`);
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          return true;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Formulář nešlo exportovat', description: res.message });
          } else {
            notification.error({ message: 'Formulář nešlo exportovat' });
          }

          return false;
        });
    },
    async reOpenMethod(id: number) {
      if (!id) {
        notification.error({ message: 'Není co otvírat' });
        return false;
      }
      this.loading = true;
      const data = {
        status: 'active',
        brute_force_close: false
      };

      return fetchWrapper
        .patch(`${import.meta.env.VITE_API_URL}/method/${id}/status`, data)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Metoda byla úspěšně otevřena' });
            this.getMethods();
            this.loading = false;

            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Aktivace metody selhala', description: res.error });
          }

          this.loading = false;
          return false;
        });
    },

    closeMethod(id: number, force: boolean) {
      if (!id) {
        notification.error({ message: 'Není co zavírat' });
        return false;
      }
      this.loading = true;
      const data = {
        status: 'closed',
        brute_force_close: force
      };

      return fetchWrapper
        .patch(`${import.meta.env.VITE_API_URL}/method/${id}/status`, data)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Metoda byla úspěšně uzavřena' });
            this.getMethods();
            this.loading = false;
            return true;
          }
          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Zavření metody selhalo', description: res.error });
          }

          this.loading = false;
          return false;
        });
    },
    async showEditModal(id: number) {
      //this.loadingMethod = true;
      const _Method = await this.getMethod(id);
      await this.getMethodHistory(id);
      if (_Method === undefined) {
        notification.error({ message: 'Metoda nebyla nalezena' });
        return;
      }

      this.method = _Method;

      this.modalOptions = {
        isEditing: true,
        isCreating: false,
        baseData: this.method ?? undefined,
        newData: undefined,
        updateData: {
          method_name: this.method?.method_name ?? '',
          instrument_id: this.method?.instrument_id ?? null,
          kolona_id: this.method?.kolona_id ?? null,
          sequence_name: this.method?.sequence_name ?? '',
          preparation_of_standard_and_sample: this.method?.preparation_of_standard_and_sample ?? '',
          note: this.method?.note ?? '',
          result: this.method?.result ?? '',
          status: this.method?.status ?? MethodStatus.ACTIVE,
          description_of_the_change: this.method?.description_of_the_change ?? '',
          save_type: this.method?.save_type ?? MethodSaveType.NEWBRANCHMAIN,
          parameters:
            this.method?.parameters?.map((param) => ({
              parameter: param.parameter,
              value: param.value
            })) ?? [],
          files_ids: _Method.files?.map((file) => file.file_id) ?? [],
          save_branch_id: this.method?.save_branch_id ?? null,
          new_branch_or_method_name: this.method?.new_branch_or_method_name ?? null,
          gradient: this.method?.gradient ?? ''
        }
      };

      // this.showMethodModal = true;
      // this.loadingMethod = false;
    },

    async updateMethod() {
      if (!this.modalOptions?.updateData || !this.modalOptions.baseData?.method_id) {
        notification.error({ message: 'Data pro aktualizaci metody nejsou k dispozici' });
        return false;
      }
      this.loading = true;

      const data = {
        method_name: this.modalOptions.updateData.method_name ?? undefined,
        instrument_id: this.modalOptions.updateData.instrument_id ?? null,
        kolona_id: this.modalOptions.updateData.kolona_id ?? null,
        sequence_name: this.modalOptions.updateData.sequence_name ?? '',
        preparation_of_standard_and_sample:
          this.modalOptions.updateData.preparation_of_standard_and_sample ?? '',
        note: this.modalOptions.updateData.note ?? '',
        result: this.modalOptions.updateData.result ?? '',
        status: this.modalOptions.updateData.status ?? MethodStatus.ACTIVE,
        parameters: this.modalOptions.updateData.parameters ?? [],
        description_of_the_change: this.modalOptions.updateData.description_of_the_change ?? '',
        save_type: this.modalOptions.updateData.save_type ?? MethodSaveType.NEWMETHOD,
        files_ids: this.modalOptions.updateData.files_ids ?? [],
        save_branch_id: this.modalOptions.updateData.save_branch_id ?? null,
        new_branch_or_method_name: this.modalOptions.updateData.new_branch_or_method_name ?? null,
        gradient: this.modalOptions.updateData.gradient ?? ''
      };

      return fetchWrapper
        .put(`${import.meta.env.VITE_API_URL}/method/${this.modalOptions.baseData.method_id}`, data)
        .then((res: BaseResponseI<MethodDto>) => {
          if (res.status_code === 200) {
            this.showMethodModal = false;

            notification.success({
              message: 'Aktualizace metody proběhla v pořádku'
            });
            this.getMethods();

            this.loading = false;
            return true;
          }

          this.loading = false;
          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Aktualizace metody selhala', description: res.error });
          } else {
            this.showMethodModal = false;
          }

          this.loading = false;
          return false;
        });
    },
    async getMethodVersions(method_id: number, methodVersionId: number) {
      this.loadingMethod = true;
      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/method/${method_id}/versions/`)
        .then((res: BaseResponseI<MethodVersionsDto[]>) => {
          this.loadingMethod = false;
          if (res.status_code === 200) {
            this.methodVersions = res.data.map((version) => new MethodVersions(version));
            const specificVersion = res.data.find(
              (version) => version.version_id === methodVersionId
            );
            if (specificVersion) {
              this.specificVersion = new MethodVersions(specificVersion);
            } else {
              this.specificVersion = null;
            }
            return this.specificVersion;
          }
        })
        .catch((res) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Načtení metod selhalo', description: res.error });
          }

          this.loadingMethod = false;
          return false;
        });
    },

    async addFileToMethod(file_id: number) {
      if (!this.method?.method_id) return false;
      this.loading = true;

      return fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/method/${this.method?.method_id}/file/${file_id}`)
        .then((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 200) {
            notification.success({ message: 'Soubor byl přidán do metody' });
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({
              message: 'Nelze přidat soubor do metody',
              description: res.message
            });
          } else {
            notification.error({ message: 'Nelze přidat soubor do metody' });
          }

          return false;
        });
    },

    async deleteFileFromMethod(file_id: number) {
      if (!this.method?.method_id) return false;

      this.loading = true;
      return fetchWrapper
        .delete(`${import.meta.env.VITE_API_URL}/method/${this.method?.method_id}/file/${file_id}`)
        .then((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 200) {
            notification.success({ message: 'Soubor byl smazán z metody' });
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({
              message: 'Nelze smazat soubor z metody',
              description: res.message
            });
          } else {
            notification.error({ message: 'Nelze smazat soubor z metody' });
          }

          return false;
        });
    },

    async getComparisonBranches(method_id: number, versionA_id: number, versionB_id: number) {
      this.loadingMethod = true;
      const usableMethodId = this.methodGet?.method_id ?? method_id;

      if (versionA_id === versionB_id) {
        notification.error({ message: 'Nelze porovnat stejné verze' });
        this.loadingMethod = false;
        return false;
      }
      if (!versionA_id || !versionB_id || !usableMethodId) {
        notification.error({ message: 'Porovnání verzí selhalo' });
        this.loadingMethod = false;
        return false;
      }

      const URL = `${import.meta.env.VITE_API_URL}/method/${usableMethodId}/versions/compare?version_a_id=${versionA_id.toString()}&version_b_id=${versionB_id.toString()}`;
      return fetchWrapper
        .get(URL)
        .then((res: BaseResponseI<VersionComparisonDto>) => {
          this.loadingMethod = false;
          if (res.status_code === 200) {
            this.versionA = new MethodVersions(res.data.version_a);
            this.versionB = new MethodVersions(res.data.version_b);
            this.differences = res.data.differences;
            return true;
          }
          return false;
        })
        .catch((res) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Porovnání verzí selhalo', description: res.error });
          }

          this.loadingMethod = false;
          return false;
        });
    },
    async changeVersion(data: SimpleVersionChange, version_id: number) {
      if (!version_id) {
        notification.error({ message: 'Není vybrána verze' });
        return false;
      }
      this.loading = true;
      return fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/version/${version_id}/branch`, data)
        .then((res: BaseResponseI<MethodDto>) => {
          if (res.status_code === 200) {
            this.showMethodModal = false;
            notification.success({
              message: 'Odčlenění verze proběhlo v pořádku'
            });
            this.getMethods();
            this.loading = false;
            return true;
          }
          this.loading = false;
          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Odčlenění verze selhalo', description: res.error });
          } else {
            this.showMethodModal = false;
          }
          this.loading = false;
          return false;
        });
    },
    async updateMethodForHistory(data: MethodUpdateDto) {
      this.loading = true;

      return fetchWrapper
        .put(`${import.meta.env.VITE_API_URL}/method/${this.methodGet?.method_id}`, data)
        .then((res: BaseResponseI<MethodDto>) => {
          if (res.status_code === 200) {
            this.showMethodModal = false;

            notification.success({
              message: 'Aktualizace metody proběhla v pořádku'
            });
            this.getMethods();

            this.loading = false;
            return true;
          }

          this.loading = false;
          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Aktualizace metody selhala', description: res.error });
          } else {
            this.showMethodModal = false;
          }

          this.loading = false;
          return false;
        });
    },
    async switchToVersion(method_id: number, version_id: number) {
      this.loadingMethod = true;
      const usableMethodId = this.methodGet?.method_id ?? method_id;
      const data = {
        target_version_id: version_id
      };
      return fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/method/${usableMethodId}/versions/switch`, data)
        .then((res: BaseResponseI<MethodVersionsDto>) => {
          this.loadingMethod = false;
          if (res.status_code === 200) {
            return true;
          }
        })
        .catch((res) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Načtení metod selhalo', description: res.error });
          }

          this.loadingMethod = false;
          return false;
        });
    },

    async setAsPrefill(method_id: number, version_id: number) {
      this.loadingMethod = true;
      const usableMethodId = this.methodGet?.method_id ?? method_id;
      return fetchWrapper
        .get(
          `${import.meta.env.VITE_API_URL}/method/${usableMethodId}/versions/set_as_prefill?version_id=${version_id}`
        )
        .then((res: BaseResponseI<MethodVersionsDto>) => {
          this.loadingMethod = false;
          if (res.status_code === 200) {
            return true;
          }
        })
        .catch((res) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Nastavení pro vývoj selhalo', description: res.error });
          }

          this.loadingMethod = false;
          return false;
        });
    },
    async redirectToMethodDetailLink(id: number) {
      return router.resolve({
        name: 'ListOfMethodForTechniqueUpdate',
        params: { method_id: id.toString() }
      });
    }
  }
});
