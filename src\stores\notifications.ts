import BaseConstructor from '@/utils/BaseConstructor';
import type { BaseResponseI } from '@/utils/axios';
import { fetchWrapper } from '@/utils/helpers/fetch-wrapper';
import { notification } from 'ant-design-vue';
import { defineStore } from 'pinia';

export enum NotificationType {
  ERROR = 'error',
  WARNING = 'warning',
  INFO = 'info',
  NEW_ANALYTICAL_REQUEST = 'NEW_ANALYTICAL_REQUEST',
  NEW_RESULT = 'NEW_RESULT',
  REOPEN_FORM_REQUEST = 'REOPEN_FORM_REQUEST'
}

export enum NotificationStatus {
  UNREAD = 'unread',
  DELETED = 'deleted',
  READ = 'read'
}

export enum FormType {
  EXPERIMENT = 'experiment',
  MESSAGE = 'message',
  ATTEMPT = 'attempt',
  INVESTIGATION = 'investigation'
}

export interface NotificationDataDto {
  batch_number?: string;
  department_id?: number;
  list_identifier?: any;
  sample_id?: number;
  sample_number?: string;
  form_id?: number;
  chapter_id?: number;
  project_department_id?: number;
  project_id?: number;
  form_type?: string;
  analytical_request_name?: string;
  requested_by?: {
    user_id: number;
    user_name: string;
  };
}

export interface NotificationDataI {
  batch_number?: string;
  department_id?: number;
  list_identifier?: any;
  project_id?: number;
  sample_id?: number;
  sample_number?: string;
  form_id?: number;
  chapter_id?: number;
  project_department_id?: number;
  form_type?: FormType;
  analytical_request_name?: string;
  requested_by?: {
    user_id: number;
    user_name: string;
  };
}

export class NotificationData
  extends BaseConstructor<NotificationDataI>()
  implements NotificationDataI
{
  constructor(data: NotificationDataDto) {
    super(data as unknown as NotificationDataI);
  }
}

export interface NotificationDto {
  notification_id: number;
  notification_content: string;
  notification_date: string;
  notification_type: string;
  notification_status: string;
  notification_data?: NotificationDataDto;
}

export interface NotificationI {
  notification_id: number;
  notification_content: string;
  notification_date: Date;
  notification_type: NotificationType;
  notification_status: NotificationStatus;
  notification_data?: NotificationDataI;
}

export class Notification extends BaseConstructor<NotificationI>() implements NotificationI {
  constructor(data: NotificationDto) {
    super(data as unknown as NotificationI);

    this.notification_date = new Date(data.notification_date);
  }

  get isActive(): boolean {
    return this.notification_status === NotificationStatus.UNREAD;
  }

  get class(): string {
    switch (this.notification_type) {
      case NotificationType.ERROR:
        return 'lighterror';
      case NotificationType.WARNING:
        return 'lightwarning';
      case NotificationType.INFO:
        return 'lightprimary';
      default:
        return 'lightsuccess';
    }
  }
}

interface NotificationsStateI {
  notifications: Notification[];
  loading: boolean;
  forceRefreshKey: number;
}

export const useNotificationsStore = defineStore({
  id: 'notifications',
  state: () =>
    ({
      notifications: [],
      loading: false,
      forceRefreshKey: 0
    }) as NotificationsStateI,
  getters: {
    unreadNotifications(): Notification[] {
      return this.notifications.filter((notification) => notification.isActive);
    }
  },
  actions: {
    async getNotifications(): Promise<boolean> {
      this.loading = true;

      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/notifications/`)
        .then((res: BaseResponseI<NotificationDto[]>) => {
          if (res.status_code === 200) {
            this.notifications = res.data.map((notification) => new Notification(notification));
            this.loading = false;
            return true;
          }

          this.loading = false;
          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Načtení notifikací selhalo', description: res.error });
          }

          this.loading = false;
          return false;
        });
    },

    async markNotificationAsRead(
      notification_id: number,
      reload: boolean = true
    ): Promise<boolean> {
      this.loading = true;

      return fetchWrapper
        .patch(`${import.meta.env.VITE_API_URL}/notification/${notification_id}`)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            if (reload) this.getNotifications();
            return true;
          }

          this.loading = false;
          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Označení notifikace jako přečtené selhalo',
              description: res.error
            });
          }

          this.loading = false;
          return false;
        });
    },

    async deleteNotification(notification_id: number): Promise<boolean> {
      this.loading = true;

      return fetchWrapper
        .delete(`${import.meta.env.VITE_API_URL}/notification/${notification_id}`)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            this.getNotifications();
            return true;
          }

          this.loading = false;
          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Smazání notifikace selhalo', description: res.error });
          }

          this.loading = false;
          return false;
        });
    },

    async markAllNotificationsAsRead() {
      this.loading = true;

      this.unreadNotifications.forEach(async (notification) => {
        await this.markNotificationAsRead(notification.notification_id, false);
      });
      this.getNotifications();
    },
    async removeAllNotifications() {
      this.notifications.forEach(async (notification) => {
        await this.deleteNotification(notification.notification_id);
      });
      this.forceRefreshKey++;
      this.getNotifications();
    }
  }
});
