import BaseConstructor from '@/utils/BaseConstructor';
import {
  loadParamsFailedReloadNotification,
  reloadWithoutParams,
  revertServerOptionsFromLocation,
  stringifyServerOptions,
  type BasePaginatorResponseI,
  type BaseResponseI
} from '@/utils/axios';
import { fetchWrapper } from '@/utils/helpers/fetch-wrapper';
import { notification } from 'ant-design-vue';
import { defineStore } from 'pinia';
import type { ServerOptions } from 'vue3-easy-data-table';
import { Project, type GetAllOptions, type PaginatorRequestDataI } from './projects';

export enum ProjectChemicalStatus {
  ACTIVE = 'active',
  DELETED = 'deleted'
}

export interface ProjectChemicalDto {
  project_chemical_id: number;
  project_id: number | null;
  csa: string;
  shortcut: string;
  name: string;
  density: number;
  molar_mass: number;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface ProjectChemicalI {
  project_chemical_id: number;
  project_id: number | null;
  csa: string;
  shortcut: string;

  name: string;
  density: number;
  molar_mass: number;

  status: ProjectChemicalStatus;
  created_at: Date;
  updated_at: Date;
}

export class ProjectChemical
  extends BaseConstructor<ProjectChemicalI>()
  implements ProjectChemicalI
{
  constructor(data: ProjectChemicalDto) {
    super(data as unknown as ProjectChemicalI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    this.status = data.status as ProjectChemicalStatus;
  }
}

interface ProjectChemicalModalOptionsI {
  isEditing: boolean;
  isCreating: boolean;
  baseData: ProjectChemical | undefined;
  newData: NewProjectChemicalModalDataI | undefined;
}

interface NewProjectChemicalModalDataI {
  csa: string | undefined;
  shortcut: string | undefined;
  name: string | undefined;
  density: number | undefined;
  molar_mass: number | undefined;
  status: string | ProjectChemicalStatus | undefined;
  confirm: boolean | false;
}

interface ProjectChemicalsStateI {
  project: Project | null;
  project_id: number | null;

  chemicals: ProjectChemical[];
  chemical: ProjectChemical | null;
  loading: boolean;

  showChemicalModal: boolean;
  modalOptions: ProjectChemicalModalOptionsI | undefined;

  totalItems?: number;
  options: ServerOptions;
  search: string | undefined;
  search_type: 'AND' | 'OR' | null;
}

export const useProjectsChemicalsStore = defineStore({
  id: 'projectChemicals',
  state: () =>
    ({
      project: null,
      project_id: null,

      chemical: null,
      chemicals: [],
      loading: false,

      showChemicalModal: false,
      modalOptions: undefined,

      search: undefined,
      search_type: null,

      totalItems: undefined,
      options: {
        page: 1,
        rowsPerPage: 25,
        sortBy: [],
        sortType: ['desc', 'asc']
      }
    }) as ProjectChemicalsStateI,
  actions: {
    async getAll(
      setData: boolean = true,
      options: GetAllOptions | undefined = {
        search_columns: ['name', 'shortcut', 'csa', 'density', 'molar_mass', 'status']
      },
      search_type: 'AND' | 'OR' = 'AND',
      project_id: number | null
    ): Promise<{
      data: ProjectChemical[];
      totalItems: number;
    }> {
      this.loading = true;

      this.project_id = project_id ?? this.project_id;
      if (this.project_id === null) {
        this.loading = false;
        return {
          data: [],
          totalItems: 0
        };
      }
      this.options.sortBy = ['status'];
      this.options.sortType = ['asc'];
      const URL =
        `${import.meta.env.VITE_API_URL}/project/${this.project_id}/chemicals` +
        '?' +
        stringifyServerOptions(
          options && options.options ? options.options : this.options,
          options && options.search ? options.search : (this.search ?? null),
          options && options.search_columns ? options.search_columns : [],
          options && options.fixedFilterOptions ? options.fixedFilterOptions : null,
          this.search_type ?? search_type
        );

      return fetchWrapper
        .get(URL)
        .then((res: BasePaginatorResponseI<ProjectChemicalDto>) => {
          if (res.status_code === 200) {
            this.totalItems = res.data.total_items;
            if (setData) {
              this.chemicals = res.data.items.map((chemical) => new ProjectChemical(chemical));
            }

            this.loading = false;
            return {
              data: res.data.items.map((chemical) => new ProjectChemical(chemical)),
              totalItems: res.data.total_items
            };
          }
          this.loading = false;
          return {
            data: [],
            totalItems: 0
          };
        })
        .catch((res) => {
          if (res.status_code === 400) {
            loadParamsFailedReloadNotification('Načtení chemikálie selhalo', res.error, () => {
              const data = reloadWithoutParams();
              this.options = data.options;
              this.search = data.term ?? undefined;
            });
          }

          this.loading = false;
          return {
            data: [],
            totalItems: 0
          };
        });
    },

    async getProjectChemicalForAutocomplete(
      projectChemicalForAutocompleteOptions: PaginatorRequestDataI<ProjectChemical>,
      addNewData = false,
      project_id: number
    ) {
      projectChemicalForAutocompleteOptions.loading = true;
      const usableProjectId = project_id ?? this.project_id;
      const URL =
        `${import.meta.env.VITE_API_URL}/project/${usableProjectId}/chemicals` +
        '?' +
        stringifyServerOptions(
          projectChemicalForAutocompleteOptions.options,
          projectChemicalForAutocompleteOptions.search ?? null,
          projectChemicalForAutocompleteOptions.search_columns,
          projectChemicalForAutocompleteOptions.filterOptions,
          projectChemicalForAutocompleteOptions.search_type ?? 'OR'
        );
      fetchWrapper
        .get(URL)
        .then((res: BasePaginatorResponseI<ProjectChemicalDto>) => {
          projectChemicalForAutocompleteOptions.loading = false;

          if (res.status_code === 200) {
            projectChemicalForAutocompleteOptions.totalItems = res.data.total_items;
            if (addNewData) {
              projectChemicalForAutocompleteOptions.results = [
                ...projectChemicalForAutocompleteOptions.results,
                ...res.data.items.map((projectChemical) => new ProjectChemical(projectChemical))
              ];
              projectChemicalForAutocompleteOptions.results = [
                ...new Map(
                  projectChemicalForAutocompleteOptions.results.map((projectChemical) => [
                    projectChemical.project_chemical_id,
                    projectChemical
                  ])
                ).values()
              ];
            } else {
              projectChemicalForAutocompleteOptions.results = Array.from(
                res.data.items.map((projectChemical) => new ProjectChemical(projectChemical))
              );
            }
            this.chemicals = projectChemicalForAutocompleteOptions.results;
          }
          /*
          if (!addNewData) {
            projectChemicalForAutocompleteOptions.results = [];
          }
*/
        })
        .catch((res) => {
          projectChemicalForAutocompleteOptions.loading = false;

          if (res.status_code === 400) {
            notification.error({
              message: 'Načtení chemikálií v projektu selhalo',
              description: res.error
            });
          }

          if (!addNewData) {
            projectChemicalForAutocompleteOptions.results = [];
          }
        });
    },

    async getChemical(id: number) {
      this.loading = true;
      if (this.project_id === null) {
        this.loading = false;
        notification.error({ message: 'Není vybrán projekt' });
        return undefined;
      }

      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/project/${this.project_id}/chemical/${id}`)
        .then((res: BaseResponseI<ProjectChemicalDto>) => {
          if (res.status_code === 200) {
            this.loading = false;
            return new ProjectChemical(res.data);
          }

          this.loading = false;
          return undefined;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Načtení chemikálie selhalo', description: res.error });
          }

          this.loading = false;
          return undefined;
        });
    },

    setParamsFromLocation(): void {
      const data = revertServerOptionsFromLocation();
      this.options = data.options;
      this.search = data.term ?? undefined;
    },

    showNewChemicalModal() {
      this.modalOptions = {
        isEditing: false,
        isCreating: true,
        baseData: undefined,
        newData: {
          csa: undefined,
          shortcut: undefined,
          name: undefined,
          density: undefined,
          molar_mass: undefined,
          status: ProjectChemicalStatus.ACTIVE,
          confirm: false
        }
      };

      this.showChemicalModal = true;
    },

    async showPreviewModal(id: number) {
      const Chemical = await this.getChemical(id);
      if (Chemical === undefined) {
        notification.error({ message: 'Chemikálie nebyla nalezena' });
        return;
      }

      this.modalOptions = {
        isEditing: false,
        isCreating: false,
        baseData: Chemical,
        newData: {
          csa: Chemical.csa,
          shortcut: Chemical.shortcut,
          name: Chemical.name,
          density: Chemical.density,
          molar_mass: Chemical.molar_mass,
          status: Chemical.status,
          confirm: false
        }
      };

      this.showChemicalModal = true;
    },

    async showEditModal(id: number) {
      const Chemical = await this.getChemical(id);
      if (Chemical === undefined) {
        notification.error({ message: 'Chemikálie nebyla nalezena' });
        return;
      }

      this.chemical = Chemical;

      this.modalOptions = {
        isEditing: true,
        isCreating: false,
        baseData: Chemical,
        newData: {
          csa: Chemical.csa,
          shortcut: Chemical.shortcut,
          name: Chemical.name,
          density: Chemical.density,
          molar_mass: Chemical.molar_mass,
          status: Chemical.status,
          confirm: false
        }
      };

      this.showChemicalModal = true;
    },

    deleteChemical(id: number) {
      this.loading = true;

      if (this.project_id === null) {
        this.loading = false;
        notification.error({ message: 'Není vybrán projekt' });
        return;
      }

      return fetchWrapper
        .delete(`${import.meta.env.VITE_API_URL}/project/${this.project_id}/chemical/${id}`)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Chemikálie byla úspěšně smazána' });
            this.getAll(undefined, undefined, undefined, null);
            this.loading = false;
          }
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Smazání chemikálie selhalo', description: res.error });
          }

          this.loading = false;
        });
    },

    async createChemical() {
      if (this.project_id === null) {
        notification.error({ message: 'Není vybrán projekt' });
        return false;
      }

      if (!this.modalOptions?.newData || this.modalOptions?.newData?.confirm === false) {
        notification.error({ message: 'Musíte potvrdit souhlas s vytvořením chemikálie' });
        return false;
      }

      this.loading = true;

      const data = {
        csa: this.modalOptions.newData.csa ?? '',
        shortcut: this.modalOptions.newData.shortcut ?? '',
        name: this.modalOptions.newData.name ?? '',
        density: this.modalOptions.newData.density ?? 0,
        molar_mass: this.modalOptions.newData.molar_mass ?? 0,
        status: this.modalOptions.newData.status ?? ProjectChemicalStatus.ACTIVE
      };

      return await fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/project/${this.project_id}/chemicals`, data)
        .then((res: BaseResponseI<ProjectChemicalDto>) => {
          if (res.status_code === 200) {
            this.showChemicalModal = false;
            notification.success({
              message: 'Vytvoření chemikálie proběhlo v pořádku',
              description: 'Název: ' + res.data.name
            });

            this.loading = false;
            this.getAll(undefined, undefined, undefined, null);
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Vytvoření chemikálie selhalo', description: res.error });
          } else {
            this.showChemicalModal = false;
          }

          this.loading = false;
          return false;
        });
    },
    async createChemicalforTable(project_id?: number) {
      const usableProjectId = project_id ?? this.project_id;

      if (usableProjectId === null) {
        notification.error({ message: 'Není vybrán projekt' });
        return false;
      }

      if (!this.modalOptions?.newData || this.modalOptions?.newData?.confirm === false) {
        notification.error({ message: 'Musíte potvrdit souhlas s vytvořením chemikálie' });
        return false;
      }

      this.loading = true;

      const data = {
        csa: this.modalOptions.newData.csa ?? '',
        shortcut: this.modalOptions.newData.shortcut ?? '',
        name: this.modalOptions.newData.name ?? '',
        density: this.modalOptions.newData.density ?? 0,
        molar_mass: this.modalOptions.newData.molar_mass ?? 0,
        status: this.modalOptions.newData.status ?? ProjectChemicalStatus.ACTIVE
      };

      return await fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/project/${usableProjectId}/chemicals`, data)
        .then((res: BaseResponseI<ProjectChemicalDto>) => {
          if (res.status_code === 200) {
            this.showChemicalModal = false;
            notification.success({
              message: 'Vytvoření chemikálie proběhlo v pořádku',
              description: 'Název: ' + res.data.name
            });
            this.loading = false;
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Vytvoření chemikálie selhalo', description: res.error });
          } else {
            this.showChemicalModal = false;
          }
          this.loading = false;
          return false;
        });
    },

    updateChemical() {
      if (this.project_id === null) {
        notification.error({ message: 'Není vybrán projekt' });
        return false;
      }

      if (!this.modalOptions?.newData) {
        notification.error({ message: 'Není co upravovat' });
        return false;
      }

      this.loading = true;

      const data = {
        csa: this.modalOptions.newData.csa ?? '',
        shortcut: this.modalOptions.newData.shortcut ?? '',
        name: this.modalOptions.newData.name ?? '',
        density: this.modalOptions.newData.density ?? 0,
        molar_mass: this.modalOptions.newData.molar_mass ?? 0,
        status: this.modalOptions.newData.status ?? ProjectChemicalStatus.ACTIVE
      };

      return fetchWrapper
        .put(
          `${import.meta.env.VITE_API_URL}/project/${this.project_id}/chemical/${this.modalOptions.baseData?.project_chemical_id}`,
          data
        )
        .then((res: BaseResponseI<ProjectChemicalDto>) => {
          if (res.status_code === 200) {
            this.showChemicalModal = false;

            notification.success({
              message: 'Úprava chemikálie proběhla v pořádku',
              description: 'Název: ' + res.data.name
            });

            this.getAll(undefined, undefined, undefined, null);
            this.loading = false;
            return true;
          }

          this.loading = false;
          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Úprava chemikálie selhala', description: res.error });
          } else {
            this.showChemicalModal = false;
          }

          this.loading = false;
          return false;
        });
    },
    resetModal() {
      this.showChemicalModal = false;
      if (this.modalOptions) this.modalOptions.baseData = undefined;
      if (this.modalOptions) this.modalOptions.newData = undefined;
    }
  }
});
