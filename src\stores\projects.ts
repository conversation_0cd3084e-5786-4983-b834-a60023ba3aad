import { router } from '@/router';
import BaseConstructor from '@/utils/BaseConstructor';
import {
  loadParamsFailedReloadNotification,
  prepareJsonParams,
  reloadWithoutParams,
  stringifyServerOptions,
  stringifySearchParams,
  type BasePaginatorResponseI,
  type BaseResponseI,
  type PaginatorData
} from '@/utils/axios';
import { fetchWrapper } from '@/utils/helpers/fetch-wrapper';
import { notification } from 'ant-design-vue';
import { defineStore } from 'pinia';
import type { ServerOptions } from 'vue3-easy-data-table';
import { User, type UserDto, useAuthStore, permissionsTranslator, rolesTranslator } from './auth';
import {
  Chapter,
  type ChapterDto,
  type ChapterSearchDto,
  ChapterSearch,
  type ChapterForSearchDto,
  ChapterForSearch
} from './chapters';
import { ProjectChemical, type ProjectChemicalDto } from './projectChemicals';
import { Technique, type TechniqueDto, type TechniqueI } from './techniques';
import {
  type SampleDto,
  type SampleI,
  Sample,
  type SimpleSampleDto,
  SimpleSample
} from './sample/samples';
import { type ChemicalDto, Chemical } from './chemicals';
import {
  type TechniqueForSearchDto,
  TechniqueForSearch,
  type SimpleStandardDto,
  SimpleStandard
} from './techniques';
import { type SimpleMethodDto, SimpleMethod } from './method/methods';
import {
  AnalyticalRequestForSample,
  type AnalyticalRequestForSampleDto,
  type AnalyticalRequestForSearchDto,
  AnalyticalRequestForSearch
} from './analyticalRequests/analyticalRequests';
import {
  type InstrumentUsageHistoryProjectSampleDto,
  InstrumentUsageHistoryProjectSample
} from './instruments';
import {
  type FormForSearchSimpleDto,
  type FormForSearchSimpleI,
  FormForSearchSimple
} from './forms';
import { isAllowed } from '@/utils/directive/isAllowed';
import { storeToRefs } from 'pinia';
import { type newFormDto, newForm, type NewFormForSearchDto, NewFormForSearch } from './forms';
import { type SearchParamsI } from '@/utils/axios';
const authstore = useAuthStore();
const { user } = storeToRefs(authstore);
const checkPermision = (permision: string) => {
  if (isAllowed([permision])) {
    return true;
  } else {
    return false;
  }
};
const baseUrl = `${import.meta.env.VITE_API_URL}/projects`;
export interface GetAllOptions {
  search?: string;
  totalItems?: number;
  options?: ServerOptions;
  search_columns?: string[];
  fixedFilterOptions?: null | Array<{
    column: string;
    value: null | number | string | string[] | number[] | object | object[] | number[];
  }>;
}

export enum ProjectStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DELETED = 'deleted'
}

export enum ProjectStatusNew {
  ACTIVE = 'active',
  INACTIVE = 'deactivated',
  CLOSED = 'closed'
}

export enum DepartmentEnum {
  SYNTHETIC = 'synthetic',
  ANALYTICAL = 'analytical',
  TECHNICAL = 'technical'
}

interface SimpleFormDto {
  form_id: number;
  batch_number_id: number;
  chapter_id: number;
  form_name: string;
  form_type: string;
  status: string;
  batch_description: string;
  created_at: string;
  updated_at: string;
  chemicals?: ChemicalDto[];
}

interface SimpleFormDtoI {
  form_id: number;
  batch_number_id: number;
  chapter_id: number;
  form_name: string;
  form_type: string;
  status: string;
  batch_description: string;
  created_at: Date;
  updated_at: Date;
  chemicals?: Chemical[];
}

export class SimpleForm extends BaseConstructor<SimpleFormDtoI>() implements SimpleFormDtoI {
  constructor(data: SimpleFormDto) {
    super(data as unknown as SimpleFormDtoI);

    this.chemicals = data.chemicals?.map((chemical) => new Chemical(chemical)) ?? [];

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);
  }
}

export interface BatchDto {
  batch_number_id: number;
  batch_number: string;
  project_department_id: string;
  created_at: string;
  updated_at: string;
  form: FormForSearchSimpleDto;
}

export interface BatchI {
  batch_number_id: number;
  batch_number: string;
  project_department_id: string;
  created_at: Date;
  updated_at: Date;
  form: SimpleForm;
}

export class Batch extends BaseConstructor<BatchI>() implements BatchI {
  constructor(data: BatchDto) {
    super(data as unknown as BatchI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    if (data.form) {
      this.form = new SimpleForm(data.form);
    }
  }
}

export interface BatchSearchDto {
  batch_number_id: number;
  batch_number: string;
  project_department_id: string;
  created_at: string;
  updated_at: string;
  form?: FormForSearchSimpleDto | null;
}

export interface BatchSearchI {
  batch_number_id: number;
  batch_number: string;
  project_department_id: string;
  created_at: Date;
  updated_at: Date;
  form?: FormForSearchSimple | null;
}

export class BatchSearch extends BaseConstructor<BatchSearchI>() implements BatchSearchI {
  constructor(data: BatchSearchDto) {
    super(data as unknown as BatchSearchI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    if (data.form) {
      this.form = new FormForSearchSimple(data.form);
    }
  }
}

export interface DepartmentForSearchDto {
  project_department_id: number;
  project_id: number;
  shortcut: string;
  number: string;
  status: string;
  created_at: string;
  updated_at: string;
  type: DepartmentEnum;
  chapters: ChapterForSearchDto[];
  project_name: string;
}

export interface DepartmentForSearchI {
  project_department_id: number;
  project_id: number;
  shortcut: string;
  number: string;
  status: string;
  type: DepartmentEnum;
  created_at: Date;
  updated_at: Date;
  chapters: ChapterForSearch[];
  name: string;
}

export class DepartmentForSearch
  extends BaseConstructor<DepartmentForSearchI>()
  implements DepartmentForSearchI
{
  constructor(data: DepartmentForSearchDto) {
    super(data as unknown as DepartmentForSearchI);
    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);
    this.name = data.project_name;
    this.chapters = data.chapters.map((chapter) => new ChapterForSearch(chapter));
  }
}

export interface DepartmentDto {
  project_department_id: number;
  project_id: number;
  shortcut: string;
  number: string;
  status: string;
  type: DepartmentEnum;
  created_at: string;
  updated_at: string;
  chapters: ChapterDto[];
  batches?: BatchDto[];
  analytical_request?: AnalyticalRequestForSearchDto[];
}
export interface DepartmentI {
  project_department_id: number;
  project_id: number;
  shortcut: string;
  number: string;
  status: string;
  type: DepartmentEnum;
  created_at: Date;
  updated_at: Date;
  chapters: Chapter[];
  batches?: Batch[];
  analytical_request?: AnalyticalRequestForSearch[];
}

export class Department extends BaseConstructor<DepartmentI>() implements DepartmentI {
  constructor(data: DepartmentDto) {
    super(data as unknown as DepartmentI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    this.chapters = data.chapters ? data.chapters.map((chapter) => new Chapter(chapter)) : [];
    if (data.batches) {
      this.batches = data.batches.map((batch) => new Batch(batch));
    }
    if (data.analytical_request) {
      this.analytical_request = data.analytical_request.map(
        (analytical_request) => new AnalyticalRequestForSearch(analytical_request)
      );
    }
  }

  get getName() {
    switch (this.type) {
      case DepartmentEnum.SYNTHETIC:
        return 'Syntetické oddělení';
      case DepartmentEnum.ANALYTICAL:
        return 'Analytické oddělení';
      case DepartmentEnum.TECHNICAL:
        return 'Technologické oddělení';
    }
  }

  isSynthetic() {
    return this.type === DepartmentEnum.SYNTHETIC;
  }

  isAnalytical() {
    return this.type === DepartmentEnum.ANALYTICAL;
  }

  isTechnical() {
    return this.type === DepartmentEnum.TECHNICAL;
  }
  get isActive() {
    return this.status === 'active';
  }
}

export interface ProjectPageGetAllProjectsResponseDto {
  my_projects: PaginatorData<SimpleProjectDto>;
  all_projects: PaginatorData<SimpleProjectDto>;
  active_projects: PaginatorData<SimpleProjectDto>;
}

export interface GetAllSearchResponseDto {
  all_projects: PaginatorData<ExtendedProjectDto>;
}

export interface PatchProjectDto {
  status: string;
  brute_force_close: boolean;
}

export interface PatchProjectI {
  status: ProjectStatusNew;
  brute_force_close: boolean;
}

export interface ProjectPermissionDto {
  project_permission_id: number;
  name: string;
}

export interface ProjectPermissionI {
  project_permission_id: number;
  name: string;
  translatedPermission?: string;
}

export class ProjectPermission
  extends BaseConstructor<ProjectPermissionI>()
  implements ProjectPermissionI
{
  constructor(data: ProjectPermissionDto) {
    super(data as unknown as ProjectPermissionI);
    this.translatedPermission = permissionsTranslator(data.name);
  }
}

export interface ProjectRoleDto {
  project_role_id: number;
  name: string;
  permissions: ProjectPermissionDto[];
}

export interface ProjectRoleI {
  project_role_id: number;
  name: string;
  permissions: ProjectPermission[];
  newPermissions: number[];
  translatedRole?: string;
}

export class ProjectRole extends BaseConstructor<ProjectRoleI>() implements ProjectRoleI {
  constructor(data: ProjectRoleDto) {
    super(data as unknown as ProjectRoleI);

    this.permissions = data.permissions.map((permission) => new ProjectPermission(permission));
    this.newPermissions = this.permissions.map((permission) => permission.project_permission_id);
    this.translatedRole = rolesTranslator(data.name);
  }
}

export interface ProjectUserDetailDto {
  user_project_id: number;
  user_id: number;
  project_id: number;
  user: UserDto;
  project: ProjectDto;
  roles: ProjectRoleDto[];
  permissions: ProjectPermissionDto[];
}

export interface ProjectUserDetailI {
  user_project_id: number;
  user_id: number;
  project_id: number;
  user: User;
  project: Project;
  roles: ProjectRole[];
  permissions: ProjectPermission[];
}

export class ProjectUserDetail
  extends BaseConstructor<ProjectUserDetailI>()
  implements ProjectUserDetailI
{
  constructor(data: ProjectUserDetailDto) {
    super(data as unknown as ProjectUserDetailI);

    this.user = new User(data.user);
    this.project = new Project(data.project);
    this.roles = data.roles.map((role) => new ProjectRole(role));
    this.permissions = data.permissions.map((permission) => new ProjectPermission(permission));
  }
}

export interface ProjectUserDto {
  project_id: number;
  user_id: number;
  created_at: string;
  updated_at: string;
  first_name: string;
  last_name: string;
  user_email: string;
  status: string;
  departments?: DepartmentDto[];
  fromResponsible?: boolean | undefined;
}

export interface ProjectUserI {
  project_id: number;
  user_id: number;
  created_at: Date;
  updated_at: Date;
  first_name: string;
  last_name: string;
  user_email: string;
  status: string;
  departments: Department[];
  fromResponsible?: boolean | undefined;
}

export class ProjectUser extends BaseConstructor<ProjectUserI>() implements ProjectUserI {
  constructor(data: ProjectUserDto) {
    super(data as unknown as ProjectUserI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    this.departments = data.departments?.map((department) => new Department(department)) ?? [];
  }

  get getName() {
    return this.first_name + ' ' + this.last_name;
  }

  get getStatusTitle() {
    return this.status === 'active' ? 'Aktivní' : 'Neaktivní';
  }

  get getShortcuts() {
    const shortcuts: string[] = [];

    this.departments.forEach((department) => {
      shortcuts.push(department.shortcut);
    });

    return shortcuts.join(' / ');
  }
}

export interface SearchResult {
  search_id: string;
  breadcrumb: string;
  route: string;
  search_object?: unknown;
}

interface SimpleProjectDto {
  project_id: number;
  user_id: number;
  name: string;

  status: string;
  created_at: string;
  updated_at: string;

  departments?: DepartmentDto[];
  responsible_users: UserDto[];
}

interface SimpleProjectDtoI {
  project_id: number;
  user_id: number;
  name: string;

  status: string;
  created_at: Date;
  updated_at: Date;

  departments?: Department[];
  responsible_users: User[];
}
interface ExtendedProjectDto {
  project_id: number;
  user_id: number;
  name: string;
  status: string;
  created_at: string;
  updated_at: string;
  analytical_techniques?: TechniqueForSearchDto[];
  methods?: SimpleMethodDto[];
  departments?: DepartmentDto[];
  responsible_users: UserDto[];
}

interface ExtendedProjectI {
  project_id: number;
  user_id: number;
  name: string;
  status: string;
  created_at: Date;
  updated_at: Date;
  analytical_techniques?: TechniqueForSearch[];
  methods?: SimpleMethod[];
  departments?: Department[];
  responsible_users: User[];
}

export class ExtendedProject
  extends BaseConstructor<ExtendedProjectI>()
  implements ExtendedProjectI
{
  constructor(data: ExtendedProjectDto) {
    super(data as unknown as ExtendedProjectI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    this.departments = data.departments?.map((department) => new Department(department)) ?? [];
    this.responsible_users = data.responsible_users.map((user) => new User(user));

    this.analytical_techniques = data.analytical_techniques?.map(
      (analytical_technique) => new TechniqueForSearch(analytical_technique)
    );

    this.methods = data.methods?.map((method) => new SimpleMethod(method)) ?? [];
  }
}

export interface SimpleProjectForSearchDto {
  project_id: number;
  name: string;
  status: string;
  created_at: string;
  updated_at: string;
  user_id: number;
}

export interface SimpleProjectForSearchI {
  project_id: number;
  name: string;
  status: string;
  created_at: Date;
  updated_at: Date;
}
export class SimpleProjectForSearch
  extends BaseConstructor<SimpleProjectForSearchI>()
  implements SimpleProjectForSearchI
{
  constructor(data: SimpleProjectForSearchDto) {
    super(data as unknown as SimpleProjectForSearchI);
    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);
  }
}

interface TagForSearchDto {
  tag_id: number;
  name: string;
  chapters: ChapterSearchDto[];
  forms: NewFormForSearchDto[];
}

interface TagForSearchI {
  tag_id: number;
  name: string;
  chapters: ChapterSearch[];
  forms: NewFormForSearch[];
}
export class TagForSearch extends BaseConstructor<TagForSearchI>() implements TagForSearchI {
  constructor(data: TagForSearchDto) {
    super(data as unknown as TagForSearchI);

    this.chapters = data.chapters.map((chapter) => new ChapterSearch(chapter));
    this.forms = data.forms.map((form) => new NewFormForSearch(form));
  }
}

interface ChemicalForSearchDto {
  chemical_id: number;
  concentration: string;
  created_at: string;
  updated_at: string;
  name: string;
  equivalent: number;
  density: number;
  grams: number;
  molar_mass: number;
  moles: number;
  notes: string;
  project_template_id: number | null;
  status: string;
  type: string;
  volume: number;
  form_id: number | null;
  forms: NewFormForSearchDto | null;
}

interface ChemicalForSearchI {
  chemical_id: number;
  concentration: string;
  created_at: Date;
  updated_at: Date;
  name: string;
  equivalent: number;
  density: number;
  grams: number;
  molar_mass: number;
  moles: number;
  notes: string;
  project_template_id: number | null;
  status: string;
  type: string;
  volume: number;
  form_id: number | null;
  forms: NewFormForSearch | null;
}

export class ChemicalForSearch
  extends BaseConstructor<ChemicalForSearchI>()
  implements ChemicalForSearchI
{
  constructor(data: ChemicalForSearchDto) {
    super(data as unknown as ChemicalForSearchI);
    if (data.forms) {
      this.forms = new NewFormForSearch(data.forms);
    }
  }
}

interface ProjectChemicalForSearchDto {
  project_chemical_id: number;
  project_id: number;
  chemical_id: number;
  created_at: string;
  updated_at: string;
  shortcut: string;
  status: string;
  name: string;
  molar_mass: number;
  density: number;
  csa: string;
  project: SimpleProjectForSearchDto;
}

interface ProjectChemicalForSearchI {
  project_chemical_id: number;
  project_id: number;
  chemical_id: number;
  created_at: Date;
  updated_at: Date;
  shortcut: string;
  status: string;
  name: string;
  molar_mass: number;
  density: number;
  csa: string;
  project: SimpleProjectForSearch;
}

export class ProjectChemicalForSearch
  extends BaseConstructor<ProjectChemicalForSearchI>()
  implements ProjectChemicalForSearchI
{
  constructor(data: ProjectChemicalForSearchDto) {
    super(data as unknown as ProjectChemicalForSearchI);
    this.project = new SimpleProjectForSearch(data.project);
  }
}

interface ExtendedSearchDto {
  analytical_requests: AnalyticalRequestForSearchDto[];
  analytical_techniques: TechniqueForSearchDto[];
  batch_numbers: BatchSearchDto[];
  chapters: ChapterSearchDto[];
  forms: NewFormForSearchDto[];
  methods: SimpleMethodDto[];
  project_departments: DepartmentForSearchDto[];
  projects: SimpleProjectForSearchDto[];
  samples: SimpleSampleDto[];
  standards: SimpleSampleDto[];
  tags: TagForSearchDto[];
  chemicals: ChemicalForSearchDto[];
  project_chemicals: ProjectChemicalForSearchDto[];
}

interface ExtendedSearchI {
  analytical_requests: AnalyticalRequestForSearch[];
  analytical_techniques: TechniqueForSearch[];
  batch_numbers: BatchSearch[];
  chapters: ChapterSearch[];
  forms: NewFormForSearch[];
  methods: SimpleMethod[];
  project_departments: DepartmentForSearch[];
  projects: SimpleProjectForSearch[];
  samples: SimpleSample[];
  standards: SimpleSample[];
  tags: TagForSearch[];
  chemicals: ChemicalForSearch[];
  project_chemicals: ProjectChemicalForSearch[];
}

export class ExtendedSearch extends BaseConstructor<ExtendedSearchI>() implements ExtendedSearchI {
  constructor(data: ExtendedSearchDto) {
    super(data as unknown as ExtendedSearchI);

    this.analytical_requests = data.analytical_requests.map(
      (analytical_request) => new AnalyticalRequestForSearch(analytical_request)
    );

    this.analytical_techniques = data.analytical_techniques.map(
      (analytical_technique) => new TechniqueForSearch(analytical_technique)
    );

    this.batch_numbers = data.batch_numbers.map((batch) => new BatchSearch(batch));

    this.chapters = data.chapters.map((chapter) => new ChapterSearch(chapter));

    this.forms = data.forms.map((form) => new NewFormForSearch(form));

    this.standards = data.standards.map((standard) => new SimpleSample(standard));

    this.methods = data.methods.map((method) => new SimpleMethod(method));

    this.projects = data.projects.map((project) => new SimpleProjectForSearch(project));

    this.samples = data.samples.map((sample) => new SimpleSample(sample));

    this.project_departments = data.project_departments.map(
      (department) => new DepartmentForSearch(department)
    );

    this.tags = data.tags.map((tag) => new TagForSearch(tag));

    this.chemicals = data.chemicals.map((chemical) => new ChemicalForSearch(chemical));

    this.project_chemicals = data.project_chemicals.map(
      (project_chemical) => new ProjectChemicalForSearch(project_chemical)
    );
  }
}

export class SimpleProject
  extends BaseConstructor<SimpleProjectDtoI>()
  implements SimpleProjectDtoI
{
  constructor(data: SimpleProjectDto) {
    super(data as unknown as SimpleProjectDtoI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    this.departments = data.departments?.map((department) => new Department(department)) ?? [];
    this.responsible_users = data.responsible_users.map((user) => new User(user));
  }

  get getShortcuts() {
    const shortcuts: string[] = [];

    const permissionMap: Record<DepartmentEnum, string> = {
      [DepartmentEnum.ANALYTICAL]: 'view_analytical_code',
      [DepartmentEnum.SYNTHETIC]: 'view_synthetic_code',
      [DepartmentEnum.TECHNICAL]: 'view_technological_code'
    };

    this.departments?.forEach((department) => {
      const permission = permissionMap[department.type];

      if (checkPermision(permission)) {
        shortcuts.push(department.shortcut);
      } else {
        shortcuts.push('-');
      }
    });

    return shortcuts.join(' / ');
  }

  get numberOfDepartments() {
    return this.departments?.length;
  }
  /*
  get getBreadcrumb() {
    return [this.name,
    this.departments?.map((department) => department.shortcut).join(' / '),
    this.departments?.map((department) => department.batches?.map((batch) => batch?.batch_number).join(' / ')).join(' / '),
    this.departments?.map((department) => department.batches?.map((batch) => batch?.form?.form_name).join(' / ')).join(' / '),
    this.departments?.map((department) => department.chapters.map((chapter) => chapter?.chapter_title).join(' / ')).join(' / ')]
    .filter(Boolean)
    .join(' / ');
  }
  */
}

export interface ProjectDto {
  project_id: number;
  name: string;
  status: string;
  responsible_users: UserDto[] | null | undefined;
  created_at: string;
  updated_at: string;
  departments: DepartmentDto[];
  analytical_techniques: TechniqueDto[];
  project_chemicals: ProjectChemicalDto[];
}

export interface ProjectI {
  project_id: number;
  name: string;
  status: ProjectStatusNew;
  responsible_users: User[] | null | undefined;
  created_at: Date;
  updated_at: Date;
  departments: Department[];
  analytical_techniques: Technique[];
  project_chemicals: ProjectChemical[];
}

export class Project extends BaseConstructor<ProjectI>() implements ProjectI {
  constructor(data: ProjectDto) {
    super(data as unknown as ProjectI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    this.responsible_users = data.responsible_users?.map((user) => new User(user)) ?? [];
    this.departments = data.departments.map((department) => new Department(department));
    this.project_chemicals = data.project_chemicals.map(
      (project_chemical) => new ProjectChemical(project_chemical)
    );
    this.analytical_techniques = data.analytical_techniques.map(
      (analytical_technique) => new Technique(analytical_technique)
    );
  }

  get getName() {
    return this.name + ' (' + this.getStatusTitle + ')';
  }

  get getCode() {
    return this.getName;
  }

  get isActive() {
    return this.status === 'active';
  }

  get getIsActive() {
    return this.status === 'active';
  }

  get getStatusTitle() {
    switch (this.status) {
      case ProjectStatusNew.ACTIVE:
        return 'Aktivní';
      case ProjectStatusNew.INACTIVE:
        return 'Neaktivní';
      case ProjectStatusNew.CLOSED:
        return 'Uzavřen';
    }
  }

  get hasAnalyticalDepartment() {
    return this.departments.some((department) => department.type === DepartmentEnum.ANALYTICAL);
  }

  get hasSyntheticDepartment() {
    return this.departments.some((department) => department.type === DepartmentEnum.SYNTHETIC);
  }

  get hasTechnicalDepartment() {
    return this.departments.some((department) => department.type === DepartmentEnum.TECHNICAL);
  }

  get getShortcuts() {
    const shortcuts: string[] = [];

    this.departments.forEach((department) => {
      shortcuts.push(department.shortcut);
    });

    return shortcuts.join(' / ');
  }
}

interface ProjectModalOptionsI {
  isEditing: boolean;
  isCreating: boolean;
  baseData: Project | undefined;
  newData: NewProjectModalDataI | undefined;
}

interface NewProjectModalDataI {
  name: string | undefined;
  status: string;
  responsible_users: number[] | undefined;

  synthetic_department: boolean | false;
  analytical_department: boolean | false;
  technological_department: boolean | false;

  synthetic_department_shortcut?: string;
  synthetic_department_number?: string;

  technological_department_shortcut?: string;
  technological_department_number?: string;

  confirm: boolean | false;
}

interface ProjectBatchDto {
  project_id: number;
  user_id: number;
  name: string;
  status: string;
  responsible_users: UserDto[] | null | undefined;
  created_at: string;
  updated_at: string;
  departments: DepartmentDto[];
  analytical_techniques: TechniqueDto[];
  project_chemicals: ProjectChemicalDto[];
}

export interface ProjectBatchI {
  project_id: number;
  user_id: number;
  name: string;
  status: ProjectStatus;
  responsible_users: User[] | null | undefined;
  created_at: Date;
  updated_at: Date;
  departments: Department[];
  analytical_techniques: Technique[];
  project_chemicals: ProjectChemical[];
}

export interface DependenciesDto {
  chapters?: ChapterDto[];
  form?: newFormDto[];
  departments?: DepartmentDto[];
}
export interface DependenciesI {
  chapters?: Chapter[];
  form?: newForm[];
  departments?: Department[];
}
export class Dependencies extends BaseConstructor<DependenciesI>() implements DependenciesI {
  constructor(data: DependenciesDto) {
    super(data as unknown as DependenciesI);
    if (data.chapters) {
      this.chapters = data.chapters.map((chapter) => new Chapter(chapter));
    }
    if (data.form) {
      this.form = data.form.map((f) => new newForm(f));
    }
    if (data.departments) {
      this.departments = data.departments.map((department) => new Department(department));
    }
  }
}

export interface canCloseDto {
  count_of_missing_dependencies: number;
  missing_dependencies?: DependenciesDto | undefined;
}

export interface canCloseI {
  count_of_missing_dependencies: number;
  missing_dependencies?: Dependencies | undefined;
}

export class canClose extends BaseConstructor<canCloseI>() implements canCloseI {
  constructor(data: canCloseDto) {
    super(data as unknown as canCloseI);
    if (data.missing_dependencies) {
      this.missing_dependencies = new Dependencies(data.missing_dependencies);
    }
  }
}

export class ProjectBatch extends BaseConstructor<ProjectBatchI>() implements ProjectBatchI {
  constructor(data: ProjectBatchDto) {
    super(data as unknown as ProjectBatchI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    this.responsible_users = data.responsible_users?.map((user) => new User(user)) ?? [];
    this.departments = data.departments.map((department) => new Department(department));
    this.project_chemicals = data.project_chemicals.map(
      (project_chemical) => new ProjectChemical(project_chemical)
    );
    this.analytical_techniques = data.analytical_techniques.map(
      (analytical_technique) => new Technique(analytical_technique)
    );
  }

  get getName() {
    return this.name + ' (' + this.getStatusTitle + ')';
  }

  get getCode() {
    return this.getName;
  }

  get isActive() {
    return this.status === 'active';
  }

  get getIsActive() {
    return this.status === 'active';
  }

  get getStatusTitle() {
    switch (this.status) {
      case ProjectStatus.ACTIVE:
        return 'Aktivní';
      case ProjectStatus.INACTIVE:
        return 'Neaktivní';
      case ProjectStatus.DELETED:
        return 'Smazán';
    }
  }

  get hasAnalyticalDepartment() {
    return this.departments.some((department) => department.type === DepartmentEnum.ANALYTICAL);
  }

  get hasSyntheticDepartment() {
    return this.departments.some((department) => department.type === DepartmentEnum.SYNTHETIC);
  }

  get hasTechnicalDepartment() {
    return this.departments.some((department) => department.type === DepartmentEnum.TECHNICAL);
  }

  get getShortcuts() {
    const shortcuts: string[] = [];

    this.departments.forEach((department) => {
      shortcuts.push(department.shortcut);
    });

    return shortcuts.join(' / ');
  }
}

export interface ProjectUsageHistoryDto {
  technique?: TechniqueDto;
  samples: InstrumentUsageHistoryProjectSampleDto[];
}

export interface ProjectUsageHistoryI {
  technique?: Technique;
  samples: InstrumentUsageHistoryProjectSample[];
}

export class ProjectUsageHistory
  extends BaseConstructor<ProjectUsageHistoryI>()
  implements ProjectUsageHistoryI
{
  constructor(data: ProjectUsageHistoryDto) {
    super(data as unknown as ProjectUsageHistoryI);
    if (data.technique) {
      this.technique = new Technique(data.technique);
    }
    this.samples = data.samples.map((sample) => new InstrumentUsageHistoryProjectSample(sample));
  }
}

enum SearchTables {
  ANALYTICAL_REQUEST = 'analytical_request',
  BATCH_NUMBER = 'batch_number',
  FORM = 'form',
  METHOD = 'method',
  CHEMICAL = 'chemical',
  TAG = 'tag',
  CHAPTER = 'chapter',
  PROJECT_DEPARTMENT = 'project_department',
  SAMPLE = 'sample',
  STANDARD = 'standard',
  PROJECT_CHEMICAL = 'project_chemical',
  ANALYTICAL_TECHNIQUE = 'analytical_technique',
  PROJECT = 'project'
}

export interface searchTables {
  default: boolean;
  table: string;
  canChange: boolean;
  translate: string;
}
interface ProjectsStateI {
  projects: Map<number, Project>;
  department: Department | null;
  chapter: Chapter | null;

  projectTechniques: Technique[];
  permissions: ProjectPermission[];
  roles: ProjectRole[];
  role: ProjectRole | undefined;
  searchTables: searchTables[];
  start_date: Date | undefined;
  end_date: Date | undefined;
  projectUser:
    | undefined
    | {
        project_id: number;
        user: User;
        roles: number[];
        permissions: number[];
      };
  project: Project | null;
  loading: boolean;
  loadingSearch: boolean;
  from_datetime: Date | undefined;
  to_datetime: Date | undefined;
  showProjectModal: boolean | false;
  showProjectRoleModal: boolean | false;
  showProjectDetailModal: boolean | false;
  showProjectDetailUserRolesModal: boolean | false;
  modalOptions: ProjectModalOptionsI | undefined;
  historyItems: ProjectUsageHistory[];
  historyLength?: number;
  totalItems?: number;
  options: ServerOptions;
  search: string | undefined;

  allProjectsOptionsPage: {
    my_projects: PaginatorRequestDataI<SimpleProject>;
    all_projects: PaginatorRequestDataI<SimpleProject>;
    active_project: PaginatorRequestDataI<SimpleProject>;
  };
  allProjectsOptionsPageSearch: {
    all_projects: PaginatorRequestDataI<ExtendedProject>;
    analytical_request: {
      search: string | undefined;
      limit: number;
    };
    analytical_technique: {
      search: string | undefined;
      limit: number;
    };
    project: {
      search: string | undefined;
      limit: number;
    };
    form: {
      search: string | undefined;
      limit: number;
    };
    batch_numbers: {
      search: string | undefined;
      limit: number;
    };
    tag: {
      search: string | undefined;
      limit: number;
    };
    chemical: {
      search: string | undefined;
      limit: number;
    };
    project_chemical: {
      search: string | undefined;
      limit: number;
    };
    chapter: {
      search: string | undefined;
      limit: number;
    };
    method: {
      search: string | undefined;
      limit: number;
    };
    project_department: {
      search: string | undefined;
      limit: number;
    };
    sample: {
      search: string | undefined;
      limit: number;
    };
    standard: {
      search: string | undefined;
      limit: number;
    };
  };
  tables: string[];
  extendsSearch: ExtendedSearchI;
  closeDependencies: canClose | undefined;
  project_permision: boolean;
  projectUsers: ProjectUser[] | [];
}

export interface PaginatorRequestDataI<T = unknown> {
  loading: boolean;
  search: string | undefined;
  search_type: 'AND' | 'OR';
  search_columns: string[];

  totalItems: number;
  options: ServerOptions;

  filterOptions: null | Array<{
    column: string;
    value: null | number | string | string[] | number[] | object | object[] | number[];
  }>;

  results: T[];
}

export const useProjectsStore = defineStore({
  id: 'projects',
  state: () =>
    ({
      project: null,
      department: null,
      chapter: null,
      searchTables: [],

      projectTechniques: [],
      projectUser: undefined,
      projects: new Map(),
      loading: false,
      loadingSearch: false,
      start_date: undefined,
      end_date: undefined,
      tables: [],
      from_datetime: new Date() as Date,
      to_datetime: new Date() as Date,
      showProjectDetailUserRolesModal: false,
      showProjectDetailModal: false,
      showProjectRoleModal: false,
      showProjectModal: false,
      modalOptions: undefined,
      project_permision: true,
      extendsSearch: new ExtendedSearch({
        analytical_requests: [],
        analytical_techniques: [],
        batch_numbers: [],
        chapters: [],
        forms: [],
        methods: [],
        project_departments: [],
        projects: [],
        samples: [],
        standards: [],
        tags: [],
        chemicals: [],
        project_chemicals: []
      }) as ExtendedSearchI,
      projectUsers: [],

      historyItems: [],
      historyLength: 0,
      search: undefined,
      totalItems: undefined,
      options: {
        page: 1,
        rowsPerPage: 25,
        sortBy: [],
        sortType: ['desc', 'asc']
      },

      role: undefined,
      permissions: [],
      roles: [],
      closeDependencies: undefined,
      allProjectsOptionsPage: {
        my_projects: {
          loading: false,
          results: [],

          search: undefined,
          search_type: 'AND',

          search_columns: [],
          totalItems: 0,
          options: {
            page: 1,
            rowsPerPage: 25,
            sortBy: ['status', 'project_id'],
            sortType: ['asc']
          },
          filterOptions: null
        },
        active_project: {
          loading: false,
          results: [],

          search: undefined,
          search_type: 'AND',

          search_columns: [],
          totalItems: 0,
          options: {
            page: 1,
            rowsPerPage: 25,
            sortBy: ['status', 'project_id'],
            sortType: ['asc']
          },
          filterOptions: null
        },
        all_projects: {
          loading: false,
          results: [],

          search: undefined,
          search_type: 'AND',

          search_columns: [],
          totalItems: 0,
          options: {
            page: 1,
            rowsPerPage: 25,
            sortBy: ['status', 'project_id'],
            sortType: ['asc']
          },
          filterOptions: null
        }
      },
      allProjectsOptionsPageSearch: {
        all_projects: {
          loading: false,
          results: [],
          search: undefined,
          search_type: 'OR',
          search_columns: [],
          totalItems: 0,
          options: {
            page: 1,
            rowsPerPage: 25,
            sortBy: ['status', 'project_id'],
            sortType: ['asc']
          },
          filterOptions: null
        },
        analytical_request: {
          search: undefined,
          limit: 15
        },
        analytical_technique: {
          search: undefined,
          limit: 15
        },
        project: {
          search: undefined,
          limit: 15
        },
        form: {
          search: undefined,
          limit: 15
        },
        batch_numbers: {
          search: undefined,
          limit: 15
        },
        tag: {
          search: undefined,
          limit: 15
        },
        chemical: {
          search: undefined,
          limit: 15
        },
        project_chemical: {
          search: undefined,
          limit: 15
        },
        chapter: {
          search: undefined,
          limit: 15
        },
        method: {
          search: undefined,
          limit: 15
        },
        project_department: {
          search: undefined,
          limit: 15
        },
        sample: {
          search: undefined,
          limit: 15
        },
        standard: {
          search: undefined,
          limit: 15
        }
      }
    }) as ProjectsStateI,
  actions: {
    getProjectStatus(status: string) {
      switch (status) {
        case ProjectStatusNew.ACTIVE:
          return 'Aktivní';
        case ProjectStatusNew.INACTIVE:
          return 'Neaktivní';
        case ProjectStatusNew.CLOSED:
          return 'Uzavřen';
      }
    },

    setProjects(projects: ProjectDto[]) {
      this.projects = new Map(
        projects.map((project) => [project.project_id, new Project(project)])
      );
    },

    setProject(project: ProjectDto | Project | undefined | null) {
      this.project = project instanceof Project ? project : project ? new Project(project) : null;
    },

    async getAll(
      setData: boolean = true,
      options: GetAllOptions | undefined = {
        search_columns: ['name']
      }
    ): Promise<{
      data: Project[];
      totalItems: number;
    }> {
      this.loading = true;

      const URL =
        baseUrl +
        '/?' +
        stringifyServerOptions(
          options && options.options ? options.options : this.options,
          options && options.search ? options.search : (this.search ?? null),
          options && options.search_columns ? options.search_columns : [],
          options && options.fixedFilterOptions ? options.fixedFilterOptions : null
        );

      return fetchWrapper
        .get(URL)
        .then((res: BasePaginatorResponseI<ProjectDto>) => {
          if (res.status_code === 200) {
            this.totalItems = res.data.total_items;
            if (setData) {
              this.projects = new Map(
                res.data.items.map((project) => [project.project_id, new Project(project)])
              );
            }

            this.loading = false;
            return {
              data: res.data.items.map((project) => new Project(project)),
              totalItems: res.data.total_items
            };
          }
          this.loading = false;
          return {
            data: [],
            totalItems: 0
          };
        })
        .catch((res) => {
          if (res.status_code === 400) {
            loadParamsFailedReloadNotification('Načtení projektů selhalo', res.error, () => {
              const data = reloadWithoutParams();
              this.options = data.options;
              this.search = data.term ?? undefined;
            });
          }

          this.loading = false;
          return {
            data: [],
            totalItems: 0
          };
        });
    },

    async getProject(id: number) {
      this.loading = true;
      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/project/${id}`)
        .then(async (res: BaseResponseI<ProjectDto>) => {
          this.loading = false;

          if (res.status_code === 200) {
            this.project = new Project(res.data);
            this.projectUsers = (await this.getProjectUsers(id)) ?? [];
            const allUsers = [...(this.project?.responsible_users || []), ...this.projectUsers];

            if (isAllowed(['edit_all'])) {
              this.project_permision = true;
            } else if (
              this.project &&
              allUsers &&
              allUsers.length > 0 &&
              user.value &&
              typeof user.value.user_id === 'number' &&
              allUsers &&
              !allUsers.some((userObj) => userObj.user_id === user.value?.user_id)
            ) {
              this.project_permision = false;
            } else {
              this.project_permision = true;
            }
            return new Project(res.data);
          }
        })
        .catch((res) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Načtení projektu selhalo', description: res.error });
          }

          this.loading = false;
          return undefined;
        });
    },

    async getDepartment(id: number) {
      this.loading = true;

      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/project-department/${id}`)
        .then((res: BaseResponseI<DepartmentDto>) => {
          this.loading = false;
          if (res.status_code === 200) {
            return new Department(res.data);
          }
        })
        .catch((res) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Načtení oddělení selhalo', description: res.error });
          }

          this.loading = false;
          return undefined;
        });
    },

    async getProjectById(id: number) {
      if (!id) {
        return;
      }
      const Project = await this.getProject(id);
      if (Project) {
        this.setProject(Project);
        return Project;
      } else {
        notification.error({
          message: 'Chyba',
          description: 'Projekt nebyl nalezen nebo nemáte oprávnění k jeho zobrazení.'
        });
        router.push({ name: 'ListOfProjects' });
      }
    },

    async getDepartmentById(id: number) {
      const Department = await this.getDepartment(id);

      if (Department) {
        this.department = Department;
      } else {
        notification.error({
          message: 'Chyba',
          description: 'Oddělení nebylo nalezeno nebo nemáte oprávnění k jeho zobrazení.'
        });
        if (this.project) {
          router.push({
            name: 'ProjectDetail',
            params: { project_id: this.project.project_id.toString() }
          });
        } else {
          router.push({ name: 'ListOfProjects' });
        }
      }
    },

    async getChapter(id: number) {
      this.loading = true;

      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/chapter/${id}`)
        .then((res: BaseResponseI<ChapterDto>) => {
          this.loading = false;

          if (res.status_code === 200) {
            return new Chapter(res.data);
          }
        })
        .catch((res) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Načtení kapitoly selhalo', description: res.error });
          }

          this.loading = false;
          return undefined;
        });
    },

    async getChapterById(id: number) {
      const _Chapter = await this.getChapter(id);

      if (_Chapter !== undefined) {
        this.chapter = _Chapter;
      } else {
        notification.error({
          message: 'Chyba',
          description: 'Kapitola nebyla nalezena nebo nemáte oprávnění k jejímu zobrazení.'
        });
        if (this.department) {
          router.push({
            name: 'DepartmentDetail',
            params: { project_department_id: this.department.project_department_id.toString() }
          });
        } else {
          router.push({ name: 'ListOfProjects' });
        }
      }
    },

    async showRoleModal(id: number) {
      const Project = await this.getProject(id);
      if (Project === undefined) {
        notification.error({ message: 'Projekt nebyl nalezen' });
        return;
      }

      this.project = Project;

      this.modalOptions = {
        isEditing: true,
        isCreating: false,
        baseData: this.project ?? undefined,
        newData: {
          name: this.project.name,
          status: this.project.status,
          responsible_users: this.project.responsible_users?.map((u) => u.user_id) ?? [],

          synthetic_department: this.project.hasSyntheticDepartment,
          analytical_department: this.project.hasAnalyticalDepartment,
          technological_department: this.project.hasTechnicalDepartment,

          synthetic_department_shortcut: this.project.departments.find(
            (department) => department.type === DepartmentEnum.SYNTHETIC
          )?.shortcut,
          synthetic_department_number: this.project.departments.find(
            (department) => department.type === DepartmentEnum.SYNTHETIC
          )?.number,

          technological_department_shortcut: this.project.departments.find(
            (department) => department.type === DepartmentEnum.TECHNICAL
          )?.shortcut,
          technological_department_number: this.project.departments.find(
            (department) => department.type === DepartmentEnum.TECHNICAL
          )?.number,

          confirm: false
        }
      };

      this.showProjectModal = true;
    },

    async showEditModal(id: number) {
      const Project = await this.getProject(id);
      if (Project === undefined) {
        notification.error({ message: 'Projekt nebyl nalezen' });
        return;
      }
      this.project = Project;

      this.modalOptions = {
        isEditing: true,
        isCreating: false,
        baseData: this.project ?? undefined,
        newData: {
          name: this.project.name,
          status: this.project.status,
          responsible_users: this.project.responsible_users?.map((u) => u.user_id) ?? [],

          synthetic_department: this.project.hasSyntheticDepartment,
          analytical_department: this.project.hasAnalyticalDepartment,
          technological_department: this.project.hasTechnicalDepartment,

          synthetic_department_shortcut:
            this.project.departments.find(
              (department) => department.type === DepartmentEnum.SYNTHETIC
            )?.shortcut ||
            this.project.departments.find(
              (department) => department.type === DepartmentEnum.ANALYTICAL
            )?.shortcut,
          synthetic_department_number:
            this.project.departments.find(
              (department) => department.type === DepartmentEnum.SYNTHETIC
            )?.number ||
            this.project.departments.find(
              (department) => department.type === DepartmentEnum.ANALYTICAL
            )?.number,

          technological_department_shortcut: this.project.departments.find(
            (department) => department.type === DepartmentEnum.TECHNICAL
          )?.shortcut,
          technological_department_number: this.project.departments.find(
            (department) => department.type === DepartmentEnum.TECHNICAL
          )?.number,

          confirm: false
        }
      };

      this.showProjectModal = true;
    },

    async showPreviewModal(id: number) {
      const Project = await this.getProject(id);
      if (Project === undefined) {
        notification.error({ message: 'Projekt nebyl nalezen' });
        return;
      }

      this.project = Project;

      this.modalOptions = {
        isEditing: false,
        isCreating: false,
        baseData: this.project ?? undefined,
        newData: {
          name: this.project.name,
          status: this.project.status,
          responsible_users: this.project.responsible_users?.map((u) => u.user_id) ?? [],

          synthetic_department: this.project.hasSyntheticDepartment,
          analytical_department: this.project.hasAnalyticalDepartment,
          technological_department: this.project.hasTechnicalDepartment,

          synthetic_department_shortcut: this.project.departments.find(
            (department) => department.type === DepartmentEnum.SYNTHETIC
          )?.shortcut,
          synthetic_department_number: this.project.departments.find(
            (department) => department.type === DepartmentEnum.SYNTHETIC
          )?.number,

          technological_department_shortcut: this.project.departments.find(
            (department) => department.type === DepartmentEnum.TECHNICAL
          )?.shortcut,
          technological_department_number: this.project.departments.find(
            (department) => department.type === DepartmentEnum.TECHNICAL
          )?.number,

          confirm: false
        }
      };

      this.showProjectModal = true;
    },

    showNewProjectModal() {
      this.modalOptions = {
        isEditing: false,
        isCreating: true,
        baseData: undefined,
        newData: {
          name: undefined,
          status: 'active',
          responsible_users: undefined,
          synthetic_department: false,
          analytical_department: false,
          technological_department: false,

          confirm: false
        }
      };

      this.showProjectModal = true;
    },

    async createProject() {
      if (!this.modalOptions?.newData || this.modalOptions?.newData?.confirm === false) {
        notification.error({ message: 'Musíte potvrdit souhlas s vytvořením projektu' });
        return false;
      }

      this.loading = true;

      const data = {
        name: this.modalOptions.newData.name,
        status: this.modalOptions.newData.status,
        responsible_users: this.modalOptions.newData.responsible_users,

        synthetic_department: this.modalOptions.newData.synthetic_department ?? false,
        analytical_department: this.modalOptions.newData.analytical_department ?? false,
        technological_department: this.modalOptions.newData.technological_department ?? false,

        synthetic_department_shortcut:
          this.modalOptions.newData.synthetic_department ||
          this.modalOptions.newData.analytical_department
            ? (this.modalOptions.newData.synthetic_department_shortcut ?? null)
            : null,
        synthetic_department_number:
          this.modalOptions.newData.synthetic_department ||
          this.modalOptions.newData.analytical_department
            ? (this.modalOptions.newData.synthetic_department_number ?? null)
            : null,

        technological_department_shortcut: this.modalOptions.newData.technological_department
          ? (this.modalOptions.newData.technological_department_shortcut ?? null)
          : null,
        technological_department_number: this.modalOptions.newData.technological_department
          ? (this.modalOptions.newData.technological_department_number ?? null)
          : null
      };

      return await fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/project/`, data)
        .then((res: BaseResponseI<ProjectDto>) => {
          if (res.status_code === 200) {
            this.projects.set(res.data.project_id, new Project(res.data));
            this.showProjectModal = false;

            notification.success({
              message: 'Vytvoření projektu proběhlo v pořádku',
              description: 'Název: ' + res.data.name
            });

            this.loading = false;
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Vytvoření projektu selhalo', description: res.error });
          } else {
            this.showProjectModal = false;
          }

          this.loading = false;
          return false;
        });
    },

    async updateProject() {
      if (!this.modalOptions?.newData) {
        notification.error({ message: 'Není co upravovat' });
        return false;
      }

      this.loading = true;

      const data = {
        name: this.modalOptions.newData.name,
        //status: this.modalOptions.newData.status,
        responsible_users: this.modalOptions.newData.responsible_users,

        synthetic_department: this.modalOptions.newData.synthetic_department ?? false,
        analytical_department: this.modalOptions.newData.analytical_department ?? false,
        technological_department: this.modalOptions.newData.technological_department ?? false,

        synthetic_department_shortcut:
          this.modalOptions.newData.synthetic_department ||
          this.modalOptions.newData.analytical_department
            ? (this.modalOptions.newData.synthetic_department_shortcut ?? null)
            : null,
        synthetic_department_number:
          this.modalOptions.newData.synthetic_department ||
          this.modalOptions.newData.analytical_department
            ? (this.modalOptions.newData.synthetic_department_number ?? null)
            : null,

        technological_department_shortcut: this.modalOptions.newData.technological_department
          ? (this.modalOptions.newData.technological_department_shortcut ?? null)
          : null,
        technological_department_number: this.modalOptions.newData.technological_department
          ? (this.modalOptions.newData.technological_department_number ?? null)
          : null
      };
      return await fetchWrapper
        .put(`${import.meta.env.VITE_API_URL}/project/${this.project?.project_id}`, data)
        .then((res: BaseResponseI<ProjectDto>) => {
          if (res.status_code === 200) {
            this.projects.set(res.data.project_id, new Project(res.data));
            this.showProjectModal = false;

            notification.success({
              message: 'Úprava projektu proběhla v pořádku',
              description: 'Název: ' + res.data.name
            });

            this.loading = false;
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400 || res.status_code === 422) {
            notification.error({ message: 'Úprava projektu selhala', description: res.error });
          } else {
            this.showProjectModal = false;
          }

          this.loading = false;
          return false;
        });
    },
    async updateProjectStatus(force: boolean): Promise<boolean> {
      if (!this.modalOptions?.newData) {
        notification.error({ message: 'Není co upravovat' });
        return false;
      }
      this.loading = true;
      const data = {
        status: this.modalOptions.newData.status,
        brute_force_close: force
      };
      return fetchWrapper
        .patch(`${import.meta.env.VITE_API_URL}/project/${this.project?.project_id}/status`, data)
        .then((res: BaseResponseI<ProjectDto>) => {
          if (res.status_code === 200) {
            this.projects.set(res.data.project_id, new Project(res.data));
            this.showProjectModal = false;

            notification.success({
              message: 'Úprava stavu projektu proběhla v pořádku',
              description: 'Název: ' + res.data.name
            });

            this.loading = false;
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400 || res.status_code === 422) {
            notification.error({ message: 'Úprava projektu selhala', description: res.error });
          } else {
            this.showProjectModal = false;
          }

          this.loading = false;
          return false;
        });
    },
    async checkIfCanCloseProject(project_id: number): Promise<canClose | false> {
      if (!project_id) {
        notification.error({ message: 'Není co zavírat' });
        return false;
      }
      this.loading = true;
      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/project/${project_id}/can_be_closed`)
        .then((res: BaseResponseI<canCloseDto>) => {
          if (res.status_code === 200) {
            this.closeDependencies = new canClose(res.data);
            this.loading = false;
            return this.closeDependencies;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Zjištění selhalo', description: res.error });
          }

          this.loading = false;
          return false;
        });
    },
    async checkIfCanCloseDepartment(department_id: number): Promise<canClose | false> {
      if (!department_id) {
        notification.error({ message: 'Není co zavírat' });
        return false;
      }
      this.loading = true;
      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/project-department/${department_id}/can_be_closed`)
        .then((res: BaseResponseI<canCloseDto>) => {
          if (res.status_code === 200) {
            this.closeDependencies = new canClose(res.data);
            this.loading = false;
            return this.closeDependencies;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Zjištění selhalo', description: res.error });
          }

          this.loading = false;
          return false;
        });
    },
    resetModal() {
      this.showProjectModal = false;
      if (this.modalOptions) this.modalOptions.baseData = undefined;
      if (this.modalOptions) this.modalOptions.newData = undefined;
    },

    closeProject(id: number, force: boolean) {
      if (!id) {
        notification.error({ message: 'Není co zavírat' });
        return false;
      }
      this.loading = true;
      const data = {
        status: 'closed',
        brute_force_close: force
      };

      return fetchWrapper
        .patch(`${import.meta.env.VITE_API_URL}/project/${id}/status`, data)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            // this.projects.delete(id);
            notification.success({ message: 'Projekt byl úspěšně uzavřen' });
            this.getAll();
            this.loading = false;

            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Zavření projektu selhalo', description: res.error });
          }

          this.loading = false;
          return false;
        });
    },
    closeDepartment(id: number, force: boolean) {
      if (!id) {
        notification.error({ message: 'Není co zavírat' });
        return false;
      }
      this.loading = true;
      const data = {
        status: 'closed',
        brute_force_close: force
      };

      return fetchWrapper
        .patch(`${import.meta.env.VITE_API_URL}/project-department/${id}/status`, data)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            //this.projects.delete(id);
            notification.success({ message: 'Oddělení bylo úspěšně uzavřeno' });
            this.getAll();
            this.loading = false;

            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Zavření oddělení selhalo', description: res.error });
          }

          this.loading = false;
          return false;
        });
    },
    reactivateDepartment(id: number) {
      if (!id) {
        notification.error({ message: 'Není co otvírat' });
        return false;
      }
      this.loading = true;
      const data = {
        status: 'active',
        brute_force_close: false
      };

      return fetchWrapper
        .patch(`${import.meta.env.VITE_API_URL}/project-department/${id}/status`, data)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            // this.projects.delete(id);
            notification.success({ message: 'Oddělení bylo úspěšně otevřeno' });
            this.getAll();
            this.loading = false;

            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Aktivace oddělení selhala', description: res.error });
          }

          this.loading = false;
          return false;
        });
    },
    deactivateProject(id: number, force: boolean) {
      this.loading = true;
      const data = {
        status: 'deactivated',
        brute_force_close: force
      };

      return fetchWrapper
        .patch(`${import.meta.env.VITE_API_URL}/project/${id}/status`, data)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            //this.projects.delete(id);
            notification.success({ message: 'Projekt byl úspěšně deaktivován' });
            this.getAll();
            this.loading = false;

            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Deaktivace projektu selhalo', description: res.error });
          }

          this.loading = false;
          return false;
        });
    },
    reactivateProject(id: number) {
      if (!id) {
        notification.error({ message: 'Není co otvírat' });
        return false;
      }
      this.loading = true;
      const data = {
        status: 'active',
        brute_force_close: false
      };

      return fetchWrapper
        .patch(`${import.meta.env.VITE_API_URL}/project/${id}/status`, data)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            //this.projects.delete(id);
            notification.success({ message: 'Projekt byl úspěšně otevřen' });
            this.getAll();
            this.loading = false;

            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Aktivace projektu selhala', description: res.error });
          }

          this.loading = false;
          return false;
        });
    },

    async getProjectUsers(id: number) {
      this.loading = true;

      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/project/${id}/users`)
        .then((res: BaseResponseI<ProjectUserDto[]>) => {
          this.loading = false;

          if (res.status_code === 200) {
            return res.data.map((user) => new ProjectUser(user));
          }
        })
        .catch((res) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Načtení uživatelů projektu selhalo',
              description: res.error
            });
          }

          this.loading = false;
          return undefined;
        });
    },

    async getProjectUser(project_id: number, user_id: number) {
      this.loading = true;

      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/project/${project_id}/users/${user_id}`)
        .then((res: BaseResponseI<ProjectUserDetailDto>) => {
          this.loading = false;

          if (res.status_code === 200) {
            return new ProjectUserDetail(res.data);
          }
        })
        .catch((res) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Načtení uživatele projektu selhalo',
              description: res.error
            });
          }

          this.loading = false;
          return undefined;
        });
    },

    async addProjectUser(id: number, user_id: number) {
      this.loading = true;

      return fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/project/${id}/users/${user_id}`)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Uživatel byl úspěšně přidán do projektu' });
            this.loading = false;
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Přidání uživatele do projektu selhalo',
              description: res.error
            });
          }

          this.loading = false;
          return false;
        });
    },

    async updateProjectUserRoleAndPermissions(
      project_id: number,
      user_id: number,
      roles: number[],
      permissions: number[]
    ) {
      this.loading = true;

      return fetchWrapper
        .put(
          `${import.meta.env.VITE_API_URL}/project/${project_id}/users/${user_id}/roles_permissions`,
          {
            roles: roles,
            permissions: permissions
          }
        )
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Role a oprávnění uživatele byly úspěšně upraveny' });
            this.loading = false;
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Upravení role a oprávnění uživatele selhalo',
              description: res.error
            });
          }

          this.loading = false;
          return false;
        });
    },

    async removeProjectUser(id: number, user_id: number) {
      this.loading = true;

      return fetchWrapper
        .delete(`${import.meta.env.VITE_API_URL}/project/${id}/users/${user_id}`)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Uživatel byl úspěšně odebrán z projektu' });
            this.loading = false;
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Odebrání uživatele z projektu selhalo',
              description: res.error
            });
          }

          this.loading = false;
          return false;
        });
    },

    async getProjectAnalyticalTechniques(id: number) {
      this.loading = true;

      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/project/${id}/analytical_techniques`)
        .then((res: BaseResponseI<TechniqueDto[]>) => {
          this.loading = false;

          if (res.status_code === 200) {
            this.projectTechniques = res.data.map((technique) => new Technique(technique));
            return res.data.map((technique) => new Technique(technique));
          }
        })
        .catch((res) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Načtení analytických technik projektu selhalo',
              description: res.error
            });
          }

          this.loading = false;
          return undefined;
        });
    },

    async addProjectAnalyticalTechnique(id: number, technique_id: number) {
      this.loading = true;

      return fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/project/${id}/analytical_technique/${technique_id}`)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({
              message: 'Analytická technika byla úspěšně přidána do projektu'
            });
            this.loading = false;
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Přidání analytické techniky do projektu selhalo',
              description: res.error
            });
          }

          this.loading = false;
          return false;
        });
    },

    async removeProjectAnalyticalTechnique(id: number, technique_id: number) {
      this.loading = true;

      return fetchWrapper
        .delete(
          `${import.meta.env.VITE_API_URL}/project/${id}/analytical_technique/${technique_id}`
        )
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({
              message: 'Analytická technika byla úspěšně odebrána z projektu'
            });
            this.loading = false;
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Odebrání analytické techniky z projektu selhalo',
              description: res.error
            });
          }

          this.loading = false;
          return false;
        });
    },

    async createProjectChemical(
      id: number,
      data: {
        csa: string;
        shortcut: string;
        name: string;
        density: number;
        molar_mass: number;
        status: string;
      }
    ) {
      this.loading = true;

      return fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/project/${id}/chemicals`, data)
        .then((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 200) {
            notification.success({ message: 'Chemikálie byla úspěšně přidána do projektu' });
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Přidání chemikálie do projektu selhalo',
              description: res.error
            });
          }

          this.loading = false;
          return false;
        });
    },

    async getProjectChemicals(id: number) {
      this.loading = true;

      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/project/${id}/chemicals`)
        .then((res: BaseResponseI<ProjectChemicalDto[]>) => {
          this.loading = false;

          if (res.status_code === 200) {
            return res.data.map((chemical) => new ProjectChemical(chemical));
          }
        })
        .catch((res) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Načtení chemikálií projektu selhalo',
              description: res.error
            });
          }

          this.loading = false;
          return undefined;
        });
    },

    async getProjectRoles(setData: boolean = true) {
      this.loading = true;

      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/project-role/`)
        .then((res: BaseResponseI<ProjectRoleDto[]>) => {
          this.loading = false;

          if (res.status_code === 200) {
            if (setData) {
              this.roles = res.data.map((role) => new ProjectRole(role));
            }

            return res.data;
          }
        })
        .catch((res) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Načtení rolí projektu selhalo',
              description: res.error
            });
          }

          this.loading = false;
          return undefined;
        });
    },

    async getProjectPermissions(setData: boolean = true) {
      this.loading = true;

      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/project-permission/`)
        .then((res: BaseResponseI<ProjectPermissionDto[]>) => {
          this.loading = false;

          if (res.status_code === 200) {
            if (setData) {
              this.permissions = res.data.map((permission) => new ProjectPermission(permission));
            }

            return res.data;
          }
        })
        .catch((res) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Načtení oprávnění projektu selhalo',
              description: res.error
            });
          }

          this.loading = false;
          return undefined;
        });
    },

    async createProjectPermission(data: { name: string }) {
      this.loading = true;

      return fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/project-permission/`, data)
        .then((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 200) {
            notification.success({ message: 'Oprávnění bylo úspěšně přidáno do projektu' });
            this.getProjectPermissions();

            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Přidání oprávnění do projektu selhalo',
              description: res.error
            });
          }

          this.loading = false;
          return false;
        });
    },

    async createProjectRole(data: { name: string; permissions: number[] }) {
      this.loading = true;

      return fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/project-role/`, data)
        .then((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 200) {
            notification.success({ message: 'Role byla úspěšně přidána do projektu' });
            this.getProjectRoles();

            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Přidání role do projektu selhalo',
              description: res.error
            });
          }

          this.loading = false;
          return false;
        });
    },

    async updateProjectRole(role: ProjectRole) {
      this.loading = true;

      const data = {
        name: role.name,
        permissions: role.newPermissions
      };

      return fetchWrapper
        .put(`${import.meta.env.VITE_API_URL}/project-role/${role.project_role_id}`, data)
        .then((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 200) {
            notification.success({ message: 'Role byla úspěšně upravena' });
            this.getProjectRoles();

            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Upravení role selhalo', description: res.error });
          }

          this.loading = false;
          return false;
        });
    },

    async deleteProjectRole(id: number) {
      this.loading = true;

      return fetchWrapper
        .delete(`${import.meta.env.VITE_API_URL}/project-role/${id}`)
        .then((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 200) {
            notification.success({ message: 'Role byla úspěšně smazána' });
            this.getProjectRoles();

            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Smazání role selhalo', description: res.error });
          }

          this.loading = false;
          return false;
        });
    },

    async projectPageGetAllProjects() {
      this.loading = true;

      /*

   const generateMultiColumns = (searchTerm: string | undefined, columns: string[]) => {
    if (!searchTerm) return [];
    const remainingColumns = columns.slice(1);
    return remainingColumns.length > 0 ? remainingColumns.map(column => ({ [column]: [searchTerm] })) : [];
  };
*/
      const my_projects = prepareJsonParams(
        this.allProjectsOptionsPage.my_projects.options,
        this.allProjectsOptionsPage.my_projects.search ?? null,
        this.allProjectsOptionsPage.my_projects.search_columns,
        this.allProjectsOptionsPage.my_projects.filterOptions ?? [],
        this.allProjectsOptionsPage.my_projects.search_type
      );

      const all_projects = prepareJsonParams(
        this.allProjectsOptionsPage.all_projects.options,
        this.allProjectsOptionsPage.all_projects.search ?? null,
        this.allProjectsOptionsPage.all_projects.search_columns,
        this.allProjectsOptionsPage.all_projects.filterOptions ?? [],
        this.allProjectsOptionsPage.all_projects.search_type
      );

      const active_project = prepareJsonParams(
        this.allProjectsOptionsPage.active_project.options,
        this.allProjectsOptionsPage.active_project.search ?? null,
        this.allProjectsOptionsPage.active_project.search_columns,
        this.allProjectsOptionsPage.active_project.filterOptions ?? [],
        this.allProjectsOptionsPage.active_project.search_type
      );
      const params = new URLSearchParams();
      params.append(
        'params',
        JSON.stringify({
          my_projects: my_projects,
          all_projects: all_projects,
          active_project: active_project
        })
      );

      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/projects/all/?` + params.toString())
        .then((res: BaseResponseI<ProjectPageGetAllProjectsResponseDto>) => {
          this.loading = false;

          if (res.status_code === 200) {
            const haveEditTechonolicalPermission = isAllowed(['edit_technological_department']);
            const haveEditSynteticPermission = isAllowed(['edit_syntetic_department']);
            const haveEditAnalyticalPermission = isAllowed(['edit_analytical_department']);

            const haveViewTechonolicalPermission = isAllowed(['view_technological_department']);
            const haveViewSynteticPermission = isAllowed(['view_syntetic_department']);
            const haveViewAnalyticalPermission = isAllowed(['view_analytical_department']);

            this.allProjectsOptionsPage.my_projects.results = res.data.my_projects.items.map(
              (project) => new SimpleProject(project)
            );
            this.allProjectsOptionsPage.active_project.results = res.data.active_projects.items.map(
              (project) => new SimpleProject(project)
            );
            this.allProjectsOptionsPage.all_projects.results = res.data.all_projects.items.map(
              (project) => new SimpleProject(project)
            );

            const filterProjectsByPermissions = (
              projects: any,
              isActiveProject: boolean = false
            ) => {
              return projects.filter((project: any) => {
                if (project.departments && project.departments.length > 0) {
                  const hasPermission = project.departments.some((department: any) => {
                    const departmentType = department.type;

                    const hasEditPermission =
                      (departmentType === DepartmentEnum.TECHNICAL &&
                        haveEditTechonolicalPermission) ||
                      (departmentType === DepartmentEnum.SYNTHETIC && haveEditSynteticPermission) ||
                      (departmentType === DepartmentEnum.ANALYTICAL &&
                        haveEditAnalyticalPermission);

                    const hasViewPermission =
                      (departmentType === DepartmentEnum.TECHNICAL &&
                        haveViewTechonolicalPermission) ||
                      (departmentType === DepartmentEnum.SYNTHETIC && haveViewSynteticPermission) ||
                      (departmentType === DepartmentEnum.ANALYTICAL &&
                        haveViewAnalyticalPermission);

                    if (isActiveProject) {
                      return hasEditPermission;
                    }

                    return hasEditPermission || hasViewPermission;
                  });

                  return hasPermission;
                }
                return false;
              });
            };

            if (
              !(
                haveEditTechonolicalPermission &&
                haveEditSynteticPermission &&
                haveEditAnalyticalPermission &&
                haveViewTechonolicalPermission &&
                haveViewSynteticPermission &&
                haveViewAnalyticalPermission
              )
            ) {
              this.allProjectsOptionsPage.my_projects.results = filterProjectsByPermissions(
                this.allProjectsOptionsPage.my_projects.results
              );
              this.allProjectsOptionsPage.active_project.results = filterProjectsByPermissions(
                this.allProjectsOptionsPage.active_project.results,
                true
              );
              this.allProjectsOptionsPage.all_projects.results = filterProjectsByPermissions(
                this.allProjectsOptionsPage.all_projects.results
              );
            }

            this.allProjectsOptionsPage.my_projects.totalItems = res.data.my_projects.total_items;
            this.allProjectsOptionsPage.active_project.totalItems =
              res.data.active_projects.total_items;
            this.allProjectsOptionsPage.all_projects.totalItems = res.data.all_projects.total_items;

            return true;
          }
        })
        .catch((res) => {
          this.loading = false;
          if (res.status_code === 400) {
            notification.error({ message: 'Načtení projektů selhalo', description: res.error });
          }
        });
    },
    async getAllForSearch() {
      this.loadingSearch = true;
      const globalSearch = this.allProjectsOptionsPageSearch.all_projects.search;

      const searchParams: SearchParamsI = {
        search: globalSearch,
        limit: 15,
        search_params: {},
        start_date: this.start_date,
        end_date: this.end_date
      };

      const activeSearchTables = this.searchTables.filter((table) => table.default);

      if (
        !activeSearchTables.length ||
        (!globalSearch &&
          !(searchParams.search_params && Object.keys(searchParams.search_params).length))
      ) {
        this.loadingSearch = false;
        notification.error({ message: 'Nejsou žádné aktivní tabulky pro vyhledávání' });
        return;
      }

      // Process each active table
      activeSearchTables.forEach((table) => {
        // Check if the table exists in allProjectsOptionsPageSearch
        const tableSearch =
          this.allProjectsOptionsPageSearch[
            table.table as keyof typeof this.allProjectsOptionsPageSearch
          ];

        if (tableSearch) {
          // Add this null check
          const tableSpecificSearch = tableSearch.search?.trim();

          // Only add table if there's either a global search or table-specific search
          if (globalSearch?.trim() || tableSpecificSearch) {
            const finalSearch = tableSpecificSearch
              ? globalSearch?.trim()
                ? `${globalSearch} & ${tableSpecificSearch}` // Both exist: combine them
                : tableSpecificSearch // Only table-specific exists
              : globalSearch?.trim() || ''; // Only global exists or empty

            if (finalSearch) {
              // Only add if we have a search string
              searchParams.search_params![table.table] = {
                search: finalSearch,
                limit: 15
              };
            }
          }
        }
      });

      // If no search_params after filtering, set it to empty object
      if (Object.keys(searchParams.search_params!).length === 0) {
        searchParams.search_params = {};
      }
      const URL =
        `${import.meta.env.VITE_API_URL}/search/all` + '?' + stringifySearchParams(searchParams);

      return fetchWrapper
        .get(URL)
        .then((res: BaseResponseI<ExtendedSearchDto>) => {
          if (res.status_code === 200) {
            this.extendsSearch = new ExtendedSearch(res.data);
            //console.log(res.data);
            this.loadingSearch = false;
            return true;
          }
        })
        .catch((res) => {
          this.loadingSearch = false;
          if (res.status_code === 400) {
            notification.error({ message: 'Vyhledávání selhala', description: res.error });
          }
        });
    },
    async getProjectDepartment(project_department_id: number) {
      this.loading = true;

      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/project-department/${project_department_id}`)
        .then((res: BaseResponseI<DepartmentDto>) => {
          this.loading = false;

          if (res.status_code === 200) {
            this.department = new Department(res.data);
          }
        })
        .catch((res) => {
          this.loading = false;
          if (res.status_code === 400) {
            notification.error({ message: 'Načtení oddělení selhalo', description: res.error });
          }
        });
    },

    async generateProjectDepartmentBatchNumbers(project_department_id: number) {
      this.loading = true;

      return fetchWrapper
        .get(
          `${import.meta.env.VITE_API_URL}/project-department/${project_department_id}/generate_batch_number`
        )
        .then(
          (
            res: BaseResponseI<{
              batch_number: string;
            }>
          ) => {
            this.loading = false;

            if (res.status_code === 200) {
              notification.success({ message: 'Generování čísel šarží proběhlo v pořádku' });
              return res.data.batch_number;
            }

            return undefined;
          }
        )
        .catch((res) => {
          this.loading = false;
          if (res.status_code === 400) {
            notification.error({
              message: 'Generování čísel šarží selhalo',
              description: res.error
            });
          }

          return undefined;
        });
    },

    async getProjectDepartmentSampleNumbers(project_department_id: number) {
      this.loading = true;

      return fetchWrapper
        .get(
          `${import.meta.env.VITE_API_URL}/project-department/${project_department_id}/generate_sample_number`
        )
        .then(
          (
            res: BaseResponseI<{
              sample_number: string;
            }>
          ) => {
            this.loading = false;

            if (res.status_code === 200) {
              notification.success({ message: 'Generování čísel vzorků proběhlo v pořádku' });
              return res.data.sample_number;
            }

            return undefined;
          }
        )
        .catch((res) => {
          this.loading = false;
          if (res.status_code === 400) {
            notification.error({ message: 'Načtení čísel vzorků selhalo', description: res.error });
          }

          return undefined;
        });
    },

    async getProjectBatches(project_id: number, project_department_id: number) {
      this.loading = true;

      const params = new URLSearchParams({ department: project_department_id.toString() });

      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/project/${project_id}/batches?${params}`)
        .then((res: BaseResponseI<ProjectBatchDto[]>) => {
          this.loading = false;

          if (res.status_code === 200) {
            return res.data.map((batch) => new ProjectBatch(batch));
          }
        })
        .catch((res) => {
          this.loading = false;
          if (res.status_code === 400) {
            notification.error({ message: 'Načtení čísel šarží selhalo', description: res.error });
          }
        });
    },
    async getHistoryUsageProject(from_datetime: Date, to_datetime: Date) {
      if (!this.project) {
        notification.error({ message: 'Přístroj nebyl vybrán' });
        return { projects: [] } as unknown as ProjectUsageHistoryI;
      }

      this.loading = true;

      const URL = `${import.meta.env.VITE_API_URL}/project/${this.project.project_id}/history_of_usage?from_datetime=${from_datetime.toISOString()}&to_datetime=${to_datetime.toISOString()}`;

      return fetchWrapper
        .get(URL)
        .then((res: BaseResponseI<ProjectUsageHistoryDto[]>) => {
          this.loading = false;
          if (res.status_code === 200) {
            this.historyItems = res.data.map(
              (history: ProjectUsageHistoryDto) => new ProjectUsageHistory(history)
            );
            this.historyLength = res.data.length;
            return res.data.map((history) => new ProjectUsageHistory(history));
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Načtení přehledu projektů selhalo',
              description: res.error
            });
          }
          this.loading = false;
          return false;
        });
    },
    async requestReopenProject(project_id: number) {
      if (!project_id) {
        notification.error({ message: 'Není zvolen projekt' });
        return false;
      }
      this.loading = true;

      const URL = `${import.meta.env.VITE_API_URL}/project/${project_id}/request_reopen`;

      return fetchWrapper
        .post(URL)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Žádost byla úspešně odeslána' });
            this.loading = false;
          }
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Odeslání žádosti selhalo', description: res.error });
          }

          this.loading = false;
        });
    },
    async requestReopenDepartment(department_id: number) {
      if (!department_id) {
        notification.error({ message: 'Není zvoleno oddělení' });
        return false;
      }
      this.loading = true;

      const URL = `${import.meta.env.VITE_API_URL}/project-department/${department_id}/request_reopen`;

      return fetchWrapper
        .post(URL)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Žádost byla úspešně odeslána' });
            this.loading = false;
          }
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Odeslání žádosti selhalo', description: res.error });
          }

          this.loading = false;
        });
    }
  }
});

export const translateKey = (key: string): string => {
  const translations: { [key: string]: string } = {
    // Project related
    project_id: 'ID projektu',
    name: 'Název',
    status: 'Stav',
    created_at: 'Vytvořeno',
    updated_at: 'Aktualizováno',
    user_id: 'ID uživatele',

    // Department related
    project_department_id: 'ID oddělení',
    shortcut: 'Zkratka',
    number: 'Číslo',
    type: 'Typ',

    // Chapter related
    chapter_id: 'ID kapitoly',
    chapter_title: 'Název kapitoly',

    // Form related
    form_id: 'ID formuláře',
    form_name: 'Název formuláře',
    form_type: 'Typ formuláře',
    batch_number: 'Číslo šarže',
    batch_description: 'Popis šarže',
    affected_batch_number: 'Číslo ovlivněné šarže',

    // Analytical related
    analytical_request_id: 'ID analytické žádosti',
    analytical_technique_id: 'ID analytické techniky',
    method_id: 'ID metody',
    sample_id: 'ID vzorku',
    sample_number: 'Číslo vzorku',

    // Chemical related
    chemical_id: 'ID chemikálie',
    concentration: 'Koncentrace',
    equivalent: 'Ekvivalent',
    density: 'Hustota',
    grams: 'Gramy',
    molar_mass: 'Molární hmotnost',
    moles: 'Moly',
    notes: 'Poznámky',
    volume: 'Objem',

    // Tag related
    tag_id: 'ID štítku',

    // Project chemical related
    project_chemical_id: 'ID chemikálie v projektu',
    csa: 'CSA',

    //method related
    method_name: 'Název metody',

    //technique related
    technique_name: 'Název techniky',
    technique_id: 'ID techniky',
    technique_notes: 'Popis techniky',
    sequence_name: 'Název sekvence'
  };

  return translations[key] || key;
};
