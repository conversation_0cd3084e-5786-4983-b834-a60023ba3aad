import { defineStore } from 'pinia';
import { type RouteLocationNormalized } from 'vue-router';

export const useRedirectStore = defineStore('redirect', {
  state: () => ({
    previousRoute: {} as RouteLocationNormalized | null
  }),
  actions: {
    async setPreviousRoute(route: RouteLocationNormalized) {
      this.previousRoute = route;
    },
    async clearPreviousRoute() {
      this.previousRoute = null;
    }
  }
});
