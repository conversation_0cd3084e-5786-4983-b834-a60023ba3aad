<template>
  <v-card
    variant="outlined"
    :color="sample.colorByStatus"
    class="card-hover-border bg-containerBg"
    :subtitle="`Stav: ${sample.status}`"
    :title="sample.sample_number"
  >
    <!-- <template v-slot:append>
      <v-btn icon @click="samplesStore.(sample.sample_id)">
        <v-icon>mdi-delete</v-icon>
      </v-btn>
    </template> -->
  </v-card>
</template>
<script lang="ts" setup>
  import type { Sample } from '../samples';

  defineEmits(['reload']);
  defineProps<{
    sample: Sample;
  }>();

  // const samplesStore = useSamplesStore();
</script>
