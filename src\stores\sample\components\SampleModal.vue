<script setup lang="ts">
  import EditorTextarea from '@/components/shared/EditorTextarea.vue';
  import TechniquesSelect from '@/components/shared/TechniquesSelect.vue';
  import { useInstrumentsStore } from '@/stores/instruments';
  import ParametersFormTable from '@/stores/method/components/ParametersFormTable.vue';
  import { useMethodsStore } from '@/stores/method/methods';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { useDebounceFn, useVModel } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import type { EditorManager, RawEditorOptions } from 'tinymce';
  import { computed, ref, watch } from 'vue';

  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    }
  });

  const instrumentsStore = useInstrumentsStore();
  const {
    instruments,
    loading: instrumentLoading,
    search: searchInstrument
  } = storeToRefs(instrumentsStore);

  const methodsStore = useMethodsStore();
  const { modalOptions, loading } = storeToRefs(methodsStore);

  const MethodForm = ref();
  async function submitFormToValidate() {
    if (MethodForm.value.isValid && modalOptions.value) {
      switch (true) {
        case modalOptions.value.isEditing && !modalOptions.value.isCreating:
          return methodsStore.updateMethod();
        case !modalOptions.value.isEditing && modalOptions.value.isCreating:
          return methodsStore.createMethod();

        default:
          return 'Náhled metody';
      }
    } else {
      if (modalOptions.value && modalOptions.value.newData) {
        modalOptions.value.newData.confirm = false;
      }
    }
  }

  const showState = useVModel(props, 'show');
  const showTitle = computed(() => {
    if (modalOptions.value === undefined) {
      return 'Náhled metody';
    }

    switch (true) {
      case modalOptions.value.isEditing && !modalOptions.value.isCreating:
        return 'Editace metody';
      case !modalOptions.value.isEditing && modalOptions.value.isCreating:
        return 'Nová metoda';
      default:
        return 'Náhled metody';
    }
  });
  const showSuccessButtonTitle = computed(() => {
    if (modalOptions.value === undefined) {
      return false;
    }

    switch (true) {
      case modalOptions.value.isEditing && !modalOptions.value.isCreating:
        return 'Upravit metodu';
      case !modalOptions.value.isEditing && modalOptions.value.isCreating:
        return 'Přidat metodu';
      default:
        return false;
    }
  });

  const onlyPreview = computed(() => {
    return modalOptions.value?.isCreating === false && modalOptions.value?.isEditing === false;
  });

  const instrumentSearch = ref('');

  watch(instrumentSearch, () => {
    debouncedProjectInstrumentSearch();
  });

  const debouncedProjectInstrumentSearch = useDebounceFn(() => {
    if (instrumentLoading.value === false && instrumentSearch.value !== '') {
      searchInstrument.value = instrumentSearch.value;
      instrumentsStore.getAll();
    }
  }, 350);
</script>
<template>
  <v-dialog v-model="showState" class="customer-modal" :z-index="1010">
    <v-card :loading="loading || instrumentLoading">
      <v-form
        v-if="modalOptions"
        ref="MethodForm"
        class="MethodForm"
        :readonly="onlyPreview"
        @submit.prevent="submitFormToValidate"
      >
        <v-card-title class="pa-5">
          <span class="text-h5">{{ showTitle }}</span>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col>
              <v-row>
                <v-col v-if="modalOptions.newData" cols="12">
                  <v-label class="mb-2">Název metody</v-label>
                  <v-text-field
                    v-model="modalOptions.newData.method_name"
                    :rules="itemRequiredRule"
                    single-line
                    placeholder="Zadejte název metody"
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></v-text-field>
                </v-col>
                <v-col v-if="modalOptions.newData" cols="12">
                  <v-label class="mb-2">Technika</v-label>
                  <TechniquesSelect
                    v-model:selected-techniques="modalOptions.newData.technique_id"
                    :readonly="onlyPreview"
                    :clearable="!onlyPreview"
                    :rules="itemRequiredRule"
                    :filter-options="[
                      { column: 'type', value: ['external', 'internal'] },
                      { column: 'status', value: 'active' }
                    ]"
                  ></TechniquesSelect>
                </v-col>

                <!-- Update section -->

                <v-col v-if="modalOptions.updateData" cols="12">
                  <v-label class="mb-2">Název metody</v-label>
                  <v-text-field
                    v-model="modalOptions.updateData.method_name"
                    :rules="itemRequiredRule"
                    single-line
                    placeholder="Zadejte název metody"
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></v-text-field>
                </v-col>
                <v-col v-if="modalOptions.updateData" cols="12">
                  <v-label class="mb-2">Přístroj</v-label>
                  <v-autocomplete
                    v-model="modalOptions.updateData.instrument_id"
                    v-model:search="instrumentSearch"
                    :readonly="onlyPreview"
                    :clearable="!onlyPreview"
                    hide-details
                    :rules="itemRequiredRule"
                    rounded="sm"
                    :items="
                      [...instruments.values()]
                        .filter((instrument) => instrument.status === 'active')
                        .map((instrument) => {
                          return {
                            value: instrument.instrument_id,
                            title: `${instrument.instrument_shortcut} (${instrument.instrument_id})`,
                            instrument: instrument
                          };
                        })
                    "
                    variant="outlined"
                    color="primary"
                    label="Vyberte přístroj"
                    single-line
                    class="autocomplete"
                    :no-data-text="'Žádná další políčka'"
                    :slim="true"
                    :loading="instrumentLoading"
                  >
                    <template #chip>
                      <v-chip
                        label
                        variant="tonal"
                        color="primary"
                        size="large"
                        class="my-1 text-subtitle-1 font-weight-regular"
                      ></v-chip>
                    </template>

                    <template #item="{ props, item }">
                      <v-list-item v-bind="props" :title="''">
                        <div class="player-wrapper pa-2">
                          <h6 class="text-subtitle-1 mb-0">
                            {{ item.raw.instrument.instrument_shortcut }} ({{
                              item.raw.instrument.instrument_id
                            }})
                          </h6>
                        </div>
                      </v-list-item>
                    </template>
                  </v-autocomplete>
                </v-col>
                <v-col v-if="modalOptions.updateData" cols="12">
                  <v-label class="mb-2">Sekvence</v-label>
                  <EditorTextarea
                    v-model="modalOptions.updateData.sequence_name"
                    :show-edit-button="false"
                    :config="
                      onlyPreview
                        ? ({ statusbar: true, resize: true } as EditorManager & RawEditorOptions)
                        : ({ statusbar: true, resize: true, min_height: 200 } as EditorManager &
                            RawEditorOptions)
                    "
                  ></EditorTextarea>
                </v-col>
                <v-col v-if="modalOptions.updateData" cols="12">
                  <v-label class="mb-2">Parametry</v-label>
                  <ParametersFormTable
                    v-model:="modalOptions.updateData.parameters"
                  ></ParametersFormTable>
                </v-col>
                <v-col v-if="modalOptions.updateData" cols="12">
                  <v-label class="mb-2">Příprava standardu a vzorku</v-label>
                  <EditorTextarea
                    v-model="modalOptions.updateData.preparation_of_standard_and_sample"
                    :show-edit-button="false"
                    :config="
                      onlyPreview
                        ? ({ statusbar: true, resize: true } as EditorManager & RawEditorOptions)
                        : ({ statusbar: true, resize: true, min_height: 200 } as EditorManager &
                            RawEditorOptions)
                    "
                  ></EditorTextarea>
                </v-col>
                <v-col v-if="modalOptions.updateData" cols="12">
                  <v-label class="mb-2">Poznámka</v-label>
                  <EditorTextarea
                    v-model="modalOptions.updateData.note"
                    :show-edit-button="false"
                    :config="
                      onlyPreview
                        ? ({ statusbar: true, resize: true } as EditorManager & RawEditorOptions)
                        : ({ statusbar: true, resize: true, min_height: 200 } as EditorManager &
                            RawEditorOptions)
                    "
                  ></EditorTextarea>
                </v-col>
                <v-col v-if="modalOptions.updateData" cols="12">
                  <v-label class="mb-2">Výsledek</v-label>
                  <EditorTextarea
                    v-model="modalOptions.updateData.result"
                    :show-edit-button="false"
                    :config="
                      onlyPreview
                        ? ({ statusbar: true, resize: true } as EditorManager & RawEditorOptions)
                        : ({ statusbar: true, resize: true, min_height: 200 } as EditorManager &
                            RawEditorOptions)
                    "
                  ></EditorTextarea>
                </v-col>

                <!-- End section -->

                <v-col v-if="modalOptions.newData" cols="12">
                  <div class="d-flex justify-space-between gap-2">
                    <div class="pb-4">
                      <h6 class="text-subtitle-1 mb-0">Potvrzení přidání</h6>
                    </div>
                    <v-switch
                      v-model="modalOptions.newData.confirm"
                      color="primary"
                      class="switchRight"
                      hide-details
                    ></v-switch>
                  </div>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" variant="text" @click="methodsStore.resetModal()">Zrušit</v-btn>
          <v-btn
            v-if="showSuccessButtonTitle"
            color="primary"
            variant="flat"
            type="submit"
            :loading="loading"
          >
            {{ showSuccessButtonTitle }}
          </v-btn>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>
