<template>
  <section class="my-5">
    <span class="text-h4">Vzorky</span>
  </section>
  <v-row v-if="samples.length > 0">
    <v-col
      v-for="sample in samples.filter((sample) =>
        sampleSearch
          ? sample.sample_number
              .toUpperCase()
              .includes(sampleSearch.toUpperCase().replace(/\s+/g, ''))
          : true
      )"
      :key="sample.sample_id"
      cols="12"
    >
      <SampleItem :sample="sample" @reload="$emit('reload')" />
    </v-col>
  </v-row>
  <NotFoundItem v-else>
    <template #notFound>Nebyly nalezeny žádné v<PERSON></template>
  </NotFoundItem>
</template>

<script lang="ts" setup>
  import NotFoundItem from '@/components/shared/NotFoundItem.vue';
  import type { Sample } from '../samples';
  import SampleItem from './SampleItem.vue';

  defineEmits(['reload']);
  defineProps<{
    samples: Sample[];
    sampleSearch: string | undefined;
  }>();
</script>
