import BaseConstructor from '@/utils/BaseConstructor';
import {
  loadParamsFailedReloadNotification,
  reloadWithoutParams,
  revertServerOptionsFromLocation,
  stringifyServerOptions,
  type BasePaginatorResponseI,
  type BaseResponseI
} from '@/utils/axios';
import { fetchWrapper } from '@/utils/helpers/fetch-wrapper';
import { notification } from 'ant-design-vue';
import { defineStore } from 'pinia';
import { User, type UserDto } from '../auth';
import { Column, type ColumnDto } from '../columns';
import { File, useFilesStore, type FileDto } from '../files';
import { Instrument, type InstrumentDto } from '../instruments';
import type { PaginatorRequestDataI } from '../projects';
import { type MethodVersionsDto, MethodVersions } from '../method/methods';
import { Technique, type TechniqueDto } from '../techniques';
import {
  AnalyticalRequestForSample,
  type AnalyticalRequestForSampleDto,
  type AnalyticalRequestForSearchDto,
  AnalyticalRequestForSearch,
  type AnalyticalRequest
} from '../analyticalRequests/analyticalRequests';
import { ChapterSearch, type ChapterSearchDto } from '../chapters';

export enum AnalysisStatus {
  NA = 'na',
  COMPLIANT = 'compliant',
  NON_COMPLIANT = 'non_compliant'
}
export enum SampleStatus {
  CREATED = 'created',
  DONE = 'done',
  CANCELLED = 'cancelled',
  REANALYSIS = 'reanalysis',
  UPDATED = 'updated'
}

export enum SampleParametrType {
  SAMPLE = 'sample',
  METHOD = 'method'
}

export enum SampleType {
  EXTERNAL = 'external',
  INTERNAL = 'internal'
}

export interface SampleParametersDto {
  parameter_id: number;
  parameter: string;
  value: string;
  type: string;
  created_at: string;
  updated_at: string;
}

export interface SampleParametersI {
  parameter_id: number;
  parameter: string;
  value: string;
  type: SampleParametrType;
  created_at: Date;
  updated_at: Date;
}

export class SampleParameters
  extends BaseConstructor<SampleParametersI>()
  implements SampleParametersI
{
  constructor(data: SampleParametersDto) {
    super(data as unknown as SampleParametersI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);
  }
}

export interface SampleDto {
  name: string;
  batch_number: string;
  sample_id: number;
  user_id: number;
  technique_id: number;
  instrument_id: number | null;
  kolona_id: number | null;
  sample_number: string;
  sequence_name: string;
  method_name: string;
  preparation_of_standard_and_sample: string;
  note_or_specification: string;
  technique_notes: string;
  result: string;
  analysis_status: AnalysisStatus;
  status: SampleStatus;
  type: string;
  reanalyze_at: string | null;
  created_at: string;
  updated_at: string;
  parameters: Array<SampleParametersDto>;
  files: Array<FileDto>;
  user?: UserDto;
  technique?: TechniqueDto | null;
  kolona?: ColumnDto | null;
  instrument?: InstrumentDto | null;
  sub_technique?: TechniqueDto | null;
  analytical_request_id: number;
  analytical_request?: AnalyticalRequestForSampleDto | null;
  version?: MethodVersionsDto;
  version_id?: number | null | undefined;
  updated_by_user?: UserDto | null;
  updated_by?: number | null | undefined;
  linked_sample_id?: number | null | undefined;
  linked_sample?: SampleDto | null;
}

export interface SampleI {
  name: string;
  batch_number: string;
  sample_id: number;
  user_id: number;
  technique_id: number;
  instrument_id: number | null;
  kolona_id: number | null;
  sample_number: string;
  sequence_name: string;
  method_name: string;
  preparation_of_standard_and_sample: string;
  note_or_specification: string;
  technique_notes: string;
  result: string;
  analysis_status: AnalysisStatus;
  status: SampleStatus;
  type: SampleType;
  reanalyze_at: Date | null;
  created_at: Date;
  updated_at: Date;
  parameters: Array<SampleParameters>;
  files: Array<File>;
  user: User;
  technique: Technique | null;
  kolona: Column | null;
  instrument: Instrument | null;
  sub_technique: Technique | null;
  analytical_request_id: number;
  analytical_request: AnalyticalRequestForSample | null;
  version?: MethodVersions;
  version_id?: number | null | undefined;
  updated_by_user?: User | null;
  updated_by?: number | null | undefined;
  linked_sample_id?: number | null | undefined;
  linked_sample?: Sample | null;
}

export class Sample extends BaseConstructor<SampleI>() implements SampleI {
  constructor(data: SampleDto) {
    super(data as unknown as SampleI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);
    this.reanalyze_at = data.reanalyze_at ? new Date(data.reanalyze_at) : null;

    this.parameters = data.parameters.map((parameter) => new SampleParameters(parameter));
    this.files = data.files.map((file) => new File(file));

    if (data.user) {
      this.user = new User(data.user);
    }

    if (data.updated_by_user) {
      this.updated_by_user = new User(data.updated_by_user);
    }

    if (data.technique) {
      this.technique = new Technique(data.technique);
    }

    if (data.kolona) {
      this.kolona = new Column(data.kolona);
    }

    if (data.instrument) {
      this.instrument = new Instrument(data.instrument);
    }

    if (data.sub_technique) {
      this.sub_technique = new Technique(data.sub_technique);
    }

    if (data.analytical_request) {
      this.analytical_request = new AnalyticalRequestForSample(data.analytical_request);
    }
    if (data.version) {
      this.version = new MethodVersions(data.version);
    }
    if (data.linked_sample) {
      this.linked_sample = new Sample(data.linked_sample);
    }
  }

  get colorByStatus() {
    switch (this.status) {
      case SampleStatus.CREATED:
        return 'primary';
      case SampleStatus.REANALYSIS:
        return 'warning';
      case SampleStatus.DONE:
        return 'success';
      case SampleStatus.UPDATED:
        return 'warning';
    }
  }
}

interface ChapterForSearchDto {
  chapter_id: number;
  name: string;
  project_department_id: number;
  project_id: number;
}

export interface SimpleSampleDto {
  analysis_status: AnalysisStatus;
  analytical_request_id: number;
  created_at: string;
  instrument_id: number | null;
  kolona_id: number | null;
  method_name: string;
  note_or_specification: string;
  preparation_of_standard_and_sample: string;
  reanalyse_at: string | null;
  result: string;
  sample_id: number;
  sample_number: string;
  status: SampleStatus;
  sequence_name: string;
  technique_id: number;
  technique_notes: string;
  type: SampleType;
  updated_at: string;
  user_id: number;
  technique?: TechniqueDto;
  analytical_request?: AnalyticalRequestForSearchDto;
  chapter?: ChapterSearchDto;
  version_id?: number | null | undefined;
  linked_sample_id?: number | null | undefined;
}

export interface SimpleSampleI {
  analysis_status: AnalysisStatus;
  analytical_request_id: number;
  created_at: Date;
  instrument_id: number | null;
  kolona_id: number | null;
  method_name: string;
  note_or_specification: string;
  preparation_of_standard_and_sample: string;
  reanalyse_at: Date | null;
  result: string;
  sample_id: number;
  sample_number: string;
  status: SampleStatus;
  sequence_name: string;
  technique_id: number;
  technique_notes: string;
  type: SampleType;
  updated_at: Date;
  user_id: number;
  technique?: Technique;
  analytical_request?: AnalyticalRequestForSearch;
  chapter?: ChapterSearch;
  version_id?: number | null | undefined;
  linked_sample_id?: number | null | undefined;
}

export class SimpleSample extends BaseConstructor<SimpleSampleI>() implements SimpleSampleI {
  constructor(data: SimpleSampleDto) {
    super(data as unknown as SimpleSampleI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);
    this.reanalyse_at = data.reanalyse_at ? new Date(data.reanalyse_at) : null;

    if (data.technique) {
      this.technique = new Technique(data.technique);
    }

    if (data.analytical_request) {
      this.analytical_request = new AnalyticalRequestForSearch(data.analytical_request);
    }

    if (data.chapter) {
      this.chapter = new ChapterSearch(data.chapter);
    }
  }
}

interface SampleModalOptionsI {
  isEditing: boolean;
  isCreating: boolean;
  baseData: Sample | undefined;
  updateData: SampleModalUpdateDataI | undefined;
  method_id: number | undefined;
}

export interface NewSampleDataI {
  technique_id?: number;
  notes?: string;
}

export interface NewSampleParametrI {
  parameter: string;
  value: string;
}

interface SampleModalUpdateDataI {
  instrument_id: number | null | undefined;
  kolona_id: number | null | undefined;
  sequence_name: string | undefined;
  method_name: string | undefined;
  preparation_of_standard_and_sample: string | undefined;
  note_or_specification: string | undefined;
  result: string | undefined;
  parameters: NewSampleParametrI[];
  files_ids: number[];
  version_id?: number | undefined;
}

export interface SampleModalUpdatePostDataI {
  instrument_id: number | null;
  kolona_id: number | null;
  sequence_name: string;
  method_name: string;
  preparation_of_standard_and_sample?: string;
  note_or_specification: string;
  result: string;
  parameters: NewSampleParametrI[];
  files_ids: number[];
  analysis_status?: AnalysisStatus;
  version_id?: number | null;
}

export interface SampleTechniqueDto {
  analytical_technique_id: number;
  name: string;
  shortcut: string;
  status: string;
  type: string;
  using_column: boolean;
  created_at: string;
  updated_at: string;
  parent_technique_id: number;
  samples_count: number;
}

export interface SampleTechniqueProjectDto {
  project_id: number;
  user_id: number;
  name: string;
  status: string;
  created_at: string;
  updated_at: string;
  samples_count: number;
}

// Removed SampleTechniqueProjectSampleDto as it is equivalent to SampleDto

interface SamplesStateI {
  external_samples_last_update: Date | null;
  samples: Sample[];
  sample: Sample | null;
  loading: boolean;
  loadingSample: boolean;

  showSampleModal: boolean;
  modalOptions: SampleModalOptionsI | undefined;

  sample_filter_type: 'sample_rd' | 'sample_qc_and_vt';
  options: PaginatorRequestDataI<Sample>;

  mainSampleTechniqueOptions: PaginatorRequestDataI<SampleTechniqueDto>;
  mainSampleTechniqueProjectOptions: PaginatorRequestDataI<SampleTechniqueProjectDto>;
  mainSampleTechniqueProjectSampleOptions: PaginatorRequestDataI<SampleDto>;
}

export const useSamplesStore = defineStore({
  id: 'samples',
  state: () =>
    ({
      external_samples_last_update: null,
      sample: null,
      samples: [],
      loading: false,
      loadingSample: false,

      showSampleModal: false,
      modalOptions: undefined,

      search: undefined,
      search_type: undefined,
      totalItems: undefined,
      sample_filter_type: 'sample_rd',
      options: {
        loading: false,
        results: [],

        search: undefined,
        search_type: 'OR',

        search_columns: [],
        totalItems: 0,
        options: {
          page: 1,
          rowsPerPage: 25,
          sortBy: [],
          sortType: []
        },
        filterOptions: null
      },
      mainSampleTechniqueOptions: {
        loading: false,
        results: [],

        search: undefined,
        search_type: 'OR',

        search_columns: ['name'],
        totalItems: 0,
        options: {
          page: 1,
          rowsPerPage: 25,
          sortBy: ['name'],
          sortType: ['desc']
        },
        filterOptions: null
      },

      mainSampleTechniqueProjectOptions: {
        loading: false,
        results: [],

        search: undefined,
        search_type: 'OR',

        search_columns: [],
        totalItems: 0,
        options: {
          page: 1,
          rowsPerPage: 25,
          sortBy: [],
          sortType: []
        },
        filterOptions: null
      },

      mainSampleTechniqueProjectSampleOptions: {
        loading: false,
        results: [],

        search: undefined,
        search_type: 'AND',

        search_columns: [],
        totalItems: 0,
        options: {
          page: 1,
          rowsPerPage: 25,
          sortBy: ['sample_id'],
          sortType: ['desc']
        },
        filterOptions: null
      }
    }) as SamplesStateI,
  actions: {
    async getSamples(setData: boolean = true): Promise<{
      data: Sample[];
      totalItems: number;
    }> {
      this.loading = true;

      const URL =
        `${import.meta.env.VITE_API_URL}/samples/` +
        `?` +
        stringifyServerOptions(
          this.options.options,
          this.options.search ?? null,
          this.options.search_columns,
          this.options.filterOptions
        );

      return fetchWrapper
        .get(URL)
        .then((res: BasePaginatorResponseI<SampleDto>) => {
          if (res.status_code === 200) {
            this.loading = false;

            this.options.totalItems = res.data.total_items;
            if (setData) {
              this.samples = res.data.items.map((sample) => new Sample(sample));
            }

            return {
              data: res.data.items.map((sample) => new Sample(sample)),
              totalItems: res.data.total_items
            };
          }

          return {
            data: [],
            totalItems: 0
          };
        })
        .catch((res) => {
          this.loading = false;

          if (res.status_code === 400) {
            loadParamsFailedReloadNotification('Načtení vzorků selhalo', res.error, () => {
              const data = reloadWithoutParams();
              this.options.options = data.options;
              this.options.search = data.term ?? undefined;
            });
          }

          return {
            data: [],
            totalItems: 0
          };
        });
    },

    async getSample(sample_id: number) {
      this.loadingSample = true;
      return await fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/sample/${sample_id}`)
        .then((res: BaseResponseI<SampleDto>) => {
          this.loadingSample = false;
          if (res.status_code === 200) {
            this.sample = new Sample(res.data);
            return this.sample;
          }

          return undefined;
        })
        .catch((res) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Načtení techniky selhalo', description: res.error });
          }

          this.loadingSample = false;
          return undefined;
        });
    },

    setParamsFromLocation(): void {
      const data = revertServerOptionsFromLocation();
      this.options.options = data.options;
      this.options.search = data.term ?? undefined;
    },

    resetModal() {
      this.showSampleModal = false;
      if (this.modalOptions) this.modalOptions.baseData = undefined;
    },

    async setEditPage(id: number) {
      // this.loadingSample = true;

      if (this.sample === undefined || this.sample === null) {
        notification.error({ message: 'Vzorek nebyl nalezen' });
        return;
      }

      const getLocalStorage = localStorage.getItem('sampleStore');
      let parsedData;
      if (getLocalStorage) {
        parsedData = JSON.parse(getLocalStorage);
      }

      this.modalOptions = {
        isEditing: true,
        isCreating: false,
        baseData: this.sample ?? undefined,
        method_id: undefined,
        updateData: {
          instrument_id: this.sample.instrument_id ?? null,
          kolona_id: this.sample.kolona_id ?? null,
          sequence_name: this.sample.sequence_name ?? '',
          method_name: this.sample.method_name ?? '',
          preparation_of_standard_and_sample: this.sample.preparation_of_standard_and_sample ?? '',
          note_or_specification: this.sample.note_or_specification ?? '',
          result: this.sample.result ?? '',
          parameters:
            this.sample.parameters.map((param) => ({
              parameter: param.parameter,
              value: param.value
            })) ?? [],
          files_ids: this.sample.files.map((file) => file.file_id) ?? []
        }
      };

      // this.showSampleModal = true;
      // this.loadingSample = false;
    },

    updateSample(): Promise<Sample | false> {
      if (!this.sample) {
        notification.error({ message: 'Vzorek nebyl nalezen' });
        return Promise.resolve(false);
      }

      if (!this.modalOptions?.updateData) {
        notification.error({ message: 'Data pro aktualizaci vzorku nejsou k dispozici' });
        return Promise.resolve(false);
      }

      this.loading = true;

      const data = {
        instrument_id: this.modalOptions.updateData.instrument_id ?? null,
        kolona_id: this.modalOptions.updateData.kolona_id ?? null,
        sequence_name: this.modalOptions.updateData.sequence_name ?? '',
        method_name: this.modalOptions.updateData.method_name ?? '',
        preparation_of_standard_and_sample:
          this.modalOptions.updateData.preparation_of_standard_and_sample ?? '',
        note_or_specification: this.modalOptions.updateData.note_or_specification ?? '',
        result: this.modalOptions.updateData.result ?? '',
        parameters: this.modalOptions.updateData.parameters ?? [],
        files_ids: this.modalOptions.updateData.files_ids ?? [],
        version_id: this.modalOptions.updateData.version_id ?? null
      } as SampleModalUpdatePostDataI;

      return fetchWrapper
        .put(`${import.meta.env.VITE_API_URL}/sample/${this.sample.sample_id}`, data)
        .then((res: BaseResponseI<SampleDto>) => {
          const filesStore = useFilesStore();
          this.loading = false;

          if (res.status_code === 200) {
            this.showSampleModal = false;

            notification.success({
              message: 'Aktualizace vzorku proběhla v pořádku'
            });
            localStorage.removeItem('sampleStore');
            return new Sample(res.data);
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Aktualizace vzorku selhalo', description: res.error });
          } else {
            this.showSampleModal = false;
          }

          return false;
        });
    },

    async saveAndReanalyzeSample() {
      if (!this.sample) {
        notification.error({ message: 'Vzorek nebyl nalezen' });
        return false;
      }

      if (this.modalOptions?.updateData === undefined) {
        notification.error({ message: 'Data pro aktualizaci vzorku nejsou k dispozici' });
        return false;
      }

      this.loading = true;

      const data = {
        instrument_id: this.modalOptions.updateData.instrument_id ?? null,
        kolona_id: this.modalOptions.updateData.kolona_id ?? null,
        sequence_name: this.modalOptions.updateData.sequence_name ?? '',
        method_name: this.modalOptions.updateData.method_name ?? '',
        preparation_of_standard_and_sample:
          this.modalOptions.updateData.preparation_of_standard_and_sample ?? '',
        note_or_specification: this.modalOptions.updateData.note_or_specification ?? '',
        result: this.modalOptions.updateData.result ?? '',
        parameters: this.modalOptions.updateData.parameters ?? [],
        files_ids: this.modalOptions.updateData.files_ids ?? [],
        version_id: this.modalOptions.updateData.version_id ?? null
      } as SampleModalUpdatePostDataI;

      return fetchWrapper
        .post(
          `${import.meta.env.VITE_API_URL}/sample/${this.sample.sample_id}/save_and_reanalyse`,
          data
        )
        .then((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 200) {
            notification.success({
              message: 'Vzorek byl úspěšně přeřazen do fronty na analýzu'
            });

            return true;
          }
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Reanalíza vzorku selhala', description: res.error });
          }

          return false;
        });
    },

    async saveAndReportSample() {
      if (!this.sample) {
        notification.error({ message: 'Vzorek nebyl nalezen' });
        return false;
      }

      if (this.modalOptions?.updateData === undefined) {
        notification.error({ message: 'Data pro aktualizaci vzorku nejsou k dispozici' });
        return false;
      }

      this.loading = true;

      const data = {
        instrument_id: this.modalOptions.updateData.instrument_id ?? null,
        kolona_id: this.modalOptions.updateData.kolona_id ?? null,
        sequence_name: this.modalOptions.updateData.sequence_name ?? '',
        method_name: this.modalOptions.updateData.method_name ?? '',
        preparation_of_standard_and_sample:
          this.modalOptions.updateData.preparation_of_standard_and_sample ?? '',
        note_or_specification: this.modalOptions.updateData.note_or_specification ?? '',
        result: this.modalOptions.updateData.result ?? '',
        parameters: this.modalOptions.updateData.parameters ?? [],
        files_ids: this.modalOptions.updateData.files_ids ?? [],
        version_id: this.modalOptions.updateData.version_id ?? null
      } as SampleModalUpdatePostDataI;

      return fetchWrapper
        .post(
          `${import.meta.env.VITE_API_URL}/sample/${this.sample.sample_id}/save_and_report`,
          data
        )
        .then((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 200) {
            notification.success({
              message: 'Vzorek byl úspěšně přeřazen do fronty na report'
            });

            return true;
          }
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Přeřazení vzorku selhalo', description: res.error });
          }

          return false;
        });
    },

    async saveSampleAsNewMethod(method_name: string) {
      if (!this.sample) {
        notification.error({ message: 'Vzorek nebyl nalezen' });
        return false;
      }

      this.loading = true;
      const data = {
        method_name: method_name
      };
      return fetchWrapper
        .post(
          `${import.meta.env.VITE_API_URL}/sample/${this.sample.sample_id}/save_as_method`,
          data
        )
        .then((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 200) {
            notification.success({
              message: 'Vzorek byl úspěšně uložen jako nová metoda'
            });

            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({
              message: 'Uložení vzorku jako nové metody selhalo',
              description: res.error
            });
          }

          return false;
        });
    },

    async cancelSampleAnalysis() {
      if (!this.sample) {
        notification.error({ message: 'Vzorek nebyl nalezen' });
        return false;
      }

      this.loading = true;

      return fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/sample/${this.sample.sample_id}/cancel_analyse`)
        .then((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 200) {
            notification.success({
              message: 'Analýza vzorku byla zrušena'
            });

            return true;
          }
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({
              message: 'Zrušení analýzy vzorku selhalo',
              description: res.error
            });
          }

          return false;
        });
    },

    async getMainSampleAnalyticalTechniques(): Promise<boolean> {
      this.mainSampleTechniqueOptions.loading = true;

      const URL =
        `${import.meta.env.VITE_API_URL}/main-sample/analytical_techniques` +
        `?sample_filter_type=${this.sample_filter_type}&` +
        stringifyServerOptions(
          this.mainSampleTechniqueOptions.options,
          this.mainSampleTechniqueOptions.search ?? null,
          this.mainSampleTechniqueOptions.search_columns,
          this.mainSampleTechniqueOptions.filterOptions
        );

      return fetchWrapper
        .get(URL)
        .then((res: BasePaginatorResponseI<SampleTechniqueDto>) => {
          this.mainSampleTechniqueOptions.loading = false;

          if (res.status_code === 200) {
            this.options.totalItems = res.data.total_items;
            this.mainSampleTechniqueOptions.results = res.data.items;

            return true;
          }

          return false;
        })
        .catch((res) => {
          this.mainSampleTechniqueOptions.loading = false;

          if (res.status_code === 400) {
            loadParamsFailedReloadNotification('Načtení selhalo', res.error, () => {
              const data = reloadWithoutParams();
              this.mainSampleTechniqueOptions.options = data.options;
              this.mainSampleTechniqueOptions.search = data.term ?? undefined;
            });
          }

          return false;
        });
    },

    async getProjectsForSpecificAnalyticalTechnique(technique_id: number): Promise<boolean> {
      this.mainSampleTechniqueProjectOptions.loading = true;

      const URL =
        `${import.meta.env.VITE_API_URL}/main-sample/analytical_technique/${technique_id}/projects` +
        `?sample_filter_type=${this.sample_filter_type}&` +
        stringifyServerOptions(
          this.mainSampleTechniqueProjectOptions.options,
          this.mainSampleTechniqueProjectOptions.search ?? null,
          this.mainSampleTechniqueProjectOptions.search_columns,
          this.mainSampleTechniqueProjectOptions.filterOptions
        );

      return fetchWrapper
        .get(URL)
        .then((res: BasePaginatorResponseI<SampleTechniqueProjectDto>) => {
          this.mainSampleTechniqueProjectOptions.loading = false;

          if (res.status_code === 200) {
            this.options.totalItems = res.data.total_items;
            this.mainSampleTechniqueProjectOptions.results = res.data.items;

            return true;
          }

          return false;
        })
        .catch((res) => {
          this.mainSampleTechniqueProjectOptions.loading = false;

          if (res.status_code === 400) {
            loadParamsFailedReloadNotification('Načtení selhalo', res.error, () => {
              const data = reloadWithoutParams();
              this.mainSampleTechniqueProjectOptions.options = data.options;
              this.mainSampleTechniqueProjectOptions.search = data.term ?? undefined;
            });
          }

          return false;
        });
    },

    async getSamplesForSpecificProject(technique_id: number, project_id: number): Promise<boolean> {
      this.mainSampleTechniqueProjectSampleOptions.loading = true;
      //this.mainSampleTechniqueProjectSampleOptions.options.sortBy = ['sample_number'];
      //this.mainSampleTechniqueProjectSampleOptions.options.sortType = ['asc'];
      const URL =
        `${import.meta.env.VITE_API_URL}/main-sample/analytical_technique/${technique_id}/project/${project_id}/samples` +
        `?sample_filter_type=${this.sample_filter_type}&` +
        stringifyServerOptions(
          this.mainSampleTechniqueProjectSampleOptions.options,
          this.mainSampleTechniqueProjectSampleOptions.search ?? null,
          this.mainSampleTechniqueProjectSampleOptions.search_columns,
          this.mainSampleTechniqueProjectSampleOptions.filterOptions
        );

      return fetchWrapper
        .get(URL)
        .then((res: BasePaginatorResponseI<SampleDto>) => {
          this.mainSampleTechniqueProjectSampleOptions.loading = false;

          if (res.status_code === 200) {
            this.options.totalItems = res.data.total_items;

            this.mainSampleTechniqueProjectSampleOptions.results = res.data.items.sort((a, b) => {
              const regex = /^\d+\/\d+$/;

              const aValid = regex.test(a.sample_number);
              const bValid = regex.test(b.sample_number);

              if (aValid && bValid) {
                const [aFirst, aSecond] = a.sample_number.split('/').map(Number);
                const [bFirst, bSecond] = b.sample_number.split('/').map(Number);

                if (aSecond !== bSecond) {
                  return aSecond - bSecond;
                }
                return aFirst - bFirst;
              }

              if (!aValid && bValid) return 1;
              if (aValid && !bValid) return -1;

              return 0;
            });
            return true;
          }

          return false;
        })
        .catch((res) => {
          this.mainSampleTechniqueProjectSampleOptions.loading = false;

          if (res.status_code === 400) {
            loadParamsFailedReloadNotification('Načtení selhalo', res.error, () => {
              const data = reloadWithoutParams();
              this.mainSampleTechniqueProjectSampleOptions.options = data.options;
              this.mainSampleTechniqueProjectSampleOptions.search = data.term ?? undefined;
            });
          }

          return false;
        });
    },
    async exportSample(sample_id?: number, sample_number?: string) {
      const currentSampleId = sample_id ? sample_id : this.sample?.sample_id;
      this.loading = true;
      return fetchWrapper
        .blob(`${import.meta.env.VITE_API_URL}/sample/${currentSampleId}/export`)
        .then((res) => {
          this.loading = false;

          const url = window.URL.createObjectURL(new Blob([res]));
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute(
            'download',
            sample_number ? `sample_${sample_number}.pdf` : 'sample.pdf'
          );
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          return true;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Analýza nešla exportovat', description: res.message });
          } else {
            notification.error({ message: 'Analýza nešla exportovat' });
          }

          return false;
        });
    },
    async exportSampleToWord(sample_id?: number, sample_number?: string) {
      const currentSampleId = sample_id ? sample_id : this.sample?.sample_id;
      this.loading = true;
      return fetchWrapper
        .blob(`${import.meta.env.VITE_API_URL}/sample/${currentSampleId}/export_to_word`)
        .then((res) => {
          this.loading = false;

          const url = window.URL.createObjectURL(new Blob([res]));
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute(
            'download',
            sample_number ? `sample_${sample_number}.docx` : 'sample.docx'
          );
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          return true;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Analýza nešla exportovat', description: res.message });
          } else {
            notification.error({ message: 'Analýza nešla exportovat' });
          }

          return false;
        });
    },
    async exportSamples({ analytical_request_id, sample_number }: AnalyticalRequest) {
      this.loading = true;
      return fetchWrapper
        .blob(
          `${import.meta.env.VITE_API_URL}/analytical-request/${analytical_request_id}/export_samples`
        )
        .then((res) => {
          this.loading = false;

          const url = window.URL.createObjectURL(new Blob([res]));
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute(
            'download',
            sample_number ? `samples_of_${sample_number}.pdf` : 'samples.pdf'
          );
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          return true;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Analýza nešla exportovat', description: res.message });
          } else {
            notification.error({ message: 'Analýza nešla exportovat' });
          }

          return false;
        });
    },
    async exportSamplesToWord({ analytical_request_id, sample_number }: AnalyticalRequest) {
      this.loading = true;
      return fetchWrapper
        .blob(
          `${import.meta.env.VITE_API_URL}/analytical-request/${analytical_request_id}/export_samples_to_word`
        )
        .then((res) => {
          this.loading = false;

          const url = window.URL.createObjectURL(new Blob([res]));
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute(
            'download',
            sample_number ? `samples_of_${sample_number}.docx` : 'samples.docx'
          );
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          return true;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Analýza nešla exportovat', description: res.message });
          } else {
            notification.error({ message: 'Analýza nešla exportovat' });
          }

          return false;
        });
    },
    updateSampleReAnalyze(sample_id: number, data: SampleModalUpdatePostDataI) {
      if (!sample_id) {
        notification.error({ message: 'Vzorek nebyl nalezen' });
        return false;
      }
      this.loading = true;
      return fetchWrapper
        .put(`${import.meta.env.VITE_API_URL}/sample/${sample_id}`, data)
        .then((res: BaseResponseI<SampleDto>) => {
          this.loading = false;
          if (res.status_code === 200) {
            notification.success({
              message: 'Aktualizace vzorku proběhla v pořádku'
            });
            return true;
          }
          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;
          if (res.status_code === 400 || res.status_code === 422) {
            notification.error({ message: 'Aktualizace vzorku selhalo', description: res.error });
          }
          return false;
        });
    },
    async addFiletoSample(file_id: number, sample_id: number) {
      if (!sample_id) {
        notification.error({ message: 'Není vybran žádost' });
        return false;
      }
      return fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/sample/${sample_id}/file/${file_id}`)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Soubor byl přidán k žádosti' });
            return true;
          }

          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({
              message: 'Přidání souboru k žádosti selhalo',
              description: res.error
            });
          } else {
            notification.error({ message: 'Přidání souboru k žádosti selhalo' });
          }

          return false;
        });
    },
    linkSample(sample_id: number, linked_sample_id: number) {
      if (!sample_id || !linked_sample_id) {
        notification.error({ message: 'Vzorek nebyl nalezen' });
        return false;
      }
      this.loading = true;
      return fetchWrapper
        .put(`${import.meta.env.VITE_API_URL}/sample/${sample_id}/link-sample/${linked_sample_id}`)
        .then((res: BaseResponseI<null>) => {
          this.loading = false;
          if (res.status_code === 200) {
            notification.success({
              message: 'Vzorek byl úspěšně propojen s dalším vzorkem'
            });
            return true;
          }
          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;
          if (res.status_code === 400 || res.status_code === 422) {
            notification.error({ message: 'Propojení vzorků selhalo', description: res.error });
          }
          return false;
        });
    },
    unlinkSample(sample_id: number, linked_sample_id: number) {
      if (!sample_id || !linked_sample_id) {
        notification.error({ message: 'Vzorek nebyl nalezen' });
        return false;
      }
      this.loading = true;
      return fetchWrapper
        .delete(`${import.meta.env.VITE_API_URL}/sample/${sample_id}/link-sample`)
        .then((res: BaseResponseI<null>) => {
          this.loading = false;
          if (res.status_code === 200) {
            notification.success({
              message: 'Vzorek byl úspěšně odpojen od dalšího vzorku'
            });
            return true;
          }
          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          this.loading = false;
          if (res.status_code === 400 || res.status_code === 422) {
            notification.error({ message: 'Odpojení vzorků selhalo', description: res.error });
          }
          return false;
        });
    }
  }
});
