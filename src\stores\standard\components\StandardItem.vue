<template>
  <v-card
    variant="outlined"
    :color="standard.colorByStatus"
    class="card-hover-border bg-containerBg"
    :subtitle="`Stav: ${standard.status}`"
    :title="standard.standard_name"
  >
    <template #append>
      <v-btn
        v-if="standard.status !== StandardStatus.DELETED"
        icon
        @click="standardsStore.deleteStandard(standard.standard_id)"
      >
        <v-icon>mdi-delete</v-icon>
      </v-btn>
    </template>
  </v-card>
</template>
<script lang="ts" setup>
  import { Standard, StandardStatus, useStandardsStore } from '../standards';

  defineEmits(['reload']);
  defineProps<{
    standard: Standard;
  }>();

  const standardsStore = useStandardsStore();
</script>
