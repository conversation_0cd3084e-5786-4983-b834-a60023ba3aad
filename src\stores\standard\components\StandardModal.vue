<script setup lang="ts">
  import { itemRequiredRule } from '@/utils/formValidation';
  import { useVModel } from '@vueuse/core';
  import { notification } from 'ant-design-vue';
  import { useAnalyticalDepartmentStore } from '@/stores/analyticalDepartment';
  import { storeToRefs } from 'pinia';
  import { computed, ref, watch } from 'vue';
  import { useStandardsStore, type StandardDto, StandardStatus } from '../standards';
  import TechniqueStandardTable from './TechniqueStandardTable.vue';
  import { useProjectsStore } from '@/stores/projects';
  import {
    NewAnalyticalRequestType,
    useAnalyticalRequestsStore,
    type AnalyticalRequestModalNewPostDataI
  } from '@/stores/analyticalRequests/analyticalRequests';
  const analyticalRequestsStore = useAnalyticalRequestsStore();
  const analyticalDepartmentStore = useAnalyticalDepartmentStore();
  const projectsStore = useProjectsStore();
  const { department } = storeToRefs(projectsStore);
  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    }
  });
  interface newSampleData {
    standard_type: string | null;
  }
  const standardType = ref<newSampleData>({ standard_type: 'rd' });
  const standardsStore = useStandardsStore();
  const { modalOptions, loading, standards } = storeToRefs(standardsStore);
  const { projectTechniques } = storeToRefs(projectsStore);
  const selectedStandard = ref();
  const MethodForm = ref();
  async function submitFormToValidate() {
    if (MethodForm.value.isValid && modalOptions.value && department.value) {
      switch (true) {
        case modalOptions.value.isEditing && !modalOptions.value.isCreating:
          if (modalOptions.value.baseData) {
            standardsStore.updateStandard(modalOptions.value.baseData);
          }
          break;
        case !modalOptions.value.isEditing && modalOptions.value.isCreating:
          if (modalOptions.value.newData?.confirm) {
            const data = {
              project_id: department.value.project_id,
              type: NewAnalyticalRequestType.STANDARD_QC,
              project_department_id: department.value.project_department_id,
              sample_number: '',
              batch_number: modalOptions.value.newData.batch_number,
              name: modalOptions.value.newData.standard_name,
              samples: modalOptions.value.newData.technique_ids,
              analytical_department_request_type: standardType.value.standard_type,
              files_ids: []
            } as AnalyticalRequestModalNewPostDataI;

            const res = await analyticalRequestsStore.createAnalyticalRequest(data);
            if (res) {
              standardsStore.resetModal();
              await analyticalDepartmentStore.getProjectStandards();
            }
          } else {
            notification.error({
              message: 'Chyba',
              description: 'Musíte potvrdit přidání standardu.'
            });
          }
          break;

        default:
          return 'Náhled standardu';
      }
    } else {
      if (modalOptions.value && modalOptions.value.newData) {
        modalOptions.value.newData.confirm = false;
      }
    }
  }

  const showState = useVModel(props, 'show');
  const showTitle = computed(() => {
    if (modalOptions.value === undefined) {
      return 'Náhled standardu';
    }

    switch (true) {
      case modalOptions.value.isEditing && !modalOptions.value.isCreating:
        return 'Editace standardu';
      case !modalOptions.value.isEditing && modalOptions.value.isCreating:
        return 'Nový standard';
      default:
        return 'Náhled standardu';
    }
  });
  const showSuccessButtonTitle = computed(() => {
    if (modalOptions.value === undefined) {
      return false;
    }

    switch (true) {
      case modalOptions.value.isEditing && !modalOptions.value.isCreating:
        return 'Upravit standard';
      case !modalOptions.value.isEditing && modalOptions.value.isCreating:
        return 'Přidat standard';
      default:
        return false;
    }
  });
  const onlyPreview = computed(() => {
    return modalOptions.value?.isCreating === false && modalOptions.value?.isEditing === false;
  });

  watch(
    () => props.show,
    (value) => {
      if (value) {
        standardsStore.getStandards();
      } else {
        selectedStandard.value = null;
      }
    }
  );
  watch(
    () => selectedStandard.value,
    () => {
      if (typeof selectedStandard.value === 'string') {
        return;
      }
      if (modalOptions.value && modalOptions.value.newData) {
        modalOptions.value.newData.technique_ids = [];
        if (selectedStandard.value && selectedStandard.value.analytical_techniques) {
          for (const technique of selectedStandard.value.analytical_techniques) {
            if (allNewAnalyticalTechniques.value.includes(technique.analytical_technique_id)) {
              modalOptions.value.newData.technique_ids.push({
                technique_id: technique.analytical_technique_id
              });
            }
          }
        }
      }
    }
  );
  const allNewAnalyticalTechniques = computed(() => {
    return projectTechniques.value.flatMap((technique) => technique.analytical_technique_id);
  });
</script>
<template>
  <v-dialog v-model="showState" class="customer-modal" :z-index="1010">
    <v-card :loading="loading">
      <v-form
        v-if="modalOptions && department"
        ref="MethodForm"
        class="MethodForm"
        :readonly="onlyPreview"
        @submit.prevent="submitFormToValidate"
      >
        <v-card-title class="pa-5">
          <span class="text-h5">{{ showTitle }}</span>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col>
              <v-row>
                <v-col v-if="modalOptions.newData" cols="12">
                  <v-label class="mb-2">Název standardu</v-label>
                  <v-text-field
                    v-model="modalOptions.newData.standard_name"
                    :rules="itemRequiredRule"
                    single-line
                    placeholder="Zadejte název standardu"
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></v-text-field>
                </v-col>
                <v-col cols="12">
                  <v-label class="mb-2">Vyberte standard</v-label>
                  <v-autocomplete
                    v-if="standards"
                    v-model="selectedStandard"
                    :items="
                      standards
                        .filter((s) => s.status === StandardStatus.ACTIVE)
                        .map((standard) => {
                          return {
                            value: standard,
                            title: `${standard.standard_name}`,
                            standard: standard
                          };
                        })
                    "
                    :clearable="true"
                    hide-details
                    rounded="sm"
                    variant="outlined"
                    color="primary"
                    label="Vyberte standard"
                    single-line
                    class="autocomplete"
                    :no-data-text="'Žádná další políčka'"
                    :slim="true"
                  >
                    <template #chip>
                      <v-chip
                        label
                        variant="tonal"
                        color="primary"
                        size="large"
                        class="my-1 text-subtitle-1 font-weight-regular"
                      ></v-chip>
                    </template>

                    <template #item="{ props, item }">
                      <v-list-item v-bind="props" :title="''">
                        <div class="player-wrapper pa-2">
                          <h6 class="text-subtitle-1 mb-0">
                            {{ item.raw.standard.standard_name }}
                          </h6>
                        </div>
                      </v-list-item>
                    </template>
                  </v-autocomplete>
                </v-col>
                <v-col v-if="modalOptions.newData" cols="12">
                  <v-label class="mb-2">Číslo sarže</v-label>
                  <v-text-field
                    v-model="modalOptions.newData.batch_number"
                    single-line
                    placeholder="Zadejte číslo sarže"
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></v-text-field>
                </v-col>
                <v-col v-if="modalOptions.newData" cols="12">
                  <v-label class="mb-2">Analyzovat jako standard</v-label>
                  <v-radio-group
                    v-model="standardType.standard_type"
                    single-line
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                    inline
                    :rules="itemRequiredRule"
                  >
                    <v-radio label="RD" :value="'rd'"></v-radio>
                    <v-radio label="QC" :value="'qc'"></v-radio>
                  </v-radio-group>
                </v-col>
                <v-col v-if="modalOptions.newData" cols="12">
                  <v-label class="mb-2">Technika</v-label>
                  <TechniqueStandardTable
                    v-model="modalOptions.newData.technique_ids"
                    :project_id="department.project_id"
                  />
                </v-col>
                <v-col v-if="modalOptions.updateData" cols="12">
                  <v-label class="mb-2">Název standardu</v-label>
                  <v-text-field
                    v-model="modalOptions.updateData.standard_name"
                    :rules="itemRequiredRule"
                    single-line
                    placeholder="Zadejte název standardu"
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></v-text-field>
                </v-col>
                <v-col v-if="modalOptions.newData" cols="12">
                  <div class="d-flex justify-space-between gap-2">
                    <div class="pb-4">
                      <h6 class="text-subtitle-1 mb-0">Potvrzení přidání</h6>
                    </div>
                    <v-switch
                      v-model="modalOptions.newData.confirm"
                      color="primary"
                      class="switchRight"
                      hide-details
                    ></v-switch>
                  </div>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" variant="text" @click="standardsStore.resetModal()">Zrušit</v-btn>
          <v-btn
            v-if="showSuccessButtonTitle"
            color="primary"
            variant="flat"
            type="submit"
            :loading="loading"
          >
            {{ showSuccessButtonTitle }}
          </v-btn>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>
