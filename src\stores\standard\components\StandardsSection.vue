<template>
  <section class="my-5">
    <span class="text-h4">Standardy</span>
  </section>
  <v-row v-if="standards.length > 0">
    <v-col
      v-for="standard in standards.filter((standard) =>
        standardSearch
          ? standard.standard_name
              .toUpperCase()
              .includes(standardSearch.toUpperCase().replace(/\s+/g, ''))
          : true
      )"
      :key="standard.standard_id"
      cols="12"
    >
      <StandardItem :standard="standard" @reload="$emit('reload')" />
    </v-col>
  </v-row>
  <NotFoundItem v-else>
    <template #notFound>Nebyly nalezeny žádné v<PERSON>ky</template>
  </NotFoundItem>
</template>

<script lang="ts" setup>
  import NotFoundItem from '@/components/shared/NotFoundItem.vue';
  import StandardItem from './StandardItem.vue';
  import type { Standard } from '../standards';

  defineEmits(['reload']);
  defineProps<{
    standards: Standard[];
    standardSearch: string | undefined;
  }>();
</script>
