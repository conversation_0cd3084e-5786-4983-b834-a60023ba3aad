<template>
  <template v-for="(field, index) in parametersState" :key="index">
    <v-row>
      <v-col md="11" cols="9">
        <v-autocomplete
          v-model="field.technique_id"
          :clearable="true"
          hide-details
          :rules="itemRequiredRule"
          rounded="sm"
          :items="
            techniques
              .filter((technique) => technique.isActive)
              .map((technique) => {
                return {
                  value: technique.analytical_technique_id,
                  title: `${technique.name} (${technique.type})`,
                  technique: technique
                };
              })
          "
          variant="outlined"
          color="primary"
          label="Vyberte techniku"
          single-line
          class="autocomplete"
          :no-data-text="'Žádná další políčka'"
          :slim="true"
        >
          <template #chip>
            <v-chip
              label
              variant="tonal"
              color="primary"
              size="large"
              class="my-1 text-subtitle-1 font-weight-regular"
            ></v-chip>
          </template>

          <template #item="{ props, item }">
            <v-list-item v-bind="props" :title="''">
              <div class="player-wrapper pa-2">
                <h6 class="text-subtitle-1 mb-0">
                  {{ item.raw.technique.parent_technique_id === null ? 'Hlavní' : 'Sub' }} -
                  {{ item.raw.technique.name }} ({{ item.raw.technique.shortcut }}) -
                  <v-chip
                    v-if="item.raw.technique.status === 'active'"
                    color="success"
                    size="small"
                    label
                  >
                    Aktivní
                  </v-chip>
                  <v-chip
                    v-if="item.raw.technique.status === 'inactive'"
                    color="warning"
                    size="small"
                    label
                  >
                    Neaktivní
                  </v-chip>
                  <v-chip
                    v-if="item.raw.technique.status === 'deleted'"
                    color="error"
                    size="small"
                    label
                  >
                    Odstraněno
                  </v-chip>
                </h6>
                <small class="text-h6 text-lightText">
                  Typ: {{ translateType(item.raw.technique.type) }} | Vytvořena:
                  {{ toLocale(item.raw.technique.created_at) }}
                </small>
              </div>
            </v-list-item>
          </template>
        </v-autocomplete>
      </v-col>

      <v-col md="1" cols="3">
        <div class="d-flex justify-end align-center gap-2 h-100">
          <v-btn
            variant="flat"
            color="error"
            density="compact"
            icon="mdi-close"
            @click="parametersState.splice(index, 1)"
          />
        </div>
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="12">
        <v-divider class="my-5"></v-divider>
      </v-col>
    </v-row>
  </template>
  <v-row>
    <v-col cols="12">
      <v-btn @click="addField">Přidat techniku</v-btn>
    </v-col>
  </v-row>
</template>

<script setup lang="ts">
  import EditorTextarea from '@/components/shared/EditorTextarea.vue';
  import { useProjectsStore } from '@/stores/projects';
  import { Technique, TechniqueType } from '@/stores/techniques';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { toLocale } from '@/utils/locales';
  import { useVModel } from '@vueuse/core';
  import type { EditorManager, RawEditorOptions } from 'tinymce';
  import { onMounted, ref } from 'vue';

  export interface AnalyticalRequestParameterI {
    technique_id: number | undefined;
  }

  const props = defineProps<{
    modelValue: AnalyticalRequestParameterI[];
    project_id: number;
  }>();

  const techniques = ref<Technique[]>([]);
  const projectsStore = useProjectsStore();
  const parametersState = useVModel(props, 'modelValue');

  onMounted(async () => {
    if (parametersState.value.length === 0) {
      addField();
    }

    techniques.value = (await projectsStore.getProjectAnalyticalTechniques(props.project_id)) ?? [];
  });

  const addField = () => {
    parametersState.value.push({ technique_id: undefined });
  };

  const translateType = (type: string): string => {
    const translations: { [key: string]: string } = {
      external: 'externí',
      internal: 'interní'
    };
    return translations[type] || type;
  };
</script>
