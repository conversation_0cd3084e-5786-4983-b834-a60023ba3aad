import BaseConstructor from '@/utils/BaseConstructor';
import type { BaseResponseI } from '@/utils/axios';
import { fetchWrapper } from '@/utils/helpers/fetch-wrapper';
import { notification } from 'ant-design-vue';
import { defineStore } from 'pinia';
import { Technique, type TechniqueDto } from '../techniques';

const baseUrl = `${import.meta.env.VITE_API_URL}/standards/`;

export enum StandardStatus {
  ACTIVE = 'active',
  DELETED = 'deleted'
}

export interface StandardDto {
  standard_id: number;
  standard_name: string;
  status: string;
  created_at: string;
  updated_at: string;
  analytical_techniques: TechniqueDto[];
}

export interface StandardI {
  standard_id: number;
  standard_name: string;
  status: StandardStatus;
  created_at: Date;
  updated_at: Date;
  analytical_techniques: Technique[];
  newAnalyticalTechniques: number[];
}
export interface AnalyticalRequestParameterI {
  technique_id: number | undefined;
}
export class Standard extends BaseConstructor<StandardI>() implements StandardI {
  constructor(data: StandardDto) {
    super(data as unknown as StandardI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    this.analytical_techniques = data.analytical_techniques.map(
      (technique) => new Technique(technique)
    );
    this.newAnalyticalTechniques = this.analytical_techniques.map(
      (technique) => technique.analytical_technique_id
    );
  }

  get colorByStatus() {
    return this.status === StandardStatus.ACTIVE ? 'primary' : 'error';
  }
}

interface StandardsStateI {
  standards: Standard[];
  standard: Standard | null;
  loading: boolean;

  showStandardModal: boolean;
  modalOptions: MethodModalOptionsI | undefined;
}

interface MethodModalOptionsI {
  isEditing: boolean;
  isCreating: boolean;
  baseData: Standard | undefined;
  newData: NewStandardModalDataI | undefined;
  updateData: UpdateStandardModalDataI | undefined;
}

interface NewStandardModalDataI {
  standard_name?: string | undefined;
  status: StandardStatus.ACTIVE;
  confirm: boolean | false;
  batch_number?: string | undefined;
  technique_ids: AnalyticalRequestParameterI[];
}

interface UpdateStandardModalDataI {
  standard_name?: string | undefined;
  analytical_techniques: number[];
  status: StandardStatus.ACTIVE;
}

export const useStandardsStore = defineStore({
  id: 'standards',
  state: () =>
    ({
      standard: null,
      standards: [],
      loading: false,

      showStandardModal: false,
      modalOptions: undefined
    }) as StandardsStateI,

  actions: {
    setStandard(standard: Standard | null): void {
      this.standard = standard;
    },

    showNewMethodModal() {
      this.modalOptions = {
        isEditing: false,
        isCreating: true,
        baseData: undefined,
        updateData: undefined,
        newData: {
          standard_name: undefined,
          status: StandardStatus.ACTIVE,
          confirm: false,
          technique_ids: []
        }
      };

      this.showStandardModal = true;
    },

    resetModal() {
      this.showStandardModal = false;
      if (this.modalOptions) this.modalOptions.baseData = undefined;
      if (this.modalOptions) this.modalOptions.newData = undefined;
    },

    setStandardById(standard_id: number): void {
      this.standard =
        this.standards.find((standard) => standard.standard_id === standard_id) || null;
    },

    async setStandardFromServerById(standard_id: number) {
      this.standard = (await this.getStandard(standard_id)) ?? null;
    },

    getStandardById(standard_id: number): Standard | null {
      return this.standards.find((standard) => standard.standard_id === standard_id) || null;
    },

    async getStandards(setData: boolean = true): Promise<undefined | Standard[]> {
      this.loading = true;

      return fetchWrapper
        .get(baseUrl)
        .then((res: BaseResponseI<StandardDto[]>) => {
          if (res.status_code === 200) {
            if (setData) {
              this.standards = res.data.map((standard) => new Standard(standard));
            }

            this.loading = false;
            return res.data.map((standard) => new Standard(standard));
          }

          this.loading = false;
          return undefined;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Načtení standardu selhalo', description: res.error });
          }

          this.loading = false;
          return undefined;
        });
    },

    async getStandard(standardId: number): Promise<undefined | Standard> {
      this.loading = true;

      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/standard/${standardId}`)
        .then((res: BaseResponseI<StandardDto>) => {
          if (res.status_code === 200) {
            this.loading = false;
            return new Standard(res.data);
          }

          this.loading = false;
          return undefined;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Načtení standardu selhalo', description: res.error });
          }

          this.loading = false;
          return undefined;
        });
    },

    async createStandard(
      data: { standard_name: string; status: StandardStatus },
      techniqueData: any | null
    ): Promise<boolean> {
      this.loading = true;

      return fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/standard/`, data)
        .then((res: BaseResponseI<StandardDto>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Standard byl úspěšně vytvořen' });

            this.getStandards();

            this.loading = false;
            return true;
          }

          this.loading = false;
          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Vytvoření standardu selhalo', description: res.error });
          }

          this.loading = false;
          return false;
        });
    },

    async updateStandard(standard: Standard): Promise<boolean> {
      this.loading = true;

      const data = {
        standard_name: standard.standard_name,
        status: standard.status,
        analytical_techniques: standard.newAnalyticalTechniques
      };

      return fetchWrapper
        .put(`${import.meta.env.VITE_API_URL}/standard/${standard.standard_id}`, data)
        .then((res: BaseResponseI<StandardDto>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Standard byl úspěšně upraven' });
            this.getStandards();

            this.loading = false;
            return true;
          }

          this.loading = false;
          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Úprava standardu selhala', description: res.error });
          }

          this.loading = false;
          return false;
        });
    },

    async deleteStandard(standard_id: number): Promise<boolean> {
      this.loading = true;

      return fetchWrapper
        .delete(`${import.meta.env.VITE_API_URL}/standard/${standard_id}`)
        .then((res: BaseResponseI<StandardDto>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Standard byl úspěšně smazán' });
            this.getStandards();

            this.loading = false;
            return true;
          }

          this.loading = false;
          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Smazání standardu selhalo', description: res.error });
          }

          this.loading = false;
          return false;
        });
    },
    async activateStandard(standard: Standard): Promise<boolean> {
      this.loading = true;

      const data = {
        standard_name: standard.standard_name,
        analytical_techniques: standard.newAnalyticalTechniques,
        status: StandardStatus.ACTIVE
      };

      return fetchWrapper
        .put(`${import.meta.env.VITE_API_URL}/standard/${standard.standard_id}`, data)
        .then((res: BaseResponseI<StandardDto>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Standard uspěšně aktivován' });
            this.getStandards();

            this.loading = false;
            return true;
          }

          this.loading = false;
          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Úprava standardu selhala', description: res.error });
          }

          this.loading = false;
          return false;
        });
    },

    async addTechniqueToStandard(standard_id: number, technique_id: number): Promise<boolean> {
      this.loading = true;

      return fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/standard/${standard_id}/technique/${technique_id}`)
        .then((res: BaseResponseI<StandardDto>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Technika byla úspěšně přidána' });
            this.getStandards();

            this.loading = false;
            return true;
          }

          this.loading = false;
          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Přidání techniky selhalo', description: res.error });
          }

          this.loading = false;
          return false;
        });
    },

    async removeTechniqueFromStandard(standard_id: number, technique_id: number): Promise<boolean> {
      this.loading = true;

      return fetchWrapper
        .delete(`${import.meta.env.VITE_API_URL}/standard/${standard_id}/technique/${technique_id}`)
        .then((res: BaseResponseI<StandardDto>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Technika byla úspěšně odebrána' });
            this.getStandards();

            this.loading = false;
            return true;
          }

          this.loading = false;
          return false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Odebrání techniky selhalo', description: res.error });
          }

          this.loading = false;
          return false;
        });
    }
  }
});
