import BaseConstructor from '@/utils/BaseConstructor';
import {
  loadParamsFailedReloadNotification,
  reloadWithoutParams,
  stringifyServerOptions,
  type BasePaginatorResponseI,
  type BaseResponseI
} from '@/utils/axios';
import { notification } from 'ant-design-vue';
import { defineStore } from 'pinia';
import type { GetAllOptions } from './projects';
import { fetchWrapper } from '@/utils/helpers/fetch-wrapper';
import type { ServerOptions } from 'vue3-easy-data-table';

const baseUrl = `${import.meta.env.VITE_API_URL}/tags/`;

export interface TagDto {
  tag_id: number;
  tag_name: string;
  status?: string;
}

export interface TagI {
  tag_id: number;
  tag_name: string;
  status?: string;
}

export class Tag extends BaseConstructor<TagI>() implements TagI {
  constructor(data: TagDto) {
    super(data as unknown as TagI);
  }
}
interface TagsStateI {
  tags: Tag[];
  tag: Tag | null;
  loading: boolean;

  totalItems?: number;
  options: ServerOptions;
  search: string | undefined;
}

export const useTagsStore = defineStore({
  id: 'tags',
  state: () =>
    ({
      tag: null,
      tags: [],
      loading: false,

      search: undefined,
      totalItems: undefined,
      options: {
        page: 1,
        rowsPerPage: 25,
        sortBy: [],
        sortType: ['desc', 'asc']
      }
    }) as TagsStateI,
  actions: {
    async getTags(
      setData: boolean = true,
      options: GetAllOptions | undefined = {
        search_columns: ['tag_name']
      }
    ): Promise<{
      data: Tag[];
      totalItems: number;
    }> {
      this.loading = true;

      const URL =
        baseUrl +
        '?' +
        stringifyServerOptions(
          options && options.options ? options.options : this.options,
          options && options.search ? options.search : (this.search ?? null),
          options && options.search_columns ? options.search_columns : [],
          options && options.fixedFilterOptions ? options.fixedFilterOptions : null
        );

      return fetchWrapper
        .get(URL)
        .then((res: BasePaginatorResponseI<TagDto>) => {
          if (res.status_code === 200) {
            this.totalItems = res.data.total_items;
            if (setData) {
              this.tags = res.data.items.map((tag) => new Tag(tag));
            }

            this.loading = false;
            return {
              data: res.data.items.map((project) => new Tag(project)),
              totalItems: res.data.total_items
            };
          }
          this.loading = false;
          return {
            data: [],
            totalItems: 0
          };
        })
        .catch((res) => {
          if (res.status_code === 400) {
            loadParamsFailedReloadNotification('Načtení štítků selhalo', res.error, () => {
              const data = reloadWithoutParams();
              this.options = data.options;
              this.search = data.term ?? undefined;
            });
          }

          this.loading = false;
          return {
            data: [],
            totalItems: 0
          };
        });
    },

    async getTag(tag_id: number): Promise<undefined | Tag> {
      this.loading = true;

      return fetch(`${baseUrl}${tag_id}`)
        .then((res) => res.json())
        .then((res: BaseResponseI<TagDto>) => {
          if (res.status_code === 200) {
            this.tag = new Tag(res.data);
            this.loading = false;
            return new Tag(res.data);
          }

          this.loading = false;
          return undefined;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Načtení štítku selhalo', description: res.error });
          }

          this.loading = false;
          return undefined;
        });
    },

    async getTagsByIds(tag_ids: number[]): Promise<undefined | Tag[]> {
      this.loading = true;

      return this.getTags(false, {
        fixedFilterOptions: [
          {
            column: 'tag_id',
            value: tag_ids
          }
        ]
      }).then((data) => {
        this.loading = false;
        return data.data;
      });
    },

    async createTag(tag_name: string): Promise<undefined | Tag> {
      this.loading = true;

      return fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/tag/`, {
          tag_name: tag_name,
          status: 'active'
        })
        .then((res: BaseResponseI<TagDto>) => {
          if (res.status_code === 200) {
            this.loading = false;
            return new Tag(res.data);
          }

          this.loading = false;
          return undefined;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Vytvoření štítku selhalo', description: res.error });
          }

          this.loading = false;
          return undefined;
        });
    }
  }
});
