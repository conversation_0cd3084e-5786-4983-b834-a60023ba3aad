import BaseConstructor from '@/utils/BaseConstructor';
import {
  loadParamsFailedReloadNotification,
  reloadWithoutParams,
  revertServerOptionsFromLocation,
  stringifyServerOptions,
  type BasePaginatorResponseI,
  type BaseResponseI
} from '@/utils/axios';
import { fetchWrapper } from '@/utils/helpers/fetch-wrapper';
import { notification } from 'ant-design-vue';
import { defineStore } from 'pinia';
import type { ServerOptions } from 'vue3-easy-data-table';
import type { PaginatorRequestDataI } from './projects';

const baseUrl = `${import.meta.env.VITE_API_URL}/analytical-techniques`;

export enum TechniqueType {
  EXTERNAL = 'external',
  INTERNAL = 'internal',
  ALL = 'all'
}

export interface TechniqueDto {
  analytical_technique_id: number;
  name: string;
  shortcut: string;
  status: string;
  type: string;
  created_at: string;
  updated_at: string;
  parent_technique_id: number | null;
  using_column: boolean;
  sub_analytical_techniques?: undefined | TechniqueDto[];
  prefill_sample_sequence_name?: boolean;
}
export interface TechniqueI {
  analytical_technique_id: number;
  name: string;
  shortcut: string;
  status: string;
  type: TechniqueType;
  created_at: Date;
  updated_at: Date;
  parent_technique_id: number | null | Technique;
  sub_analytical_techniques: Technique[];
  using_column: boolean;
  prefill_sample_sequence_name?: boolean;
}
export class Technique extends BaseConstructor<TechniqueI>() implements TechniqueI {
  constructor(data: TechniqueDto) {
    super(data as unknown as TechniqueI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);
    this.sub_analytical_techniques =
      data.sub_analytical_techniques?.map((subTechnique) => new Technique(subTechnique)) ?? [];
  }

  get isActive() {
    return this.status === 'active';
  }

  get getStatus() {
    return this.isActive ? 'Aktivní' : 'Neaktivní';
  }

  get colorByStatus() {
    return this.isActive ? 'green' : 'red';
  }

  getSubTechniques() {
    return this.sub_analytical_techniques;
  }
}

export interface SimpleStandardDto {
  standard_id: number;
  standard_name: string;
  created_at: string;
  status: string;
  updated_at: string;
  analytical_techniques?: TechniqueDto[];
}
export interface SimpleStandardI {
  standard_id: number;
  standard_name: string;
  created_at: Date;
  status: string;
  updated_at: Date;
  analytical_techniques?: Technique[];
}

export class SimpleStandard extends BaseConstructor<SimpleStandardI>() implements SimpleStandardI {
  constructor(data: SimpleStandardDto) {
    super(data as unknown as SimpleStandardI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    if (this.analytical_techniques) {
      this.analytical_techniques =
        data.analytical_techniques?.map((technique) => new Technique(technique)) ?? [];
    }
  }
}

export interface TechniqueForSearchDto {
  analytical_technique_id: number;
  created_at: string;
  name: string;
  parent_technique_id: number | null;
  shortcut: string;
  status: string;
  type: string;
  updated_at: string;
  using_column: boolean;
  standards?: SimpleStandardDto[];
}
export interface TechniqueForSearchI {
  analytical_technique_id: number;
  created_at: Date;
  name: string;
  parent_technique_id: number | null | Technique;
  shortcut: string;
  status: string;
  type: TechniqueType;
  updated_at: Date;
  using_column: boolean;
  standards: SimpleStandard[];
}
export class TechniqueForSearch
  extends BaseConstructor<TechniqueForSearchI>()
  implements TechniqueForSearchI
{
  constructor(data: TechniqueForSearchDto) {
    super(data as unknown as TechniqueForSearchI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);
    this.standards = data.standards?.map((standard) => new SimpleStandard(standard)) ?? [];
  }
}

export type TechniquesListItemI = {
  analytical_technique_id: number;
  parent_technique_id: Technique | null;
  created_at: Date;
  updated_at: Date;
  name: string;
  shortcut: string;
  using_column: boolean;
  type: string;
  status: string;
};

interface AnalyticalTechniqueModalOptionsI {
  isEditing: boolean;
  isCreating: boolean;
  baseData: Technique | undefined;
  newData: NewAnalyticalTechniqueModalDataI | undefined;
}

interface NewAnalyticalTechniqueModalDataI {
  name: string | undefined;
  sub_technique_name?: string | undefined;
  shortcut: string | undefined;
  type: string | undefined;
  parent_technique_id: number | null;
  using_column: boolean | false;
  status: string | undefined;
  confirm: boolean | false;
  prefill_sample_sequence_name?: boolean | false;
}

interface TechniquesStateI {
  external_techniques_last_update: Date | null;
  techniques: Map<number, Technique>;
  technique: Technique | null;
  loading: boolean;
  loadingTechnique: boolean;

  showAnalyticalTechniqueModal: boolean;
  modalOptions: AnalyticalTechniqueModalOptionsI | undefined;

  items: TechniquesListItemI[];
  totalItems?: number;
  options: ServerOptions;
  search: string | undefined;
  fixedFilterOptions: null | Array<{
    column: string;
    value: null | number | string | string[] | number[] | object | object[];
  }>;

  customFixedFilterOptions: null | Array<{
    column: string;
    value: null | number | string | string[] | number[] | object | object[];
  }>;
}

export const useTechniquesStore = defineStore({
  id: 'techniques',
  state: () =>
    ({
      external_techniques_last_update: null,
      technique: null,
      techniques: new Map(),
      loading: false,
      loadingTechnique: false,

      showAnalyticalTechniqueModal: false,
      modalOptions: undefined,

      items: [],
      search: undefined,
      totalItems: undefined,
      options: {
        page: 1,
        rowsPerPage: 25,
        sortBy: [],
        sortType: ['desc', 'asc']
      },
      fixedFilterOptions: null,
      customFixedFilterOptions: null
    }) as TechniquesStateI,
  actions: {
    setTechniques(techniques: TechniqueDto[]) {
      this.techniques = new Map(
        techniques.map((technique) => [technique.analytical_technique_id, new Technique(technique)])
      );
    },

    setTechnique(technique: TechniqueDto | null) {
      this.technique = technique ? new Technique(technique) : technique;
    },

    async getAll(
      type: TechniqueType | TechniqueType[] = TechniqueType.INTERNAL,
      setData: boolean = true
    ): Promise<undefined | Technique[]> {
      this.loading = true;
      let fixedFilterOptions = [];
      switch (type) {
        case TechniqueType.INTERNAL:
          fixedFilterOptions.push({ column: 'type', value: 'internal' });
          break;
        case TechniqueType.EXTERNAL:
          fixedFilterOptions.push({ column: 'type', value: 'external' });
          break;
        default:
          fixedFilterOptions.push({ column: 'type', value: ['external', 'internal'] });
          break;
      }

      fixedFilterOptions.push({
        column: 'parent_technique_id',
        value: null
      });

      if (this.customFixedFilterOptions) {
        fixedFilterOptions = this.customFixedFilterOptions;
      }
      this.options.sortBy = ['status', 'analytical_technique_id'];
      this.options.sortType = ['asc'];
      const URL =
        baseUrl +
        '/?' +
        stringifyServerOptions(
          this.options,
          this.search ?? null,
          ['name'],
          setData ? fixedFilterOptions : this.fixedFilterOptions
        );

      return fetchWrapper
        .get(URL)
        .then((res: BasePaginatorResponseI<TechniqueDto>) => {
          if (res.status_code === 200) {
            if (setData) {
              this.totalItems = res.data.total_items;
              this.techniques = new Map(
                res.data.items.map((technique) => [
                  technique.analytical_technique_id,
                  new Technique(technique)
                ])
              );
              [...this.techniques.values()].map(async (technique) => {
                if (typeof technique.parent_technique_id === 'number') {
                  const Technique = await this.getTechnique(technique.parent_technique_id);
                  technique.parent_technique_id = Technique ?? null;
                }
              });
            }

            this.loading = false;
            return res.data.items.map((technique) => new Technique(technique));
          }
          this.loading = false;
          return undefined;
        })
        .catch((res) => {
          if (res.status_code === 400) {
            loadParamsFailedReloadNotification(
              'Načtení analytických technik selhalo',
              res.error,
              () => {
                const data = reloadWithoutParams();
                this.options = data.options;
                this.search = data.term ?? undefined;
              }
            );
          }

          this.loading = false;
          return undefined;
        });
    },

    async getTechniquesForSelect(
      techniqueSelectOptions: PaginatorRequestDataI<Technique>,
      addNewData = true
    ) {
      techniqueSelectOptions.loading = true;
      const URL =
        baseUrl +
        '/?' +
        stringifyServerOptions(
          techniqueSelectOptions.options,
          techniqueSelectOptions.search ?? null,
          techniqueSelectOptions.search_columns,
          techniqueSelectOptions.filterOptions
        );

      fetchWrapper
        .get(URL)
        .then((res: BasePaginatorResponseI<TechniqueDto>) => {
          techniqueSelectOptions.loading = false;

          if (res.status_code === 200) {
            techniqueSelectOptions.totalItems = res.data.total_items;
            if (addNewData) {
              techniqueSelectOptions.results = [
                ...techniqueSelectOptions.results,
                ...res.data.items.map((technique) => new Technique(technique))
              ];
              techniqueSelectOptions.results = [
                ...new Map(
                  techniqueSelectOptions.results.map((technique) => [
                    technique.analytical_technique_id,
                    technique
                  ])
                ).values()
              ];
            } else {
              techniqueSelectOptions.results = Array.from(
                res.data.items.map((technique) => new Technique(technique))
              );
            }

            return;
          }

          if (!addNewData) {
            techniqueSelectOptions.results = [];
          }
        })
        .catch((res) => {
          techniqueSelectOptions.loading = false;

          if (res.status_code === 400) {
            if (res.status_code === 400) {
              notification.error({ message: 'Načtení technik selhalo', description: res.error });
            }
          }

          if (!addNewData) {
            techniqueSelectOptions.results = [];
          }
        });
    },

    getTechnique(id: number) {
      this.loadingTechnique = true;

      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/analytical-technique/${id}`)
        .then((res: BaseResponseI<TechniqueDto>) => {
          this.loadingTechnique = false;

          if (res.status_code === 200) {
            return new Technique(res.data);
          }
        })
        .catch((res) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Načtení techniky selhalo', description: res.error });
          }

          this.loadingTechnique = false;
          return undefined;
        });
    },

    getTechniqueAndAddToTechniques(id: number) {
      this.loading = true;

      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/analytical-technique/${id}`)
        .then((res: BaseResponseI<TechniqueDto>) => {
          this.loading = false;

          if (res.status_code === 200) {
            this.techniques.set(res.data.analytical_technique_id, new Technique(res.data));
            return new Technique(res.data);
          }
        })
        .catch((res) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Načtení techniky selhalo', description: res.error });
          }

          this.loading = false;
          return undefined;
        });
    },

    getTechniqueDto(id: number) {
      this.loading = true;

      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/analytical-technique/${id}`)
        .then((res: BaseResponseI<TechniqueDto>) => {
          this.loading = false;

          if (res.status_code === 200) {
            return res.data;
          }
        })
        .catch((res) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Načtení techniky selhalo', description: res.error });
          }

          this.loading = false;
          return undefined;
        });
    },

    setParamsFromLocation(): void {
      const data = revertServerOptionsFromLocation();
      this.options = data.options;
      this.search = data.term ?? undefined;
    },

    showNewAnalyticalTechniqueModal(type: TechniqueType = TechniqueType.INTERNAL) {
      const getLocalStorage = localStorage.getItem('analyticalTechniquesStore');
      let parsedData;
      if (getLocalStorage) {
        parsedData = JSON.parse(getLocalStorage);
      }

      this.modalOptions = {
        isEditing: false,
        isCreating: true,
        baseData: undefined,
        newData: {
          name: undefined,
          shortcut: undefined,
          type: type,
          parent_technique_id: null,
          using_column: false,
          prefill_sample_sequence_name: false,
          status: 'active',
          confirm: false
        }
      };

      this.showAnalyticalTechniqueModal = true;
    },

    createAnalyticalTechnique(type: TechniqueType = TechniqueType.INTERNAL) {
      if (!this.modalOptions?.newData || this.modalOptions?.newData?.confirm === false) {
        notification.error({ message: 'Musíte potvrdit souhlas s vytvořením techniky' });
        return;
      }

      this.loading = true;

      const data = {
        technique_name: this.modalOptions.newData.name ?? null,
        sub_technique_name: this.modalOptions.newData.sub_technique_name ?? null,
        using_column: this.modalOptions.newData.using_column ?? false,
        prefill_sample_sequence_name:
          this.modalOptions.newData.prefill_sample_sequence_name ?? false,
        shortcut: this.modalOptions.newData.shortcut ?? '',
        type: this.modalOptions.newData.type ?? type,
        parent_technique_id: this.modalOptions.newData.parent_technique_id ?? undefined,
        status: this.modalOptions.newData.status ?? 'active'
      };

      return fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/analytical-technique/`, data)
        .then((res: BaseResponseI<TechniqueDto>) => {
          if (res.status_code === 200) {
            this.techniques.set(res.data.analytical_technique_id, new Technique(res.data));
            this.showAnalyticalTechniqueModal = false;

            notification.success({
              message: 'Vytvoření techniky proběhlo v pořádku',
              description: 'Název techniky: ' + res.data.name
            });
            this.getAll(type);
            localStorage.removeItem('analyticalTechniquesStore');
            this.loading = false;
          }
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Vytvoření techniky selhalo', description: res.error });
          } else {
            this.showAnalyticalTechniqueModal = false;
          }

          this.loading = false;
        });
    },
    resetModal() {
      this.showAnalyticalTechniqueModal = false;
      if (this.modalOptions) this.modalOptions.baseData = undefined;
      if (this.modalOptions) this.modalOptions.newData = undefined;
    },

    deleteTechnique(id: number) {
      this.loading = true;

      return fetchWrapper
        .delete(`${import.meta.env.VITE_API_URL}/analytical-technique/${id}`)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            this.techniques.delete(id);
            notification.success({ message: 'Technika byla úspěšně smazána' });
            this.getAll();
            this.loading = false;
          }
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Smazání techniky selhalo', description: res.error });
          }

          this.loading = false;
        });
    },
    async showEditModal(id: number) {
      this.loadingTechnique = true;
      const _Technique = await this.getTechnique(id);

      if (_Technique === undefined) {
        notification.error({ message: 'Technika nebyla nalezena' });
        return;
      }

      this.technique = _Technique;

      this.modalOptions = {
        isEditing: true,
        isCreating: false,
        baseData: this.technique ?? undefined,
        newData: {
          name: _Technique.name,
          shortcut: _Technique.shortcut,
          type: _Technique.type,
          parent_technique_id:
            _Technique.parent_technique_id instanceof Technique
              ? _Technique.parent_technique_id.analytical_technique_id
              : typeof _Technique.parent_technique_id === 'number'
                ? _Technique.parent_technique_id
                : null,
          using_column: _Technique.using_column,
          prefill_sample_sequence_name: _Technique.prefill_sample_sequence_name ?? false,
          status: _Technique.status,
          confirm: false
        }
      };

      this.showAnalyticalTechniqueModal = true;
      this.loadingTechnique = false;
    },

    async showPreviewModal(id: number) {
      this.loadingTechnique = true;
      const _Technique = await this.getTechnique(id);

      if (_Technique === undefined) {
        notification.error({ message: 'Technika nebyla nalezena' });
        return;
      }

      this.technique = _Technique;

      this.modalOptions = {
        isEditing: false,
        isCreating: false,
        baseData: _Technique ?? undefined,
        newData: {
          name: _Technique?.name,
          shortcut: _Technique?.shortcut,
          type: _Technique?.type,
          parent_technique_id:
            _Technique?.parent_technique_id instanceof Technique
              ? _Technique.parent_technique_id.analytical_technique_id
              : typeof _Technique.parent_technique_id === 'number'
                ? _Technique.parent_technique_id
                : null,
          using_column: _Technique?.using_column,
          prefill_sample_sequence_name: _Technique?.prefill_sample_sequence_name ?? false,
          status: _Technique?.status,
          confirm: false
        }
      };

      this.showAnalyticalTechniqueModal = true;
      this.loadingTechnique = false;
    },

    updateAnalyticalTechnique(type: TechniqueType = TechniqueType.INTERNAL) {
      if (!this.modalOptions?.newData) {
        notification.error({ message: 'Data pro aktualizaci techniky nejsou k dispozici' });
        return;
      }

      this.loading = true;

      const data = {
        name: this.modalOptions.newData.name ?? '',
        shortcut: this.modalOptions.newData.shortcut ?? '',
        type: this.modalOptions.newData.type ?? TechniqueType.INTERNAL,
        parent_technique_id: this.modalOptions.newData.parent_technique_id ?? null,
        using_column: this.modalOptions.newData.using_column ?? false,
        prefill_sample_sequence_name:
          this.modalOptions.newData.prefill_sample_sequence_name ?? false,
        status: this.modalOptions.newData.status ?? 'active'
      };

      return fetchWrapper
        .put(
          `${import.meta.env.VITE_API_URL}/analytical-technique/${this.technique?.analytical_technique_id}`,
          data
        )
        .then((res: BaseResponseI<TechniqueDto>) => {
          if (res.status_code === 200) {
            this.techniques.set(res.data.analytical_technique_id, new Technique(res.data));
            this.showAnalyticalTechniqueModal = false;

            notification.success({
              message: 'Aktualizace techniky proběhla v pořádku',
              description: 'Název techniky: ' + res.data.name
            });
            this.getAll(type);
            this.loading = false;
          }
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Aktualizace techniky selhalo', description: res.error });
          } else {
            this.showAnalyticalTechniqueModal = false;
          }

          this.loading = false;
        });
    }
  }
});
