<template>
  <v-card
    variant="outlined"
    :color="technique.colorByStatus"
    class="card-hover-border bg-containerBg"
    :subtitle="`Stav: ${technique.status}`"
    :title="technique.name"
    link
    :to="{
      name: 'ListOfMethodForTechnique',
      params: { analytical_technique_id: technique.analytical_technique_id }
    }"
  >
    <template #append>
      <router-link
        :to="{
          name: 'ListOfMethodForTechnique',
          params: { analytical_technique_id: technique.analytical_technique_id }
        }"
        target="_blank"
      >
        <v-icon color="primary">mdi-open-in-new</v-icon>
      </router-link>
    </template>
  </v-card>
</template>
<script lang="ts" setup>
  import type { Technique } from '@/stores/techniques';

  defineEmits(['reload']);
  defineProps<{
    technique: Technique;
    isParentClosed: boolean;
  }>();
</script>
