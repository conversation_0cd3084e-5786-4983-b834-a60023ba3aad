<template>
  <section class="my-5">
    <span class="text-h4">Techniky</span>
  </section>
  <v-row v-if="techniques.length > 0">
    <v-col
      v-for="technique in techniques.filter((technique) =>
        techniqueSearch
          ? technique.name.toUpperCase().includes(techniqueSearch.toUpperCase().replace(/\s+/g, ''))
          : true
      )"
      :key="technique.analytical_technique_id"
      cols="12"
    >
      <TechniqueItem
        :technique="technique"
        :is-parent-closed="isParentClosed"
        @reload="$emit('reload')"
      />
    </v-col>
  </v-row>
  <NotFoundItem v-else>
    <template #notFound>Nebyly nalezeny žádné vzorky</template>
  </NotFoundItem>
</template>

<script lang="ts" setup>
  import NotFoundItem from '@/components/shared/NotFoundItem.vue';
  import TechniqueItem from './TechniqueItem.vue';
  import type { Technique } from '@/stores/techniques';

  defineEmits(['reload']);
  defineProps<{
    techniques: Technique[];
    techniqueSearch?: string | undefined;
    isParentClosed: boolean;
  }>();
</script>
