import BaseConstructor from '@/utils/BaseConstructor';
import { type BaseResponseI } from '@/utils/axios';
import { fetchWrapper } from '@/utils/helpers/fetch-wrapper';
import { defineStore } from 'pinia';
import { User, type UserDto } from './auth';
import type { FormDto } from './forms';

const baseUrl = `${import.meta.env.VITE_API_URL}/user-lock`;

export enum UserLockTableName {
  FORM = 'form',
  EXPERIMENT = 'experiment',
  ATTEMPT = 'attempt',
  INVESTIGATION = 'investigation',
  message = 'message'
}

export enum UserLockStatus {
  LOCKED = 'locked',
  UNLOCKED = 'unlocked',
  NO_LOCK_FOUND = 'no_lock_found',
  LOCKED_BY_OTHER_USER = 'locked_by_other_user'
}

export interface LockTableDto extends FormDto {
  edited_by: undefined | UserLockDto;
}

export interface LockTableI extends FormDto {
  edited_by: undefined | UserLock;
}

export interface UserLockDto {
  status: UserLockStatus;
  user_id: number | null;
  table_name: string | null;
  primary_key_value: number | null;
  lock_time: string | null;
  lock_duration: string | null;
  user: UserDto | null;
}

export interface UserLockI {
  status: UserLockStatus;
  user_id: number | null;
  table_name: string | null;
  primary_key_value: number | null;
  lock_time: Date | null;
  lock_duration: string | null;
  user: User | null;
}

export class UserLock extends BaseConstructor<UserLockI>() implements UserLockI {
  constructor(data: UserLockDto) {
    super(data as unknown as UserLockI);

    this.user = data.user ? new User(data.user) : null;
    this.lock_time = data.lock_time ? new Date(data.lock_time) : null;
  }
}

interface UserLockStateI {
  loading: boolean;
  userLock: UserLock | undefined;
}

export const useUserLock = defineStore({
  id: 'userLock',
  state: () =>
    ({
      loading: false,
      userLock: undefined
    }) as UserLockStateI,
  actions: {
    async getUserLockStatus(table_name: UserLockTableName, primary_key_value: number) {
      this.loading = true;
      return await fetchWrapper
        .get(
          `${baseUrl}/check_lock?table_name=${table_name}&primary_key_value=${primary_key_value}`
        )
        .then(async (res: BaseResponseI<UserLockDto>) => {
          this.loading = false;

          if (res.status_code === 200) {
            return new UserLock(res.data);
          }

          return undefined;
        })
        .catch(() => {
          this.loading = false;
          return undefined;
        });
    },

    async unlockRowForUser(
      user_id: number,
      table_name: UserLockTableName,
      primary_key_value: number
    ) {
      this.loading = true;
      return await fetchWrapper
        .delete(
          `${baseUrl}/unlock?table_name=${table_name}&primary_key_value=${primary_key_value}&user_id=${user_id}`
        )
        .then(async (res: BaseResponseI<UserLockDto>) => {
          this.loading = false;

          if (res.status_code === 200) {
            return new UserLock(res.data);
          }

          return undefined;
        })
        .catch(() => {
          this.loading = false;
          return undefined;
        });
    },

    async lockRowForUser(
      user_id: number,
      table_name: UserLockTableName,
      primary_key_value: number
    ) {
      this.loading = true;
      return fetchWrapper
        .post(
          `${baseUrl}/lock?table_name=${table_name}&primary_key_value=${primary_key_value}&user_id=${user_id}`
        )
        .then(async (res: BaseResponseI<UserLockDto>) => {
          this.loading = false;

          if (res.status_code === 200) {
            return new UserLock(res.data);
          }

          return undefined;
        })
        .catch(() => {
          this.loading = false;
          return undefined;
        });
    }
  }
});
