import {
  loadParamsFailedReloadNotification,
  reloadWithoutParams,
  revertServerOptionsFromLocation,
  stringifyServerOptions,
  type BasePaginatorResponseI,
  type BaseResponseI
} from '@/utils/axios';
import BaseConstructor from '@/utils/BaseConstructor';
import { fetchWrapper } from '@/utils/helpers/fetch-wrapper';
import type { CreateUserRoleI } from '@/views/pages/dashboard/management/users/components/UserRoleModal.vue';
import { notification } from 'ant-design-vue';
import { defineStore } from 'pinia';
import type { ServerOptions } from 'vue3-easy-data-table';
import { Permission, Role, User, type PermissionDto, type RoleDto, type UserDto } from './auth';
import type { GetAllOptions, PaginatorRequestDataI } from './projects';
import { fa } from 'vuetify/locale';

const baseUrl = `${import.meta.env.VITE_API_URL}/users`;

export type UsersListItemI = {
  user_id: number;
  created_at: Date;
  updated_at: Date;
  first_name: string;
  last_name: string;
  user_email: string;
  status: string;
};

interface UserModalOptionsI {
  isEditing: boolean;
  isCreating: boolean;
  user: User | undefined;
  newUserData: CreateNewUserDataI | undefined;
}

interface CreateNewUserDataI {
  first_name: string | undefined;
  last_name: string | undefined;
  user_email: string | undefined;
  name_shortcut: string | undefined;
  roles: number[] | undefined;
  permissions: number[] | undefined;
  status: string | undefined;
  confirm: boolean | false;
}

interface LinkModalOptionsI {
  isEditing: boolean;
  isCreating: boolean;
  shortcut: Shortcut | undefined;
  newShortcutData: NewShortcutDateI | undefined;
}

interface UsersStoreStateI {
  users: Map<number, User>;
  allUsers: User[];
  user: User | undefined;

  items: UsersListItemI[];
  loading: boolean;
  dummyEdit: boolean;
  showUserModal: boolean;
  showUserRoleModal: boolean;
  userModalOptions: UserModalOptionsI | undefined;

  showLinkModal: boolean;
  linkModalOptions: LinkModalOptionsI | undefined;

  totalItems?: number;
  options: ServerOptions;

  userProjects: ProjectUser[];
  userProjectsTotalItems?: number;
  userProjectsOptions: ServerOptions;

  search: string | undefined;
  search_type: 'AND' | 'OR' | null;
  fixedSearchColumns: string[];

  role: Role | undefined;
  roles: Role[];
  permissions: Array<Permission>;
}

export const useUsersStore = defineStore({
  id: 'users',
  state: () =>
    ({
      items: [],
      users: new Map(),
      user: undefined,
      allUsers: [],
      search: undefined,
      search_type: null,
      dummyEdit: false,
      showLinkModal: false,
      linkModalOptions: undefined,

      totalItems: undefined,
      fixedSearchColumns: ['first_name', 'last_name', 'user_email'],
      options: {
        page: 1,
        rowsPerPage: 25,
        sortBy: [],
        sortType: ['desc', 'asc']
      },

      userProjects: [],
      userProjectsTotalItems: undefined,
      userProjectsOptions: {
        page: 1,
        rowsPerPage: 25,
        sortBy: [],
        sortType: ['desc', 'asc']
      },

      loading: false,
      showUserModal: false,
      showUserRoleModal: false,
      userModalOptions: undefined,

      role: undefined,
      roles: [],
      permissions: []
    }) as UsersStoreStateI,
  actions: {
    async getAll(
      setData: boolean = true,
      options: GetAllOptions | undefined = {
        search_columns: ['first_name', 'last_name', 'user_email']
      },
      search_type: 'AND' | 'OR' = 'AND'
    ) {
      this.loading = true;
      this.options.sortBy = ['status', 'user_id'];
      this.options.sortType = ['asc'];
      const URL =
        baseUrl +
        '/?' +
        stringifyServerOptions(
          options && options.options ? options.options : this.options,
          options && options.search ? options.search : (this.search ?? null),
          options && options.search_columns ? options.search_columns : [],
          options && options.fixedFilterOptions ? options.fixedFilterOptions : null,
          this.search_type ?? search_type
        );

      fetchWrapper
        .get(URL)
        .then((res: BasePaginatorResponseI<UserDto>) => {
          if (res.status_code === 200) {
            if (setData) {
              this.totalItems = res.data.total_items;
              this.allUsers = res.data.items.map((user) => new User(user));
              this.users = new Map(res.data.items.map((user) => [user.user_id, new User(user)]));
            }
          }
          this.loading = false;
        })
        .catch((res) => {
          if (res.status_code === 400) {
            loadParamsFailedReloadNotification('Načtení uživatelů selhalo', res.error, () => {
              const data = reloadWithoutParams();
              this.options = data.options;
              this.search = data.term ?? undefined;
            });
          }

          this.loading = false;
        });
    },

    async getUsersForSelect(userSelectOptions: PaginatorRequestDataI<User>, addNewData = true) {
      userSelectOptions.loading = true;
      const URL =
        baseUrl +
        '/?' +
        stringifyServerOptions(
          userSelectOptions.options,
          userSelectOptions.search ?? null,
          userSelectOptions.search_columns,
          userSelectOptions.filterOptions,
          userSelectOptions.search_type
        );

      fetchWrapper
        .get(URL)
        .then((res: BasePaginatorResponseI<UserDto>) => {
          userSelectOptions.loading = false;

          if (res.status_code === 200) {
            userSelectOptions.totalItems = res.data.total_items;
            if (addNewData) {
              userSelectOptions.results = [
                ...userSelectOptions.results,
                ...res.data.items.map((user) => new User(user))
              ];
              userSelectOptions.results = [
                ...new Map(userSelectOptions.results.map((user) => [user.user_id, user])).values()
              ];
            } else {
              userSelectOptions.results = Array.from(res.data.items.map((user) => new User(user)));
            }

            return;
          }

          if (!addNewData) {
            userSelectOptions.results = [];
          }
        })
        .catch((res) => {
          userSelectOptions.loading = false;

          if (res.status_code === 400) {
            notification.error({ message: 'Načtení uživatelů selhalo', description: res.error });
          }

          if (!addNewData) {
            userSelectOptions.results = [];
          }
        });
    },

    async getUser(user_id: number) {
      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/user/${user_id}`)
        .then((res: BaseResponseI<UserDto>) => {
          if (res.status_code === 200) {
            this.user = new User(res.data);
            return new User(res.data);
          }
        });
    },

    async getUserPermissions(user: User) {
      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/user/${user.user_id}/permissions`)
        .then((res: BaseResponseI<PermissionDto[]>) => {
          if (res.status_code === 200) {
            user.permissions = res.data.map((permission) => new Permission(permission));
          }
        });
    },
    async getUserRoles(user: User) {
      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/user/${user.user_id}/roles`)
        .then((res: BaseResponseI<RoleDto[]>) => {
          if (res.status_code === 200) {
            user.roles = res.data.map((role) => new Role(role));
          }
        });
    },
    async showPreviewModal(user_id: number) {
      this.loading = true;
      const _User = await this.getUser(user_id);

      if (_User === undefined) {
        return;
      }

      this.user = _User;

      await this.getUserPermissions(_User);
      await this.getUserRoles(_User);

      this.userModalOptions = {
        newUserData: {
          first_name: _User.first_name,
          last_name: _User.last_name,
          user_email: _User.user_email,
          name_shortcut: _User.name_shortcut,
          roles: _User.roles?.map((role) => role.system_role_id) ?? [],
          permissions:
            _User.permissions?.map((permission) => permission.system_permission_id) ?? [],
          status: _User.status,
          confirm: false
        },
        isEditing: false,
        isCreating: false,
        user: _User
      };

      this.showUserModal = true;
      this.loading = false;
    },

    async showNewShortcutModal() {
      this.linkModalOptions = {
        isEditing: false,
        isCreating: true,
        shortcut: undefined,
        newShortcutData: {
          name: '',
          url_path: '',
          order: 0
        }
      };

      this.showLinkModal = true;
    },

    async showEditShortcutModal(shortcut: Shortcut) {
      this.linkModalOptions = {
        isEditing: true,
        isCreating: false,
        shortcut: shortcut,
        newShortcutData: {
          name: shortcut.name,
          url_path: shortcut.url_path,
          order: shortcut.order
        }
      };

      this.showLinkModal = true;
    },

    async resetShortcutModalData() {
      this.showLinkModal = false;
      if (this.linkModalOptions) this.linkModalOptions.shortcut = undefined;
      if (this.linkModalOptions) this.linkModalOptions.newShortcutData = undefined;
    },

    async showEditModal(user_id: number) {
      this.loading = true;
      const _User = await this.getUser(user_id);

      if (_User === undefined) {
        return;
      }

      this.user = _User;

      await this.getUserPermissions(_User);
      await this.getUserRoles(_User);

      this.userModalOptions = {
        newUserData: {
          first_name: _User.first_name,
          last_name: _User.last_name,
          user_email: _User.user_email,
          name_shortcut: _User.name_shortcut,
          roles: _User.roles?.map((role) => role.system_role_id) ?? [],
          permissions:
            _User.permissions?.map((permission) => permission.system_permission_id) ?? [],
          status: _User.status,
          confirm: false
        },
        isEditing: true,
        isCreating: false,
        user: _User
      };

      this.showUserModal = true;
      this.loading = false;
    },
    async showRoleModal() {
      this.showUserRoleModal = true;
    },
    async showCreateModal() {
      const getLocalStorage = localStorage.getItem('usersStore');
      let parsedData;
      if (getLocalStorage) {
        parsedData = JSON.parse(getLocalStorage);
        if (parsedData?.newUserData?.permissions.length > 0) {
          this.dummyEdit = true;
        }
      }

      this.userModalOptions = {
        newUserData: {
          first_name: '',
          last_name: '',
          user_email: '',
          name_shortcut: '',
          roles: undefined,
          permissions: undefined,
          status: 'active',
          confirm: false
        },
        isEditing: false,
        isCreating: true,
        user: undefined
      };

      this.showUserModal = true;
    },
    async resetModalUserData() {
      this.showUserModal = false;
      if (this.userModalOptions) this.userModalOptions.user = undefined;
      if (this.userModalOptions) this.userModalOptions.newUserData = undefined;
    },
    async createUser(password: string) {
      if (!password) {
        password = await this.generatePassowrd();
      }
      if (
        !this.userModalOptions?.newUserData ||
        this.userModalOptions?.newUserData?.confirm === false
      ) {
        notification.error({ message: 'Musíte potvrdit souhlas s vytvořením uživatele' });
        return;
      }
      this.loading = true;
      if (this.userModalOptions?.newUserData?.permissions) {
        if (
          !this.hasPermissionByName(
            this.userModalOptions.newUserData.permissions,
            'edit_technological_department'
          )
        ) {
          this.userModalOptions.newUserData.name_shortcut = undefined;
        }
      }
      const data = {
        first_name: this.userModalOptions.newUserData.first_name,
        last_name: this.userModalOptions.newUserData.last_name,
        user_email: this.userModalOptions.newUserData.user_email,

        password_hash: password,
        name_shortcut: this.userModalOptions.newUserData.name_shortcut || null,
        roles: this.userModalOptions.newUserData.roles ?? [],
        permissions: this.userModalOptions.newUserData.permissions ?? []
      };

      return fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/user/`, data)
        .then((res: BaseResponseI<UserDto>) => {
          if (res.status_code === 200) {
            this.users.set(res.data.user_id, new User(res.data));
            this.showUserModal = false;

            notification.success({
              message: 'Vytvoření uživatele proběhlo v pořádku',
              description: 'Email uživatele: ' + res.data.user_email
            });
            this.getAll();
            localStorage.removeItem('usersStore');
            this.loading = false;
          }
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Vytvoření uživatele selhalo', description: res.error });
          } else {
            this.showUserModal = false;
          }

          this.loading = false;
        });
    },
    async updateUser() {
      if (!this.userModalOptions?.newUserData || !this.userModalOptions.user) {
        notification.error({ message: 'Vypleňte data pro aktualizaci uživatele' });
        return false;
      }

      this.loading = true;

      const data = {
        first_name: this.userModalOptions.newUserData.first_name,
        last_name: this.userModalOptions.newUserData.last_name,
        user_email: this.userModalOptions.newUserData.user_email,
        name_shortcut: this.userModalOptions.newUserData.name_shortcut || null,
        roles: this.userModalOptions.newUserData.roles ?? [],
        permissions: this.userModalOptions.newUserData.permissions ?? [],

        status: this.userModalOptions.newUserData.status ?? 'active'
      };

      return fetchWrapper
        .put(`${import.meta.env.VITE_API_URL}/user/${this.userModalOptions.user.user_id}`, data)
        .then((res: BaseResponseI<UserDto>) => {
          this.loading = false;

          if (res.status_code === 200) {
            this.showUserModal = false;
            notification.success({ message: 'Aktualizace uživatele proběhla v pořádku' });
            this.getAll();
            return true;
          }

          return false;
        })
        .catch(() => {
          this.showUserModal = false;
          this.loading = false;

          return false;
        });
    },
    async deleteUser(user_id: number) {
      return fetchWrapper
        .delete(`${import.meta.env.VITE_API_URL}/user/${user_id}`)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            this.users.delete(user_id);
            this.showUserModal = false;

            notification.success({ message: 'Uživatel byl odstraněn' });

            this.getAll();
          }
        })
        .catch(() => {
          this.showUserModal = false;
        });
    },

    async getAllRoles() {
      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/system-roles/`)
        .then((res: BaseResponseI<RoleDto[]>) => {
          if (res.status_code === 200) {
            const roles = res.data.map((role) => new Role(role));
            this.roles = roles;
          }
        });
    },
    async getAllPermissions() {
      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/system-permissions/`)
        .then((res: BaseResponseI<PermissionDto[]>) => {
          if (res.status_code === 200) {
            const permissions = res.data.map((permission) => new Permission(permission));
            this.permissions = permissions;
          }
        });
    },

    setParamsFromLocation(): void {
      const data = revertServerOptionsFromLocation();
      this.options = data.options;
      this.search = data.term ?? undefined;
    },
    updateRole(role: Role) {
      this.role = role;
    },
    createNewUserRole(data: CreateUserRoleI): Promise<boolean | undefined> {
      if (data.name === '') {
        notification.error({ message: 'Název role nesmí být prázdný' });
        return new Promise(() => false);
      }

      this.loading = true;

      return fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/system-role/`, data)
        .then((res: BaseResponseI<RoleDto>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Role byla vytvořena' });
            this.getAllRoles();

            this.loading = false;
            return true;
          }

          this.loading = false;
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Vytvoření role selhalo', description: res.error });
          }

          this.loading = false;
          return false;
        });
    },
    updateSystemRole(id: number, permissions_ids: number[]) {
      this.loading = true;

      const role = this.roles.find((role) => role.system_role_id === id);
      if (role === undefined) {
        notification.error({ message: 'Role nebyla nalezena' });
        this.loading = false;
        return false;
      }

      const data = {
        name: role.name,
        permissions: permissions_ids
      };

      return fetchWrapper
        .put(`${import.meta.env.VITE_API_URL}/system-role/${role.system_role_id}`, data)
        .then((res: BaseResponseI<RoleDto>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Role byla aktualizována' });
            this.getAllRoles();

            this.loading = false;
          }
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Aktualizace role selhala', description: res.error });
          }

          this.loading = false;
        });
    },
    async fullUpdateSystemRole(role: Role) {
      this.loading = true;

      const data = {
        name: role.name,
        permissions: role.newPermissions
      };

      return fetchWrapper
        .put(`${import.meta.env.VITE_API_URL}/system-role/${role.system_role_id}`, data)
        .then((res: BaseResponseI<RoleDto>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Role byla aktualizována' });
            this.getAllRoles();

            this.loading = false;
          }
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Aktualizace role selhala', description: res.error });
          }

          this.loading = false;
        });
    },
    deleteSystemRole(id: number) {
      this.loading = true;

      const role = this.roles.find((role) => role.system_role_id === id);
      if (role === undefined) {
        notification.error({ message: 'Role nebyla nalezena' });
        this.loading = false;
        return false;
      }

      return fetchWrapper
        .delete(`${import.meta.env.VITE_API_URL}/system-role/${role.system_role_id}`)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Role byla odstraněna' });
            this.getAllRoles();

            this.loading = false;
          }
        })
        .catch((res: BaseResponseI<null>) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Odstranění role selhalo', description: res.error });
          }

          this.loading = false;
        });
    },

    async getAllUserProjects(user_id: number) {
      this.loading = true;
      const URL =
        `${import.meta.env.VITE_API_URL}/user/${user_id}/projects` +
        '?' +
        stringifyServerOptions(this.userProjectsOptions, null, [], null, this.search_type ?? 'AND');

      return fetchWrapper
        .get(URL)
        .then((res: BasePaginatorResponseI<ProjectUserDto>) => {
          this.loading = false;

          if (res.status_code === 200) {
            this.userProjectsTotalItems = res.data.total_items;
            this.userProjects = res.data.items.map((project) => new ProjectUser(project));
          }

          return undefined;
        })
        .catch((res) => {
          if (res.status_code === 400) {
            loadParamsFailedReloadNotification('Načtení uživatelů selhalo', res.error, () => {
              const data = reloadWithoutParams();
              this.userProjectsOptions = data.options;
              this.search = data.term ?? undefined;
            });
          }

          this.loading = false;
          return undefined;
        });
    },

    async addLaboratoryToUser(user_id: number, laborant_id: number) {
      this.loading = true;

      return fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/user/${user_id}/laborant/${laborant_id}`)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Laborant byl přidán uživateli' });
          }

          this.loading = false;
        })
        .catch((res) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Přidání laboranta selhalo', description: res.error });
          }
          this.loading = false;
        });
    },

    async deleteLaboratoryFromUser(user_id: number, laborant_id: number) {
      this.loading = true;

      return fetchWrapper
        .delete(`${import.meta.env.VITE_API_URL}/user/${user_id}/laborants/${laborant_id}`)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Laborant byl odebrán uživateli' });
          }

          this.loading = false;
        })
        .catch((res) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Odebrání laboranta selhalo', description: res.error });
          }
          this.loading = false;
        });
    },

    async getUserShortcuts(user_id: number) {
      return fetchWrapper
        .get(`${import.meta.env.VITE_API_URL}/user/${user_id}/shortcuts`)
        .then((res: BaseResponseI<ShortcutDto[]>) => {
          if (res.status_code === 200) {
            return res.data.map((shortcut) => new Shortcut(shortcut));
          }

          return [];
        });
    },

    async createShortcut(user_id: number, data: { name: string; url_path: string; order: number }) {
      return fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/user/${user_id}/shortcuts`, data)
        .then((res: BaseResponseI<ShortcutDto>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Odkaz byl vytvořen' });
            return true;
          }

          return false;
        })
        .catch((res) => {
          if (res.status_code === 400) {
            notification.error({ message: 'Vytvoření odkazu selhalo', description: res.error });
          } else {
            notification.error({ message: 'Vytvoření odkazu selhalo' });
          }

          return false;
        });
    },

    async deleteShortcut(user_id: number, shortcut_id: number) {
      return fetchWrapper
        .delete(`${import.meta.env.VITE_API_URL}/user/${user_id}/shortcuts/${shortcut_id}`)
        .then((res: BaseResponseI<null>) => {
          if (res.status_code === 200) {
            notification.success({ message: 'Odkaz byl odstraněn' });
            return true;
          }

          return false;
        });
    },

    async updateShortcut(
      user_id: number,
      shortcut_id: number,
      data: { name: string; url_path: string; order: number }
    ) {
      this.loading = true;
      return fetchWrapper
        .put(`${import.meta.env.VITE_API_URL}/user/${user_id}/shortcuts/${shortcut_id}`, data)
        .then((res: BaseResponseI<ShortcutDto>) => {
          this.loading = false;

          if (res.status_code === 200) {
            // notification.success({ message: 'Odkaz byl aktualizován' });
            return true;
          }

          return false;
        });
    },
    async generatePassowrd() {
      this.loading = true;
      const lowercase = 'abcdefghijklmnopqrstuvwxyz';
      const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
      const numbers = '0123456789';
      const specialChars = '!@#$%^&*()-_=+[]{}|;:,.<>?';
      const passwordLength = 10;
      const getRandomChar = (charset: string) =>
        charset[Math.floor(Math.random() * charset.length)];
      let password = '';
      password += getRandomChar(lowercase);
      password += getRandomChar(uppercase);
      password += getRandomChar(numbers);
      password += getRandomChar(specialChars);
      const allChars = lowercase + uppercase + numbers + specialChars;
      while (password.length < passwordLength) {
        password += getRandomChar(allChars);
      }
      password = password
        .split('')
        .sort(() => Math.random() - 0.5)
        .join('');
      this.loading = false;

      return password;
    },
    async updateUserPassword(user_id: number, new_password: string) {
      this.loading = true;
      return fetchWrapper
        .post(`${import.meta.env.VITE_API_URL}/user/${user_id}/reset-password`, {
          password: new_password
        })
        .then((res: BaseResponseI<null>) => {
          this.loading = false;

          if (res.status_code === 200) {
            notification.success({ message: 'Heslo bylo změněno' });
            return true;
          }

          return false;
        });
    },
    generateInitials(first_name: string, last_name: string) {
      if (!this.allUsers || !first_name || !last_name) {
        return;
      }
      const existingShortcuts = new Set(this.allUsers.map((u) => u.name_shortcut));
      const buildShortcut = (firstName: string, lastName: string, length: number): string => {
        const firstPart = firstName.slice(0, Math.min(length, firstName.length)).toUpperCase();
        const lastPart = lastName.slice(0, Math.max(1, length - firstPart.length)).toUpperCase();
        return firstPart + lastPart;
      };
      let length = 1;
      let shortcut = buildShortcut(first_name, last_name, length);

      while (existingShortcuts.has(shortcut)) {
        length++;
        if (length > first_name.length + last_name.length) {
          return buildShortcut(first_name, last_name, 1);
        }
        shortcut = buildShortcut(first_name, last_name, length);
      }
      notification.success({ message: 'Iniciály byla vygenerovány' });
      return shortcut;
    },

    hasPermissionByName(permissionIds: number[] | undefined, permissionName: string): boolean {
      if (!permissionIds || permissionIds.length === 0) {
        return false;
      }

      const permission = this.permissions.find((p) => p.name === permissionName);
      if (!permission) {
        return false;
      }

      return permissionIds.includes(permission.system_permission_id);
    }
  }
});

export interface NewShortcutDateI {
  name: string;
  url_path: string;
  order: number;
}

interface ShortcutDto {
  shortcut_id: number;
  user_id: number;
  name: string;
  url_path: string;
  order: number;
}
export class Shortcut extends BaseConstructor<ShortcutDto>() implements ShortcutDto {
  constructor(data: ShortcutDto) {
    super(data as unknown as ShortcutDto);
  }
}

interface ProjectUserDto {
  project_id: number;
  user_id: number;
  name: string;
  status: string;
  created_at: string;
  updated_at: string;
  responsible_users: UserDto[];
}

interface ProjectUserI {
  project_id: number;
  user_id: number;
  name: string;
  status: string;
  created_at: Date;
  updated_at: Date;
  responsible_users: User[];
}

export class ProjectUser extends BaseConstructor<ProjectUserI>() implements ProjectUserI {
  constructor(data: ProjectUserDto) {
    super(data as unknown as ProjectUserI);

    this.created_at = new Date(data.created_at);
    this.updated_at = new Date(data.updated_at);

    this.responsible_users = data.responsible_users.map((user) => new User(user));
  }
}
