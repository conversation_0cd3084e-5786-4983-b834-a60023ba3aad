import { defineStore } from 'pinia';
import { notification } from 'ant-design-vue';
import { useAuthStore } from './auth';
import { translateFieldName } from '@/utils/fieldNameTranslations';

export interface LockFieldMessage {
  type: 'lock_field';
  field_name: string;
}

export interface UnlockFieldMessage {
  type: 'unlock_field';
  field_name: string;
}

export interface RequestFieldAccessMessage {
  type: 'request_field_access';
  field_name: string;
  message?: string;
}

export interface RespondToAccessRequestMessage {
  type: 'respond_to_access_request';
  field_name: string;
  requester_id: string;
  approved: boolean;
  response_message?: string;
}

export interface GetLockedFieldsMessage {
  type: 'get_locked_fields';
}

export interface GetPendingRequestsMessage {
  type: 'get_pending_requests';
  field_name?: string;
}

export interface FormUpdateMessage {
  type: 'form_update';
  field: string;
  value: any;
}

// Real-time field update (no type field, direct format)
export interface RealTimeFieldUpdate {
  field: string;
  value: any;
}

export type ClientMessage =
  | LockFieldMessage
  | UnlockFieldMessage
  | RequestFieldAccessMessage
  | RespondToAccessRequestMessage
  | GetLockedFieldsMessage
  | GetPendingRequestsMessage
  | FormUpdateMessage
  | RealTimeFieldUpdate;

// WebSocket Message Types - Server to Client
export interface LockResponseMessage {
  type: 'lock_response';
  field_name: string;
  success: boolean;
  message: string;
}

export interface UnlockResponseMessage {
  type: 'unlock_response';
  field_name: string;
  success: boolean;
  message: string;
}

export interface FieldStatusMessage {
  type: 'field_status';
  field_name: string;
  user_id: string;
  status: 'locked' | 'unlocked' | 'auto_unlocked';
  timestamp: number;
}

export interface AccessRequestMessage {
  type: 'access_request';
  field_name: string;
  requester_id: string;
  requester_message?: string;
  timestamp: number;
}

export interface AccessResponseMessage {
  type: 'access_response';
  field_name: string;
  approved: boolean;
  response_message?: string;
  timestamp: number;
}

export interface CurrentLocksMessage {
  type: 'current_locks';
  locked_fields: Record<
    string,
    {
      user_id: string;
      timestamp: number;
    }
  >;
}

export interface UpdateErrorMessage {
  type: 'update_error';
  field: string;
  message: string;
}

export interface PresenceUpdateMessage {
  type: 'presence_update';
  users: string[];
}

export interface FormUpdateBroadcastMessage {
  type: 'form_update';
  success: boolean;
  user_id?: string; // ID of user who made the update
  payload?: {
    field: string;
    value: any;
  };
}

export interface LockedFieldsResponseMessage {
  type: 'locked_fields_response';
  locked_fields: Record<string, FieldLockInfo>;
}

export type ServerMessage =
  | LockResponseMessage
  | UnlockResponseMessage
  | FieldStatusMessage
  | AccessRequestMessage
  | AccessResponseMessage
  | CurrentLocksMessage
  | UpdateErrorMessage
  | PresenceUpdateMessage
  | FormUpdateBroadcastMessage
  | LockedFieldsResponseMessage;

// Field Lock Information
export interface FieldLockInfo {
  user_id: string;
  timestamp: number;
  user_name?: string;
}

// Access Request Information
export interface AccessRequestInfo {
  field_name: string;
  requester_id: string;
  requester_message?: string;
  timestamp: number;
}

// WebSocket Connection Status
export enum WebSocketStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}

// Store State Interface
interface WebSocketState {
  // Connection
  ws: WebSocket | null;
  status: WebSocketStatus;
  reconnectAttempts: number;
  maxReconnectAttempts: number;
  reconnectInterval: number;

  // Form Context
  currentFormId: number | null;
  currentUserId: string | null;

  // Field Locks
  lockedFields: Record<string, FieldLockInfo>;
  myLockedFields: Set<string>;

  // Real-time Updates
  fieldUpdates: Record<string, { value: any; userId: string; timestamp: number }>;
  typingIndicators: Record<string, { userId: string; timestamp: number }>;

  // Access Requests
  pendingAccessRequests: AccessRequestInfo[];

  // User Presence
  activeUsers: string[];

  // UI State
  showNotifications: boolean;
  loading: boolean;
}

export const useWebSocketStore = defineStore({
  id: 'websocket',
  state: () =>
    ({
      // Connection
      ws: null,
      status: WebSocketStatus.DISCONNECTED,
      reconnectAttempts: 0,
      maxReconnectAttempts: 5,
      reconnectInterval: 3000,

      // Form Context
      currentFormId: null,
      currentUserId: null,

      // Field Locks
      lockedFields: {},
      myLockedFields: new Set(),

      // Real-time Updates
      fieldUpdates: {},
      typingIndicators: {},

      // Access Requests
      pendingAccessRequests: [],

      // User Presence
      activeUsers: [],

      // UI State
      showNotifications: true,
      loading: false
    }) as WebSocketState,

  getters: {
    isConnected: (state) => state.status === WebSocketStatus.CONNECTED,

    isFieldLocked: (state) => (fieldName: string) => {
      return fieldName in state.lockedFields;
    },

    isFieldLockedByMe: (state) => (fieldName: string) => {
      return state.myLockedFields.has(fieldName);
    },

    isFieldLockedByOther: (state) => (fieldName: string) => {
      const lockInfo = state.lockedFields[fieldName];
      return lockInfo && lockInfo.user_id !== state.currentUserId;
    },

    getFieldLockInfo: (state) => (fieldName: string) => {
      return state.lockedFields[fieldName] || null;
    },

    hasPendingAccessRequests: (state) => state.pendingAccessRequests.length > 0,

    getAccessRequestsForField: (state) => (fieldName: string) => {
      return state.pendingAccessRequests.filter((req) => req.field_name === fieldName);
    }
  },

  actions: {
    // Connection Management
    async connect(formId: number, userId: string, token?: string) {
      // Validate inputs
      if (!formId || !userId) {
        console.error('Cannot connect WebSocket: missing formId or userId');
        return;
      }

      if (!token) {
        console.error('Cannot connect WebSocket: missing authentication token');
        return;
      }

      if (this.ws && this.status === WebSocketStatus.CONNECTED) {
        this.disconnect();
      }

      this.currentFormId = formId;
      this.currentUserId = userId;
      this.status = WebSocketStatus.CONNECTING;

      try {
        const wsUrl = `${import.meta.env.VITE_WS_URL || 'ws://localhost:8080'}/ws/form/${formId}/${userId}`;
        const urlWithToken = `${wsUrl}?token=${encodeURIComponent(token)}`;

        this.ws = new WebSocket(urlWithToken);
        this.setupWebSocketHandlers();
      } catch (error) {
        console.error('WebSocket connection error:', error);
        this.status = WebSocketStatus.ERROR;
        this.handleReconnect();
      }
    },

    disconnect() {
      if (this.ws) {
        this.ws.close();
        this.ws = null;
      }
      this.status = WebSocketStatus.DISCONNECTED;
      this.reconnectAttempts = 0;
      this.resetState();
    },

    // Handle authentication state changes (logout/login)
    handleAuthStateChange() {
      const authStore = useAuthStore();

      // If user logged out, disconnect immediately
      if (!authStore.user || !authStore.token) {
        console.log('User logged out, disconnecting WebSocket');
        this.disconnect();
        return;
      }

      // If user changed (different user_id), reconnect with new user
      const newUserId = authStore.user.user_id.toString();
      if (this.currentUserId && this.currentUserId !== newUserId) {
        console.log('User changed, reconnecting WebSocket with new user');
        if (this.currentFormId) {
          this.connect(this.currentFormId, newUserId, authStore.token.access_token);
        }
      }
    },

    setupWebSocketHandlers() {
      console.log(this.ws);
      if (!this.ws) return;

      this.ws.onopen = () => {
        console.log('🟢 WebSocket connected successfully');
        this.status = WebSocketStatus.CONNECTED;
        this.reconnectAttempts = 0;
        /*
        if (this.showNotifications) {
          notification.success({
            message: 'Připojeno k real-time editaci',
            duration: 2
          });
        }
*/
        // Request current state
        this.getLockedFields();
      };

      this.ws.onmessage = (event) => {
        try {
          const message: ServerMessage = JSON.parse(event.data);
          this.handleServerMessage(message);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      this.ws.onerror = (error) => {
        console.error('🔥 WebSocket error:', {
          error,
          readyState: this.ws?.readyState,
          timestamp: new Date().toISOString()
        });
        this.status = WebSocketStatus.ERROR;
      };

      this.ws.onclose = (event) => {
        console.log('🔴 WebSocket closed:', event.code, event.reason || '(no reason)');
        this.status = WebSocketStatus.DISCONNECTED;

        // Don't reconnect if it was a manual close, auth error, or server crash
        if (
          event.code === 1000 ||
          event.code === 1001 ||
          event.code === 1006 ||
          event.code === 1008
        ) {
          console.log('WebSocket closed (code:', event.code, '), not reconnecting');
          this.resetState();

          // Show user-friendly message for server errors
          /*
          if (event.code === 1006 && this.showNotifications) {
            notification.warning({
              message: 'WebSocket server nedostupný',
              description: 'Real-time editace není momentálně k dispozici. Zkuste to později.',
              duration: 5
            });
          }
            */
          return;
        }

        // Check if we still have valid auth before reconnecting
        const authStore = useAuthStore();
        if (!authStore.user || !authStore.token?.access_token) {
          console.log('No valid auth, not reconnecting WebSocket');
          this.resetState();
          return;
        }

        this.handleReconnect();
      };
    },

    handleReconnect() {
      if (
        this.reconnectAttempts < this.maxReconnectAttempts &&
        this.currentFormId &&
        this.currentUserId
      ) {
        this.reconnectAttempts++;
        this.status = WebSocketStatus.RECONNECTING;

        setTimeout(() => {
          const authStore = useAuthStore();

          // Check if user is still authenticated
          if (!authStore.user || !authStore.token?.access_token) {
            console.warn('User not authenticated, stopping WebSocket reconnection');
            this.status = WebSocketStatus.DISCONNECTED;
            this.resetState();
            return;
          }

          this.connect(this.currentFormId!, this.currentUserId!, authStore.token.access_token);
        }, this.reconnectInterval * this.reconnectAttempts);
      } else {
        // Max reconnection attempts reached or missing required data
        this.status = WebSocketStatus.DISCONNECTED;
        this.resetState();
      }
    },

    resetState() {
      this.lockedFields = {};
      this.myLockedFields.clear();
      this.pendingAccessRequests = [];
      this.activeUsers = [];
      this.currentFormId = null;
      this.currentUserId = null;
    },

    // Message Sending
    sendMessage(message: ClientMessage) {
      if (!this.ws || this.status !== WebSocketStatus.CONNECTED) {
        console.warn('WebSocket not connected, cannot send message:', message);
        return false;
      }

      try {
        this.ws.send(JSON.stringify(message));
        return true;
      } catch (error) {
        console.error('Error sending WebSocket message:', error);
        return false;
      }
    },

    // Server Message Handling
    handleServerMessage(message: ServerMessage) {
      switch (message.type) {
        case 'lock_response':
          this.handleLockResponse(message);
          break;
        case 'unlock_response':
          this.handleUnlockResponse(message);
          break;
        case 'field_status':
          this.handleFieldStatus(message);
          break;
        case 'access_request':
          this.handleAccessRequest(message);
          break;
        case 'access_response':
          this.handleAccessResponse(message);
          break;
        case 'current_locks':
          this.handleCurrentLocks(message);
          break;
        case 'update_error':
          this.handleUpdateError(message);
          break;
        case 'presence_update':
          this.handlePresenceUpdate(message);
          break;
        case 'form_update':
          this.handleFormUpdate(message);
          break;
        case 'locked_fields_response':
          this.handleLockedFieldsResponse(message);
          break;
        default:
          console.warn('Unknown message type:', message);
      }
    },

    handleLockResponse(message: LockResponseMessage) {
      if (message.success) {
        this.myLockedFields.add(message.field_name);
        if (this.showNotifications) {
          console.log('Lock response:', message);
          /*
          notification.success({
            message: `Pole "${message.field_name}" bylo uzamčeno`,
            duration: 2
          });
          */
        }
      } else {
        if (this.showNotifications) {
          console.log('Lock response:', message);
          /*
          notification.warning({
            message: `Nelze uzamčit pole "${message.field_name}"`,
            description: message.message,
            duration: 4
          });
          */
        }
      }
    },

    handleUnlockResponse(message: UnlockResponseMessage) {
      if (message.success) {
        this.myLockedFields.delete(message.field_name);
        if (this.showNotifications) {
          console.log('Unlock response:', message);
          /*
          notification.success({
            message: `Pole "${message.field_name}" bylo odemčeno`,
            duration: 2
          });
          */
        }
      } else {
        if (this.showNotifications) {
          console.log('Unlock response:', message);
          /*
          notification.warning({
            message: `Nelze odemčit pole "${message.field_name}"`,
            description: message.message,
            duration: 4
          });
          */
        }
      }
    },

    handleFieldStatus(message: FieldStatusMessage) {
      if (message.status === 'locked') {
        this.lockedFields[message.field_name] = {
          user_id: message.user_id,
          timestamp: message.timestamp
        };

        /*
        if (message.user_id !== this.currentUserId && this.showNotifications) {
          notification.info({
            message: `Pole "${message.field_name}" bylo uzamčeno jiným uživatelem`,
            description: `Uživatel: ${message.user_id}`,
            duration: 3
          });
        }
          */
      } else {
        delete this.lockedFields[message.field_name];
        this.myLockedFields.delete(message.field_name);
        /*
        if (this.showNotifications) {
          notification.info({
            message: `Pole "${message.field_name}" bylo odemčeno`,
            duration: 2
          });
        }
          */
      }
    },

    handleAccessRequest(message: AccessRequestMessage) {
      this.pendingAccessRequests.push({
        field_name: message.field_name,
        requester_id: message.requester_id,
        requester_message: message.requester_message,
        timestamp: message.timestamp
      });

      if (this.showNotifications) {
        const translatedFieldName = translateFieldName(message.field_name);
        console.log(`Field name translation: "${message.field_name}" → "${translatedFieldName}"`);

        notification.info({
          message: `Žádost o přístup k poli "${translatedFieldName}"`,
          description: `Od uživatele: ${message.requester_id}${message.requester_message ? '\nZpráva: ' + message.requester_message : ''}`,
          duration: 0
        });
      }
    },

    handleAccessResponse(message: AccessResponseMessage) {
      if (message.approved) {
        if (this.showNotifications) {
          notification.success({
            message: `Přístup k poli "${translateFieldName(message.field_name)}" byl povolen`,
            description: message.response_message,
            duration: 4
          });
        }
        // Automatically try to lock the field
        setTimeout(() => {
          this.lockField(message.field_name);
        }, 500);
      } else {
        if (this.showNotifications) {
          /*
          notification.warning({
            message: `Přístup k poli "${translateFieldName(message.field_name)}" byl zamítnut`,
            description: message.response_message,
            duration: 4
          });
          */
        }
      }
    },

    handleCurrentLocks(message: CurrentLocksMessage) {
      this.lockedFields = {};
      Object.entries(message.locked_fields).forEach(([fieldName, lockInfo]) => {
        this.lockedFields[fieldName] = lockInfo;
        if (lockInfo.user_id === this.currentUserId) {
          this.myLockedFields.add(fieldName);
        }
      });
    },

    handleUpdateError(message: UpdateErrorMessage) {
      if (this.showNotifications) {
        notification.error({
          message: `Chyba při aktualizaci pole "${message.field}"`,
          description: message.message,
          duration: 4
        });
      }
    },

    handlePresenceUpdate(message: PresenceUpdateMessage) {
      this.activeUsers = message.users;
    },

    handleFormUpdate(message: FormUpdateBroadcastMessage) {
      console.log('📨 WebSocket Store: Received form_update:', message);

      if (message.success && message.payload) {
        const fieldName = message.payload.field;
        const userId = message.user_id || 'unknown';

        console.log('📨 WebSocket Store: Processing form update:', {
          fieldName,
          value: message.payload.value,
          userId,
          currentUserId: this.currentUserId
        });

        // Check if this is a chemical table update
        if (fieldName === 'chemicals' && typeof message.payload.value === 'object') {
          const chemicalUpdate = message.payload.value;

          if (chemicalUpdate.table_primary_key && chemicalUpdate.field) {
            console.log('🧪 WebSocket Store: Processing chemical table update:', {
              chemical_id: chemicalUpdate.table_primary_key,
              field: chemicalUpdate.field,
              value: chemicalUpdate.value,
              userId
            });

            // Store chemical table update with nested structure
            this.fieldUpdates[fieldName] = {
              value: {
                table_primary_key: chemicalUpdate.table_primary_key,
                field: chemicalUpdate.field,
                value: chemicalUpdate.value
              },
              userId: userId,
              timestamp: Date.now()
            };
          }
        }
        // Check if this is a conclusions table update
        else if (fieldName === 'conclusions' && typeof message.payload.value === 'object') {
          const conclusionUpdate = message.payload.value;

          if (conclusionUpdate.table_primary_key && conclusionUpdate.field) {
            console.log('📝 WebSocket Store: Processing conclusion table update:', {
              conclusion_id: conclusionUpdate.table_primary_key,
              field: conclusionUpdate.field,
              value: conclusionUpdate.value,
              userId
            });

            // Store conclusion table update with nested structure
            this.fieldUpdates[fieldName] = {
              value: {
                table_primary_key: conclusionUpdate.table_primary_key,
                field: conclusionUpdate.field,
                value: conclusionUpdate.value
              },
              userId: userId,
              timestamp: Date.now()
            };
          }
        } else {
          // Regular field update
          this.fieldUpdates[fieldName] = {
            value: message.payload.value,
            userId: userId,
            timestamp: Date.now()
          };
        }

        console.log('📨 WebSocket Store: Updated fieldUpdates:', this.fieldUpdates);

        // Update typing indicator (show user is actively editing)
        if (userId !== this.currentUserId) {
          this.typingIndicators[fieldName] = {
            userId: userId,
            timestamp: Date.now()
          };

          // Auto-remove typing indicator after 3 seconds
          setTimeout(() => {
            if (this.typingIndicators[fieldName]?.userId === userId) {
              delete this.typingIndicators[fieldName];
            }
          }, 3000);
        }

        // Show notification if enabled and from another user
        /*
        if (this.showNotifications && userId !== this.currentUserId) {
          notification.info({
            message: `Pole "${fieldName}" bylo aktualizováno jiným uživatelem`,
            description: `Nová hodnota: ${message.payload.value}`,
            duration: 3
          });
        }
*/
        console.log('Real-time field update:', message.payload);
      }
    },

    handleLockedFieldsResponse(message: LockedFieldsResponseMessage) {
      // Update the locked fields state with the response from server
      this.lockedFields = message.locked_fields;
      console.log('Updated locked fields:', this.lockedFields);
    },

    // Helper Methods
    createAccessRequestButtons(message: AccessRequestMessage) {
      const h =
        (window as any).Vue?.h ||
        ((tag: string, props: any, children: any) => ({ tag, props, children }));

      return h('div', { style: 'margin-top: 8px;' }, [
        h(
          'button',
          {
            style:
              'margin-right: 8px; padding: 4px 8px; background: #52c41a; color: white; border: none; border-radius: 4px; cursor: pointer;',
            onClick: () =>
              this.respondToAccessRequest(message.field_name, message.requester_id, true)
          },
          'Povolit'
        ),
        h(
          'button',
          {
            style:
              'padding: 4px 8px; background: #ff4d4f; color: white; border: none; border-radius: 4px; cursor: pointer;',
            onClick: () =>
              this.respondToAccessRequest(message.field_name, message.requester_id, false)
          },
          'Zamítnout'
        )
      ]);
    },

    // Public API Methods
    lockField(fieldName: string): boolean {
      return this.sendMessage({
        type: 'lock_field',
        field_name: fieldName
      });
    },

    unlockField(fieldName: string): boolean {
      return this.sendMessage({
        type: 'unlock_field',
        field_name: fieldName
      });
    },

    async requestFieldAccess(fieldName: string, message?: string): Promise<boolean> {
      return this.sendMessage({
        type: 'request_field_access',
        field_name: fieldName,
        message
      });
    },

    respondToAccessRequest(
      fieldName: string,
      requesterId: string,
      approved: boolean,
      responseMessage?: string
    ): boolean {
      // Remove the request from pending list
      this.pendingAccessRequests = this.pendingAccessRequests.filter(
        (req) => !(req.field_name === fieldName && req.requester_id === requesterId)
      );

      return this.sendMessage({
        type: 'respond_to_access_request',
        field_name: fieldName,
        requester_id: requesterId,
        approved,
        response_message: responseMessage
      });
    },

    getLockedFields(): boolean {
      return this.sendMessage({
        type: 'get_locked_fields'
      });
    },

    getPendingRequests(fieldName?: string): boolean {
      return this.sendMessage({
        type: 'get_pending_requests',
        field_name: fieldName
      });
    },

    updateFormField(fieldName: string, value: any): boolean {
      return this.sendMessage({
        type: 'form_update',
        field: fieldName,
        value
      });
    },

    // Send real-time field updates (immediate, for live synchronization)
    sendRealTimeUpdate(fieldName: string, value: any): boolean {
      console.log('📤 WebSocket Store: Sending real-time update:', { field: fieldName, value });
      // Backend expects same format as regular form updates
      const success = this.sendMessage({
        field: fieldName,
        value: value
      });
      console.log('📤 WebSocket Store: Send result:', success);
      return success;
    },

    // Send chemical table field updates (for nested table structure)
    sendChemicalTableUpdate(tablePrimaryKey: number, fieldName: string, value: any): boolean {
      console.log('📤 WebSocket Store: Sending chemical table update:', {
        tablePrimaryKey,
        fieldName,
        value
      });

      const success = this.sendMessage({
        field: 'chemicals',
        value: {
          table_primary_key: tablePrimaryKey,
          field: fieldName,
          value: value
        }
      });

      console.log('📤 WebSocket Store: Chemical table send result:', success);
      return success;
    },

    // Send live typing updates (for showing "user is typing" indicators)
    // Note: Backend doesn't have separate typing messages, so we use same format
    sendLiveTyping(fieldName: string, value: any): boolean {
      // For now, use same format as real-time updates
      // Could be enhanced later if backend adds typing-specific support
      return this.sendMessage({
        field: fieldName,
        value: value
      });
    },

    // Utility Methods
    canEditField(fieldName: string): boolean {
      if (!this.isConnected) return true; // Allow editing when not connected

      const lockInfo = this.lockedFields[fieldName];
      if (!lockInfo) return true; // Field is not locked

      return lockInfo.user_id === this.currentUserId; // Can edit if locked by me
    },

    shouldShowFieldAsLocked(fieldName: string): boolean {
      return this.isFieldLockedByOther(fieldName);
    },

    getFieldLockOwner(fieldName: string): string | null {
      const lockInfo = this.lockedFields[fieldName];
      return lockInfo ? lockInfo.user_id : null;
    },

    toggleNotifications() {
      this.showNotifications = !this.showNotifications;
    },

    // Cleanup method for component unmount
    cleanup() {
      // Unlock all fields locked by current user
      this.myLockedFields.forEach((fieldName) => {
        this.unlockField(fieldName);
      });

      // Disconnect WebSocket
      this.disconnect();
    },

    // Auto-lock field when user starts editing
    async autoLockField(fieldName: string): Promise<boolean> {
      if (this.isFieldLockedByOther(fieldName)) {
        if (this.showNotifications) {
          notification.warning({
            message: `Pole "${translateFieldName(fieldName)}" je uzamčeno jiným uživatelem`,
            description: 'Chcete požádat o přístup?',
            duration: 0
          });
        }
        return false;
      }

      if (!this.isFieldLockedByMe(fieldName)) {
        return this.lockField(fieldName);
      }

      return true;
    },

    // Auto-unlock field when user stops editing (with debounce)
    scheduleAutoUnlock(fieldName: string, delay: number = 30000) {
      setTimeout(() => {
        if (this.isFieldLockedByMe(fieldName)) {
          this.unlockField(fieldName);
          console.log(`Auto-unlocked field: ${fieldName}`);
        }
      }, delay);
    },

    // Force reset connection state (useful for debugging)
    forceReset() {
      console.log('Force resetting WebSocket state');
      this.disconnect();
      this.reconnectAttempts = 0;
      this.status = WebSocketStatus.DISCONNECTED;
    },

    // Unlock all fields locked by current user (for form submission)
    unlockAllMyFields() {
      const fieldsToUnlock = Array.from(this.myLockedFields);
      fieldsToUnlock.forEach((fieldName) => {
        this.unlockField(fieldName);
      });
    }
  }
});
