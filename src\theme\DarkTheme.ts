import type { ThemeTypes } from '@/types/themeTypes/ThemeType';

const DarkDefaultTheme: ThemeTypes = {
  name: 'DarkDefaultTheme',
  dark: true,
  variables: {
    'border-color': '#595959',
    gradient:
      'linear-gradient(250.38deg, #111a2c 2.39%, #15417e 34.42%, #1668dc 60.95%, #3c89e8 84.83%, #8dc5f8 104.37%)',
    'card-shadow':
      '0px 1px 1px rgba(0, 0, 0, 0.14), 0px 2px 1px rgba(0, 0, 0, 0.12), 0px 1px 3px rgba(0, 0, 0, 0.20)'
  },
  colors: {
    primary: '#1668dc',
    secondary: '#d9d9d9',
    info: '#13a8a8',
    success: '#49aa19',
    accent: '#fc4b6c',
    warning: '#d89614',
    error: '#a61d24',
    lightprimary: '#111a2c',
    lightsecondary: '#343131',
    lightsuccess: '#162312',
    lighterror: '#2a1215',
    lightwarning: '#2b2111',
    darkprimary: '#0c4dab',
    darksecondary: '#8c8c8c',
    darkText: '#e0e0e0',
    lightText: '#7d7d7d',
    borderLight: '#292929',
    inputBorder: '#595959',
    containerBg: '#121212',
    surface: '#1e1e1e',
    background: '#1e1e1e',
    'on-surface-variant': '#1e1e1e',
    facebook: '#4267b2',
    twitter: '#1da1f2',
    linkedin: '#0e76a8',
    gray100: '#141414',
    primary200: '#8dc5f8',
    secondary200: '#bdbdbd'
  }
};

const DarkTheme1: ThemeTypes = {
  name: 'DarkTheme1',
  dark: true,
  variables: {
    'border-color': '#595959',
    gradient:
      'linear-gradient(250.38deg, #1c2134 2.39%, #273e83 34.42%, #305bdd 60.95%, #567fe9 84.83%, #a9c5f8 104.37%)',
    'card-shadow':
      '0px 1px 1px rgba(0, 0, 0, 0.14), 0px 2px 1px rgba(0, 0, 0, 0.12), 0px 1px 3px rgba(0, 0, 0, 0.20)'
  },
  colors: {
    primary: '#305bdd',
    secondary: '#d9d9d9',
    info: '#4c88dd',
    success: '#4fba28',
    accent: '#d9534f',
    warning: '#dda116',
    error: '#dd3f27',
    lightprimary: '#1c2134',
    lightsecondary: '#343434',
    lightsuccess: '#1f2e1c',
    lighterror: '#341d1b',
    lightwarning: '#342a1a',
    darkprimary: '#273e83',
    darksecondary: '#b5b2b2',
    darkText: '#e0e0e0',
    lightText: '#7d7d7d',
    borderLight: '#292929',
    inputBorder: '#595959',
    containerBg: '#121212',
    surface: '#1e1e1e',
    background: '#1e1e1e',
    'on-surface-variant': '#1e1e1e',
    facebook: '#4267b2',
    twitter: '#1da1f2',
    linkedin: '#0e76a8',
    gray100: '#141414',
    primary200: '#567fe9',
    secondary200: '#a9a9a9'
  }
};

const DarkTheme2: ThemeTypes = {
  name: 'DarkTheme2',
  dark: true,
  variables: {
    'border-color': '#595959',
    gradient:
      'linear-gradient(250.38deg, #222130 2.39%, #443e78 34.42%, #655ac8 60.95%, #9186dd 84.83%, #c3baf4 104.37%)',
    'card-shadow':
      '0px 1px 1px rgba(0, 0, 0, 0.14), 0px 2px 1px rgba(0, 0, 0, 0.12), 0px 1px 3px rgba(0, 0, 0, 0.20)'
  },
  colors: {
    primary: '#655ac8',
    secondary: '#d9d9d9',
    info: '#058e98',
    success: '#05934c',
    accent: '#FFAB91',
    warning: '#dda705',
    error: '#d13c31',
    lightprimary: '#222130',
    lightsecondary: '#3c3c3c',
    lightsuccess: '#1a2721',
    lighterror: '#321d1d',
    lightwarning: '#342c1a',
    darkprimary: '#4d4597',
    darksecondary: '#b5b5b5',
    darkText: '#e0e0e0',
    lightText: '#7d7d7d',
    borderLight: '#292929',
    inputBorder: '#595959',
    containerBg: '#121212',
    surface: '#1e1e1e',
    background: '#1e1e1e',
    'on-surface-variant': '#1e1e1e',
    facebook: '#4267b2',
    twitter: '#1da1f2',
    linkedin: '#0e76a8',
    gray100: '#141414',
    primary200: '#9186dd',
    secondary200: '#999999'
  }
};

const DarkTheme3: ThemeTypes = {
  name: 'DarkTheme3',
  dark: true,
  variables: {
    'border-color': '#595959',
    gradient:
      'linear-gradient(250.38deg, #1a231f 2.39%, #13502f 34.42%, #0a7d3e 60.95%, #1f8f4e 84.83%, #5cb07a 104.37%)',
    'card-shadow':
      '0px 1px 1px rgba(0, 0, 0, 0.14), 0px 2px 1px rgba(0, 0, 0, 0.12), 0px 1px 3px rgba(0, 0, 0, 0.20)'
  },
  colors: {
    primary: '#0a7d3e',
    secondary: '#d9d9d9',
    info: '#058e98',
    success: '#05934c',
    accent: '#FFAB91',
    warning: '#dda705',
    error: '#d13c31',
    lightprimary: '#1a231f',
    lightsecondary: '#4a4848',
    lightsuccess: '#1a2721',
    lighterror: '#321d1d',
    lightwarning: '#342c1a',
    darkprimary: '#096c36',
    darksecondary: '#bfb7b7',
    darkText: '#e0e0e0',
    lightText: '#7d7d7d',
    borderLight: '#292929',
    inputBorder: '#595959',
    containerBg: '#121212',
    surface: '#1e1e1e',
    background: '#1e1e1e',
    'on-surface-variant': '#1e1e1e',
    facebook: '#4267b2',
    twitter: '#1da1f2',
    linkedin: '#0e76a8',
    gray100: '#141414',
    primary200: '#5cb07a',
    secondary200: '#9d9d9d'
  }
};

const DarkTheme4: ThemeTypes = {
  name: 'DarkTheme4',
  dark: true,
  variables: {
    'border-color': '#595959',
    gradient:
      'linear-gradient(250.38deg, #1d212d 2.39%, #2c3e6e 34.42%, #385ab5 60.95%, #5d7dcb 84.83%, #b9cef0 104.37%)',
    'card-shadow':
      '0px 1px 1px rgba(0, 0, 0, 0.14), 0px 2px 1px rgba(0, 0, 0, 0.12), 0px 1px 3px rgba(0, 0, 0, 0.20)'
  },
  colors: {
    primary: '#385ab5',
    secondary: '#d9d9d9',
    info: '#058e98',
    success: '#05934c',
    accent: '#FFAB91',
    warning: '#dda705',
    error: '#d13c31',
    lightprimary: '#1d212d',
    lightsecondary: '#343434',
    lightsuccess: '#1a2721',
    lighterror: '#321d1d',
    lightwarning: '#342c1a',
    darkprimary: '#2d4995',
    darksecondary: '#c1c0c0',
    darkText: '#e0e0e0',
    lightText: '#7d7d7d',
    borderLight: '#292929',
    inputBorder: '#595959',
    containerBg: '#121212',
    surface: '#1e1e1e',
    background: '#1e1e1e',
    'on-surface-variant': '#1e1e1e',
    facebook: '#4267b2',
    twitter: '#1da1f2',
    linkedin: '#0e76a8',
    gray100: '#141414',
    primary200: '#5d7dcb',
    secondary200: '#727272'
  }
};

const DarkTheme5: ThemeTypes = {
  name: 'DarkTheme5',
  dark: true,
  variables: {
    'border-color': '#595959',
    gradient:
      'linear-gradient(250.38deg, #32221a 2.39%, #7d4319 34.42%, #d26415 60.95%, #e9883a 84.83%, #f8c48c 104.37%)',
    'card-shadow':
      '0px 1px 1px rgba(0, 0, 0, 0.14), 0px 2px 1px rgba(0, 0, 0, 0.12), 0px 1px 3px rgba(0, 0, 0, 0.20)'
  },
  colors: {
    primary: '#d26415',
    secondary: '#d9d9d9',
    info: '#058e98',
    success: '#05934c',
    accent: '#FFAB91',
    warning: '#dda705',
    error: '#d13c31',
    lightprimary: '#32221a',
    lightsecondary: '#424242',
    lightsuccess: '#1a2721',
    lighterror: '#321d1d',
    lightwarning: '#342c1a',
    darkprimary: '#b75611',
    darksecondary: '#c5c5c5',
    darkText: '#e0e0e0',
    lightText: '#7d7d7d',
    borderLight: '#292929',
    inputBorder: '#595959',
    containerBg: '#121212',
    surface: '#1e1e1e',
    background: '#1e1e1e',
    'on-surface-variant': '#1e1e1e',
    facebook: '#4267b2',
    twitter: '#1da1f2',
    linkedin: '#0e76a8',
    gray100: '#141414',
    primary200: '#e9883a',
    secondary200: '#7c7b7b'
  }
};

const DarkTheme6: ThemeTypes = {
  name: 'DarkTheme6',
  dark: true,
  variables: {
    'border-color': '#595959',
    gradient:
      'linear-gradient(250.38deg, #1c2628 2.39%, #23595f 34.42%, #288d99 60.95%, #47a6ad 84.83%, #96d0d0 104.37%)',
    'card-shadow':
      '0px 1px 1px rgba(0, 0, 0, 0.14), 0px 2px 1px rgba(0, 0, 0, 0.12), 0px 1px 3px rgba(0, 0, 0, 0.20)'
  },
  colors: {
    primary: '#288d99',
    secondary: '#d9d9d9',
    info: '#058e98',
    success: '#05934c',
    accent: '#FFAB91',
    warning: '#dda705',
    error: '#d13c31',
    lightprimary: '#1c2628',
    lightsecondary: '#343434',
    lightsuccess: '#1a2721',
    lighterror: '#321d1d',
    lightwarning: '#342c1a',
    darkprimary: '#237c87',
    darksecondary: '#c7c7c7',
    darkText: '#e0e0e0',
    lightText: '#7d7d7d',
    borderLight: '#292929',
    inputBorder: '#595959',
    containerBg: '#121212',
    surface: '#1e1e1e',
    background: '#1e1e1e',
    'on-surface-variant': '#1e1e1e',
    facebook: '#4267b2',
    twitter: '#1da1f2',
    linkedin: '#0e76a8',
    gray100: '#141414',
    primary200: '#47a6ad',
    secondary200: '#ada7a7'
  }
};

const DarkTheme7: ThemeTypes = {
  name: 'DarkTheme7',
  dark: true,
  variables: {
    'border-color': '#595959',
    gradient:
      'linear-gradient(250.38deg, #1a2721 2.39%, #115c36 34.42%, #05934c 60.95%, #1da65d 84.83%, #61ca8b 104.37%)',
    'card-shadow':
      '0px 1px 1px rgba(0, 0, 0, 0.14), 0px 2px 1px rgba(0, 0, 0, 0.12), 0px 1px 3px rgba(0, 0, 0, 0.20)'
  },
  colors: {
    primary: '#05934c',
    secondary: '#d9d9d9',
    info: '#058e98',
    success: '#05934c',
    accent: '#FFAB91',
    warning: '#dda705',
    error: '#d13c31',
    lightprimary: '#1a2721',
    lightsecondary: '#404040',
    lightsuccess: '#1a2721',
    lighterror: '#321d1d',
    lightwarning: '#342c1a',
    darkprimary: '#077a41',
    darksecondary: '#c5c5c5',
    darkText: '#e0e0e0',
    lightText: '#7d7d7d',
    borderLight: '#292929',
    inputBorder: '#595959',
    containerBg: '#121212',
    surface: '#1e1e1e',
    background: '#1e1e1e',
    'on-surface-variant': '#1e1e1e',
    facebook: '#4267b2',
    twitter: '#1da1f2',
    linkedin: '#0e76a8',
    gray100: '#141414',
    primary200: '#1da65d',
    secondary200: '#9b9b9b'
  }
};

const DarkTheme8: ThemeTypes = {
  name: 'DarkTheme8',
  dark: true,
  variables: {
    'border-color': '#595959',
    gradient:
      'linear-gradient(250.38deg, #1a2524 2.39%, #11544e 34.42%, #058478 60.95%, #1a9686 84.83%, #59b8a5 104.37%)',
    'card-shadow':
      '0px 1px 1px rgba(0, 0, 0, 0.14), 0px 2px 1px rgba(0, 0, 0, 0.12), 0px 1px 3px rgba(0, 0, 0, 0.20)'
  },
  colors: {
    primary: '#058478',
    secondary: '#d9d9d9',
    info: '#058e98',
    success: '#05934c',
    accent: '#FFAB91',
    warning: '#dda705',
    error: '#d13c31',
    lightprimary: '#1a2524',
    lightsecondary: '#3e3e3e',
    lightsuccess: '#1a2721',
    lighterror: '#321d1d',
    lightwarning: '#342c1a',
    darkprimary: '#06746a',
    darksecondary: '#c5c5c5',
    darkText: '#e0e0e0',
    lightText: '#7d7d7d',
    borderLight: '#292929',
    inputBorder: '#595959',
    containerBg: '#121212',
    surface: '#1e1e1e',
    background: '#1e1e1e',
    'on-surface-variant': '#1e1e1e',
    facebook: '#4267b2',
    twitter: '#1da1f2',
    linkedin: '#0e76a8',
    gray100: '#141414',
    primary200: '#1a9686',
    secondary200: '#b3b3b3'
  }
};

export {
  DarkDefaultTheme,
  DarkTheme1,
  DarkTheme6,
  DarkTheme5,
  DarkTheme2,
  DarkTheme3,
  DarkTheme4,
  DarkTheme7,
  DarkTheme8
};
