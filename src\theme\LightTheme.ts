import type { ThemeTypes } from '@/types/themeTypes/ThemeType';

const DefaultTheme: ThemeTypes = {
  name: 'DefaultTheme',
  dark: false,
  variables: {
    'border-color': '#f0f0f0',
    'carousel-control-size': 10,
    gradient:
      'linear-gradient(250.38deg, #e6f4ff 2.39%, #69b1ff 34.42%, #1677ff 60.95%, #0958d9 84.83%, #002c8c 104.37%)',
    gradient2: 'linear-gradient(to right, rgb(9, 89, 218), rgb(22, 119, 255))',
    'card-shadow': '0px 1px 4px rgba(0, 0, 0, 0.08)'
  },
  colors: {
    primary: '#1677ff',
    secondary: '#8c8c8c',
    info: '#13c2c2',
    success: '#52c41a',
    accent: '#FFAB91',
    warning: '#faad14',
    error: '#ff4d4f',
    lightprimary: '#e6f4ff',
    lightsecondary: '#f5f5f5',
    lightsuccess: '#EAFCD4',
    lighterror: '#FFE7D3',
    lightwarning: '#FFF6D0',
    darkText: '#212121',
    lightText: '#8c8c8c',
    darkprimary: '#0958d9',
    darksecondary: '#7a7878',
    borderLight: '#e6ebf1',
    inputBorder: '#a1a1a5',
    containerBg: '#fafafb',
    surface: '#fff',
    'on-surface-variant': '#fff',
    facebook: '#4267b2',
    twitter: '#1da1f2',
    linkedin: '#0e76a8',
    gray100: '#f5f5f5',
    primary200: '#a1d2ff',
    secondary200: '#eeeeee'
  }
};

const LightTheme1: ThemeTypes = {
  name: 'LightTheme1',
  dark: false,
  variables: {
    'border-color': '#d9d9d9',
    gradient:
      'linear-gradient(250.38deg, #D6E4FF 2.39%, #84A9FF 34.42%, #3366FF 60.95%, #254EDB 84.83%, #102693 104.37%)',
    gradient2: 'linear-gradient(to right, rgb(37, 78, 219), rgb(51, 102, 255))',
    'card-shadow': '0px 1px 4px rgba(0, 0, 0, 0.08)'
  },
  colors: {
    primary: '#3366FF',
    secondary: '#8c8c8c',
    info: '#549bff',
    success: '#58d62a',
    accent: '#d9534f',
    warning: '#ffb814',
    error: '#ff4528',
    lightprimary: '#d6e4ff',
    lightsecondary: '#f5f5f5',
    lightsuccess: '#eafcd4',
    lighterror: '#ffe7d3',
    lightwarning: '#fff6d0',
    darkprimary: '#254edb',
    darksecondary: '#7a7878',
    darkText: '#262626',
    lightText: '#8c8c8c',
    borderLight: '#e6ebf1',
    inputBorder: '#a1a1a5',
    containerBg: '#fafafb',
    surface: '#fff',
    'on-surface-variant': '#fff',
    facebook: '#4267b2',
    twitter: '#1da1f2',
    linkedin: '#0e76a8',
    gray100: '#fafafa',
    primary200: '#84a9ff',
    secondary200: '#f0f0f0'
  }
};

const LightTheme2: ThemeTypes = {
  name: 'LightTheme2',
  dark: false,
  variables: {
    'border-color': '#d9d9d9',
    gradient:
      'linear-gradient(250.38deg, #EEEDFC 2.39%, #9C93EE 34.42%, #7265E6 60.95%, #6A5DE3 84.83%, #5549DB 104.37%)',
    gradient2: 'linear-gradient(to right, rgb(106, 93, 227), rgb(114, 101, 230))',
    'card-shadow': '0px 1px 4px rgba(0, 0, 0, 0.08)'
  },
  colors: {
    primary: '#7265e6',
    secondary: '#8c8c8c',
    info: '#00a2ae',
    success: '#00a854',
    accent: '#FFAB91',
    warning: '#ffbf00',
    error: '#f04134',
    lightprimary: '#eeedfc',
    lightsecondary: '#f5f5f5',
    lightsuccess: '#e0f5ea',
    lighterror: '#fde8e7',
    lightwarning: '#fff7e0',
    darkprimary: '#6559cb',
    darksecondary: '#7e7c7c',
    darkText: '#212121',
    lightText: '#8c8c8c',
    borderLight: '#e6ebf1',
    inputBorder: '#a1a1a5',
    containerBg: '#fafafb',
    surface: '#fff',
    'on-surface-variant': '#fff',
    facebook: '#4267b2',
    twitter: '#1da1f2',
    linkedin: '#0e76a8',
    gray100: '#f5f5f5',
    primary200: '#9c93ee',
    secondary200: '#dbd9d9'
  }
};

const LightTheme3: ThemeTypes = {
  name: 'LightTheme3',
  dark: false,
  variables: {
    'border-color': '#d9d9d9',
    gradient:
      'linear-gradient(250.38deg, #E6F3EC 2.39%, #3da866 34.42%, #068e44 60.95%, #006933 84.83%, #001c0f 104.37%)',
    gradient2: 'linear-gradient(to right, rgb(0, 105, 51), rgb(6, 142, 68))',
    'card-shadow': '0px 1px 4px rgba(0, 0, 0, 0.08)'
  },
  colors: {
    primary: '#068e44',
    secondary: '#8c8c8c',
    info: '#00a2ae',
    success: '#00a854',
    accent: '#FFAB91',
    warning: '#ffbf00',
    error: '#f04134',
    lightprimary: '#e6f3ec',
    lightsecondary: '#f5f5f5',
    lightsuccess: '#e0f5ea',
    lighterror: '#fde8e7',
    lightwarning: '#fff7e0',
    darkprimary: '#0a7c3e',
    darksecondary: '#787878',
    darkText: '#212121',
    lightText: '#8c8c8c',
    borderLight: '#e6ebf1',
    inputBorder: '#a1a1a5',
    containerBg: '#fafafb',
    surface: '#fff',
    'on-surface-variant': '#fff',
    facebook: '#4267b2',
    twitter: '#1da1f2',
    linkedin: '#0e76a8',
    gray100: '#f5f5f5',
    primary200: '#3da866',
    secondary200: '#c9c9c9'
  }
};

const LightTheme4: ThemeTypes = {
  name: 'LightTheme4',
  dark: false,
  variables: {
    'border-color': '#d9d9d9',
    gradient:
      'linear-gradient(250.38deg, #f0f6ff 2.39%, #8faeeb 34.42%, #3c64d0 60.95%, #2947ab 84.83%, #0d1b5e 104.37%)',
    gradient2: 'linear-gradient(to right, rgb(41, 71, 171), rgb(60, 100, 208))',
    'card-shadow': '0px 1px 4px rgba(0, 0, 0, 0.08)'
  },
  colors: {
    primary: '#3c64d0',
    secondary: '#8c8c8c',
    info: '#00a2ae',
    success: '#00a854',
    accent: '#FFAB91',
    warning: '#ffbf00',
    error: '#f04134',
    lightprimary: '#f0f6ff',
    lightsecondary: '#f5f5f5',
    lightsuccess: '#e0f5ea',
    lighterror: '#fde8e7',
    lightwarning: '#fff7e0',
    darkprimary: '#3051ab',
    darksecondary: '#7a7a7a',
    darkText: '#212121',
    lightText: '#8c8c8c',
    borderLight: '#e6ebf1',
    inputBorder: '#a1a1a5',
    containerBg: '#fafafb',
    surface: '#fff',
    'on-surface-variant': '#fff',
    facebook: '#4267b2',
    twitter: '#1da1f2',
    linkedin: '#0e76a8',
    gray100: '#f5f5f5',
    primary200: '#8faeeb',
    secondary200: '#d1d0d0'
  }
};

const LightTheme5: ThemeTypes = {
  name: 'LightTheme5',
  dark: false,
  variables: {
    'border-color': '#d9d9d9',
    gradient:
      'linear-gradient(250.38deg, #fff4e6 2.39%, #ffb066 34.42%, #f27013 60.95%, #cc5206 84.83%, #802800 104.37%)',
    gradient2: 'linear-gradient(to right, rgb(204, 82, 6), rgb(242, 112, 19))',
    'card-shadow': '0px 1px 4px rgba(0, 0, 0, 0.08)'
  },
  colors: {
    primary: '#f27013',
    secondary: '#8c8c8c',
    info: '#00a2ae',
    success: '#00a854',
    accent: '#FFAB91',
    warning: '#ffbf00',
    error: '#f04134',
    lightprimary: '#fff4e6',
    lightsecondary: '#f5f5f5',
    lightsuccess: '#e0f5ea',
    lighterror: '#fde8e7',
    lightwarning: '#fff7e0',
    darkprimary: '#d96818',
    darksecondary: '#7a7a7a',
    darkText: '#212121',
    lightText: '#8c8c8c',
    borderLight: '#e6ebf1',
    inputBorder: '#a1a1a5',
    containerBg: '#fafafb',
    surface: '#fff',
    'on-surface-variant': '#fff',
    facebook: '#4267b2',
    twitter: '#1da1f2',
    linkedin: '#0e76a8',
    gray100: '#f5f5f5',
    primary200: '#ffb066',
    secondary200: '#d3d3d3'
  }
};

const LightTheme6: ThemeTypes = {
  name: 'LightTheme6',
  dark: false,
  variables: {
    'border-color': '#d9d9d9',
    gradient:
      'linear-gradient(250.38deg, #e1f0ef 2.39%, #71c6c9 34.42%, #2aa1af 60.95%, #1a7b8a 84.83%, #06323d 104.37%)',
    gradient2: 'linear-gradient(to right, rgb(26, 123, 138), rgb(42, 161, 175))',
    'card-shadow': '0px 1px 4px rgba(0, 0, 0, 0.08)'
  },
  colors: {
    primary: '#2aa1af',
    secondary: '#8c8c8c',
    info: '#00a2ae',
    success: '#00a854',
    accent: '#FFAB91',
    warning: '#ffbf00',
    error: '#f04134',
    lightprimary: '#e1f0ef',
    lightsecondary: '#f5f5f5',
    lightsuccess: '#e0f5ea',
    lighterror: '#fde8e7',
    lightwarning: '#fff7e0',
    darkprimary: '#2694a1',
    darksecondary: '#7e7777',
    darkText: '#212121',
    lightText: '#8c8c8c',
    borderLight: '#e6ebf1',
    inputBorder: '#a1a1a5',
    containerBg: '#fafafb',
    surface: '#fff',
    'on-surface-variant': '#fff',
    facebook: '#4267b2',
    twitter: '#1da1f2',
    linkedin: '#0e76a8',
    gray100: '#f5f5f5',
    primary200: '#71c6c9',
    secondary200: '#bfbfbf'
  }
};

const LightTheme7: ThemeTypes = {
  name: 'LightTheme7',
  dark: false,
  variables: {
    'border-color': '#d9d9d9',
    gradient:
      'linear-gradient(250.38deg, #d1e8d99c 2.39%, #3ec277 34.42%, #00a854 60.95%, #008245 84.83%, #003620 104.37%)',
    gradient2: 'linear-gradient(to right, rgb(0, 130, 69), rgb(0, 168, 84))',
    'card-shadow': '0px 1px 4px rgba(0, 0, 0, 0.08)'
  },
  colors: {
    primary: '#00a854',
    secondary: '#8c8c8c',
    info: '#00a2ae',
    success: '#00a854',
    accent: '#FFAB91',
    warning: '#ffbf00',
    error: '#f04134',
    lightprimary: '#e3f1e8',
    lightsecondary: '#f5f5f5',
    lightsuccess: '#e0f5ea',
    lighterror: '#fde8e7',
    lightwarning: '#fff7e0',
    darkprimary: '#029149',
    darksecondary: '#7e7777',
    darkText: '#212121',
    lightText: '#8c8c8c',
    borderLight: '#e6ebf1',
    inputBorder: '#a1a1a5',
    containerBg: '#fafafb',
    surface: '#fff',
    'on-surface-variant': '#fff',
    facebook: '#4267b2',
    twitter: '#1da1f2',
    linkedin: '#0e76a8',
    gray100: '#f5f5f5',
    primary200: '#3ec277',
    secondary200: '#d9d9d9'
  }
};

const LightTheme8: ThemeTypes = {
  name: 'LightTheme8',
  dark: false,
  variables: {
    'border-color': '#d9d9d9',
    gradient:
      'linear-gradient(250.38deg, #c1d6d066 2.39%, #38b09c 34.42%, #009688 60.95%, #007069 84.83%, #002424 104.37%)',
    gradient2: 'linear-gradient(to right, rgb(0, 112, 105), rgb(0, 150, 136))',
    'card-shadow': '0px 1px 4px rgba(0, 0, 0, 0.08)'
  },
  colors: {
    primary: '#009688',
    secondary: '#8c8c8c',
    info: '#00a2ae',
    success: '#00a854',
    accent: '#FFAB91',
    warning: '#ffbf00',
    error: '#f04134',
    lightprimary: '#e6efec',
    lightsecondary: '#f5f5f5',
    lightsuccess: '#e0f5ea',
    lighterror: '#fde8e7',
    lightwarning: '#fff7e0',
    darkprimary: '#04897c',
    darksecondary: '#7c7c7c',
    darkText: '#212121',
    lightText: '#8c8c8c',
    borderLight: '#e6ebf1',
    inputBorder: '#a1a1a5',
    containerBg: '#fafafb',
    surface: '#fff',
    'on-surface-variant': '#fff',
    facebook: '#4267b2',
    twitter: '#1da1f2',
    linkedin: '#0e76a8',
    gray100: '#f5f5f5',
    primary200: '#38b09c',
    secondary200: '#bbbbbb'
  }
};

export {
  DefaultTheme,
  LightTheme1,
  LightTheme6,
  LightTheme5,
  LightTheme2,
  LightTheme3,
  LightTheme4,
  LightTheme7,
  LightTheme8
};
