import { jwtDecode, type JwtPayload } from 'jwt-decode';

export interface JwtPayloadEx extends JwtPayload {
  user_id: number;
  system_roles: string[];
  system_permissions: string[];
}

export function getJwtPayload(token: string): JwtPayloadEx {
  return jwtDecode(token) as JwtPayloadEx;
}

export function getTokenJwtPayloadWithExpirationDate(
  token: string
): { decoded: JwtPayloadEx; expirationDate: Date } | null {
  const decoded = jwtDecode(token);
  if (!decoded.exp) return null;

  const expirationDate = new Date(0);
  expirationDate.setUTCSeconds(decoded.exp);

  return { decoded: decoded as JwtPayloadEx, expirationDate: expirationDate };
}
