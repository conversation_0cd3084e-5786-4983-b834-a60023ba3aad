import { useCustomizerStore } from '@/stores/customizer';
import type { EditorManager, RawEditorOptions } from 'tinymce';
import { computed } from 'vue';

export const toolbar =
  'undo redo | styles | bold italic strikethrough underline subscript superscript | fontsize | forecolor backcolor hilitecolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent';
/**
 * Full configuration for TinyMCE editor -> https://www.tiny.cloud/docs/tinymce/latest/
 */
export function simpleConfig(): { [key: string]: string | boolean | Function } {
  const customizer = useCustomizerStore();
  const isDark = computed(() => customizer.theme.toUpperCase().includes('DARK'));

  return {
    entity_encoding: 'raw',
    license_key: 'gpl',
    language_url: `/assets/langs/cs.js`,
    language: 'cs',
    plugins: 'lists table charmap insertdatetime autoresize',
    toolbar: toolbar + ' | greekLetters | table | mathFormulas | charmap | insertdatetime',
    menubar: false,
    branding: false,
    promotion: false,
    statusbar: false,

    style_formats: [
      { title: 'Heading 1', format: 'h1' },
      { title: 'Heading 2', format: 'h2' },
      { title: 'Heading 3', format: 'h3' },
      { title: 'Heading 4', format: 'h4' },
      { title: 'Heading 5', format: 'h5' },
      { title: 'Heading 6', format: 'h6' },
      { title: 'Paragraph', format: 'p' }
    ],

    skin: isDark.value ? 'oxide-dark' : undefined,
    content_css: isDark.value ? 'dark' : undefined,

    setup: (editor: any) => {
      editor.ui.registry.addMenuButton('greekLetters', {
        text: 'Řecká písmena',
        fetch: (callback: any) => {
          const greekAlphabet = [
            { text: 'Alfa (α)', value: 'α' },
            { text: 'Beta (β)', value: 'β' },
            { text: 'Gama (γ)', value: 'γ' },
            { text: 'Delta (Δ)', value: 'Δ' },
            { text: 'Epsilon (ε)', value: 'ε' },
            { text: 'Zeta (ζ)', value: 'ζ' },
            { text: 'Théta (θ)', value: 'θ' },
            { text: 'Lambda (λ)', value: 'λ' },
            { text: 'My (μ)', value: 'μ' },
            { text: 'Pi (π)', value: 'π' },
            { text: 'Sigma (σ)', value: 'σ' },
            { text: 'Tau (τ)', value: 'τ' },
            { text: 'Fi (ϕ)', value: 'ϕ' },
            { text: 'Psi (ψ)', value: 'ψ' },
            { text: 'Omega (Ω)', value: 'Ω' }
          ];

          const items = greekAlphabet.map((letter) => ({
            type: 'menuitem',
            text: letter.text,
            onAction: () => editor.insertContent(letter.value)
          }));

          callback(items);
        }
      });
      editor.ui.registry.addMenuButton('mathFormulas', {
        text: 'Matematické vzorce',
        fetch: (callback: any) => {
          const mathFormulas = [
            { text: 'Chemická reakce (A + B → C)', value: 'A + B → C' },
            { text: 'Molarita (C = n/V)', value: 'C = n/V' },
            { text: 'Výpočet pH (pH = -log[H+])', value: 'pH = -log[H+]' },
            { text: 'Ideální plynový zákon (PV = nRT)', value: 'PV = nRT' }
          ];

          const items = mathFormulas.map((formula) => ({
            type: 'menuitem',
            text: formula.text,
            onAction: () => editor.insertContent(formula.value)
          }));

          callback(items);
        }
      });
    }
  } as EditorManager & RawEditorOptions;
}
