import { Button, notification } from 'ant-design-vue';
import axios from 'axios';
import { h } from 'vue';
import type { ServerOptions, SortType } from 'vue3-easy-data-table';

const axiosServices = axios.create();

axiosServices.interceptors.response.use(
  (response) => response,
  (error) => Promise.reject((error.response && error.response.data) || 'Wrong Services')
);

export default axiosServices;

export interface ResponseI {
  api_id: string;
  error: null | string;
  message: string;
  status_code: number;
  unread_notification?: boolean | undefined;
}

export interface BasePaginatorResponseI<T> extends ResponseI {
  data: PaginatorData<T>;
}

export interface BaseResponseI<T> extends ResponseI {
  data: T;
}

export interface PaginatorData<T> {
  current_page: number;
  items_per_page: number;
  total_pages: number;
  total_items: number;
  items: T[];
}

export interface SearchParamsI {
  search?: string;
  start_date?: Date;
  end_date?: Date;
  limit?: number;
  tables?: string[];
  search_params?: {
    [key: string]: {
      search: string;
      limit: number;
    };
  };
}

export function prepareSearchParams(params: SearchParamsI): URLSearchParams {
  const searchParams = new URLSearchParams();

  if (params.search_params) {
    searchParams.append('search_params', JSON.stringify(params.search_params));
  }
  if (params.start_date) {
    searchParams.append('start_date', params.start_date.toISOString());
  }
  if (params.end_date) {
    searchParams.append('end_date', params.end_date.toISOString());
  }

  return searchParams;
}

export function stringifySearchParams(params: SearchParamsI): string {
  return prepareSearchParams(params).toString();
}

export function stringifyServerOptions(
  options: ServerOptions,
  search: null | string = null,
  search_columns: string[] = [],
  fixedFilterOptions: null | Array<{
    column: string;
    value: null | number | string | string[] | number[] | object | object[];
  }> = null,
  search_type: 'AND' | 'OR' = 'AND'
): string {
  return prepareParams(options, search, search_type, search_columns, fixedFilterOptions).toString();
}

export function prepareParams(
  options: ServerOptions,
  search: null | string = null,
  search_type: 'AND' | 'OR' = 'AND',
  search_columns: string[] = [],
  fixedFilterOptions: null | Array<{
    column: string;
    value: null | number | string | string[] | number[] | object | object[];
  }> = null
) {
  const prepareParams = prepareParamsObject(
    options,
    search,
    search_type,
    search_columns,
    fixedFilterOptions
  );
  const params = new URLSearchParams();

  params.append('current_page', prepareParams.current_page.toString());
  params.append('items_per_page', prepareParams.rowsPerPage.toString());
  params.append('sorting_params', JSON.stringify(prepareParams.sorting_params));
  params.append('filter_params', JSON.stringify(prepareParams.filter_params));

  return params;
}

export function prepareJsonParams(
  options: ServerOptions,
  search: null | string = null,
  search_columns: string[] = [],
  fixedFilterOptions: null | Array<{
    column: string;
    value: null | number | string | string[] | number[] | object | object[];
  }> = null,
  search_type: 'AND' | 'OR' = 'AND'
) {
  const prepareParams = prepareParamsObject(
    options,
    search,
    search_type,
    search_columns,
    fixedFilterOptions
  );
  return {
    current_page: prepareParams.current_page,
    items_per_page: prepareParams.rowsPerPage,
    sorting_params: prepareParams.sorting_params,
    filter_params: prepareParams.filter_params
  };
}

export interface PrepareParamsObjectI {
  current_page: number;
  rowsPerPage: number;
  sorting_params: { [key: string]: SortType }[];
  filter_params: null | FilterParamsBaseI;
}

export interface FilterParamsBaseI {
  [key: string]:
    | null
    | undefined
    | number
    | string
    | object
    | object[]
    | string[]
    | number[]
    | [
        {
          term: string;
          columns: string[];
          type: 'AND' | 'OR';
        }
      ];
  search?: [
    {
      term: string;
      columns: string[];
      type: 'AND' | 'OR';
    }
  ];
}

export function prepareParamsObject(
  options: ServerOptions,
  search: null | string = null,
  search_type: 'AND' | 'OR' = 'AND',
  search_columns: string[] = [],
  fixedFilterOptions: null | Array<{
    column: string;
    value: null | number | string | string[] | number[] | object | object[];
  }> = null
): PrepareParamsObjectI {
  const data: PrepareParamsObjectI = {
    current_page: options.page,
    rowsPerPage: options.rowsPerPage,
    sorting_params: [],
    filter_params: {}
  };

  const sorting_params: { [key: string]: SortType }[] = [];
  if (options.sortBy) {
    if (Array.isArray(options.sortBy)) {
      options.sortBy.forEach((sortBy, index) => {
        sorting_params.push({ [sortBy]: (options.sortType?.[index] as SortType) ?? 'asc' });
      });
    } else if (typeof options.sortBy === 'string' && typeof options.sortType === 'string') {
      sorting_params.push({ [options.sortBy]: options.sortType ?? 'asc' });
    }
  }

  const filter_params: FilterParamsBaseI = {};
  if (search) {
    filter_params['search'] = [
      {
        term: search,
        columns: search_columns,
        type: search_type
      }
    ];
  }

  if (fixedFilterOptions) {
    for (const filterOption of fixedFilterOptions) {
      filter_params[filterOption.column] = filterOption.value;
    }
  }

  data.sorting_params = sorting_params;
  data.filter_params = filter_params;

  return data;
}

export function revertServerOptionsFromLocation(): { options: ServerOptions; term: string | null } {
  const params = new URLSearchParams(window.location.search);

  const page = params.get('current_page');
  const perPage = params.get('items_per_page');
  const sortingParams = params.get('sorting_params');
  const filterParams = params.get('filter_params');

  const search = filterParams ? (JSON.parse(filterParams)?.search?.term ?? null) : null;

  const sortBy: string[] = [];
  const sortType: SortType[] = [];

  if (sortingParams) {
    const sortingParamsArray: Array<{ [key: string]: SortType }> = JSON.parse(sortingParams);
    for (const sortingParam of sortingParamsArray) {
      for (const key in sortingParam) {
        if (sortBy.includes(key)) continue;

        sortBy.push(key);
        sortType.push(sortingParam[key]);
      }
    }
  }

  return {
    options: {
      page: page ? parseInt(page) : 1,
      rowsPerPage: perPage ? parseInt(perPage) : 25,
      sortBy: sortBy,
      sortType: sortType
    },
    term: search
  };
}

export function addParamsToLocation(data: PrepareParamsObjectI) {
  const url = new URL(window.location.href);
  history.pushState(data, '', url);
}

export function reloadWithoutParams() {
  history.pushState({}, '', window.location.pathname);

  return {
    options: {
      page: 1,
      rowsPerPage: 25,
      sortBy: [],
      sortType: []
    },
    term: null
  };
}

export function loadParamsFailedReloadNotification(
  title: string,
  description: string,
  callback?: () => void
) {
  const key = `open${Date.now()}`;

  notification.error({
    message: title,
    description: description,
    duration: 0,
    closeIcon: false,
    btn: () =>
      h(
        Button,
        {
          type: 'primary',
          class:
            'v-btn v-theme--LightTheme6 bg-primary v-btn--density-default v-btn--size-default v-btn--variant-flat',
          outlined: true,
          variant: 'flat',
          onClick: () => {
            notification.close(key);
            if (callback) {
              callback();
            }
          }
        },
        { default: () => 'Smazání filtrů' }
      ),
    key,
    onClick: () => {
      notification.close(key);
      if (callback) {
        callback();
      }
    }
  });
}
