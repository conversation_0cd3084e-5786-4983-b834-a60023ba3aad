import { useAuthStore } from '@/stores/auth';
import { storeToRefs } from 'pinia';
import { type Directive } from 'vue';

export function isAllowed(permissions: string[]): boolean {
  const AuthStore = useAuthStore();
  const { user } = storeToRefs(AuthStore);

  const userPermissions: string[] = Array.from(
    new Set([
      ...(user.value?.permissions?.map((p) => p.name) ?? []),
      ...(user.value?.roles?.flatMap((r) => r.permissions?.map((p) => p.name)) ?? [])
    ])
  );

  return permissions.every((v) => userPermissions.includes(v));
}

export function isAllowedAny(permissions: string[]): boolean {
  const AuthStore = useAuthStore();
  const { user } = storeToRefs(AuthStore);

  const userPermissions: string[] = Array.from(
    new Set([
      ...(user.value?.permissions?.map((p) => p.name) ?? []),
      ...(user.value?.roles?.flatMap((r) => r.permissions?.map((p) => p.name)) ?? [])
    ])
  );

  return permissions.some((v) => userPermissions.includes(v));
}

export function missingPermissions(permissions: string[]): string[] {
  const AuthStore = useAuthStore();
  const { user } = storeToRefs(AuthStore);

  const userPermissions: string[] = Array.from(
    new Set([
      ...(user.value?.permissions?.map((p) => p.name) ?? []),
      ...(user.value?.roles?.flatMap((r) => r.permissions?.map((p) => p.name)) ?? [])
    ])
  );

  return permissions.filter((v) => !userPermissions.includes(v));
}

const isAllowedDirective: Directive<HTMLElement, string[]> = {
  mounted(el, { value }) {
    if (!isAllowed(value)) {
      el.style.display = 'none';
    }
  },
  updated(el, { value }) {
    if (!isAllowed(value)) {
      el.style.display = 'none';
    } else {
      el.style.display = '';
    }
  }
};

export default isAllowedDirective;
