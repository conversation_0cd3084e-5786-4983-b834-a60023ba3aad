// Field name translations for user-facing notifications
export const FIELD_NAME_TRANSLATIONS: Record<string, string> = {
  // Common fields (all forms)
  form_name: '<PERSON><PERSON><PERSON><PERSON>',
  batch_number: '<PERSON><PERSON><PERSON> šarže',
  batch_description: '<PERSON><PERSON> šarže',
  conclusion_text: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  conclusions: '<PERSON><PERSON><PERSON><PERSON><PERSON>',

  // Investigation fields (InvestigationDetail.vue)
  affected_batch_number: 'Č<PERSON>lo ovlivněné šarže',
  impact_on_quality: 'Vliv na kvalitu',
  impact_on_yield: 'Vliv na výtěžek',
  problem_description: 'Popis problému',
  investigation_in_production: 'Šetření ve výrobě',
  investigation_in_laboratory: 'Šetření v laboratoři',
  recommendations: 'Doporučení',

  // Experiment fields (ExperimentDetail.vue)
  reaction_procedure: 'Postup reakce',
  content_percentage: 'Obsah (%)',
  product_weight: 'Produkt',
  yield_percentage: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (%)',

  // Attempt fields (AttemptDetail.vue)
  apparatus: 'Aparatura',

  // Message fields (MessageDetail.vue)
  message_text: 'Textové pole',

  // Chemical table fields (CustomChemicalTable.vue)
  chemicals: 'Tabulka chemikálií',
  name: 'Chemikálie',
  concentration: 'Obsah (%)',
  density: 'Hustota (ρ)',
  molar_mass: 'Molární hmotnost (M)',
  grams: 'Hmotnost (m)',
  moles: 'Látkové množství (n)',
  volume_ml: 'Objem (V)',
  equivalent: 'Ekvivalent',
  notes: 'Poznámka',
  action: 'Akce',

  // Legacy fields (for backward compatibility)
  nazev: 'Název',
  textove_pole: 'Textové pole',
  tabulka_chemikalii: 'Tabulka chemikálií',
  zaver: 'Závěr',
  poznamky: 'Poznámky',
  vysledek: 'Výsledek',
  cislo_ovlivnene_sarze: 'Číslo ovlivněné šarže',
  popis_problemu: 'Popis problému',
  setreni_ve_vyrobe: 'Šetření ve výrobě',
  setreni_v_laboratori: 'Šetření v laboratoři',
  doporuceni: 'Doporučení',
  popis_sarze: 'Popis šarže',
  aparatura: 'Aparatura',
  postup_reakce: 'Postup reakce',
  obsah: 'Obsah (%)',
  hmotnost_produktu: 'Produkt',
  vytezeek: 'Výtěžek (%)',

  // Dynamic fields (all detail pages)
  ostatni_zavery0: 'Ostatní závěr 1',
  ostatni_zavery1: 'Ostatní závěr 2',
  ostatni_zavery2: 'Ostatní závěr 3',
  ostatni_zavery3: 'Ostatní závěr 4',
  ostatni_zavery4: 'Ostatní závěr 5',
  ostatni_zavery5: 'Ostatní závěr 6',
  ostatni_zavery6: 'Ostatní závěr 7',
  ostatni_zavery7: 'Ostatní závěr 8',
  ostatni_zavery8: 'Ostatní závěr 9',
  ostatni_zavery9: 'Ostatní závěr 10'
};

/**
 * Translates technical field names to user-friendly Czech labels
 * @param fieldName - The technical field name (e.g., 'nazev', 'ostatni_zavery0')
 * @returns The translated field name (e.g., 'Název', 'Ostatní závěr 1')
 */
export function translateFieldName(fieldName: string): string {
  // Handle dynamic field names (like ostatni_zavery0, ostatni_zavery1, etc.)
  if (fieldName.startsWith('ostatni_zavery')) {
    const match = fieldName.match(/ostatni_zavery(\d+)/);
    if (match) {
      const index = parseInt(match[1]) + 1;
      return `Ostatní závěr ${index}`;
    }
  }

  return FIELD_NAME_TRANSLATIONS[fieldName] || fieldName;
}

/**
 * Gets all available field translations
 * @returns Record of all field name translations
 */
export function getAllFieldTranslations(): Record<string, string> {
  return { ...FIELD_NAME_TRANSLATIONS };
}

/**
 * Adds or updates field name translations
 * @param translations - Object with field name translations to add/update
 */
export function addFieldTranslations(translations: Record<string, string>): void {
  Object.assign(FIELD_NAME_TRANSLATIONS, translations);
}
