// Field name translations for user-facing notifications
export const FIELD_NAME_TRANSLATIONS: Record<string, string> = {
  // Common fields
  nazev: '<PERSON><PERSON>ze<PERSON>',
  textove_pole: 'Textové pole',
  tabulka_chemikalii: 'Tabulka chemikálií',
  zaver: '<PERSON><PERSON><PERSON><PERSON><PERSON>',

  // Experiment fields (ExperimentDetail.vue)
  reaction_procedure: 'Postup reakce',
  poznamky: '<PERSON>z<PERSON>m<PERSON>',
  vys<PERSON>ek: '<PERSON><PERSON><PERSON><PERSON>',

  // Investigation fields (InvestigationDetail.vue)
  cislo_ovlivnene_sarze: '<PERSON><PERSON>lo ovlivněné šarže',
  popis_problemu: 'Popis problému',
  setreni_ve_vyrobe: 'Šetření ve výrobě',
  setreni_v_laboratori: 'Šetření v laboratoři',
  doporuceni: 'Doporučení',

  // Attempt fields (AttemptDetail.vue)
  popis_sarze: 'Popis šarže',
  aparatura: 'Aparatura',
  postup_reakce: 'Postup reakce',
  obsah: 'O<PERSON><PERSON> (%)',
  hmotnost_produktu: 'Produkt',
  vytezeek: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (%)',

  // Dynamic fields (all detail pages)
  ostatni_zavery0: 'Ostatní závěr 1',
  ostatni_zavery1: 'Ostatní závěr 2',
  ostatni_zavery2: 'Ostatní závěr 3',
  ostatni_zavery3: 'Ostatní závěr 4',
  ostatni_zavery4: 'Ostatní závěr 5',
  ostatni_zavery5: 'Ostatní závěr 6',
  ostatni_zavery6: 'Ostatní závěr 7',
  ostatni_zavery7: 'Ostatní závěr 8',
  ostatni_zavery8: 'Ostatní závěr 9',
  ostatni_zavery9: 'Ostatní závěr 10'
};

/**
 * Translates technical field names to user-friendly Czech labels
 * @param fieldName - The technical field name (e.g., 'nazev', 'ostatni_zavery0')
 * @returns The translated field name (e.g., 'Název', 'Ostatní závěr 1')
 */
export function translateFieldName(fieldName: string): string {
  // Handle dynamic field names (like ostatni_zavery0, ostatni_zavery1, etc.)
  if (fieldName.startsWith('ostatni_zavery')) {
    const match = fieldName.match(/ostatni_zavery(\d+)/);
    if (match) {
      const index = parseInt(match[1]) + 1;
      return `Ostatní závěr ${index}`;
    }
  }

  return FIELD_NAME_TRANSLATIONS[fieldName] || fieldName;
}

/**
 * Gets all available field translations
 * @returns Record of all field name translations
 */
export function getAllFieldTranslations(): Record<string, string> {
  return { ...FIELD_NAME_TRANSLATIONS };
}

/**
 * Adds or updates field name translations
 * @param translations - Object with field name translations to add/update
 */
export function addFieldTranslations(translations: Record<string, string>): void {
  Object.assign(FIELD_NAME_TRANSLATIONS, translations);
}
