import { ref } from 'vue';

export const passwordRules = ref([
  (v: string) => !!v || 'Zadejte heslo prosím!',
  (v: string) => (v && v.length >= 10) || 'Heslo musí mít minimálně 10 znaků!',
  (v: string) => /(?=.*[A-Z])/.test(v) || 'Heslo musí obsahovat alespoň jedno velké písmeno',
  (v: string) => /(?=.*\d)/.test(v) || 'Heslo musí obsahovat alespoň jedno číslo'
]);

export const passwordRulesOptional = ref([
  (v: string) => !v || v.length >= 10 || 'Heslo musí mít minimálně 10 znaků!',
  (v: string) => !v || /(?=.*[A-Z])/.test(v) || 'Heslo musí obsahovat alespoň jedno velké písmeno',
  (v: string) => !v || /(?=.*\d)/.test(v) || 'Heslo musí obsahovat alespoň jedno číslo'
]);

export const emailRules = ref([
  (v: string) => !!v || 'Zadejte prosím email',
  (v: string) => /.+@.+\..+/.test(v) || 'Email musí být platný!'
]);

export const projectRules = ref([(v: string) => !!v || 'Zadejte prosím název projektu!']);
export const itemRequiredRule = ref([(v: string) => !!v || 'Položka je povinná']);
export const lastRules = ref([(v: string) => !!v || 'Příjmení je povinné']);

export const itemShortcutRequiredRule = ref([
  (v: string) => !!v || 'Položka je povinná',
  (v: string) => v.length <= 4 || 'Zkratka položky může mít maximálně 4 znaky'
]);
