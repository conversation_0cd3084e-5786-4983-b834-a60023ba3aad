export { fakeBackend };

function fakeBackend() {
  const realFetch = window.fetch;
   
  window.fetch = function (url: any, opts: any) {
    return new Promise((resolve: any, reject) => {
      function handleRoute() {
        switch (true) {
          default:
            return realFetch(url, opts)
              .then((response) => resolve(response))
              .catch((error) => reject(error));
        }
      }

      handleRoute();
    });
  };
}
