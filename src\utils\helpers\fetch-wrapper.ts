import { useAuthStore } from '@/stores/auth';
import type { BasePaginatorResponseI, BaseResponseI } from '../axios';
import { useNotificationsStore } from '@/stores/notifications';

export const fetchWrapper = {
  get: request('GET') as unknown as <T = any>(
    url: string,
    body?: BodyInit | object | null | undefined
  ) => Promise<T>,
  post: request('POST') as unknown as <T = any>(
    url: string,
    body?: BodyInit | object | null | undefined
  ) => Promise<T>,
  put: request('PUT') as unknown as <T = any>(
    url: string,
    body?: BodyInit | object | null | undefined
  ) => Promise<T>,
  delete: request('DELETE') as unknown as <T = any>(
    url: string,
    body?: BodyInit | object | null | undefined
  ) => Promise<T>,
  patch: request('PATCH') as unknown as <T = any>(
    url: string,
    body?: BodyInit | object | null | undefined
  ) => Promise<T>,
  blob: request('GET') as unknown as (
    url: string,
    body?: BodyInit | object | null | undefined
  ) => Promise<Blob>
};

function request(method: string) {
  return async (url: string, body?: BodyInit | object | null | undefined) => {
    const requestOptions: RequestInit = {
      method
    };

    if (!(body instanceof FormData)) {
      requestOptions.headers = {
        ...requestOptions.headers,
        ...{ 'Content-Type': 'application/json' }
      };
    }

    const auth = await authHeader(url);
    requestOptions.headers = {
      ...requestOptions.headers,
      ...(auth ? { Authorization: auth } : [])
    };

    if (body) {
      requestOptions.body = body instanceof FormData ? body : JSON.stringify(body);
    }

    return fetch(url, requestOptions).then((response) => handleResponse(response, requestOptions));
  };
}

async function authHeader(url: string) {
  const authStore = useAuthStore();
  const token = url.includes('auth/refresh_token')
    ? authStore.token?.refresh_token
    : authStore.token?.access_token;
  const isLoggedIn = !!token;
  const isFakeApiUrl = url.startsWith(import.meta.env.VITE_FAKE_API_URL);
  const isApiUrl = url.startsWith(import.meta.env.VITE_API_URL);

  if (isLoggedIn && (isApiUrl || isFakeApiUrl)) {
    return `Bearer ${token}`;
  } else {
    return null;
  }
}

async function handleResponse(
  response: Response,
  originalRequest?: RequestInit
): Promise<Blob | BaseResponseI<unknown> | BasePaginatorResponseI<unknown>> {
  // check is response is a blob return blob if text/json return JSON
  const contentType = response.headers.get('content-type');
  if (contentType && contentType.includes('application/json')) {
    return response.text().then(async (text: string) => {
      const data = (text && JSON.parse(text)) as
        | BaseResponseI<unknown>
        | BasePaginatorResponseI<unknown>;

      if (!response.ok) {
        const authStore = useAuthStore();
        const { user } = authStore;

        if (response.status === 401 && user && !response.url.includes('auth/refresh_token')) {
          try {
            const newToken = await authStore.getRefreshToken();

            if (newToken) {
              const retryResponse = await fetch(response.url, {
                ...(originalRequest || {}),
                headers: {
                  ...(originalRequest?.headers || {}),
                  Authorization: `Bearer ${newToken}`
                }
              });
              return handleResponse(retryResponse, originalRequest);
            }
          } catch (error) {
            authStore.logout();
            return Promise.reject(data);
          }
        }

        return Promise.reject(data);
      }

      if (data.unread_notification) {
        useNotificationsStore().getNotifications();
      }

      return data;
    });
  }

  if (response.ok) {
    return response.blob();
  }

  return Promise.reject(response.statusText);
}
