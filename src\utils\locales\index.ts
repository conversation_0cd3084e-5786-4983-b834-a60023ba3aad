import { format } from 'date-fns';

export function toLocale(date: Date): string {
  return format(date, 'yyyy-MM-dd');
}

export function toTimeLocale(date: Date): string {
  const options = {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
    localeMatcher: 'lookup'
  } as Intl.DateTimeFormatOptions;

  return format(date, 'dd.MM.yyyy HH:mm');
}

export function formatDate(date: Date | null | undefined) {
  return date ? format(date, 'yyyy-MM-dd') : '';
}
