import { segmentTitleMap } from './titleMap';
import type { RouteLocationNormalized } from 'vue-router';

export function buildDynamicTitle(route: RouteLocationNormalized): string {
  const segments: string[] = [];
  route.matched.forEach((record) => {
    const parts = record.path.split('/').filter(Boolean);
    parts.forEach((part) => {
      if (part.startsWith(':')) {
        const paramKey = part.slice(1);
        const paramValue = route.params[paramKey];
        if (paramValue) segments.push(String(paramValue));
      } else {
        segments.push(segmentTitleMap[part] || part);
      }
    });
  });

  const uniqueSegments = segments.filter((seg, i) => segments.indexOf(seg) === i);
  return uniqueSegments.join(' / ') || 'FarMak';
}

export function setPageTitle(title: string): void {
  document.title = title;
}

export function setPageTitleMultiple(titles: string[], separator: string = ' / '): void {
  const title = titles.filter(Boolean).join(separator);
  document.title = title || 'FarMak';
}
