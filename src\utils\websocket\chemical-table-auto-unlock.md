# Chemical Table Auto-Unlock for Action Buttons

## Problem Solved

### **🔒 The Issue**

When users clicked action buttons like "Dopočítat hodnoty" or "Vymazat dopočítané hodnoty":

1. **Table gets locked** (as expected for WebSocket collaboration)
2. **Button click doesn't trigger focus/blur** events
3. **Table remains locked forever** - no automatic unlock
4. **Other users can't edit** until manual unlock

### **💡 The Solution**

Implemented **automatic unlock after 2 seconds** for all action buttons using the existing `scheduleAutoUnlock` method from the chemical table WebSocket composable.

## Implementation Details

### **1. Auto-Unlock Function**

```javascript
// Auto-unlock function for action buttons
const autoUnlockAfterAction = () => {
  if (chemicalTableWS && props.enableWebSocket) {
    console.log('⏰ Setting auto-unlock timer for action button (2 seconds)');

    // Reset editing state after 2 seconds to match the unlock timing
    setTimeout(() => {
      isEditing.value = false;
      console.log('👁️ Reset isEditing state after action button');
    }, 2000);

    // Use the built-in scheduleAutoUnlock method with 2 second delay
    chemicalTableWS.scheduleAutoUnlock(2000);
  }
};
```

### **2. Integration with Action Button Functions**

#### **Calculate Functions:**

```javascript
async function calculateRowData(chemical_id: number, suppressErrorNotification: boolean = false) {
  // ... existing calculation logic ...

  if (calculationSuccess && finalStateChanged) {
    triggerSuccessNotification('Údaje byli úspěšně dopočítany.');
  } else if (!suppressErrorNotification) {
    triggerErrorNotification('Nepodařilo se dopočítat údaje.');
  }

  // Auto-unlock the table after action button
  autoUnlockAfterAction();
}

const calculateRowDataLast = async (chemical_id: number, suppressErrorNotification: boolean = false) => {
  // ... existing calculation logic ...

  if (calculationTriggered) {
    triggerSuccessNotification('Údaje byli úspěšně dopočítany.');
  } else if (!suppressErrorNotification) {
    triggerErrorNotification('Nepodařilo se dopočítat údaje.');
  }

  // Auto-unlock the table after action button
  autoUnlockAfterAction();
};
```

#### **Reset Function:**

```javascript
function resetCalculatedValues(chemical_id: number) {
  // ... existing reset logic ...

  triggerSuccessNotification('Hodnoty byly úspěšně vymazány.');

  // Auto-unlock the table after action button
  autoUnlockAfterAction();
}
```

### **3. Uses Existing WebSocket Infrastructure**

The implementation leverages the existing `scheduleAutoUnlock` method from `useChemicalTableWebSocket.ts`:

```javascript
// From useChemicalTableWebSocket.ts
function scheduleAutoUnlock(delay: number = 1000) {
  setTimeout(() => {
    if (isLockedByMe.value) {
      console.log('🔓 Auto-unlocking chemical table:', tableName);
      unlockTable();
    }
  }, delay);
}
```

## How It Works

### **🎯 Action Button Flow:**

1. **User clicks "Dopočítat hodnoty"**
2. Table gets locked (existing WebSocket behavior)
3. Calculation function executes
4. Results are calculated and synced via WebSocket
5. `autoUnlockAfterAction()` is called
6. **2-second timer starts**
7. After 2 seconds → Table automatically unlocks
8. Other users can edit again

### **🧹 Reset Button Flow:**

1. **User clicks "Vymazat dopočítané hodnoty"**
2. Table gets locked (existing WebSocket behavior)
3. Reset function executes
4. Values are reset and synced via WebSocket
5. `autoUnlockAfterAction()` is called
6. **2-second timer starts**
7. After 2 seconds → Table automatically unlocks
8. Other users can edit again

## Console Output Examples

### **Action Button with Auto-Unlock:**

```javascript
// User clicks "Dopočítat hodnoty"
🔒 Locking chemical table: chemicals
🧮 Calculation logic executes...
✅ Údaje byli úspěšně dopočítany.
⏰ Setting auto-unlock timer for action button (2 seconds)
🔓 Auto-unlocking chemical table: chemicals (after 2 seconds)
```

### **Reset Button with Auto-Unlock:**

```javascript
// User clicks "Vymazat dopočítané hodnoty"
🔒 Locking chemical table: chemicals
🧹 Reset logic executes...
✅ Hodnoty byly úspěšně vymazány.
⏰ Setting auto-unlock timer for action button (2 seconds)
🔓 Auto-unlocking chemical table: chemicals (after 2 seconds)
```

## Visual State Management

### **🎨 The Visual Issue**

After implementing functional auto-unlock, the **visual wrapper stayed visible**:

- Table unlocked functionally ✅
- Other users could edit ✅
- But colored border and "Upravujete" indicator remained ❌

### **🔧 The Visual Fix**

The issue was that `isEditing.value` state wasn't reset after auto-unlock:

```javascript
// Visual wrapper classes controlled by:
:class="{
  'field-locked': enableWebSocket && isLockedByOther,
  'field-locked-by-me': enableWebSocket && isLockedByMe,
  'field-editing': enableWebSocket && isEditing  // ← This stayed true!
}"
```

**Solution:** Reset `isEditing.value = false` in sync with the unlock timing:

```javascript
// Reset editing state after 2 seconds to match the unlock timing
setTimeout(() => {
  isEditing.value = false;
  console.log('👁️ Reset isEditing state after action button');
}, 2000);
```

### **✅ Complete Visual Flow**

1. **Click action button** → `isEditing.value = true` → Visual wrapper appears
2. **Operation executes** → Colored border and "Upravujete" indicator visible
3. **After 2 seconds** → `isEditing.value = false` → Visual wrapper disappears
4. **Perfect sync** → Functional unlock + visual unlock happen together

## Benefits

### **✅ Automatic Unlock**

- No more permanently locked tables after action buttons
- 2-second delay allows operations to complete
- Uses existing, tested unlock infrastructure

### **✅ Collaborative Friendly**

- Other users can edit again after brief delay
- Maintains WebSocket locking during operations
- Prevents conflicts during calculations/resets

### **✅ Visual Consistency**

- Visual indicators disappear when table unlocks
- Perfect sync between functional and visual state
- No lingering "Upravujete" indicators

### **✅ User Experience**

- Seamless operation - users don't need to think about unlocking
- Reasonable 2-second delay feels natural
- Consistent behavior across all action buttons
- Clean visual feedback

### **✅ Robust Implementation**

- Uses existing `scheduleAutoUnlock` method
- Proper cleanup and timeout management
- Only runs when WebSocket is enabled
- Synchronized visual and functional state

## Action Buttons Covered

### **✅ Calculate Buttons:**

1. **"Dopočítat hodnoty"** (regular chemicals) → `calculateRowData()`
2. **"Dopočítat hodnoty"** (result chemical) → `calculateRowDataLast()`

### **✅ Reset Buttons:**

1. **"Vymazat dopočítané hodnoty"** (all chemicals) → `resetCalculatedValues()`

### **✅ All Scenarios:**

- First chemical calculations
- Regular chemical calculations
- Result chemical calculations
- Value resets
- Both successful and failed operations

## Testing Scenarios

### **✅ Single User:**

1. Click "Dopočítat hodnoty" → Calculations appear → Table unlocks after 2 seconds
2. Click "Vymazat dopočítané hodnoty" → Values reset → Table unlocks after 2 seconds

### **✅ Multiple Users:**

1. **User A** clicks "Dopočítat hodnoty" → Table locks
2. **User B** sees lock indicator and calculated results
3. After 2 seconds → Table unlocks automatically
4. **User B** can now edit fields normally

### **✅ Rapid Actions:**

1. Click "Dopočítat" → 2-second timer starts
2. Click "Vymazat" before timer expires → Timer resets to 2 seconds
3. Table unlocks 2 seconds after last action

## Result

The chemical table now provides **perfect action button behavior** with:

- 🔒 **Proper locking** during operations to prevent conflicts
- ⏰ **Automatic unlock** after 2 seconds - no manual intervention needed
- 🤝 **Collaborative friendly** - other users can edit after brief delay
- 🎯 **Consistent behavior** across all action buttons
- 🛡️ **Robust implementation** using existing WebSocket infrastructure

**Action buttons now work seamlessly in collaborative editing!**
