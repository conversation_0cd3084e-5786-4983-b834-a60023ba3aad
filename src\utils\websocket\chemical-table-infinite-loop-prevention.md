# Chemical Table WebSocket Infinite Loop Prevention

## Critical Issue Solved

### **🚨 The Problem**
The watcher-based approach created **infinite loops** between users:

1. **User A** types → WebSocket update sent
2. **User B** receives update → `combinedChemicals` changes 
3. **User B's watcher** triggers → Sends update back to User A
4. **User A** receives update → `combinedChemicals` changes
5. **User A's watcher** triggers → Sends update back to User B
6. **Infinite loop!** 🔄

### **💡 The Solution**
Implemented **user ID checking and remote update flag** system, following the same pattern as `FieldLockWrapper.vue` and `EditorTextareaWebSocket.vue`.

## Implementation Details

### **1. Remote Update Detection Flag**

```javascript
// Flag to prevent triggering updates on remote changes
const isReceivingRemoteUpdate = ref(false);
```

### **2. Watcher Protection**

```javascript
watch(
  combinedChemicals,
  (newVal, oldVal) => {
    // Skip WebSocket updates if this is a remote update to prevent infinite loops
    if (isReceivingRemoteUpdate.value) {
      console.log('⏭️ Skipping watcher WebSocket updates - remote update in progress');
      return;
    }

    // Only proceed with WebSocket updates if this is a local change
    if (chemicalTableWS && props.enableWebSocket && newVal && Array.isArray(newVal)) {
      // ... rest of watcher logic
    }
  },
  { deep: true }
);
```

### **3. Incoming Update Monitor**

```javascript
// Watch for incoming WebSocket updates to prevent infinite loops
if (chemicalTableWS) {
  watch(
    () => {
      // Create a reactive dependency on all possible chemical field updates
      const allUpdates: any[] = [];
      allChemicals.value.forEach(chemical => {
        if (chemical.chemical_id) {
          const fieldsToWatch = ['name', 'concentration', 'density', 'molar_mass', 'grams', 'moles', 'volume_ml', 'equivalent', 'notes'];
          fieldsToWatch.forEach(field => {
            const update = chemicalTableWS.getChemicalFieldUpdate(chemical.chemical_id, field);
            if (update) {
              allUpdates.push(update);
            }
          });
        }
      });
      return allUpdates;
    },
    (updates) => {
      // Check if any update is from another user
      const hasRemoteUpdate = updates.some(update => 
        update && update.userId !== chemicalTableWS.webSocketStore?.currentUserId
      );
      
      if (hasRemoteUpdate) {
        console.log('🔄 Setting isReceivingRemoteUpdate flag - incoming WebSocket updates detected');
        isReceivingRemoteUpdate.value = true;
        
        // Reset flag after a short delay to allow the watcher to process
        setTimeout(() => {
          isReceivingRemoteUpdate.value = false;
          console.log('✅ Cleared isReceivingRemoteUpdate flag');
        }, 500);
      }
    },
    { deep: true }
  );
}
```

## How It Works

### **🔄 Normal Flow (Local Changes)**
1. **User A** types in field
2. `combinedChemicals` changes
3. `isReceivingRemoteUpdate.value` is `false`
4. Watcher proceeds → WebSocket update sent
5. **User B** receives update via ItemCell watchers
6. **No infinite loop!** ✅

### **🛡️ Protected Flow (Remote Changes)**
1. **User B** types in field → WebSocket update sent
2. **User A** receives update via ItemCell watchers
3. ItemCell updates the data → `combinedChemicals` changes
4. Incoming update monitor detects remote update → `isReceivingRemoteUpdate.value = true`
5. Main watcher sees the flag → **Skips WebSocket sending**
6. After 500ms → Flag resets to `false`
7. **No infinite loop!** ✅

## User ID Checking Pattern

### **Consistent with Other Components:**

#### **FieldLockWrapper.vue:**
```javascript
if (newUpdate && newUpdate.userId !== webSocketStore.currentUserId) {
  // Process remote update
}
```

#### **EditorTextareaWebSocket.vue:**
```javascript
if (newUpdate && newUpdate.userId !== webSocketStore?.currentUserId) {
  // Process remote update
}
```

#### **ItemCell.vue (Chemical Table):**
```javascript
if (update && update.userId !== chemicalTableWS.webSocketStore?.currentUserId) {
  // Process remote update
}
```

#### **CustomChemicalTable.vue (New):**
```javascript
const hasRemoteUpdate = updates.some(update => 
  update && update.userId !== chemicalTableWS.webSocketStore?.currentUserId
);
```

## Console Output Examples

### **Local Change (Sends Update):**
```javascript
combinedChemicals [Array with changes]
🔄 Chemical field changed via watcher: {
  chemicalId: 26,
  field: 'concentration',
  from: 10,
  to: 15
}
📝 Queued update: { chemicalId: 26, field: 'concentration', value: 15, queueSize: 1 }
🚀 Processing batch updates: 1 updates
📤 Sending WebSocket update: { chemicalId: 26, field: 'concentration', value: 15 }
```

### **Remote Change (Skips Update):**
```javascript
🔄 Setting isReceivingRemoteUpdate flag - incoming WebSocket updates detected
combinedChemicals [Array with remote changes]
⏭️ Skipping watcher WebSocket updates - remote update in progress
✅ Cleared isReceivingRemoteUpdate flag
```

## Benefits

### **✅ Prevents Infinite Loops**
- Local changes send WebSocket updates
- Remote changes skip WebSocket sending
- No ping-pong effect between users

### **✅ Maintains Real-Time Sync**
- Users still see each other's changes instantly
- ItemCell watchers handle incoming updates
- Main watcher handles outgoing updates

### **✅ Consistent Pattern**
- Same approach as other WebSocket components
- Reliable user ID checking
- Proper flag management

### **✅ Robust Timing**
- 500ms delay ensures proper processing
- Flag resets automatically
- No race conditions

## Testing Scenarios

### **✅ Two Users Typing:**
1. User A types "123" → User B sees "123" instantly
2. User B types "456" → User A sees "456" instantly
3. No infinite loops, no duplicate updates

### **✅ Action Buttons:**
1. User A clicks "Vymazat" → All fields reset to 0
2. User B sees all resets instantly
3. User B clicks "Dopočítat" → All calculations appear
4. User A sees all calculations instantly
5. No infinite loops, all updates work

### **✅ Fast Typing:**
1. User A types very fast → Debounced final value sent
2. User B sees final value instantly
3. User B types fast → User A sees their final value
4. No conflicts, no infinite loops

## Result

The chemical table now provides **safe, reliable real-time collaboration** with:
- 🛡️ **Infinite loop prevention** - proper user ID checking
- ⚡ **Real-time updates** - instant sync between users
- 🔄 **Bidirectional sync** - both users can edit simultaneously
- 🎯 **Reliable delivery** - debounced batch updates
- 🧮 **Action button support** - calculate/reset operations sync
- 🔒 **Consistent pattern** - same approach as other components

**Perfect collaborative editing without infinite loops!**
