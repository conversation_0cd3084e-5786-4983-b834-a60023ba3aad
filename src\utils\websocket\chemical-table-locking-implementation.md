# Chemical Table WebSocket Locking Implementation

## Overview

This implementation adds WebSocket-based locking functionality to the CustomChemicalTable component, providing real-time collaborative editing with visual indicators and conflict prevention.

## Key Features

### 🔒 **Table-Level Locking**

- Locks the entire chemical table (field name: "chemicals")
- No individual cell locking - whole table approach
- Auto-lock on focus, auto-unlock after 1 second on blur

### 🎨 **Visual Indicators**

- **Colored borders** showing who's editing
- **Lock overlay** when table is locked by another user
- **User indicator** showing current editor
- **Request access button** for locked tables

### ⚡ **Conditional Functionality**

- Only enabled when `enableWebSocket` prop is true
- Graceful fallback for non-WebSocket usage
- Maintains all existing functionality

## Implementation Details

### **Props Integration**

```vue
<CustomChemicalTable
  :enable-web-socket="true"
  :is-editable="!isReadOnly"
  :form-id="experiment.form_id"
/>
```

### **Visual States**

#### 1. **Normal State** (No WebSocket)

- Standard table appearance
- No borders or indicators

#### 2. **Editing State** (User is editing)

- Colored border matching user's color
- "Upravujete" indicator in top-right corner
- Table auto-locks on focus

#### 3. **Locked by Other** (Another user editing)

- Colored border matching lock owner's color
- Semi-transparent overlay with lock icon
- "Požádat o přístup" button
- Table content disabled (pointer-events: none)

#### 4. **Locked by Me** (I have the lock)

- My color border
- "Upravujete" indicator
- Full editing capabilities

### **CSS Classes & Styling**

```css
.chemical-table-wrapper {
  position: relative;
  transition: all 0.2s ease;
}

.websocket-enabled {
  border: 2px solid transparent;
  border-radius: 8px;
  padding: 2px;
}

.locked-by-other {
  border-color: var(--lock-owner-color);
  box-shadow: 0 0 0 1px var(--lock-owner-color);
}

.lock-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  backdrop-filter: blur(2px);
}
```

### **Event Handling**

#### **Focus Events**

```javascript
@focusin="handleTableFocusIn"   // Auto-lock table
@focusout="handleTableFocusOut" // Schedule auto-unlock
```

#### **Access Requests**

```javascript
@click="isLockedByOther ? requestTableAccess() : null"
```

### **WebSocket Integration**

#### **Lock Management**

- Uses existing `useWebSocketStore` for locking
- Field name: `"chemicals"`
- Auto-lock/unlock with 1-second delay
- Integrates with existing access request system

#### **Real-time Updates**

- Uses existing `useChemicalTableWebSocket` composable
- Maintains current real-time sync functionality
- No changes to existing WebSocket message protocol

## Usage Examples

### **ExperimentDetail.vue**

```vue
<CustomChemicalTable
  ref="ChemicalTableRef"
  :init-value="experiment.chemicals"
  :form-id="experiment.form_id"
  :is-editable="!isReadOnly && !isParentClosed"
  :enable-web-socket="true"  <!-- Enables locking -->
/>
```

### **AttemptDetail.vue**

```vue
<CustomChemicalTable
  ref="ChemicalTableRef"
  :init-value="attempt.chemicals"
  :form-id="attempt.form_id"
  :is-editable="!isReadOnly && !isParentClosed"
  :enable-web-socket="true"  <!-- Enables locking -->
/>
```

### **Other Components** (No WebSocket)

```vue
<CustomChemicalTable
  :init-value="chemicals"
  :is-editable="true"
  <!-- No enableWebSocket prop = no locking -->
/>
```

## User Experience

### **For the Editor**

1. Click anywhere in the chemical table
2. Table border turns to user's color
3. "Upravujete" indicator appears
4. Table auto-locks for exclusive editing
5. Focus away → auto-unlock after 1 second

### **For Other Users**

1. See colored border indicating who's editing
2. Table becomes semi-transparent with lock overlay
3. Click "Požádat o přístup" to request access
4. Wait for approval or table to unlock

### **Access Request Flow**

1. User clicks "Požádat o přístup"
2. Lock owner receives notification
3. Owner can approve/deny access
4. Requesting user gets table access or denial notification

## Technical Benefits

1. **Seamless Integration**: Uses existing WebSocket infrastructure
2. **Backward Compatible**: Works with/without WebSocket
3. **Performance Optimized**: Minimal overhead when disabled
4. **User-Friendly**: Clear visual feedback and intuitive interactions
5. **Conflict Prevention**: Prevents simultaneous editing issues

## Testing

### **Manual Testing**

1. Open ExperimentDetail or AttemptDetail in multiple tabs
2. Click in chemical table in one tab
3. Observe locking behavior in other tabs
4. Test access request functionality
5. Verify auto-unlock timing

### **Edge Cases**

- WebSocket disconnection during editing
- Multiple rapid focus/blur events
- Page refresh while holding lock
- Network latency scenarios

## Future Enhancements

- Row-level locking option
- Bulk edit mode
- Lock timeout configuration
- Enhanced conflict resolution
- Mobile touch optimization

## Recent Updates (Latest)

### **Visual Consistency Fix** ✅

- **Chemical Table**: Updated to match FieldLockWrapper styling exactly
- **User Indicator**: Added "Upravujete" indicator to all WebSocket components
- **Consistent CSS**: All components now use identical lock overlay and styling

### **Components Updated:**

1. **CustomChemicalTable.vue**:

   - Fixed styling to match FieldLockWrapper exactly
   - Added field-content wrapper structure
   - Updated CSS classes and overlay design

2. **FieldLockWrapper.vue**:

   - Added user indicator showing "Upravujete" when editing
   - Enhanced visual feedback for active editing

3. **EditorTextareaWebSocket.vue**:
   - Added user indicator showing "Upravujete" when editing
   - Consistent styling with other components

### **Visual Elements Now Consistent:**

- ✅ Lock overlay background and blur
- ✅ Lock indicator pill design
- ✅ Button styling and colors
- ✅ User indicator with account icon
- ✅ Border colors and transitions
- ✅ CSS class naming convention
