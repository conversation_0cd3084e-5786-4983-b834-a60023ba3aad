# Chemical Table New WebSocket API Implementation

## Overview

Updated the chemical table WebSocket integration to support the new API requirements:
- Required `type: 'form_update'` field
- New `action` parameter: `update|add|delete`
- Support for chemicals without IDs (local first/last rows)
- Automatic chemical creation when needed

## Changes Made

### **1. WebSocket Store Updates (`src/stores/websocket.ts`)**

#### **A. Updated Interfaces:**
```typescript
// Added action parameter to FormUpdateMessage
export interface FormUpdateMessage {
  type: 'form_update';
  field: string;
  value: any;
  action?: 'update' | 'add' | 'delete';  // ← NEW
}

// Added action to FormUpdateBroadcastMessage payload
export interface FormUpdateBroadcastMessage {
  type: 'form_update';
  success: boolean;
  user_id?: string;
  payload?: {
    field: string;
    value: any;
    action?: 'update' | 'add' | 'delete';  // ← NEW
  };
}
```

#### **B. Updated Methods:**
```typescript
// Updated sendChemicalTableUpdate with new signature
sendChemicalTableUpdate(
  tablePrimaryKey: number | null,  // ← Can be null for 'add'
  fieldName: string, 
  value: any, 
  action: 'update' | 'add' | 'delete' = 'update'  // ← NEW
): boolean

// Updated other methods to include type and action
updateFormField(fieldName: string, value: any, action: 'update' | 'add' | 'delete' = 'update')
sendRealTimeUpdate(fieldName: string, value: any, action: 'update' | 'add' | 'delete' = 'update')
```

#### **C. Updated Message Format:**
```typescript
// Old format
{
  field: 'chemicals',
  value: { table_primary_key: 123, field: 'name', value: 'Chemical' }
}

// New format
{
  type: 'form_update',        // ← REQUIRED
  field: 'chemicals',
  action: 'update',           // ← NEW: update|add|delete
  value: { table_primary_key: 123, field: 'name', value: 'Chemical' }
}
```

#### **D. Enhanced Message Processing:**
```typescript
// Now handles action parameter and null table_primary_key
const action = message.payload.action || 'update';
if ((chemicalUpdate.table_primary_key || action === 'add') && chemicalUpdate.field) {
  // Process update with action context
}
```

### **2. Chemical Table WebSocket Composable (`src/composables/useChemicalTableWebSocket.ts`)**

#### **A. Updated Method Signatures:**
```typescript
// Updated to support null IDs and actions
sendChemicalFieldUpdate(
  tablePrimaryKey: number | null,  // ← Can be null
  fieldName: string, 
  value: any, 
  action: 'update' | 'add' | 'delete' = 'update'  // ← NEW
)

sendImmediateChemicalFieldUpdate(
  tablePrimaryKey: number | null,  // ← Can be null
  fieldName: string,
  value: any,
  action: 'update' | 'add' | 'delete' = 'update'  // ← NEW
)
```

#### **B. New Methods Added:**
```typescript
// Add a new chemical (without ID)
addChemical(chemicalData: Record<string, any>): void

// Delete a chemical
deleteChemical(chemicalId: number): void
```

#### **C. Enhanced Field Update Retrieval:**
```typescript
// Now includes action in returned data
function getChemicalFieldUpdate(chemical_id: number, field_name: string) {
  return {
    value: update.value.value,
    userId: update.userId,
    timestamp: update.timestamp,
    action: update.value.action || 'update'  // ← NEW
  };
}
```

### **3. Chemical Table Component (`src/components/shared/chemicalTable/CustomChemicalTable.vue`)**

#### **A. Updated WebSocket Calls:**
```typescript
// Now determines action based on chemical ID presence
const action = update.chemicalId ? 'update' : 'add';
chemicalTableWS.sendChemicalFieldUpdate(
  update.chemicalId,    // Can be null for new chemicals
  update.field,
  update.value,
  action               // ← NEW parameter
);
```

## Usage Scenarios

### **1. Regular Field Updates (Existing Chemicals)**
```typescript
// Chemical with ID - uses 'update' action
chemicalTableWS.sendChemicalFieldUpdate(123, 'name', 'New Name', 'update');

// Message sent:
{
  type: 'form_update',
  field: 'chemicals',
  action: 'update',
  value: { table_primary_key: 123, field: 'name', value: 'New Name' }
}
```

### **2. New Chemical Creation (No ID)**
```typescript
// Chemical without ID - uses 'add' action
chemicalTableWS.sendChemicalFieldUpdate(null, 'name', 'New Chemical', 'add');

// Message sent:
{
  type: 'form_update',
  field: 'chemicals',
  action: 'add',
  value: { table_primary_key: null, field: 'name', value: 'New Chemical' }
}
```

### **3. Chemical Deletion**
```typescript
// Delete chemical - uses 'delete' action
chemicalTableWS.deleteChemical(123);

// Message sent:
{
  type: 'form_update',
  field: 'chemicals',
  action: 'delete',
  value: { table_primary_key: 123, field: '', value: null }
}
```

### **4. Bulk Chemical Addition**
```typescript
// Add complete chemical data
chemicalTableWS.addChemical({
  name: 'New Chemical',
  concentration: 100,
  density: 1.2,
  molar_mass: 150
});

// Sends multiple messages with action: 'add' and table_primary_key: null
```

## Default First/Last Row Handling

### **Current Behavior:**
1. **Default rows exist locally** without `chemical_id`
2. **When user edits** → Automatically uses `action: 'add'` and `table_primary_key: null`
3. **Backend creates chemical** → Assigns ID and broadcasts to all users
4. **Other users receive** → Create new row or update existing local row

### **Implementation Flow:**
```typescript
// 1. User types in default row (no chemical_id)
const action = chemical_id ? 'update' : 'add';  // → 'add'
sendChemicalFieldUpdate(null, 'name', 'Chemical Name', 'add');

// 2. Backend processes and assigns ID
// 3. Backend broadcasts with new ID to all users
// 4. All users receive update and create/update local rows
```

## Backward Compatibility

### **✅ Maintained:**
- All existing method calls work (action defaults to 'update')
- Existing chemical updates continue to function
- No breaking changes to component interfaces

### **🆕 Enhanced:**
- Support for chemicals without IDs
- Proper action-based message routing
- Required `type: 'form_update'` field
- Enhanced message processing with action context

## Next Steps

### **Still Needed:**
1. **Update other WebSocket integrations** to include `type: 'form_update'`
2. **Handle incoming 'add' messages** to create new rows in UI
3. **Handle incoming 'delete' messages** to remove rows from UI
4. **Test with multiple users** creating/deleting chemicals simultaneously
5. **Update file operations** to use new message format

### **Files That May Need Updates:**
- `src/components/shared/websocket/FieldLockWrapper.vue`
- `src/components/shared/EditorTextareaWebSocket.vue`
- Any other components using `webSocketStore.sendRealTimeUpdate()`

## Testing

### **Manual Testing Scenarios:**
1. **Create new chemical** in default first/last row
2. **Edit existing chemical** with ID
3. **Delete chemical** and verify removal
4. **Multiple users** editing simultaneously
5. **Action buttons** (calculate/reset) with new chemicals

### **Expected Results:**
- ✅ New chemicals get created with proper IDs
- ✅ All users see real-time updates
- ✅ Default rows work seamlessly
- ✅ Action buttons work with new chemicals
- ✅ No infinite loops or conflicts

The chemical table now fully supports the new WebSocket API with proper action-based messaging and chemical creation!
