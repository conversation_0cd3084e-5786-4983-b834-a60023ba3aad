# Chemical Table New WebSocket API Implementation

## Overview

Updated the chemical table WebSocket integration to support the new API requirements:

- Required `type: 'form_update'` field
- New `action` parameter: `update|add|delete`
- Support for chemicals without IDs (local first/last rows)
- Automatic chemical creation when needed

## Changes Made

### **1. WebSocket Store Updates (`src/stores/websocket.ts`)**

#### **A. Updated Interfaces:**

```typescript
// Added action parameter to FormUpdateMessage
export interface FormUpdateMessage {
  type: 'form_update';
  field: string;
  value: any;
  action?: 'update' | 'add' | 'delete'; // ← NEW
}

// Added action to FormUpdateBroadcastMessage payload
export interface FormUpdateBroadcastMessage {
  type: 'form_update';
  success: boolean;
  user_id?: string;
  payload?: {
    field: string;
    value: any;
    action?: 'update' | 'add' | 'delete'; // ← NEW
  };
}
```

#### **B. Updated Methods:**

```typescript
// Updated sendChemicalTableUpdate with new signature
sendChemicalTableUpdate(
  tablePrimaryKey: number | null,  // ← Can be null for 'add'
  fieldName: string,
  value: any,
  action: 'update' | 'add' | 'delete' = 'update'  // ← NEW
): boolean

// Updated other methods to include type and action
updateFormField(fieldName: string, value: any, action: 'update' | 'add' | 'delete' = 'update')
sendRealTimeUpdate(fieldName: string, value: any, action: 'update' | 'add' | 'delete' = 'update')
```

#### **C. Updated Message Format:**

```typescript
// Old format
{
  field: 'chemicals',
  value: { table_primary_key: 123, field: 'name', value: 'Chemical' }
}

// New format
{
  type: 'form_update',        // ← REQUIRED
  field: 'chemicals',
  action: 'update',           // ← NEW: update|add|delete
  value: { table_primary_key: 123, field: 'name', value: 'Chemical' }
}
```

#### **D. Enhanced Message Processing:**

```typescript
// Now handles action parameter and null table_primary_key
const action = message.payload.action || 'update';
if ((chemicalUpdate.table_primary_key || action === 'add') && chemicalUpdate.field) {
  // Process update with action context
}
```

### **2. Chemical Table WebSocket Composable (`src/composables/useChemicalTableWebSocket.ts`)**

#### **A. Updated Method Signatures:**

```typescript
// Updated to support null IDs and actions
sendChemicalFieldUpdate(
  tablePrimaryKey: number | null,  // ← Can be null
  fieldName: string,
  value: any,
  action: 'update' | 'add' | 'delete' = 'update'  // ← NEW
)

sendImmediateChemicalFieldUpdate(
  tablePrimaryKey: number | null,  // ← Can be null
  fieldName: string,
  value: any,
  action: 'update' | 'add' | 'delete' = 'update'  // ← NEW
)
```

#### **B. New Methods Added:**

```typescript
// Add a new chemical (without ID)
addChemical(chemicalData: Record<string, any>): void

// Delete a chemical
deleteChemical(chemicalId: number): void
```

#### **C. Enhanced Field Update Retrieval:**

```typescript
// Now includes action in returned data
function getChemicalFieldUpdate(chemical_id: number, field_name: string) {
  return {
    value: update.value.value,
    userId: update.userId,
    timestamp: update.timestamp,
    action: update.value.action || 'update' // ← NEW
  };
}
```

### **3. Chemical Table Component (`src/components/shared/chemicalTable/CustomChemicalTable.vue`)**

#### **A. Updated WebSocket Calls:**

```typescript
// Now determines action based on chemical ID presence
const action = update.chemicalId ? 'update' : 'add';
chemicalTableWS.sendChemicalFieldUpdate(
  update.chemicalId, // Can be null for new chemicals
  update.field,
  update.value,
  action // ← NEW parameter
);
```

## Usage Scenarios

### **1. Regular Field Updates (Existing Chemicals)**

```typescript
// Chemical with ID - uses 'update' action
chemicalTableWS.sendChemicalFieldUpdate(123, 'name', 'New Name', 'update');

// Message sent:
{
  type: 'form_update',
  field: 'chemicals',
  action: 'update',
  value: { table_primary_key: 123, field: 'name', value: 'New Name' }
}
```

### **2. New Chemical Creation (No ID)**

```typescript
// Chemical without ID - uses 'add' action
chemicalTableWS.sendChemicalFieldUpdate(null, 'name', 'New Chemical', 'add');

// Message sent:
{
  type: 'form_update',
  field: 'chemicals',
  action: 'add',
  value: { table_primary_key: null, field: 'name', value: 'New Chemical' }
}
```

### **3. Chemical Deletion**

```typescript
// Delete chemical - uses 'delete' action
chemicalTableWS.deleteChemical(123);

// Message sent:
{
  type: 'form_update',
  field: 'chemicals',
  action: 'delete',
  value: { table_primary_key: 123, field: '', value: null }
}
```

### **4. Bulk Chemical Addition**

```typescript
// Add complete chemical data
chemicalTableWS.addChemical({
  name: 'New Chemical',
  concentration: 100,
  density: 1.2,
  molar_mass: 150
});

// Sends multiple messages with action: 'add' and table_primary_key: null
```

## Default First/Last Row Handling

### **Current Behavior:**

1. **Default rows exist locally** without `chemical_id`
2. **When user edits** → Automatically uses `action: 'add'` and `table_primary_key: null`
3. **Backend creates chemical** → Assigns ID and broadcasts to all users
4. **Other users receive** → Create new row or update existing local row

### **Implementation Flow:**

```typescript
// 1. User types in default row (no chemical_id)
const action = chemical_id ? 'update' : 'add'; // → 'add'
sendChemicalFieldUpdate(null, 'name', 'Chemical Name', 'add');

// 2. Backend processes and assigns ID
// 3. Backend broadcasts with new ID to all users
// 4. All users receive update and create/update local rows
```

## Backward Compatibility

### **✅ Maintained:**

- All existing method calls work (action defaults to 'update')
- Existing chemical updates continue to function
- No breaking changes to component interfaces

### **🆕 Enhanced:**

- Support for chemicals without IDs
- Proper action-based message routing
- Required `type: 'form_update'` field
- Enhanced message processing with action context

## ✅ **COMPLETED: Incoming Message Handling**

### **4. Enhanced Chemical Table Component**

#### **A. Added Incoming Update Handlers:**

```typescript
// Main handler for all incoming updates
const handleIncomingChemicalUpdate = (update: any) => {
  const action = update.action || 'update';

  if (action === 'add') {
    handleIncomingChemicalAdd(update);
  } else if (action === 'delete') {
    handleIncomingChemicalDelete(update);
  } else {
    handleIncomingChemicalFieldUpdate(update);
  }
};
```

#### **B. Chemical Addition Handler:**

```typescript
// Handles when other users create new chemicals
const handleIncomingChemicalAdd = (update: any) => {
  const newChemicalId = update.chemical_id;
  const fieldName = update.field_name;
  const fieldValue = update.value;

  // Check if chemical already exists (subsequent field updates)
  const existingChemical = allChemicals.value.find((c) => c.chemical_id === newChemicalId);
  if (existingChemical) {
    // Update existing chemical field
    existingChemical[fieldName] = fieldValue;
    return;
  }

  // Chemical doesn't exist, create new row
  // 1. Try to use empty default rows first
  if (firstChemical.value && isChemicalEmpty(firstChemical.value)) {
    firstChemical.value.chemical_id = newChemicalId;
    firstChemical.value[fieldName] = fieldValue;
    return;
  }

  if (resultChemical.value && isChemicalEmpty(resultChemical.value)) {
    resultChemical.value.chemical_id = newChemicalId;
    resultChemical.value[fieldName] = fieldValue;
    return;
  }

  // 2. Default rows occupied, create new chemical in main array
  const newChemical = createNewChemicalWithId(newChemicalId);
  newChemical[fieldName] = fieldValue;
  chemicals.value.push(newChemical);
};
```

#### **C. Chemical Deletion Handler:**

```typescript
// Handles when other users delete chemicals
const handleIncomingChemicalDelete = (update: any) => {
  const chemicalId = update.chemical_id;

  // Remove from chemicals array
  const chemicalIndex = chemicals.value.findIndex((c) => c.chemical_id === chemicalId);
  if (chemicalIndex !== -1) {
    chemicals.value.splice(chemicalIndex, 1);
    return;
  }

  // Reset first/result chemicals to default state
  if (firstChemical.value?.chemical_id === chemicalId) {
    // Reset to empty default state
  }
};
```

#### **D. Helper Functions:**

```typescript
// Check if a chemical row is empty (no meaningful data)
const isChemicalEmpty = (chemical: any): boolean => {
  return (
    (!chemical.name || chemical.name.trim() === '') &&
    (!chemical.concentration || chemical.concentration === 100) &&
    (!chemical.density || chemical.density === 0) &&
    (!chemical.molar_mass || chemical.molar_mass === 0) &&
    (!chemical.grams || chemical.grams === 0) &&
    (!chemical.moles || chemical.moles === 0) &&
    (!chemical.volume_ml || chemical.volume_ml === 0) &&
    (!chemical.notes || chemical.notes.trim() === '')
  );
};

// Create a new chemical with given ID and default values
const createNewChemicalWithId = (chemicalId: number): any => {
  return {
    chemical_id: chemicalId,
    name: '',
    concentration: 100,
    density: 0,
    molar_mass: 0,
    grams: 0,
    moles: 0,
    volume_ml: 0,
    equivalent: 1,
    notes: '',
    type: 'chemical',
    options: {
      /* all show flags true */
    }
  };
};
```

#### **E. Enhanced Update Processing:**

```typescript
// Now processes different action types
updates.forEach((update) => {
  if (update && update.userId !== chemicalTableWS.webSocketStore?.currentUserId) {
    handleIncomingChemicalUpdate(update); // ← NEW
  }
});
```

## ✅ **COMPLETED: Other WebSocket Integrations**

### **5. Verified Backward Compatibility**

#### **A. FieldLockWrapper.vue:**

- ✅ Uses `useRealTimeFieldSync` composable
- ✅ Calls `webSocketStore.sendRealTimeUpdate()` with default parameters
- ✅ Automatically includes `type: 'form_update'` and `action: 'update'`

#### **B. EditorTextareaWebSocket.vue:**

- ✅ Calls `webSocketStore.sendRealTimeUpdate()` directly
- ✅ Automatically includes `type: 'form_update'` and `action: 'update'`

#### **C. useRealTimeFieldSync.ts:**

- ✅ Calls `webSocketStore.sendRealTimeUpdate()`
- ✅ All existing functionality preserved

### **All Existing Components Work Without Changes!**

## Next Steps

### **🧪 Ready for Testing:**

1. **Test default first/last row behavior** with multiple users
2. **Test new chemical creation** across multiple browsers
3. **Test chemical deletion** and UI updates
4. **Test action buttons** (calculate/reset) with new chemicals
5. **Verify all existing WebSocket features** still work

### **🔧 Future Enhancements:**

1. **Add chemical creation UI** (add row button)
2. **Add chemical deletion UI** (delete row button)
3. **Enhanced error handling** for failed operations
4. **Optimistic UI updates** for better UX
5. **Conflict resolution** for simultaneous edits

## Testing

### **Manual Testing Scenarios:**

1. **Create new chemical** in default first/last row
2. **Edit existing chemical** with ID
3. **Delete chemical** and verify removal
4. **Multiple users** editing simultaneously
5. **Action buttons** (calculate/reset) with new chemicals

### **Expected Results:**

- ✅ New chemicals get created with proper IDs
- ✅ All users see real-time updates
- ✅ Default rows work seamlessly
- ✅ Action buttons work with new chemicals
- ✅ No infinite loops or conflicts

The chemical table now fully supports the new WebSocket API with proper action-based messaging and chemical creation!
