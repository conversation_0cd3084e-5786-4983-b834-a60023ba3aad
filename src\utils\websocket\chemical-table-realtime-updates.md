# Chemical Table Real-Time WebSocket Updates - Watcher Approach

## Overview

This implementation uses a **watcher-based approach** to detect all chemical table changes and send real-time WebSocket updates. Instead of relying on focus/blur events, we monitor the `combinedChemicals` data directly.

## Key Benefits

### ✅ **Comprehensive Coverage**
- **All changes detected**: Typing, calculations, resets, additions, deletions
- **No missed updates**: Watcher catches everything that modifies the data
- **Action button support**: Calculate/reset operations automatically sync

### ✅ **Reliable & Simple**
- **No focus/blur complexity**: Single watcher handles all scenarios
- **State comparison**: Compares previous vs current state
- **Field-level precision**: Only sends updates for changed fields

### ✅ **Real-Time Performance**
- **Immediate updates**: Changes appear instantly in other users' browsers
- **Efficient**: Only sends what actually changed
- **Debounced**: Uses existing WebSocket debouncing (300ms)

## Implementation Details

### **1. Watcher Setup (CustomChemicalTable.vue)**

```javascript
// Store previous state for comparison
const previousChemicalsState = ref<any[]>([]);

watch(
  combinedChemicals,
  (newVal, oldVal) => {
    // WebSocket updates for chemical table changes
    if (chemicalTableWS && props.enableWebSocket && newVal && Array.isArray(newVal)) {
      // Skip initial load
      if (!previousChemicalsState.value || previousChemicalsState.value.length === 0) {
        previousChemicalsState.value = JSON.parse(JSON.stringify(newVal));
        return;
      }
      
      // Compare each chemical for changes
      newVal.forEach((newChemical: any) => {
        if (!newChemical.chemical_id) return;
        
        const previousChemical = previousChemicalsState.value.find(
          (prev: any) => prev.chemical_id === newChemical.chemical_id
        );
        
        // Compare each field for changes
        const fieldsToWatch = ['name', 'concentration', 'density', 'molar_mass', 'grams', 'moles', 'volume_ml', 'equivalent', 'notes'];
        
        fieldsToWatch.forEach(field => {
          const newValue = newChemical[field];
          const oldValue = previousChemical[field];
          
          if (newValue !== oldValue) {
            // Send WebSocket update for the changed field
            chemicalTableWS.sendChemicalFieldUpdate(
              newChemical.chemical_id,
              field,
              newValue
            );
          }
        });
      });
      
      // Update previous state for next comparison
      previousChemicalsState.value = JSON.parse(JSON.stringify(newVal));
    }
  },
  { deep: true }
);
```

### **2. Data Structure Monitored**

The watcher monitors the `combinedChemicals` computed property which contains:

```javascript
[
  {
    "chemical_id": 26,
    "name": "Chemical Name",
    "density": 4,
    "molar_mass": 5,
    "notes": "Notes",
    "grams": 6,
    "moles": 7,
    "equivalent": 0,
    "volume_ml": 0,
    "concentration": 25,
    "type": "chemical"
    // options object ignored for WebSocket
  }
]
```

### **3. Change Detection Logic**

#### **Field Comparison:**
```javascript
fieldsToWatch.forEach(field => {
  const newValue = newChemical[field];
  const oldValue = previousChemical[field];
  
  if (newValue !== oldValue) {
    console.log('🔄 Chemical field changed via watcher:', {
      chemicalId: newChemical.chemical_id,
      field,
      from: oldValue,
      to: newValue
    });
    
    chemicalTableWS.sendChemicalFieldUpdate(
      newChemical.chemical_id,
      field,
      newValue
    );
  }
});
```

#### **New Chemical Detection:**
```javascript
if (!previousChemical) {
  // New chemical added - send all fields
  const fieldsToSend = ['name', 'concentration', 'density', 'molar_mass', 'grams', 'moles', 'volume_ml', 'equivalent', 'notes'];
  
  fieldsToSend.forEach(field => {
    if (newChemical[field] !== undefined && newChemical[field] !== null) {
      chemicalTableWS.sendChemicalFieldUpdate(
        newChemical.chemical_id,
        field,
        newChemical[field]
      );
    }
  });
}
```

## Scenarios Covered

### **✅ Real-Time Typing**
1. User types in any field
2. `combinedChemicals` updates
3. Watcher detects field change
4. WebSocket update sent immediately
5. Other users see typing in real-time

### **✅ Calculate Button**
1. User clicks "Dopočítat hodnoty"
2. Calculation functions modify multiple fields
3. `combinedChemicals` updates with new calculated values
4. Watcher detects all changed fields (e.g., grams, moles, volume_ml, equivalent)
5. Multiple WebSocket updates sent for each changed field
6. Other users see all calculated results instantly

### **✅ Reset Button**
1. User clicks "Vymazat dopočítané hodnoty"
2. Reset function sets fields to 0
3. `combinedChemicals` updates with reset values
4. Watcher detects all changed fields
5. Multiple WebSocket updates sent for each reset field
6. Other users see all reset values instantly

### **✅ New Chemical Added**
1. User adds new chemical row
2. New chemical appears in `combinedChemicals`
3. Watcher detects new chemical (no previous state)
4. All fields sent via WebSocket
5. Other users see new chemical with all data

## Removed Complexity

### **❌ No More Focus/Blur Events**
- Removed `validateWebsocket` function from ItemCell.vue
- Removed WebSocket code from `validateInput` and `validateInputComboBox`
- Simplified event handling

### **❌ No More Manual WebSocket Calls**
- Calculate functions no longer need manual WebSocket updates
- Reset functions no longer need manual WebSocket updates
- All handled automatically by watcher

## Console Logging

```javascript
// Real-time field changes
🔄 Chemical field changed via watcher: {
  chemicalId: 26,
  field: 'concentration',
  from: 10,
  to: 15
}

// New chemicals
🆕 New chemical detected: 27

// Data monitoring
combinedChemicals [Array of chemicals with all data]
```

## Performance & Efficiency

### **✅ Optimized Updates**
- Only sends fields that actually changed
- Deep comparison prevents unnecessary updates
- Skips initial load to prevent spam

### **✅ State Management**
- Stores previous state for accurate comparison
- Deep clones prevent reference issues
- Efficient field-by-field comparison

### **✅ Conditional Execution**
- Only runs when WebSocket is enabled
- Graceful fallback for non-WebSocket usage
- No impact on existing functionality

## Result

The chemical table now provides **seamless real-time collaboration** with:
- ⚡ **Instant typing updates** as users type
- 🧮 **Automatic calculation sync** when buttons are clicked
- 🧹 **Automatic reset sync** when values are cleared
- 🆕 **New chemical sync** when rows are added
- 🔄 **Complete coverage** of all possible changes

All achieved with a **single, simple watcher** that monitors the data directly!
