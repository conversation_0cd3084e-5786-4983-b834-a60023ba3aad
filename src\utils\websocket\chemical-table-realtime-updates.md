# Chemical Table Real-Time WebSocket Updates - Watcher Approach

## Overview

This implementation uses a **watcher-based approach** to detect all chemical table changes and send real-time WebSocket updates. Instead of relying on focus/blur events, we monitor the `combinedChemicals` data directly.

## Key Benefits

### ✅ **Comprehensive Coverage**

- **All changes detected**: Typing, calculations, resets, additions, deletions
- **No missed updates**: Watcher catches everything that modifies the data
- **Action button support**: Calculate/reset operations automatically sync

### ✅ **Reliable & Simple**

- **No focus/blur complexity**: Single watcher handles all scenarios
- **State comparison**: Compares previous vs current state
- **Field-level precision**: Only sends updates for changed fields

### ✅ **Real-Time Performance**

- **Immediate updates**: Changes appear instantly in other users' browsers
- **Efficient**: Only sends what actually changed
- **Debounced**: Uses existing WebSocket debouncing (300ms)

## Implementation Details

### **1. Watcher Setup (CustomChemicalTable.vue)**

```javascript
// Store previous state for comparison
const previousChemicalsState = ref<any[]>([]);

watch(
  combinedChemicals,
  (newVal, oldVal) => {
    // WebSocket updates for chemical table changes
    if (chemicalTableWS && props.enableWebSocket && newVal && Array.isArray(newVal)) {
      // Skip initial load
      if (!previousChemicalsState.value || previousChemicalsState.value.length === 0) {
        previousChemicalsState.value = JSON.parse(JSON.stringify(newVal));
        return;
      }

      // Compare each chemical for changes
      newVal.forEach((newChemical: any) => {
        if (!newChemical.chemical_id) return;

        const previousChemical = previousChemicalsState.value.find(
          (prev: any) => prev.chemical_id === newChemical.chemical_id
        );

        // Compare each field for changes
        const fieldsToWatch = ['name', 'concentration', 'density', 'molar_mass', 'grams', 'moles', 'volume_ml', 'equivalent', 'notes'];

        fieldsToWatch.forEach(field => {
          const newValue = newChemical[field];
          const oldValue = previousChemical[field];

          if (newValue !== oldValue) {
            // Send WebSocket update for the changed field
            chemicalTableWS.sendChemicalFieldUpdate(
              newChemical.chemical_id,
              field,
              newValue
            );
          }
        });
      });

      // Update previous state for next comparison
      previousChemicalsState.value = JSON.parse(JSON.stringify(newVal));
    }
  },
  { deep: true }
);
```

### **2. Data Structure Monitored**

The watcher monitors the `combinedChemicals` computed property which contains:

```javascript
[
  {
    chemical_id: 26,
    name: 'Chemical Name',
    density: 4,
    molar_mass: 5,
    notes: 'Notes',
    grams: 6,
    moles: 7,
    equivalent: 0,
    volume_ml: 0,
    concentration: 25,
    type: 'chemical'
    // options object ignored for WebSocket
  }
];
```

### **3. Change Detection Logic**

#### **Field Comparison:**

```javascript
fieldsToWatch.forEach((field) => {
  const newValue = newChemical[field];
  const oldValue = previousChemical[field];

  if (newValue !== oldValue) {
    console.log('🔄 Chemical field changed via watcher:', {
      chemicalId: newChemical.chemical_id,
      field,
      from: oldValue,
      to: newValue
    });

    chemicalTableWS.sendChemicalFieldUpdate(newChemical.chemical_id, field, newValue);
  }
});
```

#### **New Chemical Detection:**

```javascript
if (!previousChemical) {
  // New chemical added - send all fields
  const fieldsToSend = [
    'name',
    'concentration',
    'density',
    'molar_mass',
    'grams',
    'moles',
    'volume_ml',
    'equivalent',
    'notes'
  ];

  fieldsToSend.forEach((field) => {
    if (newChemical[field] !== undefined && newChemical[field] !== null) {
      chemicalTableWS.sendChemicalFieldUpdate(newChemical.chemical_id, field, newChemical[field]);
    }
  });
}
```

## Scenarios Covered

### **✅ Real-Time Typing**

1. User types in any field
2. `combinedChemicals` updates
3. Watcher detects field change
4. WebSocket update sent immediately
5. Other users see typing in real-time

### **✅ Calculate Button**

1. User clicks "Dopočítat hodnoty"
2. Calculation functions modify multiple fields
3. `combinedChemicals` updates with new calculated values
4. Watcher detects all changed fields (e.g., grams, moles, volume_ml, equivalent)
5. Multiple WebSocket updates sent for each changed field
6. Other users see all calculated results instantly

### **✅ Reset Button**

1. User clicks "Vymazat dopočítané hodnoty"
2. Reset function sets fields to 0
3. `combinedChemicals` updates with reset values
4. Watcher detects all changed fields
5. Multiple WebSocket updates sent for each reset field
6. Other users see all reset values instantly

### **✅ New Chemical Added**

1. User adds new chemical row
2. New chemical appears in `combinedChemicals`
3. Watcher detects new chemical (no previous state)
4. All fields sent via WebSocket
5. Other users see new chemical with all data

## Removed Complexity

### **❌ No More Focus/Blur Events**

- Removed `validateWebsocket` function from ItemCell.vue
- Removed WebSocket code from `validateInput` and `validateInputComboBox`
- Simplified event handling

### **❌ No More Manual WebSocket Calls**

- Calculate functions no longer need manual WebSocket updates
- Reset functions no longer need manual WebSocket updates
- All handled automatically by watcher

## Console Logging

```javascript
// Real-time field changes
🔄 Chemical field changed via watcher: {
  chemicalId: 26,
  field: 'concentration',
  from: 10,
  to: 15
}

// New chemicals
🆕 New chemical detected: 27

// Data monitoring
combinedChemicals [Array of chemicals with all data]
```

## Performance & Efficiency

### **✅ Optimized Updates**

- Only sends fields that actually changed
- Deep comparison prevents unnecessary updates
- Skips initial load to prevent spam

### **✅ State Management**

- Stores previous state for accurate comparison
- Deep clones prevent reference issues
- Efficient field-by-field comparison

### **✅ Conditional Execution**

- Only runs when WebSocket is enabled
- Graceful fallback for non-WebSocket usage
- No impact on existing functionality

## Enhanced with Debounced Batch Updates

### **🚀 Problem Solved: Fast Typing & Action Buttons**

**Issue:** When typing fast or clicking action buttons (vymazat/calculate), multiple fields change simultaneously but only some WebSocket updates were getting through.

**Solution:** Implemented a **debounced batch update system** that:

1. **Queues all updates** in a short time window
2. **Processes them as separate updates** with small delays
3. **Handles rapid changes** reliably

### **🔧 Implementation Details**

#### **Debounced Queue System:**

```javascript
// Queue system for batching updates
const pendingUpdates = ref<Map<string, any>>(new Map());
const updateTimeout = ref<number | null>(null);
const DEBOUNCE_DELAY = 300; // ms

// Queue function - collects updates
const queueWebSocketUpdate = (chemicalId: number, field: string, value: any) => {
  const updateKey = `${chemicalId}-${field}`;
  pendingUpdates.value.set(updateKey, {
    chemicalId, field, value, timestamp: Date.now()
  });

  // Reset debounce timer
  if (updateTimeout.value) {
    window.clearTimeout(updateTimeout.value);
  }

  // Process after delay
  updateTimeout.value = window.setTimeout(() => {
    processPendingUpdates();
  }, DEBOUNCE_DELAY);
};

// Process function - sends all queued updates
const processPendingUpdates = () => {
  const updates = Array.from(pendingUpdates.value.values());

  // Send each update with 50ms delay between them
  updates.forEach((update, index) => {
    setTimeout(() => {
      chemicalTableWS.sendChemicalFieldUpdate(
        update.chemicalId, update.field, update.value
      );
    }, index * 50);
  });

  pendingUpdates.value.clear();
};
```

#### **Watcher Integration:**

```javascript
// Instead of direct WebSocket calls:
// chemicalTableWS.sendChemicalFieldUpdate(chemicalId, field, value);

// Now uses queue:
queueWebSocketUpdate(chemicalId, field, value);
```

### **🎯 How It Works**

#### **Fast Typing Scenario:**

1. User types "12345" quickly in concentration field
2. Each keystroke triggers watcher → `queueWebSocketUpdate(26, 'concentration', '1')`
3. Next keystroke resets timer → `queueWebSocketUpdate(26, 'concentration', '12')`
4. Continues until typing stops
5. After 300ms delay → `processPendingUpdates()` sends final value: `'12345'`
6. **Result:** Only final value sent, no spam

#### **"Vymazat" Button Scenario:**

1. User clicks "Vymazat dopočítané hodnoty"
2. Multiple fields change to 0: grams, moles, volume_ml, equivalent
3. Watcher detects all changes → 4x `queueWebSocketUpdate()` calls
4. All updates queued in same batch
5. After 300ms delay → `processPendingUpdates()` sends all 4 updates
6. Each update sent with 50ms delay: grams→0, moles→0, volume_ml→0, equivalent→0
7. **Result:** All 4 separate updates sent reliably

#### **"Dopočítat" Button Scenario:**

1. User clicks "Dopočítat hodnoty"
2. Calculation changes multiple fields: grams→45.2, moles→0.25, volume_ml→150.5
3. Watcher detects all changes → 3x `queueWebSocketUpdate()` calls
4. All updates queued in same batch
5. After 300ms delay → All 3 updates sent with delays
6. **Result:** All calculated values sync perfectly

### **📊 Console Output Examples**

```javascript
// Fast typing - only final value sent
📝 Queued update: { chemicalId: 26, field: 'concentration', value: '1', queueSize: 1 }
📝 Queued update: { chemicalId: 26, field: 'concentration', value: '12', queueSize: 1 }
📝 Queued update: { chemicalId: 26, field: 'concentration', value: '123', queueSize: 1 }
🚀 Processing batch updates: 1 updates
📤 Sending WebSocket update: { chemicalId: 26, field: 'concentration', value: '123' }

// Vymazat button - all 4 fields sent
📝 Queued update: { chemicalId: 26, field: 'grams', value: 0, queueSize: 1 }
📝 Queued update: { chemicalId: 26, field: 'moles', value: 0, queueSize: 2 }
📝 Queued update: { chemicalId: 26, field: 'volume_ml', value: 0, queueSize: 3 }
📝 Queued update: { chemicalId: 26, field: 'equivalent', value: 0, queueSize: 4 }
🚀 Processing batch updates: 4 updates
📤 Sending WebSocket update: { chemicalId: 26, field: 'grams', value: 0 }
📤 Sending WebSocket update: { chemicalId: 26, field: 'moles', value: 0 }
📤 Sending WebSocket update: { chemicalId: 26, field: 'volume_ml', value: 0 }
📤 Sending WebSocket update: { chemicalId: 26, field: 'equivalent', value: 0 }
```

### **⚡ Performance Benefits**

- **Reduced WebSocket spam**: Fast typing sends only final value
- **Reliable batch updates**: Action buttons send all changes
- **Staggered delivery**: 50ms delays prevent WebSocket conflicts
- **Memory efficient**: Queue clears after processing
- **Cleanup on unmount**: No memory leaks

## Result

The chemical table now provides **seamless real-time collaboration** with:

- ⚡ **Reliable fast typing** - no missed keystrokes
- 🧮 **Complete calculation sync** - all calculated values sent
- 🧹 **Complete reset sync** - all reset values sent
- 🆕 **New chemical sync** - all fields sent
- 🔄 **Batch processing** - efficient and reliable
- 🎯 **No WebSocket conflicts** - staggered delivery

**Perfect solution for fast typing and action buttons!**
