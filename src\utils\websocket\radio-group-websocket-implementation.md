# Radio Group WebSocket Implementation

## Overview

This implementation provides real-time collaborative editing for Vue.js radio button groups using WebSocket technology. Unlike regular form fields, radio groups have special requirements:

- **No visual wrappers**: No lock overlays, borders, or visual indicators
- **Invisible locking**: Functional locking without UI changes
- **Boolean synchronization**: Real-time sync of true/false values
- **Auto-unlock**: 1-second delay after selection

## Files Created/Modified

### New Files
- `src/composables/useRadioGroupWebSocket.ts` - Main composable for radio group WebSocket integration
- `src/views/test/RadioGroupWebSocketTest.vue` - Test component for verification

### Modified Files
- `src/views/pages/dashboard/projects/project/department/chapter/investigation/InvestigationDetail.vue` - Implementation example

## Implementation Details

### 1. Composable Structure

```typescript
export function useRadioGroupWebSocket(options: RadioGroupWebSocketOptions) {
  // Auto-lock on selection, unlock after 1 second
  // Real-time synchronization with other users
  // No visual indicators
}
```

### 2. Key Features

#### Invisible Locking
- Field locks when user selects a radio button
- No visual changes to the UI
- Auto-unlocks after 1 second
- Prevents conflicts between users

#### Real-time Synchronization
- Boolean values sync immediately across all connected users
- Debounced updates to prevent network spam
- Echo prevention to avoid infinite loops

#### Type Safety
- Handles `boolean | null` values from v-radio-group
- Proper TypeScript interfaces
- Computed ref support for disabled state

### 3. Usage Pattern

```vue
<script setup>
// 1. Create composable instance
const radioWebSocket = useRadioGroupWebSocket({
  fieldName: 'impact_on_quality',
  enabled: true,
  disabled: computed(() => isReadOnly.value)
});

// 2. Set up remote update watcher after data is loaded
const setupHandlers = () => {
  radioWebSocket.watchRemoteUpdates(
    computed({
      get: () => updateData.value?.impact_on_quality,
      set: (value) => { updateData.value.impact_on_quality = value; }
    })
  );
};

onMounted(() => {
  // Call after data is loaded
  setupHandlers();
});
</script>

<template>
  <!-- 3. Add event handler to radio group -->
  <v-radio-group
    v-model="updateData.impact_on_quality"
    @update:model-value="radioWebSocket.onRadioChange"
  >
    <v-radio label="Yes" :value="true"></v-radio>
    <v-radio label="No" :value="false"></v-radio>
  </v-radio-group>
</template>
```

### 4. WebSocket Message Flow

#### User Selection
1. User clicks radio button
2. `onRadioChange` handler triggered
3. Field auto-locks (invisible)
4. Real-time update sent via WebSocket
5. Auto-unlock scheduled (1 second)

#### Remote Update
1. WebSocket receives `form_update` message
2. `watchRemoteUpdates` processes the update
3. Model value updated programmatically
4. UI reflects the change
5. Echo prevention prevents loops

### 5. Integration with Existing System

The radio group implementation integrates seamlessly with the existing WebSocket infrastructure:

- Uses same `useWebSocketStore` for connection management
- Follows same message protocol (`form_update`)
- Supports same field locking mechanism
- Compatible with user presence system

## Testing

Use the test component at `src/views/test/RadioGroupWebSocketTest.vue`:

1. Open multiple browser tabs
2. Navigate to the test page
3. Select different radio values in each tab
4. Observe real-time synchronization
5. Check browser console for WebSocket messages

## Benefits

1. **Seamless UX**: No visual disruption for users
2. **Conflict Prevention**: Invisible locking prevents data conflicts
3. **Real-time Sync**: Immediate updates across all users
4. **Type Safe**: Full TypeScript support
5. **Reusable**: Easy to implement in other forms

## Future Enhancements

- Support for multi-value radio groups
- Custom unlock delays
- Integration with form validation
- Batch radio group updates
- Advanced conflict resolution

## Troubleshooting

### Common Issues

1. **Values not syncing**: Check WebSocket connection status
2. **Type errors**: Ensure proper boolean/null handling
3. **Echo loops**: Verify `isReceivingRemoteUpdate` flag usage
4. **Lock conflicts**: Check field name consistency

### Debug Tips

- Enable WebSocket console logging
- Use browser dev tools to monitor WebSocket messages
- Check `fieldUpdates` in WebSocket store
- Verify `updateData` model binding
