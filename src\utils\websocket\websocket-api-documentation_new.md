# WebSocket API Documentation

## Overview
This document describes the WebSocket API endpoints and message formats for the Farmak application. The WebSocket API provides real-time communication for form collaboration and notifications.

## Authentication
All WebSocket connections require authentication via query parameter:
- **Parameter**: `token`
- **Type**: JWT token
- **Example**: `ws://localhost:8000/ws/form/123/456?token=your_jwt_token`

## Base URL
- **Development**: `ws://localhost:8000/ws`
- **Production**: `wss://your-domain.com/ws`

---

## Form WebSocket API

### Connection
**Endpoint**: `/ws/form/{form_id}/{user_id}`

**Parameters**:
- `form_id` (integer): ID of the form to collaborate on
- `user_id` (integer): ID of the connected user

### Message Types

#### 1. Client → Server Messages

##### Lock Field
Lock a specific field for editing.

```json
{
  "type": "lock_field",
  "field_name": "string"
}
```

**Response**:
```json
{
  "type": "lock_response",
  "field_name": "string",
  "success": boolean,
  "message": "string"
}
```

##### Unlock Field
Unlock a previously locked field.

```json
{
  "type": "unlock_field",
  "field_name": "string"
}
```

**Response**:
```json
{
  "type": "unlock_response",
  "field_name": "string",
  "success": boolean,
  "message": "string"
}
```

##### Form Update
Update a form field value.

```json
{
  "type": "form_update",
  "field": "string",
  "value": "any",
  "action": "string" // optional, defaults to "update"
}
```

**Response** (broadcast to all users):
```json
{
  "type": "form_update",
  "payload": {
    "field": "string",
    "value": "any",
    "action": "string"
  },
  "success": boolean
}
```

**Error Response**:
```json
{
  "type": "update_error",
  "field": "string",
  "message": "string"
}
```

##### Get Locked Fields
Request current locked fields information.

```json
{
  "type": "get_locked_fields"
}
```

**Response**:
```json
{
  "type": "locked_fields_response",
  "locked_fields": {
    "field_name": {
      "user_id": number,
      "timestamp": number
    }
  }
}
```

##### Request Field Access
Request access to a locked field from another user.

```json
{
  "type": "request_field_access",
  "field_name": "string",
  "message": "string" // optional
}
```

**Response**:
```json
{
  "type": "access_request_response",
  "field_name": "string",
  "success": boolean,
  "message": "string"
}
```

##### Respond to Access Request
Respond to a field access request from another user.

```json
{
  "type": "respond_to_access_request",
  "field_name": "string",
  "requester_id": number,
  "approved": boolean,
  "response_message": "string" // optional
}
```

**Response**:
```json
{
  "type": "access_response_confirmation",
  "field_name": "string",
  "requester_id": number,
  "approved": boolean,
  "success": boolean,
  "message": "string"
}
```

##### Get Pending Requests
Get pending access requests for a field.

```json
{
  "type": "get_pending_requests",
  "field_name": "string" // optional
}
```

**Response**:
```json
{
  "type": "pending_requests_response",
  "requests": [
    {
      "requester_id": number,
      "field_name": "string",
      "message": "string",
      "timestamp": number
    }
  ]
}
```

#### 2. Server → Client Messages

##### Current Locks
Sent automatically when a user connects to inform about currently locked fields.

```json
{
  "type": "current_locks",
  "locked_fields": {
    "field_name": {
      "user_id": number,
      "timestamp": number
    }
  }
}
```

##### User Presence
Broadcast when users connect/disconnect.

```json
{
  "type": "user_presence",
  "users": [
    {
      "user_id": number,
      "connected_at": number
    }
  ]
}
```

##### Field Lock Broadcast
Broadcast when a field is locked/unlocked by any user.

```json
{
  "type": "field_lock_broadcast",
  "field_name": "string",
  "user_id": number,
  "action": "locked" | "unlocked"
}
```

##### Access Request Notification
Sent to field owner when someone requests access.

```json
{
  "type": "access_request_notification",
  "field_name": "string",
  "requester_id": number,
  "message": "string",
  "timestamp": number
}
```

##### Access Response Notification
Sent to requester when access request is responded to.

```json
{
  "type": "access_response_notification",
  "field_name": "string",
  "owner_id": number,
  "approved": boolean,
  "response_message": "string",
  "timestamp": number
}
```

##### Error Message
General error message format.

```json
{
  "type": "error",
  "message": "string",
  "code": "string" // optional error code
}
```

---

## Notification WebSocket API

### Connection
**Endpoint**: `/ws/notification/{user_id}`

**Parameters**:
- `user_id` (integer): ID of the user receiving notifications

### Message Types

#### Server → Client Messages

##### Notification Broadcast
Sent when there are unread notifications for the user.

```json
{
  "type": "unread_notifications",
  "data": boolean
}
```

---

## Connection Management

### Connection States
- **CONNECTING**: WebSocket is establishing connection
- **OPEN**: Connection is active and ready for communication
- **CLOSING**: Connection is being closed
- **CLOSED**: Connection has been terminated

### Reconnection Strategy
Implement exponential backoff for reconnection:
1. Initial delay: 1 second
2. Maximum delay: 30 seconds
3. Backoff factor: 2
4. Maximum attempts: 5

### Error Handling
Handle the following WebSocket error codes:
- **1000**: Normal closure
- **1001**: Going away
- **1002**: Protocol error
- **1003**: Unsupported data
- **1006**: Abnormal closure
- **1008**: Policy violation (authentication failure)
- **1011**: Internal server error

---

## Usage Examples

### TypeScript Interface Definitions

```typescript
// Base message interface
interface WebSocketMessage {
  type: string;
  [key: string]: any;
}

// Form WebSocket messages
interface LockFieldMessage extends WebSocketMessage {
  type: 'lock_field';
  field_name: string;
}

interface FormUpdateMessage extends WebSocketMessage {
  type: 'form_update';
  field: string;
  value: any;
  action?: string;
}

interface LockResponse extends WebSocketMessage {
  type: 'lock_response';
  field_name: string;
  success: boolean;
  message: string;
}

// Notification WebSocket messages
interface NotificationBroadcast extends WebSocketMessage {
  type: 'notification_broadcast';
  has_unread: boolean;
  notifications: Notification[];
}

interface Notification {
  id: number;
  title: string;
  message: string;
  type: string;
  read: boolean;
  created_at: string;
  updated_at: string;
}
```

### JavaScript Connection Example

```javascript
class FormWebSocket {
  constructor(formId, userId, token) {
    this.formId = formId;
    this.userId = userId;
    this.token = token;
    this.ws = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
  }

  connect() {
    const url = `ws://localhost:8000/ws/form/${this.formId}/${this.userId}?token=${this.token}`;
    this.ws = new WebSocket(url);

    this.ws.onopen = () => {
      console.log('Connected to form WebSocket');
      this.reconnectAttempts = 0;
    };

    this.ws.onmessage = (event) => {
      const message = JSON.parse(event.data);
      this.handleMessage(message);
    };

    this.ws.onclose = (event) => {
      console.log('WebSocket closed:', event.code, event.reason);
      if (event.code !== 1000) {
        this.reconnect();
      }
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
  }

  send(message) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    }
  }

  lockField(fieldName) {
    this.send({
      type: 'lock_field',
      field_name: fieldName
    });
  }

  updateField(field, value, action = 'update') {
    this.send({
      type: 'form_update',
      field: field,
      value: value,
      action: action
    });
  }

  handleMessage(message) {
    switch (message.type) {
      case 'lock_response':
        this.onLockResponse(message);
        break;
      case 'form_update':
        this.onFormUpdate(message);
        break;
      case 'current_locks':
        this.onCurrentLocks(message);
        break;
      // Add more message handlers as needed
    }
  }

  reconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
      setTimeout(() => {
        this.reconnectAttempts++;
        this.connect();
      }, delay);
    }
  }
}
```

---

## Best Practices

1. **Message Validation**: Always validate incoming messages on the client side
2. **Error Handling**: Implement proper error handling for all message types
3. **Reconnection**: Use exponential backoff for reconnection attempts
4. **State Management**: Keep track of connection state and handle accordingly
5. **Performance**: Debounce frequent updates to avoid overwhelming the server
6. **Security**: Never trust client-side data; always validate on the server

---

## Testing

### Manual Testing
Use the provided test file at `/temp/websocket_test.html` for manual testing of WebSocket connections.

### Automated Testing
Consider using tools like:
- **ws** library for Node.js testing
- **WebSocket testing frameworks** for comprehensive testing
- **Postman** for WebSocket API testing

---

*Last updated: July 8, 2025*
