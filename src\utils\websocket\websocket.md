## WebSocket Message Types

### Client → Server Messages

#### 1. Lock Field

```json
{
  "type": "lock_field",
  "field_name": "field_to_lock"
}
```

#### 2. Unlock Field

```json
{
  "type": "unlock_field",
  "field_name": "field_to_unlock"
}
```

#### 3. Request Field Access

```json
{
  "type": "request_field_access",
  "field_name": "locked_field_name",
  "message": "Optional message to field owner"
}
```

#### 4. Respond to Access Request

```json
{
  "type": "respond_to_access_request",
  "field_name": "field_name",
  "requester_id": "user_requesting_access",
  "approved": true,
  "response_message": "Optional response message"
}
```

#### 5. Get Locked Fields

```json
{
  "type": "get_locked_fields"
}
```

#### 6. Get Pending Requests

```json
{
  "type": "get_pending_requests",
  "field_name": "optional_specific_field"
}
```

#### 7. Form Update (with lock validation)

```json
{
  "type": "form_update",
  "field": "field_name",
  "value": "new_value"
}
```

### Server → Client Messages

#### 1. Lock Response

```json
{
  "type": "lock_response",
  "field_name": "field_name",
  "success": true,
  "message": "Field locked successfully"
}
```

#### 2. Unlock Response

```json
{
  "type": "unlock_response",
  "field_name": "field_name",
  "success": true,
  "message": "Field unlocked successfully"
}
```

#### 3. Field Status Broadcast

```json
{
  "type": "field_status",
  "field_name": "field_name",
  "user_id": "user_who_locked",
  "status": "locked|unlocked|auto_unlocked",
  "timestamp": 1625097600.0
}
```

#### 4. Access Request Notification

```json
{
  "type": "access_request",
  "field_name": "field_name",
  "requester_id": "requesting_user_id",
  "requester_message": "Message from requester",
  "timestamp": 1625097600.0
}
```

#### 5. Access Response

```json
{
  "type": "access_response",
  "field_name": "field_name",
  "approved": true,
  "response_message": "Response from field owner",
  "timestamp": 1625097600.0
}
```

#### 6. Current Locks

```json
{
  "type": "current_locks",
  "locked_fields": {
    "field_name": {
      "user_id": "locking_user",
      "timestamp": 1625097600.0
    }
  }
}
```

#### 7. Update Error

```json
{
  "type": "update_error",
  "field": "field_name",
  "message": "Field is locked by user user_id"
}
```

#### 8. Presence Update

```json
{
  "type": "presence_update",
  "users": ["user1", "user2", "user3"]
}
```

## API Endpoints

### WebSocket Connection

```
ws://localhost:8080/ws/form/{form_id}/{user_id}
```

**Parameters:**

- `form_id` (int): Form ID
- `user_id` (string): User ID

**Authentication:**

- Requires authentication via the `token` parameter in the URL.

## Configuration

### Timeout Settings

```python
LOCK_TIMEOUT = 300  # 5 minutes in seconds
```

## Error Handling

### Possible Errors and Solutions:

1. **Field already locked**

   - Response: `{"success": false, "message": "Field is already locked by another user"}`
   - Solution: Offer access request

2. **Field not locked**

   - Response: `{"success": false, "message": "Field is not locked"}`
   - Context: When requesting access to an unlocked field

3. **Already pending request**

   - Response: `{"success": false, "message": "You already have a pending request for this field"}`
   - Context: Duplicate access request

4. **Unauthorized unlock**

   - Response: `{"success": false, "message": "Field was not locked by you"}`
   - Context: Attempt to unlock someone else's lock

5. **WebSocket disconnect**
   - Automatic release of all user's locks
   - Broadcast presence update
