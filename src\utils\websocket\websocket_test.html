<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test - Form Updates</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin: 10px 0; }
        input, textarea, button { padding: 8px; margin: 5px; }

        /* Field locking styles */
        .locked-field { background-color: #ffeb3b; border: 2px solid #ff9800; }
        .field-controls { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .notification {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            display: none;
        }
        .notification.info { background-color: #d1ecf1; border: 1px solid #bee5eb; }
        .notification.warning { background-color: #fff3cd; border: 1px solid #ffeaa7; }
        .notification.success { background-color: #d4edda; border: 1px solid #c3e6cb; }
        .access-request {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }

        #messages {
            border: 1px solid #ccc;
            height: 300px;
            overflow-y: scroll;
            padding: 10px;
            background: #f9f9f9;
        }
        .message { margin: 5px 0; padding: 5px; border-left: 3px solid #007bff; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .success { border-left-color: #28a745; background: #d4edda; }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket Test - Form Updates</h1>

        <div class="form-group">
            <label>WebSocket URL:</label>
            <input type="text" id="wsUrl" value="ws://localhost:8080/ws/form/1/1" style="width: 400px;">
            <button onclick="connect()">Connect</button>
            <button onclick="disconnect()">Disconnect</button>
        </div>

        <div class="form-group">
            <label>Authorization Token:</label>
            <input type="text" id="authToken" value="" placeholder="Bearer token" style="width: 400px;">
        </div>

        <div class="form-group">
            <label>Connection Status:</label>
            <span id="status">Disconnected</span>
        </div>

        <h3>Send Form Update</h3>
        <div class="form-group">
            <label>Field:</label>
            <input type="text" id="field" value="form_name" placeholder="field name">
        </div>
        <div class="form-group">
            <label>Value:</label>
            <input type="text" id="value" value="Test Form Name" placeholder="field value">
        </div>
        <button onclick="sendUpdate()">Send Update</button>

        <h3>Send Related Field Update</h3>
        <div class="form-group">
            <label>Field:</label>
            <input type="text" id="relatedField" value="chemicals" placeholder="related field">
        </div>
        <div class="form-group">
            <label>Primary Key:</label>
            <input type="number" id="primaryKey" value="1" placeholder="table_primary_key">
        </div>
        <div class="form-group">
            <label>Field to Update:</label>
            <input type="text" id="fieldToUpdate" value="name" placeholder="field">
        </div>
        <div class="form-group">
            <label>New Value:</label>
            <input type="text" id="newValue" value="Updated Chemical Name" placeholder="value">
        </div>
        <button onclick="sendRelatedUpdate()">Send Related Update</button>

        <h3>Field Locking & Access Control</h3>
        <div class="field-controls">
            <div class="form-group">
                <label>Field to Lock/Unlock:</label>
                <input type="text" id="lockField" value="form_name" placeholder="field name">
            </div>
            <button onclick="lockField()">Lock Field</button>
            <button onclick="unlockField()">Unlock Field</button>
            <button onclick="getLockedFields()">Get Locked Fields</button>
        </div>

        <div class="field-controls">
            <h4>Request Access to Locked Field</h4>
            <div class="form-group">
                <label>Field Name:</label>
                <input type="text" id="requestField" value="form_name" placeholder="field name">
            </div>
            <div class="form-group">
                <label>Request Message:</label>
                <textarea id="requestMessage" placeholder="Optional message to field owner" rows="2" style="width: 300px;"></textarea>
            </div>
            <button onclick="requestFieldAccess()">Request Access</button>
        </div>

        <!-- Notifications Area -->
        <div id="lockNotification" class="notification"></div>

        <!-- Access Requests Area -->
        <div id="accessRequests" style="margin-top: 20px;"></div>

        <h3>Messages</h3>
        <div id="messages"></div>
        <button onclick="clearMessages()">Clear Messages</button>
    </div>

    <script>
        let ws = null;

        function connect() {
            const url = document.getElementById('wsUrl').value;
            const token = document.getElementById('authToken').value;
            ws = new WebSocket(url + (token ? '?token=' + encodeURIComponent(token) : ''));

            ws.onopen = function(event) {
                document.getElementById('status').textContent = 'Connected';
                document.getElementById('status').style.color = 'green';
                addMessage('Connected to WebSocket', 'success');
            };

            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                addMessage('Received: ' + JSON.stringify(data, null, 2), 'message');

                console.log('WebSocket message received:', data);

                // Handle different message types
                handleWebSocketMessage(data);
            };

            ws.onerror = function(error) {
                addMessage('WebSocket Error: ' + error, 'error');
            };

            ws.onclose = function(event) {
                document.getElementById('status').textContent = 'Disconnected';
                document.getElementById('status').style.color = 'red';
                addMessage('WebSocket connection closed', 'error');
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function sendUpdate() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                addMessage('WebSocket not connected', 'error');
                return;
            }

            const field = document.getElementById('field').value;
            const value = document.getElementById('value').value;

            const message = {
                field: field,
                value: value
            };

            ws.send(JSON.stringify(message));
            addMessage('Sent: ' + JSON.stringify(message, null, 2), 'success');
        }

        function sendRelatedUpdate() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                addMessage('WebSocket not connected', 'error');
                return;
            }

            const field = document.getElementById('relatedField').value;
            const primaryKey = parseInt(document.getElementById('primaryKey').value);
            const fieldToUpdate = document.getElementById('fieldToUpdate').value;
            const newValue = document.getElementById('newValue').value;

            const message = {
                field: field,
                value: {
                    table_primary_key: primaryKey,
                    field: fieldToUpdate,
                    value: newValue
                }
            };

            ws.send(JSON.stringify(message));
            addMessage('Sent Related Update: ' + JSON.stringify(message, null, 2), 'success');
        }

        function lockField() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                addMessage('WebSocket not connected', 'error');
                return;
            }

            const field = document.getElementById('lockField').value;

            const message = {
                type: 'lock_field',
                field_name: field
            };

            ws.send(JSON.stringify(message));
            addMessage('Locking field: ' + field, 'success');
        }

        function unlockField() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                addMessage('WebSocket not connected', 'error');
                return;
            }

            const field = document.getElementById('lockField').value;

            const message = {
                type: 'unlock_field',
                field_name: field
            };

            ws.send(JSON.stringify(message));
            addMessage('Unlocking field: ' + field, 'success');
        }

        function getLockedFields() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                addMessage('WebSocket not connected', 'error');
                return;
            }

            const message = {
                type: 'get_locked_fields'
            };

            ws.send(JSON.stringify(message));
            addMessage('Requesting locked fields', 'success');
        }

        function requestFieldAccess() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                addMessage('WebSocket not connected', 'error');
                return;
            }

            const field = document.getElementById('requestField').value;
            const message = document.getElementById('requestMessage').value;

            const request = {
                type: 'request_field_access',
                field_name: field,
                message: message
            };

            ws.send(JSON.stringify(request));
            addMessage('Access request sent for field: ' + field, 'success');
        }

        // Handle different WebSocket message types
        function handleWebSocketMessage(data) {
            switch(data.type) {
                case 'form_update':
                    handleFormUpdate(data);
                    break;
                case 'field_status':
                    handleFieldStatus(data);
                    break;
                case 'lock_response':
                    handleLockResponse(data);
                    break;
                case 'unlock_response':
                    handleUnlockResponse(data);
                    break;
                case 'locked_fields_response':
                    handleLockedFieldsResponse(data);
                    break;
                case 'access_request':
                    handleAccessRequest(data);
                    break;
                case 'access_response':
                    handleAccessResponse(data);
                    break;
                case 'access_request_response':
                    handleAccessRequestResponse(data);
                    break;
                case 'current_locks':
                    handleCurrentLocks(data);
                    break;
                case 'update_error':
                    handleUpdateError(data);
                    break;
                case 'presence_update':
                    handlePresenceUpdate(data);
                    break;
            }
        }

        function handleFormUpdate(data) {
            if (data.success && data.payload) {
                const currentField = document.getElementById('field').value;
                if (data.payload.field === currentField) {
                    document.getElementById('value').value = data.payload.value;
                }
                showNotification(`Field "${data.payload.field}" updated by another user`, 'info');
            }
        }

        function handleFieldStatus(data) {
            const fieldInput = document.getElementById('lockField');
            if (data.field_name === fieldInput.value) {
                if (data.status === 'locked') {
                    fieldInput.classList.add('locked-field');
                    showNotification(`Field "${data.field_name}" locked by user ${data.user_id}`, 'warning');
                } else if (data.status === 'unlocked' || data.status === 'auto_unlocked') {
                    fieldInput.classList.remove('locked-field');
                    showNotification(`Field "${data.field_name}" unlocked`, 'success');
                }
            }
        }

        function handleLockResponse(data) {
            if (data.success) {
                showNotification(`Successfully locked field "${data.field_name}"`, 'success');
                document.getElementById('lockField').classList.add('locked-field');
            } else {
                showNotification(`Failed to lock field "${data.field_name}": ${data.message}`, 'warning');
                // Show option to request access
                if (data.message.includes('already locked')) {
                    showAccessRequestOption(data.field_name);
                }
            }
        }

        function handleUnlockResponse(data) {
            if (data.success) {
                showNotification(`Successfully unlocked field "${data.field_name}"`, 'success');
                document.getElementById('lockField').classList.remove('locked-field');
            } else {
                showNotification(`Failed to unlock field "${data.field_name}": ${data.message}`, 'warning');
            }
        }

        function handleLockedFieldsResponse(data) {
            const lockedFields = data.locked_fields;
            let message = 'Currently locked fields:\n';
            for (const [field, info] of Object.entries(lockedFields)) {
                message += `- ${field}: locked by ${info.user_id} at ${new Date(info.timestamp * 1000).toLocaleTimeString()}\n`;
            }
            if (Object.keys(lockedFields).length === 0) {
                message = 'No fields are currently locked';
            }
            showNotification(message, 'info');
        }

        function handleAccessRequest(data) {
            // Show access request from another user
            showAccessRequestDialog(data);
        }

        function handleAccessResponse(data) {
            if (data.approved) {
                showNotification(`Access approved for field "${data.field_name}"! ${data.response_message}`, 'success');
                // Try to lock the field automatically
                setTimeout(() => {
                    document.getElementById('lockField').value = data.field_name;
                    lockField();
                }, 500);
            } else {
                showNotification(`Access denied for field "${data.field_name}": ${data.response_message}`, 'warning');
            }
        }

        function handleAccessRequestResponse(data) {
            if (data.success) {
                showNotification(`Access request sent for field "${data.field_name}"`, 'success');
            } else {
                showNotification(`Failed to send access request: ${data.message}`, 'warning');
            }
        }

        function handleCurrentLocks(data) {
            // Show current locks when connecting
            handleLockedFieldsResponse(data);
        }

        function handleUpdateError(data) {
            showNotification(`Update failed for field "${data.field}": ${data.message}`, 'error');
        }

        function handlePresenceUpdate(data) {
            showNotification(`Users online: ${data.users.join(', ')}`, 'info');
        }

        function showNotification(message, type) {
            const notification = document.getElementById('lockNotification');
            notification.textContent = message;
            notification.className = `notification ${type}`;
            notification.style.display = 'block';

            // Auto-hide after 5 seconds
            setTimeout(() => {
                notification.style.display = 'none';
            }, 5000);
        }

        function showAccessRequestOption(fieldName) {
            const notification = document.getElementById('lockNotification');
            notification.innerHTML = `
                <strong>Field "${fieldName}" is locked by another user.</strong><br>
                <button onclick="requestAccessForField('${fieldName}')" style="margin-top: 10px;">Request Access</button>
            `;
            notification.className = 'notification warning';
            notification.style.display = 'block';
        }

        function requestAccessForField(fieldName) {
            document.getElementById('requestField').value = fieldName;
            document.getElementById('requestMessage').value = 'Please allow me to edit this field';
            requestFieldAccess();
        }

        function showAccessRequestDialog(data) {
            const accessRequestsDiv = document.getElementById('accessRequests');
            const requestDiv = document.createElement('div');
            requestDiv.className = 'access-request';
            requestDiv.innerHTML = `
                <h4>Access Request for Field: "${data.field_name}"</h4>
                <p><strong>From:</strong> User ${data.requester_id}</p>
                <p><strong>Message:</strong> ${data.requester_message || 'No message'}</p>
                <p><strong>Time:</strong> ${new Date(data.timestamp * 1000).toLocaleString()}</p>
                <div>
                    <button onclick="respondToAccessRequest('${data.field_name}', '${data.requester_id}', true, this.parentElement.parentElement)">
                        Approve
                    </button>
                    <button onclick="respondToAccessRequest('${data.field_name}', '${data.requester_id}', false, this.parentElement.parentElement)">
                        Deny
                    </button>
                    <input type="text" placeholder="Optional response message" id="response_${data.requester_id}" style="margin-left: 10px; width: 200px;">
                </div>
            `;
            accessRequestsDiv.appendChild(requestDiv);
        }

        function respondToAccessRequest(fieldName, requesterId, approved, requestDiv) {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                addMessage('WebSocket not connected', 'error');
                return;
            }

            const responseMessage = document.getElementById(`response_${requesterId}`).value;

            const message = {
                type: 'respond_to_access_request',
                field_name: fieldName,
                requester_id: requesterId,
                approved: approved,
                response_message: responseMessage
            };

            ws.send(JSON.stringify(message));
            addMessage(`${approved ? 'Approved' : 'Denied'} access request for field: ${fieldName}`, 'success');

            // Remove the request div
            requestDiv.remove();
        }

        function addMessage(text, type = 'message') {
            const messages = document.getElementById('messages');
            const div = document.createElement('div');
            div.className = 'message ' + type;
            div.innerHTML = '<strong>' + new Date().toLocaleTimeString() + ':</strong> <pre>' + text + '</pre>';
            messages.appendChild(div);
            messages.scrollTop = messages.scrollHeight;
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }
    </script>
</body>
</html>
