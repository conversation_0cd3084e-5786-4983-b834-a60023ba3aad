<script setup lang="ts">
  import { ref } from 'vue';
  import { emailRules } from '@/utils/formValidation';

  const valid = ref(false);
  const logform = ref();
  const email = ref('');

  function validate() {
    logform.value.validate();
  }
</script>

<template>
  <v-form
    ref="logform"
    v-model="valid"
    lazy-validation
    action="/"
    class="mt-7 loginForm"
    @submit.prevent="validate"
  >
    <v-label>Emailová adresa</v-label>
    <v-text-field
      v-model="email"
      :rules="emailRules"
      placeholder="Zadejte emailovou adresu"
      class="mt-2 mb-6"
      required
      hide-details="auto"
      variant="outlined"
      color="primary"
    ></v-text-field>

    <!-- <h6 class="text-caption">Do not forgot to check SPAM box.</h6> -->
    <v-btn
      color="primary"
      block
      class="mt-2"
      variant="flat"
      size="large"
      :disabled="valid"
      @click="validate()"
    >
      Odeslání e-mailu pro obnovení hesla
    </v-btn>
  </v-form>
</template>
