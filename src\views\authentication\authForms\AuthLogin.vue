<script setup lang="ts">
  import { ref } from 'vue';
  // icons
  import { EyeInvisibleOutlined, EyeOutlined } from '@ant-design/icons-vue';
  import { useAuthStore } from '@/stores/auth';
  import { Form } from 'vee-validate';

  import { emailRules } from '@/utils/formValidation';
  import { notification } from 'ant-design-vue';

  const valid = ref(false);
  const show1 = ref(false);
  const password = ref<string | undefined>(undefined);
  const username = ref<string | undefined>(undefined);

  async function validate(values: any, { setErrors }: any) {
    if (username.value === undefined || password.value === undefined) {
      return;
    }

    const authStore = useAuthStore();
    await authStore
      .login(username.value, password.value)
      .catch((error) => setErrors({ apiError: error }));

    if (authStore.user !== null) {
      notification.success({
        message: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> probě<PERSON><PERSON>',
        description: 'Víte<PERSON>te z<PERSON>'
      });
    }
  }
</script>

<template>
  <div class="d-flex justify-space-between align-center">
    <h3 class="text-h3 text-center mb-0">Přihlášení</h3>
  </div>
  <Form v-slot="{ errors, isSubmitting }" class="mt-7 loginForm" @submit="validate">
    <div class="mb-6">
      <v-label>Emailová adresa</v-label>
      <v-text-field
        v-model="username"
        aria-label="email address"
        :rules="emailRules"
        class="mt-2"
        required
        hide-details="auto"
        variant="outlined"
        color="primary"
      ></v-text-field>
    </div>
    <div>
      <v-label>Heslo</v-label>
      <v-text-field
        v-model="password"
        aria-label="password"
        required
        variant="outlined"
        color="primary"
        hide-details="auto"
        :type="show1 ? 'text' : 'password'"
        class="pwdInput mt-2"
      >
        <template #append>
          <v-btn color="secondary" icon rounded="sm" variant="text">
            <EyeInvisibleOutlined
              v-if="show1 == false"
              :style="{ color: 'rgb(var(--v-theme-secondary))' }"
              @click="show1 = !show1"
            />
            <EyeOutlined
              v-if="show1 == true"
              :style="{ color: 'rgb(var(--v-theme-secondary))' }"
              @click="show1 = !show1"
            />
          </v-btn>
        </template>
      </v-text-field>
    </div>
    <v-btn
      color="primary"
      :loading="isSubmitting"
      block
      class="mt-5"
      variant="flat"
      size="large"
      :disabled="valid"
      type="submit"
    >
      Odeslat
    </v-btn>
    <div v-if="errors.apiError" class="mt-2">
      <v-alert color="error">{{ errors.apiError }}</v-alert>
    </div>
  </Form>
</template>
<style lang="scss">
  .loginForm {
    .v-text-field .v-field--active input {
      font-weight: 500;
    }
  }
</style>
