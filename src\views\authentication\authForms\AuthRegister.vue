<script setup lang="ts">
  import { ref } from 'vue';
  // icons
  import { EyeInvisibleOutlined, EyeOutlined } from '@ant-design/icons-vue';
  import { emailRules, passwordRules, itemRequiredRule, lastRules } from '@/utils/formValidation';

  const show1 = ref(false);
  const password = ref('');
  const email = ref('');
  const Regform = ref();
  const firstname = ref('');
  const lastname = ref('');

  function validate() {
    Regform.value.validate();
  }
</script>

<template>
  <div class="d-flex justify-space-between align-center">
    <h3 class="text-h3 text-center mb-0">Registrace</h3>
    <router-link to="/auth/login" class="text-primary text-decoration-none">
      Máte již vytvořený účet?
    </router-link>
  </div>
  <v-form ref="Regform" lazy-validation class="mt-7 loginForm">
    <v-row class="my-0">
      <v-col cols="12" sm="6" class="py-0">
        <div class="mb-6">
          <v-label>Jméno*</v-label>
          <v-text-field
            v-model="firstname"
            :rules="itemRequiredRule"
            hide-details="auto"
            required
            variant="outlined"
            class="mt-2"
            color="primary"
            placeholder="Petr"
          ></v-text-field>
        </div>
      </v-col>
      <v-col cols="12" sm="6" class="py-0">
        <div class="mb-6">
          <v-label>Příjmení*</v-label>
          <v-text-field
            v-model="lastname"
            :rules="lastRules"
            hide-details="auto"
            required
            variant="outlined"
            class="mt-2"
            color="primary"
            placeholder="Novák"
          ></v-text-field>
        </div>
      </v-col>
    </v-row>
    <div class="mb-6">
      <v-label>Emailová adresa*</v-label>
      <v-text-field
        v-model="email"
        :rules="emailRules"
        placeholder="<EMAIL>"
        class="mt-2"
        required
        hide-details="auto"
        variant="outlined"
        color="primary"
      ></v-text-field>
    </div>
    <div class="mb-6">
      <v-label>Heslo</v-label>
      <v-text-field
        v-model="password"
        :rules="passwordRules"
        placeholder="*****"
        required
        variant="outlined"
        color="primary"
        hide-details="auto"
        :type="show1 ? 'text' : 'password'"
        class="pwdInput mt-2"
      >
        <template #append>
          <v-btn color="secondary" icon rounded="sm" variant="text">
            <EyeInvisibleOutlined
              v-if="show1 == false"
              :style="{ color: 'rgb(var(--v-theme-secondary))' }"
              @click="show1 = !show1"
            />
            <EyeOutlined
              v-if="show1 == true"
              :style="{ color: 'rgb(var(--v-theme-secondary))' }"
              @click="show1 = !show1"
            />
          </v-btn>
        </template>
      </v-text-field>
    </div>
    <v-btn color="primary" block class="mt-4" variant="flat" size="large" @click="validate()">
      Vytvořit účet
    </v-btn>
  </v-form>
</template>
