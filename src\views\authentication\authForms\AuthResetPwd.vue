<script setup lang="ts">
  import { ref } from 'vue';
  import { useRouter } from 'vue-router';
  // icons
  import { EyeInvisibleOutlined, EyeOutlined } from '@ant-design/icons-vue';

  const valid = ref(false);
  const logform = ref();
  const password = ref('');
  const conpassword = ref('');
  const show1 = ref(false);
  const passwordRules = ref([
    (v: string) => !!v || 'Password is required',
    (v: string) => (v && v.length <= 10) || 'Password must be less than 10 characters'
  ]);
  const confirmpasswordRules = ref([
    (v: string) => !!v || 'Password is required',
    (v: string) => (v && v.length <= 10) || 'Password must be less than 10 characters'
  ]);

  const router = useRouter();

  function validate() {
    logform.value.validate();
    if (logform.value) {
      router.push('/starter');
    }
  }
</script>

<template>
  <v-form
    ref="logform"
    v-model="valid"
    lazy-validation
    action="/starter"
    class="mt-7 loginForm"
    @submit.prevent="validate"
  >
    <div class="mb-6">
      <v-label>Password</v-label>
      <v-text-field
        v-model="password"
        :rules="passwordRules"
        required
        placeholder="Enter password"
        variant="outlined"
        color="primary"
        hide-details="auto"
        :type="show1 ? 'text' : 'password'"
        class="pwdInput mt-2 mb-4"
      >
        <template #append>
          <v-btn color="secondary" icon rounded="sm" variant="text">
            <EyeInvisibleOutlined
              v-if="show1 == false"
              :style="{ color: 'rgb(var(--v-theme-secondary))' }"
              @click="show1 = !show1"
            />
            <EyeOutlined
              v-if="show1 == true"
              :style="{ color: 'rgb(var(--v-theme-secondary))' }"
              @click="show1 = !show1"
            />
          </v-btn>
        </template>
      </v-text-field>
    </div>
    <div class="mb-6">
      <v-label>Confirm Password</v-label>
      <v-text-field
        v-model="conpassword"
        :rules="confirmpasswordRules"
        placeholder="Enter confirm password"
        required
        variant="outlined"
        color="primary"
        hide-details="auto"
        class="pwdInput mt-2"
      ></v-text-field>
    </div>
    <v-btn
      color="primary"
      block
      class="mt-5"
      variant="flat"
      size="large"
      :disabled="valid"
      type="submit"
    >
      Reset Password
    </v-btn>
  </v-form>
</template>
