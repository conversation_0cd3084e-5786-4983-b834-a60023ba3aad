<script setup lang="ts">
  import { computed, shallowRef } from 'vue';

  import BaseBreadcrumb from '@/components/shared/BaseBreadcrumb.vue';

  const page = computed(() => ({ title: 'MainMenu.Farmak' }));
  const breadcrumbs = shallowRef([
    {
      title: 'Farmak',
      disabled: true,
      href: '/'
    }
  ]);
</script>

<template>
  <BaseBreadcrumb :title="page.title" :breadcrumbs="breadcrumbs"></BaseBreadcrumb>
</template>
