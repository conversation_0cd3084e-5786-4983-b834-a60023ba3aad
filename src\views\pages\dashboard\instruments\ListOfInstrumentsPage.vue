<script setup lang="ts">
  import type { BreadcrumbI } from '@/components/shared/BaseBreadcrumb.vue';
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import CustomTable from '@/components/shared/CustomTable.vue';
  import TopPageBreadcrumb from '@/components/shared/TopPageBreadcrumb.vue';
  import LoaderWrapper from '@/layouts/dashboard/LoaderWrapper.vue';
  import { useInstrumentsStore } from '@/stores/instruments';
  import { Technique, TechniqueType, useTechniquesStore } from '@/stores/techniques';
  import { toLocale } from '@/utils/locales';
  import { EditOutlined, EyeOutlined, SearchOutlined } from '@ant-design/icons-vue';
  import { useDebounceFn } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue';
  import { onBeforeRouteUpdate, useRouter } from 'vue-router';
  import type { Header } from 'vue3-easy-data-table';
  import InstrumentModal from './components/InstrumentModal.vue';
  import { isAllowed } from '@/utils/directive/isAllowed';
  import { notification } from 'ant-design-vue';
  const ConfirmRef = ref<undefined | typeof ConfirmDlg>(undefined);

  const router = useRouter();
  const techniquesStore = useTechniquesStore();
  const { loading: techniqueLoading } = storeToRefs(techniquesStore);

  const instrumentsStore = useInstrumentsStore();
  const { options, loading, totalItems, items, search } = storeToRefs(instrumentsStore);

  const loadFromServer = async () => {
    instrumentsStore.getAll();
  };

  const debouncedSearch = useDebounceFn(() => {
    if (loading.value === false) loadFromServer();
  }, 350);
  const havePermision = ref<boolean>(false);
  const missingPermison = ref<string>();
  const baseDataLoaded = ref(false);
  onMounted(async () => {
    await checkPermision();
    instrumentsStore.setParamsFromLocation();
    debouncedSearch();
    techniquesStore.getAll([TechniqueType.EXTERNAL, TechniqueType.INTERNAL]);
    baseDataLoaded.value = true;
  });

  const checkPermision = async () => {
    if (isAllowed(['add_edit_analytical_instruments'])) {
      havePermision.value = true;
    } else {
      missingPermison.value = 'add_edit_analytical_instruments';
      havePermision.value = false;
    }
  };
  watch(
    [options, totalItems],
    () => {
      if (loading.value === false) debouncedSearch();
    },
    { deep: true }
  );

  watch(search, () => {
    if (loading.value === false) debouncedSearch();
  });

  onBeforeRouteUpdate((to, from, next) => {
    if (to.path === from.path && to.query !== from.query) {
      instrumentsStore.setParamsFromLocation();
      debouncedSearch();
    }

    next();
  });

  onBeforeUnmount(() => {
    instrumentsStore.search = '';
  });

  const headers: Header[] = [
    { text: 'ID', value: 'instrument_id', sortable: true },
    { text: 'Zkratka', value: 'instrument_shortcut', sortable: true },
    { text: 'Název/ID techniky', value: 'technique_id', sortable: false },
    { text: 'Stav', value: 'status', sortable: true },
    { text: 'Akce', value: 'action' }
  ];

  const breadcrumbItems = computed<BreadcrumbI[]>(() => {
    return [
      {
        title: 'Analytické přístroje',
        disabled: true,
        href: router.resolve({ name: 'AnalyticalInstruments' }).href
      }
    ];
  });
</script>
<template>
  <LoaderWrapper v-if="!baseDataLoaded" />
  <template v-else>
    <TopPageBreadcrumb title="Analytické přístroje" :_breadcrumbs="breadcrumbItems" />
    <v-row>
      <v-col cols="12" md="12">
        <v-card elevation="0" variant="outlined" class="withbg pageSize">
          <v-card-item>
            <v-row justify="space-between" class="align-center">
              <v-col cols="12" md="9">
                <v-text-field
                  v-model="search"
                  type="text"
                  variant="outlined"
                  persistent-placeholder
                  placeholder="Hledat přístroj"
                  hide-details
                >
                  <template #prepend-inner>
                    <SearchOutlined :style="{ fontSize: '14px' }" />
                  </template>
                </v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <div class="d-flex gap-2 justify-end flex-wrap">
                  <v-btn
                    variant="flat"
                    color="primary"
                    :disabled="!havePermision"
                    @click.prevent="instrumentsStore.showNewInstrumentModal()"
                  >
                    Přidat přístroj
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </v-card-item>
          <v-divider></v-divider>
          <v-card-text>
            <CustomTable
              v-model:server-options="options"
              :server-items-length="totalItems"
              :loading="loading || techniqueLoading"
              :headers="headers"
              :items="items"
              multi-sort
            >
              <template #item-technique_id="{ technique_id }: { technique_id: number | Technique }">
                <template v-if="typeof technique_id === 'number'">{{ technique_id }}</template>
                <template v-else>
                  <div class="player-wrapper">
                    <h6 class="text-subtitle-1 mb-0">
                      {{ technique_id.name }} ({{ technique_id.shortcut }}) -
                      <v-chip
                        v-if="technique_id.status === 'active'"
                        color="success"
                        size="small"
                        label
                      >
                        Aktivní
                      </v-chip>
                      <v-chip
                        v-if="technique_id.status === 'inactive'"
                        color="warning"
                        size="small"
                        label
                      >
                        Neaktivní
                      </v-chip>
                      <v-chip
                        v-if="technique_id.status === 'deleted'"
                        color="error"
                        size="small"
                        label
                      >
                        Odstraněno
                      </v-chip>
                    </h6>
                    <small class="text-h6 text-lightText">
                      Typ: {{ technique_id.type }} | Vytvořena:
                      {{ toLocale(technique_id.created_at) }}
                    </small>
                  </div>
                </template>
              </template>

              <template #item-status="{ status }">
                <v-chip v-if="status === 'active'" color="success" size="small" label>
                  Aktivní
                </v-chip>
                <v-chip v-if="status === 'inactive'" color="warning" size="small" label>
                  Neaktivní
                </v-chip>
                <v-chip v-if="status === 'deleted'" color="error" size="small" label>
                  Odstraněno
                </v-chip>
              </template>

              <template #item-action="{ instrument_id }">
                <div class="operation-wrapper">
                  <v-btn
                    icon
                    color="secondary"
                    variant="text"
                    rounded="sm"
                    @click.prevent="instrumentsStore.showPreviewModal(instrument_id)"
                  >
                    <EyeOutlined />
                  </v-btn>
                  <v-btn
                    icon
                    color="primary"
                    variant="text"
                    rounded="sm"
                    :disabled="!havePermision"
                    @click.prevent="instrumentsStore.showEditModal(instrument_id)"
                  >
                    <EditOutlined />
                  </v-btn>
                  <!-- <v-btn
                  v-if="status !== 'deleted'"
                  icon
                  color="error"
                  variant="text"
                  rounded="sm"
                  @click.prevent="
                    async () => {
                      if (await ConfirmRef?.open('Potvrzení', 'Opravdu chcete smazat přístroje?', { color: 'error', notclosable: true })) {
                        instrumentsStore.deleteInstrument(instrument_id);
                      }
                    }
                  "
                >
                  <DeleteOutlined />
                </v-btn> -->
                </div>
              </template>
            </CustomTable>
          </v-card-text>
        </v-card>
      </v-col>

      <ConfirmDlg ref="ConfirmRef" />
      <InstrumentModal
        v-if="instrumentsStore.showInstrumentModal"
        v-model:show="instrumentsStore.showInstrumentModal"
      />
    </v-row>
  </template>
</template>
