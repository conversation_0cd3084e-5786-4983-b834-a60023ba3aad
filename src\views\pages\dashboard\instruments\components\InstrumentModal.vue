<script setup lang="ts">
  import TechniquesSelect from '@/components/shared/TechniquesSelect.vue';
  import { useInstrumentsStore } from '@/stores/instruments';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { useColumnsStore } from '@/stores/columns';
  import { useVModel } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import { computed, ref, watch } from 'vue';
  import LineChart from '@/components/shared/charts/ApexLineChart.vue';
  import type { Header } from 'vue3-easy-data-table';
  import CustomTable from '@/components/shared/CustomTable.vue';
  import { useRouter } from 'vue-router';
  import { notification } from 'ant-design-vue';
  import { isAllowed } from '@/utils/directive/isAllowed';
  const router = useRouter();
  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    }
  });
  const tab = ref('one');
  const columnsStore = useColumnsStore();
  const instrumentsStore = useInstrumentsStore();
  const { modalOptions, loading, historyItems, options, historyLength } =
    storeToRefs(instrumentsStore);

  const CreateInstrumentForm = ref();
  async function submitFormToValidate() {
    if (CreateInstrumentForm.value.isValid && modalOptions.value) {
      switch (true) {
        case modalOptions.value.isEditing && !modalOptions.value.isCreating:
          return instrumentsStore.updateInstrument();
        case !modalOptions.value.isEditing && modalOptions.value.isCreating:
          return instrumentsStore.createInstrument();

        default:
          return 'Náhled přístroje';
      }
    } else {
      if (modalOptions.value && modalOptions.value.newData) {
        modalOptions.value.newData.confirm = false;
      }
    }
  }

  const showState = useVModel(props, 'show');
  const showTitle = computed(() => {
    if (modalOptions.value === undefined) {
      return 'Náhled přístroje';
    }

    switch (true) {
      case modalOptions.value.isEditing && !modalOptions.value.isCreating:
        return 'Editace přístroje';
      case !modalOptions.value.isEditing && modalOptions.value.isCreating:
        return 'Nový přístroj';
      default:
        return 'Náhled přístroje';
    }
  });
  const showSuccessButtonTitle = computed(() => {
    if (modalOptions.value === undefined) {
      return false;
    }

    switch (true) {
      case modalOptions.value.isEditing && !modalOptions.value.isCreating:
        return 'Upravit přístroj';
      case !modalOptions.value.isEditing && modalOptions.value.isCreating:
        return 'Přidat přístroj';
      default:
        return false;
    }
  });

  const onlyPreview = computed(() => {
    return modalOptions.value?.isCreating === false && modalOptions.value?.isEditing === false;
  });

  const maxFromDate = computed(() => {
    if (modalOptions.value && modalOptions.value.newData?.to_datetime) {
      const toDate = new Date(modalOptions.value.newData.to_datetime);
      toDate.setHours(23, 59, 59, 999);
      return toDate;
    }
    return null;
  });

  const minToDate = computed(() => {
    if (modalOptions.value && modalOptions.value.newData?.from_datetime) {
      const fromDate = new Date(modalOptions.value.newData.from_datetime);
      fromDate.setHours(0, 0, 0, 0);
      return fromDate;
    }
    return null;
  });

  const onFromDateChange = (newFromDate: Date | null) => {
    if (newFromDate) {
      newFromDate.setHours(0, 0, 0, 0);
      if (modalOptions.value && modalOptions.value.newData?.to_datetime) {
        modalOptions.value.newData.from_datetime = newFromDate;
      }
    }
  };

  const onToDateChange = (newToDate: Date | null) => {
    if (newToDate) {
      newToDate.setHours(23, 59, 59, 999);
      if (modalOptions.value && modalOptions.value.newData?.from_datetime) {
        modalOptions.value.newData.to_datetime = newToDate;
      }
    }
  };

  watch(
    [
      () => modalOptions.value?.newData?.from_datetime,
      () => modalOptions.value?.newData?.to_datetime
    ],
    async ([newFrom, newTo]) => {
      if (newFrom && newTo) {
        const fromDate = new Date(newFrom);
        fromDate.setHours(0, 0, 0, 0);
        const toDate = new Date(newTo);
        toDate.setHours(23, 59, 59, 999);
        await instrumentsStore.getHistoryOfInstrumentUsage(fromDate, toDate);
      }
    }
  );

  watch(
    () => tab.value,
    async (newTab, oldTab) => {
      if (newTab) {
        if (newTab === 'three') {
          if (await checkPermision()) {
            if (
              modalOptions.value?.newData?.from_datetime &&
              modalOptions.value?.newData?.to_datetime
            ) {
              const fromDate = new Date(modalOptions.value.newData.from_datetime);
              fromDate.setHours(0, 0, 0, 0);
              const toDate = new Date(modalOptions.value.newData.to_datetime);
              toDate.setHours(23, 59, 59, 999);
              await instrumentsStore.getHistoryOfInstrumentUsage(fromDate, toDate);
            }
          } else {
            tab.value = oldTab;
          }
        }
      }
    }
  );

  const havePermision = ref<boolean>(true);
  const missingPermison = ref<string>();
  const checkPermision = async () => {
    if (isAllowed(['view_instrument_statistics'])) {
      havePermision.value = true;
      return true;
    } else {
      missingPermison.value = 'view_instrument_statistics';
      havePermision.value = false;
    }
    if (!havePermision.value) {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description:
          'Nemáte dostanečné oprávnení pro prohlížení historie přístrojů: ' +
          missingPermison.value +
          '.'
      });
    }
    return false;
  };

  const headers: Header[] = [
    { text: 'Název projektu', value: 'name', sortable: false },
    { text: 'Počet vzorků', value: 'samples', sortable: false }
  ];

  const headersExtended: Header[] = [
    { text: 'Název metody', value: 'method_name', sortable: false },
    { text: 'Číslo vzorku', value: 'sample_number', sortable: false },
    { text: 'Vytvořil', value: 'user', sortable: false },
    { text: 'Kolona', value: 'kolona', sortable: false },
    { text: 'Číslo šarže', value: 'analytical_request', sortable: false }
  ];

  const handleRowClick = async (colonId: number) => {
    if (colonId) {
      await router.push({ name: 'SeznamKolon' });
      columnsStore.showPreviewModal(colonId, 'three');
    }
  };

  watch(
    () => modalOptions.value?.newData,
    (newData) => {
      if (newData && modalOptions.value?.isCreating) {
        localStorage.setItem('instrumentStore', JSON.stringify(newData));
      }
    },
    { deep: true }
  );
</script>
<template>
  <v-dialog v-model="showState" class="instrument-modal" :z-index="1010">
    <v-card :loading="loading">
      <v-form
        v-if="modalOptions?.newData"
        ref="CreateInstrumentForm"
        class="createInstrumentForm"
        :readonly="onlyPreview"
        @submit.prevent="submitFormToValidate"
      >
        <v-card-title class="pa-5">
          <span class="text-h5">{{ showTitle }}</span>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text v-if="!onlyPreview">
          <v-row>
            <v-col>
              <v-row>
                <v-col>
                  <v-label class="mb-2">Zkratka</v-label>
                  <v-text-field
                    v-model="modalOptions.newData.instrument_shortcut"
                    :rules="itemRequiredRule"
                    single-line
                    placeholder="Zadejte zkratku"
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></v-text-field>
                </v-col>
                <v-col cols="12">
                  <v-label class="mb-2">Technika</v-label>
                  <TechniquesSelect
                    v-model:selected-techniques="modalOptions.newData.technique_id"
                    :readonly="onlyPreview"
                    :clearable="!onlyPreview"
                    :rules="itemRequiredRule"
                    :filter-options="[
                      { column: 'type', value: ['internal'] },
                      { column: 'parent_technique_id', value: null },
                      { column: 'status', value: 'active' }
                    ]"
                  ></TechniquesSelect>
                </v-col>

                <v-col v-if="!modalOptions.isCreating" cols="12">
                  <v-label class="mb-2">Status</v-label>
                  <v-autocomplete
                    v-model="modalOptions.newData.status"
                    :readonly="onlyPreview"
                    :items="[
                      { value: 'active', title: 'Aktivní' },
                      { value: 'deleted', title: 'Odstraněný' }
                    ]"
                    rounded="sm"
                    color="primary"
                    single-line
                    hide-details
                    variant="outlined"
                    :no-data-text="'Žádná další políčka'"
                  ></v-autocomplete>
                </v-col>
                <v-col v-if="modalOptions.isCreating" cols="12">
                  <div class="d-flex justify-space-between gap-2">
                    <div class="pb-4">
                      <h6 class="text-subtitle-1 mb-0">Potvrzení přidání</h6>
                    </div>
                    <v-switch
                      v-model="modalOptions.newData.confirm"
                      color="primary"
                      class="switchRight"
                      hide-details
                    ></v-switch>
                  </div>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-text v-else>
          <v-card flat>
            <v-tabs v-model="tab" density="compact" class="mb-6">
              <v-tab value="one">Náhled přístroje</v-tab>
              <v-tab value="two">Přehled týdnů</v-tab>
              <v-tab value="three">Historie použití</v-tab>
            </v-tabs>
            <v-card-text class="pa-0">
              <v-tabs-window v-model="tab">
                <v-tabs-window-item value="one">
                  <v-row>
                    <v-col cols="12">
                      <v-label class="mb-2">Zkratka</v-label>
                      <v-text-field
                        v-model="modalOptions.newData.instrument_shortcut"
                        :rules="itemRequiredRule"
                        single-line
                        placeholder="Zadejte zkratku"
                        hide-details="auto"
                        variant="outlined"
                        rounded="sm"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12">
                      <v-label class="mb-2">Technika</v-label>
                      <TechniquesSelect
                        v-model:selected-techniques="modalOptions.newData.technique_id"
                        :readonly="onlyPreview"
                        :clearable="!onlyPreview"
                        :rules="itemRequiredRule"
                        :filter-options="[
                          { column: 'type', value: ['internal'] },
                          { column: 'parent_technique_id', value: null },
                          { column: 'status', value: 'active' }
                        ]"
                      ></TechniquesSelect>
                    </v-col>

                    <v-col v-if="!modalOptions.isCreating" cols="12">
                      <v-label class="mb-2">Status</v-label>
                      <v-autocomplete
                        v-model="modalOptions.newData.status"
                        :readonly="onlyPreview"
                        :items="[
                          { value: 'active', title: 'Aktivní' },
                          { value: 'deleted', title: 'Odstraněný' }
                        ]"
                        rounded="sm"
                        color="primary"
                        single-line
                        hide-details
                        variant="outlined"
                        :no-data-text="'Žádná další políčka'"
                      ></v-autocomplete>
                    </v-col>
                    <v-col v-if="modalOptions.isCreating" cols="12">
                      <div class="d-flex justify-space-between gap-2">
                        <div class="pb-4">
                          <h6 class="text-subtitle-1 mb-0">Potvrzení přidání</h6>
                        </div>
                        <v-switch
                          v-model="modalOptions.newData.confirm"
                          color="primary"
                          class="switchRight"
                          hide-details
                        ></v-switch>
                      </div>
                    </v-col>
                  </v-row>
                </v-tabs-window-item>
                <v-tabs-window-item value="two">
                  <v-row>
                    <v-col cols="12">
                      <LineChart />
                    </v-col>
                  </v-row>
                </v-tabs-window-item>
                <v-tabs-window-item value="three">
                  <v-row align="center" class="mb-2">
                    <v-col cols="12" md="2">
                      <v-label style="margin-left: 1em"><h3>Projekty</h3></v-label>
                    </v-col>
                    <v-spacer></v-spacer>
                    <v-col cols="12" md="3" class="d-flex align-center">
                      <v-label class="mb-0 mr-2">Od</v-label>
                      <v-date-input
                        v-model="modalOptions.newData.from_datetime"
                        density="compact"
                        single-line
                        hide-details="auto"
                        variant="outlined"
                        rounded="sm"
                        cancel-text="Zrušit"
                        ok-text="Potvrdit"
                        prepend-icon=""
                        :max="maxFromDate"
                        @input="onFromDateChange"
                      ></v-date-input>
                    </v-col>
                    <v-col cols="12" md="3" class="d-flex align-center">
                      <v-label class="mb-0 mr-2">Do</v-label>
                      <v-date-input
                        v-model="modalOptions.newData.to_datetime"
                        density="compact"
                        single-line
                        hide-details="auto"
                        variant="outlined"
                        rounded="sm"
                        cancel-text="Zrušit"
                        ok-text="Potvrdit"
                        prepend-icon=""
                        :min="minToDate"
                        @input="onToDateChange"
                      ></v-date-input>
                    </v-col>
                  </v-row>
                  <v-divider></v-divider>
                  <v-row class="mb-4">
                    <v-col cols="12">
                      <CustomTable
                        :loading="loading"
                        :headers="headers"
                        :items="[...historyItems.values()]"
                        :show-header="false"
                        :show-index="false"
                        :hide-footer="false"
                      >
                        <template #item-name="{ name }">
                          {{ name }}
                        </template>

                        <template #item-samples="{ samples }">({{ samples.length }})</template>

                        <template #expand="sample">
                          <CustomTable
                            :headers="headersExtended"
                            :items="sample.samples"
                            :hide-footer="true"
                            hide-rows-per-page
                            table-class-name="customize-table customize"
                            :rows-per-page="sample.samples.length"
                          >
                            <template #item-method_name="{ method_name }">
                              <template v-if="method_name">
                                {{ method_name }}
                              </template>
                              <template v-else>/</template>
                            </template>

                            <template #item-sample_number="{ sample_number }">
                              {{ sample_number }}
                            </template>

                            <template #item-user="{ user }">
                              {{ user.first_name }} {{ user.last_name }}
                            </template>

                            <template #item-kolona="{ kolona }">
                              <template v-if="kolona">
                                <div
                                  style="cursor: pointer"
                                  @click.prevent="handleRowClick(kolona.kolona_id)"
                                >
                                  {{ kolona.name }}
                                </div>
                              </template>
                              <template v-else>
                                <div>/</div>
                              </template>
                            </template>

                            <template #item-analytical_request="{ analytical_request }">
                              {{ analytical_request.batch_number?.batch_number }}
                            </template>
                          </CustomTable>
                        </template>
                      </CustomTable>
                    </v-col>
                  </v-row>
                </v-tabs-window-item>
              </v-tabs-window>
            </v-card-text>
          </v-card>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" variant="text" @click="instrumentsStore.resetModal()">Zrušit</v-btn>
          <v-btn
            v-if="showSuccessButtonTitle"
            color="primary"
            variant="flat"
            type="submit"
            :loading="loading"
          >
            {{ showSuccessButtonTitle }}
          </v-btn>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>
<style scoped>
  :deep(.v-tabs-window) {
    overflow: hidden !important;
  }
  :deep(.customize) {
    --da0d4328: auto !important;
  }
</style>
