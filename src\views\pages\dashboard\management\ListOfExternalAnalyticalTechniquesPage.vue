<script setup lang="ts">
  import type { BreadcrumbI } from '@/components/shared/BaseBreadcrumb.vue';
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import CustomTable from '@/components/shared/CustomTable.vue';
  import TopPageBreadcrumb from '@/components/shared/TopPageBreadcrumb.vue';
  import LoaderWrapper from '@/layouts/dashboard/LoaderWrapper.vue';
  import { Technique, TechniqueType, useTechniquesStore } from '@/stores/techniques';
  import { toLocale } from '@/utils/locales';
  import { EditOutlined, EyeOutlined, ReloadOutlined, SearchOutlined } from '@ant-design/icons-vue';
  import { useDebounceFn } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue';
  import { onBeforeRouteUpdate, useRouter } from 'vue-router';
  import type { Header } from 'vue3-easy-data-table';
  import AnalyticalTechniqueModal from './components/AnalyticalTechniqueModal.vue';
  import { isAllowed } from '@/utils/directive/isAllowed';
  import { notification } from 'ant-design-vue';
  const ConfirmRef = ref<undefined | typeof ConfirmDlg>(undefined);

  const router = useRouter();
  const techniquesStore = useTechniquesStore();
  const { options, loading, totalItems, techniques, search, loadingTechnique } =
    storeToRefs(techniquesStore);

  const loadFromServer = async () => {
    techniquesStore.getAll(TechniqueType.EXTERNAL);
  };

  const debouncedSearch = useDebounceFn(() => {
    if (loading.value === false) loadFromServer();
  }, 500);

  const baseDataLoaded = ref(false);
  const havePermision = ref<boolean>(false);
  const missingPermison = ref<string>();
  onMounted(async () => {
    await checkPermision();
    techniquesStore.setParamsFromLocation();
    debouncedSearch();
    baseDataLoaded.value = true;
  });

  const checkPermision = async () => {
    if (isAllowed(['add_edit_techniques'])) {
      havePermision.value = true;
    } else {
      missingPermison.value = 'add_edit_techniques';
      havePermision.value = false;
    }
  };
  watch(
    options,
    () => {
      if (loading.value === false) debouncedSearch();
    },
    { deep: true }
  );

  watch(search, () => {
    if (loading.value === false) debouncedSearch();
  });

  onBeforeRouteUpdate((to, from, next) => {
    if (to.path === from.path && to.query !== from.query) {
      techniquesStore.setParamsFromLocation();
      debouncedSearch();
    }

    next();
  });

  onBeforeUnmount(() => {
    techniquesStore.search = '';
  });

  const headers: Header[] = [
    // { text: 'ID', value: 'analytical_technique_id', sortable: true },
    { text: 'Název', value: 'name', sortable: true },
    { text: 'Nadřazená technika', value: 'parent_technique_id', sortable: false },
    { text: 'Zkratka', value: 'shortcut', sortable: true },
    // { text: 'Sub technika', value: 'parent_technique_id', sortable: true },
    { text: 'Kolona', value: 'using_column', sortable: false },
    { text: 'Sekvence', value: 'prefill_sample_sequence_name', sortable: false },
    { text: 'Stav', value: 'status', sortable: true },
    { text: 'Akce', value: 'action' }
  ];

  const breadcrumbItems = computed<BreadcrumbI[]>(() => {
    return [
      {
        title: 'Správa externích analytických technik',
        disabled: true,
        href: router.resolve({ name: 'ExternalTechniquesManagement' }).href
      }
    ];
  });
</script>

<template>
  <LoaderWrapper v-if="!baseDataLoaded" />
  <template v-else>
    <TopPageBreadcrumb
      title="Správa externích analytických technik"
      :_breadcrumbs="breadcrumbItems"
    />
    <v-row>
      <v-col cols="12" md="12">
        <v-card
          elevation="0"
          variant="outlined"
          class="withbg pageSize"
          :loading="loadingTechnique"
        >
          <v-card-item>
            <v-row justify="space-between" class="align-center">
              <v-col cols="12" md="9">
                <v-text-field
                  v-model="search"
                  type="text"
                  variant="outlined"
                  persistent-placeholder
                  placeholder="Hledat analytickou techniku"
                  hide-details
                >
                  <template #prepend-inner>
                    <SearchOutlined :style="{ fontSize: '14px' }" />
                  </template>
                </v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <div class="d-flex gap-2 justify-end flex-wrap">
                  <v-btn
                    variant="flat"
                    color="primary"
                    :disabled="!havePermision"
                    @click.prevent="
                      techniquesStore.showNewAnalyticalTechniqueModal(TechniqueType.EXTERNAL)
                    "
                  >
                    Přidat analytickou techniku
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </v-card-item>
          <v-divider></v-divider>
          <v-card-text>
            <CustomTable
              v-model:server-options="options"
              :server-items-length="totalItems"
              :loading="loading"
              :headers="headers"
              :items="[...techniques.values()]"
              multi-sort
            >
              <template #item-parent_technique_id="technique: Technique">
                <template v-if="technique.parent_technique_id">
                  <template v-if="typeof technique.parent_technique_id === 'number'">
                    <template v-if="techniques.get(technique.parent_technique_id)">
                      {{ techniques.get(technique.parent_technique_id)?.name }} ({{
                        techniques.get(technique.parent_technique_id)?.shortcut
                      }})
                    </template>
                    <template v-else>
                      Technika není načtena
                      <v-btn
                        icon
                        color="secondary"
                        variant="text"
                        rounded="sm"
                        :disabled="!havePermision"
                        @click.prevent="
                          techniquesStore.getTechniqueAndAddToTechniques(
                            technique.parent_technique_id
                          )
                        "
                      >
                        <ReloadOutlined />
                      </v-btn>
                    </template>
                  </template>
                  <template v-else>
                    {{ technique.parent_technique_id.name }} ({{
                      technique.parent_technique_id.shortcut
                    }})
                  </template>
                </template>
                <template v-else>
                  Hlavní technika (
                  <template
                    v-if="technique.sub_analytical_techniques.filter((t) => t.isActive).length > 0"
                  >
                    {{ technique.sub_analytical_techniques.filter((t) => t.isActive).length }}
                  </template>
                  <template v-else>Bez subtechnik</template>
                  )
                </template>
              </template>

              <template #item-created_at="{ created_at }">
                {{ toLocale(created_at) }}
              </template>

              <template #item-updated_at="{ updated_at }">
                {{ toLocale(updated_at) }}
              </template>

              <template #item-using_column="{ using_column }">
                <template v-if="using_column">Ano</template>
                <template v-else>Ne</template>
              </template>

              <template #item-prefill_sample_sequence_name="{ prefill_sample_sequence_name }">
                <template v-if="prefill_sample_sequence_name">Ano</template>
                <template v-else>Ne</template>
              </template>

              <template #item-status="{ status }">
                <v-chip v-if="status === 'active'" color="success" size="small" label>
                  Aktivní
                </v-chip>
                <v-chip v-if="status === 'inactive'" color="warning" size="small" label>
                  Neaktivní
                </v-chip>
                <v-chip v-if="status === 'deleted'" color="error" size="small" label>
                  Odstraněno
                </v-chip>
              </template>

              <template #item-action="{ analytical_technique_id }">
                <div class="operation-wrapper">
                  <v-btn
                    icon
                    color="secondary"
                    variant="text"
                    rounded="sm"
                    @click.prevent="techniquesStore.showPreviewModal(analytical_technique_id)"
                  >
                    <EyeOutlined />
                  </v-btn>
                  <v-btn
                    icon
                    color="primary"
                    variant="text"
                    rounded="sm"
                    :disabled="!havePermision"
                    @click.prevent="techniquesStore.showEditModal(analytical_technique_id)"
                  >
                    <EditOutlined />
                  </v-btn>
                  <!-- <v-btn
                  v-if="status !== 'deleted'"
                  icon
                  color="error"
                  variant="text"
                  rounded="sm"
                  @click.prevent="
                    async () => {
                      if (await ConfirmRef?.open('Potvrzení', 'Opravdu chcete smazat techniku?', { color: 'error', notclosable: true })) {
                        techniquesStore.deleteTechnique(analytical_technique_id);
                      }
                    }
                  "
                >
                  <DeleteOutlined />
                </v-btn> -->
                </div>
              </template>

              <template #expand="technique: Technique">
                <template
                  v-if="technique.sub_analytical_techniques.filter((t) => t.isActive).length > 0"
                >
                  <CustomTable
                    :headers="headers"
                    :items="technique.sub_analytical_techniques.filter((t) => t.isActive)"
                    hide-footer
                    hide-rows-per-page
                    table-class-name="customize-table customize"
                  >
                    <template
                      #item-parent_technique_id="{
                        parent_technique_id
                      }: {
                        parent_technique_id: number | Technique | null;
                      }"
                    >
                      <template v-if="parent_technique_id">
                        <template v-if="typeof parent_technique_id === 'number'">
                          <template v-if="techniques.get(parent_technique_id)">
                            {{ techniques.get(parent_technique_id)?.name }} ({{
                              techniques.get(parent_technique_id)?.shortcut
                            }})
                          </template>
                          <template v-else>
                            Technika není načtena
                            <v-btn
                              icon
                              color="secondary"
                              variant="text"
                              rounded="sm"
                              :disabled="!havePermision"
                              @click.prevent="
                                techniquesStore.getTechniqueAndAddToTechniques(parent_technique_id)
                              "
                            >
                              <ReloadOutlined />
                            </v-btn>
                          </template>
                        </template>
                        <template v-else>
                          {{ parent_technique_id.name }} ({{ parent_technique_id.shortcut }})
                        </template>
                      </template>
                      <template v-else>Hlavní technika</template>
                    </template>

                    <template #item-created_at="{ created_at }">
                      {{ toLocale(created_at) }}
                    </template>

                    <template #item-updated_at="{ updated_at }">
                      {{ toLocale(updated_at) }}
                    </template>

                    <template #item-using_column="{ using_column }">
                      <template v-if="using_column">Ano</template>
                      <template v-else>Ne</template>
                    </template>

                    <template #item-prefill_sample_sequence_name="{ prefill_sample_sequence_name }">
                      <template v-if="prefill_sample_sequence_name">Ano</template>
                      <template v-else>Ne</template>
                    </template>

                    <template #item-status="{ status }">
                      <v-chip v-if="status === 'active'" color="success" size="small" label>
                        Aktivní
                      </v-chip>
                      <v-chip v-if="status === 'inactive'" color="warning" size="small" label>
                        Neaktivní
                      </v-chip>
                      <v-chip v-if="status === 'deleted'" color="error" size="small" label>
                        Odstraněno
                      </v-chip>
                    </template>

                    <template #item-action="{ analytical_technique_id }">
                      <div class="operation-wrapper">
                        <v-btn
                          icon
                          color="secondary"
                          variant="text"
                          rounded="sm"
                          @click.prevent="techniquesStore.showPreviewModal(analytical_technique_id)"
                        >
                          <EyeOutlined />
                        </v-btn>
                        <v-btn
                          icon
                          color="primary"
                          variant="text"
                          rounded="sm"
                          :disabled="!havePermision"
                          @click.prevent="techniquesStore.showEditModal(analytical_technique_id)"
                        >
                          <EditOutlined />
                        </v-btn>
                      </div>
                    </template>
                  </CustomTable>
                </template>
                <div v-else style="padding: 15px">Technika nemá subtechniky</div>
              </template>
            </CustomTable>
          </v-card-text>
        </v-card>
      </v-col>

      <ConfirmDlg ref="ConfirmRef" />
      <AnalyticalTechniqueModal
        v-model:show="techniquesStore.showAnalyticalTechniqueModal"
        :type="TechniqueType.EXTERNAL"
      />
    </v-row>
  </template>
</template>
<style lang="less" scoped>
  :deep(.customize) {
    --da0d4328: auto !important;
  }
</style>
