<script setup lang="ts">
  import type { BreadcrumbI } from '@/components/shared/BaseBreadcrumb.vue';
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import TechniquesSelect from '@/components/shared/TechniquesSelect.vue';
  import TopPageBreadcrumb from '@/components/shared/TopPageBreadcrumb.vue';
  import LoaderWrapper from '@/layouts/dashboard/LoaderWrapper.vue';
  import { StandardStatus, useStandardsStore, type Standard } from '@/stores/standard/standards';
  import { TechniqueType } from '@/stores/techniques';
  import { toLocale } from '@/utils/locales';
  import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons-vue';
  import { storeToRefs } from 'pinia';
  import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue';
  import { useRouter } from 'vue-router';
  import { isAllowed } from '@/utils/directive/isAllowed';
  import { notification } from 'ant-design-vue';
  export interface CreateStandardI {
    name: string;
    analytical_techniques: number[];
  }

  const ConfirmRef = ref<undefined | typeof ConfirmDlg>(undefined);

  const router = useRouter();

  const standardsStore = useStandardsStore();
  const { standards, loading, standard } = storeToRefs(standardsStore);
  const havePermision = ref<boolean>(false);
  const missingPermison = ref<string>();
  const baseDataLoaded = ref(false);
  onMounted(async () => {
    await checkPermision();
    standardsStore.getStandards();
    baseDataLoaded.value = true;
  });
  const checkPermision = async () => {
    if (isAllowed(['add_edit_standards'])) {
      havePermision.value = true;
    } else {
      missingPermison.value = 'add_edit_standards';
      havePermision.value = false;
    }
  };
  const openedMenu = ref(true);
  const removeStandard = (v: number) => {
    standardsStore.deleteStandard(v);
  };

  const activateStandard = (standard: Standard) => {
    standardsStore.activateStandard(standard);
  };

  const setStandard = (id: number) => {
    standardsStore.setStandardById(id);
  };

  const refreshKey = ref(0);
  const refresh = () => {
    standards.value.forEach((standard) => {
      rolesSelections[standard.standard_id] = standard.analytical_techniques.map(
        (technique) => technique.analytical_technique_id
      );
    });

    refreshKey.value += 1;
  };

  const rolesSelections: Record<number, number[]> = reactive({});
  const showUserRoleModalState = ref(false);
  const showEditUserRoleModalState = ref(false);

  watch(standards, () => {
    refresh();
  });

  const createNewUserRole = reactive({
    standard_name: '',
    status: StandardStatus.ACTIVE
  });

  const submitNewUserRole = async () => {
    const res = await standardsStore.createStandard(createNewUserRole, null);
    if (res) {
      showUserRoleModalState.value = false;
      resetNewUserRoleModal();
    }
  };

  const submitEditUserRole = async () => {
    if (!standard.value) return;

    await standardsStore.updateStandard(standard.value);
    resetEditUserRoleModal();
  };

  const resetNewUserRoleModal = () => {
    showUserRoleModalState.value = false;

    createNewUserRole.standard_name = '';
    createNewUserRole.status = StandardStatus.ACTIVE;
  };

  const resetEditUserRoleModal = async () => {
    showEditUserRoleModalState.value = false;

    await standardsStore.getStandards();
    if (standard.value) setStandard(standard.value.standard_id);

    await nextTick();

    refresh();
  };

  const selectedAnalyticalTechnique = ref<number | undefined>(undefined);
  const selectedExternalAnalyticalTechnique = ref<number | undefined>(undefined);

  const addProjectAnalyticalTechnique = async () => {
    if (
      (selectedAnalyticalTechnique.value || selectedExternalAnalyticalTechnique.value) &&
      standard.value
    ) {
      const res = await standardsStore.addTechniqueToStandard(
        standard.value.standard_id,
        selectedAnalyticalTechnique.value ?? selectedExternalAnalyticalTechnique.value ?? 0
      );

      if (res) {
        selectedAnalyticalTechnique.value = undefined;
        selectedExternalAnalyticalTechnique.value = undefined;
        standardsStore.setStandardFromServerById(standard.value.standard_id);
      }
    }
  };

  watch(
    () => [selectedAnalyticalTechnique.value, selectedExternalAnalyticalTechnique.value],
    () => {
      addProjectAnalyticalTechnique();
    }
  );

  const removeProjectAnalyticalTechnique = async (analytical_technique_id: number) => {
    if (standard.value) {
      const res = await standardsStore.removeTechniqueFromStandard(
        standard.value.standard_id,
        analytical_technique_id
      );
      if (res) {
        standardsStore.setStandardFromServerById(standard.value.standard_id);
      }
    }
  };
  const externalTechniques = computed(
    () =>
      (refreshKey.value >= 0 &&
        standard.value?.analytical_techniques.filter(
          (technique) => technique.type === TechniqueType.EXTERNAL
        )) ??
      []
  );
  const internalTechniques = computed(
    () =>
      (refreshKey.value >= 0 &&
        standard.value?.analytical_techniques.filter(
          (technique) => technique.type === TechniqueType.INTERNAL
        )) ??
      []
  );

  const isMobile = computed(() => {
    return window.innerWidth <= 960;
  });

  const breadcrumbItems = computed<BreadcrumbI[]>(() => {
    return [
      {
        title: 'Správa externích analytických technik',
        disabled: true,
        href: router.resolve({ name: 'ExternalTechniquesManagement' }).href
      }
    ];
  });

  const orderedStandards = computed(() =>
    [...standards.value].sort((a, b) => (a.status === StandardStatus.ACTIVE ? -1 : 1))
  );
  const isReadOnly = computed(() => {
    return standard.value?.status === StandardStatus.DELETED;
  });
</script>
<template>
  <LoaderWrapper v-if="!baseDataLoaded" />
  <template v-else>
    <TopPageBreadcrumb title="Správa standardů" :_breadcrumbs="breadcrumbItems" />
    <v-row>
      <v-col cols="12" md="12">
        <v-card elevation="0" variant="outlined" class="withbg">
          <v-card-text>
            <v-layout
              class="rounded border border-secondary border-md br-0"
              style="min-height: 70vh"
            >
              <v-navigation-drawer
                v-if="havePermision"
                v-model="openedMenu"
                left
                elevation="0"
                rail-width="60"
                mobile-breakpoint="lg"
                app
                class="leftSidebar"
                expand-on-hover
                :width="325"
              >
                <!--
              
                  For the v-list-item component we need to add the following attributes if we want the same look as when managing permissions:
                  rounded="sm"
                  class="mb-0 br-0 pt-6 pb-6 border-secondary"
                  :border="'md'"
                  density="comfortable"
                -->

                <v-list aria-busy="true" aria-label="menu list" class="pb-0 pt-0">
                  <v-list-item
                    :border="'md'"
                    rounded="sm"
                    class="mb-0 br-0 pt-6 pb-6 border-secondary"
                    density="comfortable"
                  >
                    <v-list-item-title class="text-h5">Standardy</v-list-item-title>
                    <template v-if="isMobile" #append>
                      <v-btn icon variant="text" @click.prevent.stop="openedMenu = false">
                        Zavřít
                      </v-btn>
                    </template>
                  </v-list-item>

                  <v-list-item
                    v-for="standard of orderedStandards"
                    :key="standard.standard_id"
                    color="primary"
                    :value="standard"
                    :border="'md'"
                    @click="setStandard(standard.standard_id)"
                  >
                    <v-list-item-title>{{ standard.standard_name }}</v-list-item-title>
                    <template #append>
                      <v-btn
                        icon
                        variant="text"
                        :color="standard.status === StandardStatus.ACTIVE ? 'error' : 'success'"
                        @click.prevent.stop="
                          async () => {
                            if (standard.status === StandardStatus.ACTIVE) {
                              if (
                                await ConfirmRef?.open(
                                  'Potvrzení',
                                  'Opravdu chcete smazat standard?',
                                  {
                                    color: 'error',
                                    notclosable: true,
                                    zIndex: 1011
                                  }
                                )
                              ) {
                                removeStandard(standard.standard_id);
                              }
                            } else {
                              activateStandard(standard);
                            }
                          }
                        "
                      >
                        <v-icon
                          :icon="
                            standard.status === StandardStatus.ACTIVE ? 'mdi-close' : 'mdi-check'
                          "
                        ></v-icon>
                      </v-btn>
                    </template>
                  </v-list-item>
                </v-list>
              </v-navigation-drawer>

              <v-app-bar elevation="0" height="60">
                <v-btn
                  v-if="openedMenu"
                  class="text-secondary mr-3"
                  color="darkText"
                  icon
                  rounded="sm"
                  variant="text"
                  size="small"
                  @click.stop="openedMenu = !openedMenu"
                >
                  <MenuFoldOutlined :style="{ fontSize: '16px' }" />
                </v-btn>
                <v-btn
                  v-else
                  class="text-secondary ms-3"
                  color="darkText"
                  icon
                  rounded="sm"
                  variant="text"
                  size="small"
                  @click.stop="openedMenu = !openedMenu"
                >
                  <MenuUnfoldOutlined :style="{ fontSize: '16px' }" />
                </v-btn>
                <v-spacer />

                <v-btn
                  variant="flat"
                  color="primary"
                  :disabled="!havePermision"
                  @click="showUserRoleModalState = true"
                >
                  Přidat standard
                </v-btn>
              </v-app-bar>

              <v-main :key="refreshKey" class="d-flex align-center justify-center">
                <v-card v-if="standard" class="pageSizeCustom">
                  <v-card-title class="mt-5">
                    {{ standard.standard_name }}
                    <v-btn
                      icon
                      color="secondary"
                      variant="text"
                      :disabled="isReadOnly"
                      @click="showEditUserRoleModalState = true"
                    >
                      <v-icon>mdi-pencil</v-icon>
                    </v-btn>
                  </v-card-title>
                  <v-form class="pageSizeCustomBottom">
                    <v-card-text>
                      <v-row>
                        <v-col>
                          <v-row>
                            <v-col cols="12">
                              <span class="text-h5">Analytické techniky</span>

                              <TechniquesSelect
                                v-model:selected-techniques="selectedAnalyticalTechnique"
                                :disabled="isReadOnly"
                                :techniques="
                                  standard.analytical_techniques.filter(
                                    (technique) => technique.type === TechniqueType.INTERNAL
                                  )
                                "
                                :filter-options="[
                                  { column: 'type', value: [TechniqueType.INTERNAL] },
                                  { column: 'status', value: 'active' }
                                ]"
                              >
                                <template #append>
                                  <v-btn
                                    variant="flat"
                                    color="primary"
                                    :disabled="selectedAnalyticalTechnique === null"
                                    @click.prevent="addProjectAnalyticalTechnique()"
                                  >
                                    Přidat techniku
                                  </v-btn>
                                </template>
                              </TechniquesSelect>

                              <!-- Region: List of internal techniques  -->
                              <v-row
                                v-if="internalTechniques && internalTechniques.length > 0"
                                style="max-height: 360px; overflow-y: auto"
                                class="mt-4"
                              >
                                <v-col cols="12">
                                  <v-list lines="two">
                                    <v-list-item
                                      v-for="projectAnalyticalTechnique in internalTechniques"
                                      :key="projectAnalyticalTechnique.analytical_technique_id"
                                      class="customListItem"
                                      :title="''"
                                      variant="tonal"
                                      rounded
                                      :disabled="isReadOnly"
                                    >
                                      <div>
                                        <h6 class="text-subtitle-1 mb-0">
                                          {{
                                            projectAnalyticalTechnique.parent_technique_id === null
                                              ? 'Hlavní'
                                              : 'Sub'
                                          }}
                                          - {{ projectAnalyticalTechnique.name }} ({{
                                            projectAnalyticalTechnique.shortcut
                                          }}) -
                                          <v-chip
                                            v-if="projectAnalyticalTechnique.status === 'active'"
                                            color="success"
                                            size="small"
                                            label
                                          >
                                            Aktivní
                                          </v-chip>
                                          <v-chip
                                            v-if="projectAnalyticalTechnique.status === 'inactive'"
                                            color="warning"
                                            size="small"
                                            label
                                          >
                                            Neaktivní
                                          </v-chip>
                                          <v-chip
                                            v-if="projectAnalyticalTechnique.status === 'deleted'"
                                            color="error"
                                            size="small"
                                            label
                                          >
                                            Odstraněno
                                          </v-chip>
                                        </h6>
                                        <small class="text-h6 text-lightText">
                                          Typ: {{ projectAnalyticalTechnique.type }} | Vytvořena:
                                          {{ toLocale(projectAnalyticalTechnique.created_at) }}
                                        </small>
                                      </div>

                                      <template #append>
                                        <v-btn
                                          icon
                                          variant="text"
                                          color="error"
                                          @click.prevent.stop="
                                            async () => {
                                              if (
                                                await ConfirmRef?.open(
                                                  'Potvrzení',
                                                  'Opravdu chcete odebrat techniku ze standardu?',
                                                  {
                                                    color: 'error',
                                                    notclosable: true,
                                                    zIndex: 1011
                                                  }
                                                )
                                              ) {
                                                removeProjectAnalyticalTechnique(
                                                  projectAnalyticalTechnique.analytical_technique_id
                                                );
                                              }
                                            }
                                          "
                                        >
                                          <v-icon :icon="'mdi-close'"></v-icon>
                                        </v-btn>
                                      </template>
                                    </v-list-item>
                                  </v-list>
                                </v-col>
                              </v-row>
                              <v-row v-else class="mt-4">
                                <v-col cols="12">
                                  <span class="text-h6">Není vybrána žádná interní technika</span>
                                </v-col>
                              </v-row>
                              <!-- End region -->
                            </v-col>

                            <v-col cols="12">
                              <span class="text-h5">Externí analytické techniky</span>

                              <TechniquesSelect
                                v-model:selected-techniques="selectedExternalAnalyticalTechnique"
                                :disabled="isReadOnly"
                                :techniques="
                                  standard.analytical_techniques.filter(
                                    (technique) => technique.type === TechniqueType.EXTERNAL
                                  )
                                "
                                :filter-options="[
                                  { column: 'type', value: [TechniqueType.EXTERNAL] },
                                  { column: 'status', value: 'active' }
                                ]"
                              >
                                <template #append>
                                  <v-btn
                                    variant="flat"
                                    color="primary"
                                    :disabled="selectedExternalAnalyticalTechnique === null"
                                    @click.prevent="addProjectAnalyticalTechnique()"
                                  >
                                    Přidat techniku
                                  </v-btn>
                                </template>
                              </TechniquesSelect>
                              <!-- Region: List of external techniques  -->
                              <v-row
                                v-if="externalTechniques && externalTechniques.length > 0"
                                style="max-height: 360px; overflow-y: auto"
                                class="mt-4"
                              >
                                <v-col cols="12">
                                  <v-list lines="two">
                                    <v-list-item
                                      v-for="projectAnalyticalTechnique in externalTechniques"
                                      :key="projectAnalyticalTechnique.analytical_technique_id"
                                      class="customListItem"
                                      :title="''"
                                      variant="tonal"
                                      rounded
                                    >
                                      <div>
                                        <h6 class="text-subtitle-1 mb-0">
                                          {{
                                            projectAnalyticalTechnique.parent_technique_id === null
                                              ? 'Hlavní'
                                              : 'Sub'
                                          }}
                                          - {{ projectAnalyticalTechnique.name }} ({{
                                            projectAnalyticalTechnique.shortcut
                                          }}) -
                                          <v-chip
                                            v-if="projectAnalyticalTechnique.status === 'active'"
                                            color="success"
                                            size="small"
                                            label
                                          >
                                            Aktivní
                                          </v-chip>
                                          <v-chip
                                            v-if="projectAnalyticalTechnique.status === 'inactive'"
                                            color="warning"
                                            size="small"
                                            label
                                          >
                                            Neaktivní
                                          </v-chip>
                                          <v-chip
                                            v-if="projectAnalyticalTechnique.status === 'deleted'"
                                            color="error"
                                            size="small"
                                            label
                                          >
                                            Odstraněno
                                          </v-chip>
                                        </h6>
                                        <small class="text-h6 text-lightText">
                                          Typ: {{ projectAnalyticalTechnique.type }} | Vytvořena:
                                          {{ toLocale(projectAnalyticalTechnique.created_at) }}
                                        </small>
                                      </div>

                                      <template #append>
                                        <v-btn
                                          icon
                                          variant="text"
                                          color="error"
                                          @click.prevent.stop="
                                            async () => {
                                              if (
                                                await ConfirmRef?.open(
                                                  'Potvrzení',
                                                  'Opravdu chcete odebrat techniku ze standardu?',
                                                  {
                                                    color: 'error',
                                                    notclosable: true,
                                                    zIndex: 1011
                                                  }
                                                )
                                              ) {
                                                removeProjectAnalyticalTechnique(
                                                  projectAnalyticalTechnique.analytical_technique_id
                                                );
                                              }
                                            }
                                          "
                                        >
                                          <v-icon :icon="'mdi-close'"></v-icon>
                                        </v-btn>
                                      </template>
                                    </v-list-item>
                                  </v-list>
                                </v-col>
                              </v-row>
                              <v-row v-else class="mt-4">
                                <v-col cols="12">
                                  <span class="text-h6">Není vybrána žádná externí technika</span>
                                </v-col>
                              </v-row>
                              <!-- End region -->
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-form>
                </v-card>

                <span v-else class="text-h4">Není vybrán standard</span>

                <v-dialog v-model="showUserRoleModalState" class="customer-modal" :z-index="1010">
                  <v-card :loading="loading">
                    <v-form
                      ref="CreateNewUserRoleForm"
                      class="CreateNewUserRoleForm"
                      @submit.prevent="submitNewUserRole"
                    >
                      <v-card-title class="pa-5">
                        <span class="text-h5">Přidat standard</span>
                      </v-card-title>
                      <v-divider></v-divider>
                      <v-card-text>
                        <v-row>
                          <v-col>
                            <v-row>
                              <v-col cols="12">
                                <v-label class="mb-2">Název standardu</v-label>
                                <v-text-field
                                  v-model="createNewUserRole.standard_name"
                                  single-line
                                  placeholder="Zadejte název standardu"
                                  hide-details="auto"
                                  variant="outlined"
                                  required
                                  rounded="sm"
                                ></v-text-field>
                              </v-col>
                            </v-row>
                          </v-col>
                        </v-row>
                      </v-card-text>
                      <v-divider></v-divider>
                      <v-card-actions>
                        <v-spacer></v-spacer>
                        <v-btn color="error" variant="text" @click="resetNewUserRoleModal">
                          Zrušit
                        </v-btn>
                        <v-btn color="primary" variant="flat" type="submit" :loading="loading">
                          Přidat standard
                        </v-btn>
                      </v-card-actions>
                    </v-form>
                  </v-card>
                </v-dialog>

                <v-dialog
                  v-if="showEditUserRoleModalState && standard"
                  v-model="showEditUserRoleModalState"
                  class="customer-modal"
                  :z-index="1010"
                >
                  <v-card :loading="loading">
                    <v-form
                      ref="CreateNewUserRoleForm"
                      class="CreateNewUserRoleForm"
                      @submit.prevent="submitEditUserRole"
                    >
                      <v-card-title class="pa-5">
                        <span class="text-h5">Upravit standard</span>
                      </v-card-title>
                      <v-divider></v-divider>
                      <v-card-text>
                        <v-row>
                          <v-col>
                            <v-row>
                              <v-col cols="12">
                                <v-label class="mb-2">Název standardu</v-label>
                                <v-text-field
                                  v-model="standard.standard_name"
                                  single-line
                                  placeholder="Zadejte název standardu"
                                  hide-details="auto"
                                  variant="outlined"
                                  required
                                  rounded="sm"
                                ></v-text-field>
                              </v-col>
                            </v-row>
                          </v-col>
                        </v-row>
                      </v-card-text>
                      <v-divider></v-divider>
                      <v-card-actions>
                        <v-spacer></v-spacer>
                        <v-btn color="error" variant="text" @click="resetEditUserRoleModal">
                          Zrušit
                        </v-btn>
                        <v-btn color="primary" variant="flat" type="submit" :loading="loading">
                          Upravit standard
                        </v-btn>
                      </v-card-actions>
                    </v-form>
                  </v-card>
                </v-dialog>
              </v-main>
            </v-layout>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <ConfirmDlg ref="ConfirmRef" />
  </template>
</template>
<style lang="less" scoped>
  .v-list-item.customListItem {
    margin-top: 10px;
    margin-bottom: 10px;
  }
</style>
