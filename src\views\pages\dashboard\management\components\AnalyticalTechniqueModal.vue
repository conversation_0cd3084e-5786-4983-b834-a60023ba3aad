<script setup lang="ts">
  import { Technique, TechniqueType, useTechniquesStore } from '@/stores/techniques';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { useDebounceFn, useVModel } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import { computed, onMounted, ref, watch, type PropType } from 'vue';

  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    },
    type: {
      type: String as PropType<TechniqueType>,
      required: true
    }
  });

  const techniquesStore = useTechniquesStore();
  const { modalOptions, loading } = storeToRefs(techniquesStore);

  const CreateAnalyticalTechniqueForm = ref();
  async function submitFormToValidate() {
    if (CreateAnalyticalTechniqueForm.value.isValid && modalOptions.value) {
      switch (true) {
        case modalOptions.value.isEditing && !modalOptions.value.isCreating:
          return techniquesStore.updateAnalyticalTechnique(props.type);
        case !modalOptions.value.isEditing && modalOptions.value.isCreating:
          return techniquesStore.createAnalyticalTechnique(props.type);

        default:
          return 'Náhled techniky';
      }
    } else {
      if (modalOptions.value && modalOptions.value.newData) {
        modalOptions.value.newData.confirm = false;
      }
    }
  }

  const showState = useVModel(props, 'show');
  const showTitle = computed(() => {
    if (modalOptions.value === undefined) {
      return 'Náhled techniky';
    }

    switch (true) {
      case modalOptions.value.isEditing && !modalOptions.value.isCreating:
        return 'Editace techniky';
      case !modalOptions.value.isEditing && modalOptions.value.isCreating:
        return 'Nová technika';
      default:
        return 'Náhled techniky';
    }
  });
  const showSuccessButtonTitle = computed(() => {
    if (modalOptions.value === undefined) {
      return false;
    }

    switch (true) {
      case modalOptions.value.isEditing && !modalOptions.value.isCreating:
        return 'Upravit techniku';
      case !modalOptions.value.isEditing && modalOptions.value.isCreating:
        return 'Přidat techniku';
      default:
        return false;
    }
  });

  const onlyPreview = computed(() => {
    return modalOptions.value?.isCreating === false && modalOptions.value?.isEditing === false;
  });

  const search = ref('');
  const items = ref<Technique[]>([]);
  const selected = ref<string | Technique>('');

  onMounted(async () => {
    debouncedTagsSearch();
    techniquesStore.fixedFilterOptions = [];
    techniquesStore.fixedFilterOptions.push(
      { column: 'type', value: props.type },
      { column: 'status', value: 'active' },
      { column: 'parent_technique_id', value: null }
    );
    techniquesStore.fixedFilterOptions.push();

    const res = await techniquesStore.getAll([], false);
    if (res) {
      const combinedItems = [...items.value, ...res];
      const uniqueItems = combinedItems.reduce((acc, current) => {
        const x = acc.find(
          (item) => item.analytical_technique_id === current.analytical_technique_id
        );
        if (!x) {
          return acc.concat([current]);
        } else {
          return acc;
        }
      }, [] as Technique[]);
      items.value = uniqueItems;
    }
  });

  watch(showState, async (val) => {
    if (val === true) {
      if (modalOptions.value?.isCreating) {
        selected.value = modalOptions.value?.newData?.name ?? '';
      } else {
        if (typeof modalOptions.value?.baseData?.parent_technique_id === 'number') {
          selected.value =
            (await techniquesStore.getTechnique(modalOptions.value.baseData.parent_technique_id)) ??
            '';
        } else {
          selected.value = modalOptions.value?.baseData?.name ?? '';
        }
      }
    }
  });

  watch(search, () => {
    debouncedTagsSearch();
  });

  const debouncedTagsSearch = useDebounceFn(async () => {
    if (!modalOptions.value?.isCreating) return;

    if (loading.value === false && search.value !== '') {
      techniquesStore.fixedFilterOptions = [];
      techniquesStore.fixedFilterOptions.push(
        { column: 'type', value: props.type },
        { column: 'status', value: 'active' },
        { column: 'parent_technique_id', value: null }
      );
      techniquesStore.fixedFilterOptions.push({
        column: 'search',
        value: [{ term: search.value, columns: ['name'], type: 'AND' }]
      });

      const res = await techniquesStore.getAll([], false);
      if (res) {
        const combinedItems = [...items.value, ...res];
        const uniqueItems = combinedItems.reduce((acc, current) => {
          const x = acc.find(
            (item) => item.analytical_technique_id === current.analytical_technique_id
          );
          if (!x) {
            return acc.concat([current]);
          } else {
            return acc;
          }
        }, [] as Technique[]);
        items.value = uniqueItems;
      }
    }
  }, 350);

  watch(selected, () => {
    if (!modalOptions.value?.isCreating) return;

    if (modalOptions.value?.newData) {
      if (typeof selected.value === 'string') {
        modalOptions.value.newData.name = selected.value;
        modalOptions.value.newData.parent_technique_id = null;
        modalOptions.value.newData.sub_technique_name = undefined;
      } else if (selected.value instanceof Technique) {
        modalOptions.value.newData.name = undefined;
        modalOptions.value.newData.parent_technique_id = selected.value.analytical_technique_id;
      } else {
        modalOptions.value.newData.name = '';
        modalOptions.value.newData.parent_technique_id = null;
      }
    }
  });

  watch(
    () => modalOptions.value?.newData,
    (newData) => {
      if (newData && modalOptions.value?.isCreating) {
        localStorage.setItem('analyticalTechniquesStore', JSON.stringify(newData));
      }
    },
    { deep: true }
  );
</script>
<template>
  <v-dialog v-model="showState" class="customer-modal" :z-index="1010">
    <v-card :loading="loading">
      <v-form
        v-if="modalOptions?.newData"
        ref="CreateAnalyticalTechniqueForm"
        class="createAnalyticalTechniqueForm"
        :readonly="onlyPreview"
        @submit.prevent="submitFormToValidate"
      >
        <v-card-title class="pa-5">
          <span class="text-h5">{{ showTitle }}</span>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col>
              <v-row>
                <template v-if="modalOptions.isCreating">
                  <v-col cols="12" sm="6">
                    <v-label class="mb-2">Název techniky</v-label>
                    <v-combobox
                      v-model="selected"
                      v-model:search="search"
                      :hide-no-data="false"
                      :items="items"
                      :rules="itemRequiredRule"
                      color="primary"
                      rounded="sm"
                      variant="outlined"
                      hide-details="auto"
                      persistent-hint
                      small-chips
                      max="5"
                      clearable
                      :loading="loading"
                      :item-value="'analytical_technique_id'"
                      :item-title="'name'"
                    >
                      <template #no-data>
                        <v-list-item>
                          <v-list-item-title>
                            <template v-if="search.length > 0">
                              Žádné výsledky pro hledaný výraz "
                              <strong>{{ search }}</strong>
                              ".
                              <br />
                              Pro přidání klikněte na
                              <kbd>enter</kbd>
                              .
                            </template>
                            <template v-else>Žádné techniky nejsou k dispozici.</template>
                          </v-list-item-title>
                        </v-list-item>
                      </template>
                    </v-combobox>
                  </v-col>

                  <v-col v-if="typeof selected === 'string'" cols="12" sm="6">
                    <v-label class="mb-2">Název subtechniky</v-label>
                    <v-text-field
                      v-model="modalOptions.newData.sub_technique_name"
                      :rules="[]"
                      single-line
                      placeholder="Zadejte název subtechniky"
                      hide-details="auto"
                      variant="outlined"
                      rounded="sm"
                      disabled
                    ></v-text-field>
                  </v-col>
                  <v-col v-if="!(typeof selected === 'string')" cols="12" sm="6">
                    <v-label class="mb-2">Název subtechniky</v-label>
                    <v-text-field
                      v-model="modalOptions.newData.sub_technique_name"
                      :rules="itemRequiredRule"
                      single-line
                      placeholder="Zadejte název subtechniky"
                      hide-details="auto"
                      variant="outlined"
                      rounded="sm"
                    ></v-text-field>
                  </v-col>
                </template>
                <template v-else>
                  <v-col cols="12" sm="6">
                    <v-label class="mb-2">Název techniky</v-label>
                    <v-text-field
                      v-model="modalOptions.newData.name"
                      :rules="itemRequiredRule"
                      single-line
                      placeholder="Zadejte název techniky"
                      hide-details="auto"
                      variant="outlined"
                      rounded="sm"
                    ></v-text-field>
                  </v-col>
                </template>

                <v-col cols="12" sm="4">
                  <v-label class="mb-2">Zkratka</v-label>
                  <v-text-field
                    v-model="modalOptions.newData.shortcut"
                    :rules="itemRequiredRule"
                    single-line
                    placeholder="Zadejte zkratku"
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></v-text-field>
                </v-col>

                <v-col cols="12" sm="4">
                  <v-label class="mb-2">Kolona</v-label>
                  <v-checkbox
                    v-model="modalOptions.newData.using_column"
                    color="primary"
                    hide-details
                    label="Používá technika kolonu? "
                  ></v-checkbox>
                </v-col>
                <v-col cols="12" sm="4">
                  <v-label class="mb-2">Technika bude předvypňovat sekvenci</v-label>
                  <v-checkbox
                    v-model="modalOptions.newData.prefill_sample_sequence_name"
                    color="primary"
                    hide-details
                    label="Používá technika sekvenci? "
                  ></v-checkbox>
                </v-col>

                <v-col v-if="!modalOptions.isCreating" cols="12">
                  <v-label class="mb-2">Status</v-label>
                  <v-autocomplete
                    v-model="modalOptions.newData.status"
                    :readonly="onlyPreview"
                    :items="[
                      { value: 'active', title: 'Aktivní' },
                      { value: 'deleted', title: 'Odstraněný' }
                    ]"
                    rounded="sm"
                    color="primary"
                    single-line
                    hide-details
                    variant="outlined"
                    :no-data-text="'Žádná další políčka'"
                  ></v-autocomplete>
                </v-col>
                <v-col v-if="modalOptions.isCreating" cols="12">
                  <div class="d-flex justify-space-between gap-2">
                    <div class="pb-4">
                      <h6 class="text-subtitle-1 mb-0">Potvrzení přidání</h6>
                    </div>
                    <v-switch
                      v-model="modalOptions.newData.confirm"
                      color="primary"
                      class="switchRight"
                      hide-details
                    ></v-switch>
                  </div>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" variant="text" @click="techniquesStore.resetModal()">Zrušit</v-btn>
          <v-btn
            v-if="showSuccessButtonTitle"
            color="primary"
            variant="flat"
            type="submit"
            :loading="loading"
          >
            {{ showSuccessButtonTitle }}
          </v-btn>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>
