<script setup lang="ts">
  import type { BreadcrumbI } from '@/components/shared/BaseBreadcrumb.vue';
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import CustomTable from '@/components/shared/CustomTable.vue';
  import TopPageBreadcrumb from '@/components/shared/TopPageBreadcrumb.vue';
  import { responsiveCardClass } from '@/config';
  import LoaderWrapper from '@/layouts/dashboard/LoaderWrapper.vue';
  import { useUsersStore } from '@/stores/users';
  import { toLocale } from '@/utils/locales';
  import { EditOutlined, EyeOutlined, SearchOutlined } from '@ant-design/icons-vue';
  import { useDebounceFn } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import { computed, onMounted, ref, watch } from 'vue';
  import { useRouter } from 'vue-router';
  import type { Header } from 'vue3-easy-data-table';
  import UserModal from './components/UserModal.vue';
  import UserRoleModal from './components/UserRoleModal.vue';
  import { isAllowed } from '@/utils/directive/isAllowed';
  import { notification } from 'ant-design-vue';
  const ConfirmRef = ref<undefined | typeof ConfirmDlg>(undefined);

  const router = useRouter();
  const usersStore = useUsersStore();
  const { options, loading, totalItems, users, search } = storeToRefs(usersStore);

  const loadFromServer = async () => {
    usersStore.getAll();
  };

  const debouncedSearch = useDebounceFn(() => {
    if (loading.value === false) {
      loadFromServer();
    }
  }, 350);

  const baseDataLoaded = ref(false);
  const havePermisionCreate = ref<boolean>(false);
  const havePermisionRoles = ref<boolean>(false);
  const missingPermisonCreate = ref<string>();
  const missingPermisonRoles = ref<string>();
  onMounted(async () => {
    await checkPermision();
    usersStore.setParamsFromLocation();
    debouncedSearch();
    baseDataLoaded.value = true;
  });
  const checkPermision = async () => {
    if (isAllowed(['add_edit_users'])) {
      havePermisionCreate.value = true;
    } else {
      missingPermisonCreate.value = 'add_edit_roles_permissions';
      havePermisionCreate.value = false;
    }
    if (isAllowed(['add_edit_roles_permissions'])) {
      havePermisionRoles.value = true;
    } else {
      missingPermisonRoles.value = 'manage_roles';
      havePermisionRoles.value = false;
    }
    if (!havePermisionCreate.value || !havePermisionRoles.value) {
      const buildMessage = () => {
        let message = '';
        if (!havePermisionCreate.value) {
          message += 'přidávání a editace uživatelů, ';
        }
        if (!havePermisionRoles.value) {
          message += 'správa rolí, ';
        }
        return message;
      };
    }
  };
  watch(
    options,
    () => {
      if (loading.value === false) debouncedSearch();
    },
    { deep: true }
  );

  watch(search, () => {
    if (loading.value === false && search.value !== undefined) debouncedSearch();
  });

  const headers: Header[] = [
    { text: 'ID', value: 'user_id', sortable: true },
    { text: 'Jméno', value: 'first_name', sortable: true },
    { text: 'Příjmení', value: 'last_name', sortable: true },
    { text: 'Email', value: 'user_email', sortable: true },
    { text: 'Stav', value: 'status', sortable: true },
    { text: 'Akce', value: 'action' }
  ];

  const breadcrumbItems = computed<BreadcrumbI[]>(() => {
    return [
      {
        title: 'Správa uživatelů',
        disabled: true,
        href: router.resolve({ name: 'UsersManagement' }).href
      }
    ];
  });
</script>
<template>
  <LoaderWrapper v-if="!baseDataLoaded" />
  <template v-else>
    <TopPageBreadcrumb title="Správa uživatelů" :_breadcrumbs="breadcrumbItems" />
    <v-row>
      <v-col cols="12" md="12">
        <v-card elevation="0" variant="outlined" class="withbg pageSize">
          <v-card-item :class="responsiveCardClass">
            <v-row justify="space-between" class="align-center">
              <v-col cols="12" md="7">
                <v-text-field
                  v-model="search"
                  type="text"
                  variant="outlined"
                  persistent-placeholder
                  placeholder="Hledat uživatele"
                  hide-details
                >
                  <template #prepend-inner>
                    <SearchOutlined :style="{ fontSize: '14px' }" />
                  </template>
                </v-text-field>
              </v-col>
              <v-col cols="12" md="5">
                <div class="d-flex gap-2 justify-end flex-wrap">
                  <v-btn
                    variant="flat"
                    color="primary"
                    :disabled="!havePermisionRoles"
                    @click.prevent="usersStore.showRoleModal()"
                  >
                    <template #prepend>
                      <SearchOutlined />
                    </template>
                    Správa rolí
                  </v-btn>
                  <v-btn
                    variant="flat"
                    color="primary"
                    :disabled="!havePermisionCreate"
                    @click.prevent="usersStore.showCreateModal()"
                  >
                    Přidat uživatele
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </v-card-item>
          <v-divider></v-divider>
          <v-card-text :class="responsiveCardClass">
            <CustomTable
              v-model:server-options="options"
              :server-items-length="totalItems"
              :loading="loading"
              :headers="headers"
              :items="[...users.values()]"
              multi-sort
            >
              <template #item-created_at="{ created_at }">
                {{ toLocale(created_at) }}
              </template>

              <template #item-updated_at="{ updated_at }">
                {{ toLocale(updated_at) }}
              </template>

              <template #item-status="{ status }">
                <v-chip v-if="status === 'active'" color="success" size="small" label>
                  Aktivní
                </v-chip>
                <v-chip v-if="status === 'inactive'" color="warning" size="small" label>
                  Neaktivní
                </v-chip>
                <v-chip v-if="status === 'deleted'" color="error" size="small" label>
                  Smazaný uživatel
                </v-chip>
              </template>
              <template #item-action="{ user_id }">
                <div class="operation-wrapper">
                  <v-btn
                    icon
                    color="secondary"
                    variant="text"
                    rounded="sm"
                    @click.prevent="usersStore.showPreviewModal(user_id)"
                  >
                    <EyeOutlined />
                  </v-btn>
                  <v-btn
                    icon
                    color="primary"
                    variant="text"
                    rounded="sm"
                    :disabled="!havePermisionCreate"
                    @click.prevent="usersStore.showEditModal(user_id)"
                  >
                    <EditOutlined />
                  </v-btn>
                </div>
              </template>
            </CustomTable>
          </v-card-text>
        </v-card>
      </v-col>

      <ConfirmDlg ref="ConfirmRef" />
      <UserModal v-if="usersStore.showUserModal" v-model:show="usersStore.showUserModal" />
      <UserRoleModal v-model:show="usersStore.showUserRoleModal" />
    </v-row>
  </template>
</template>
