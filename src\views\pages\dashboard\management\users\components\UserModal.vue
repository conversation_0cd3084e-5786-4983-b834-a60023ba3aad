<script setup lang="ts">
  import UserSelect from '@/components/shared/UserSelect.vue';
  import CustomTable from '@/components/shared/CustomTable.vue';
  import { responsiveCardClass } from '@/config';
  import type { User } from '@/stores/auth';
  import { useUsersStore } from '@/stores/users';
  import { emailRules, itemRequiredRule, lastRules, passwordRules } from '@/utils/formValidation';
  import { toLocale } from '@/utils/locales';
  import { useDebounceFn, useVModel } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import { computed, onMounted, ref, watch, watchEffect } from 'vue';
  import { isAllowed } from '@/utils/directive/isAllowed';
  import { notification } from 'ant-design-vue';
  import { forEach } from 'lodash';
  // stores
  const usersStore = useUsersStore();
  const {
    user,
    userModalOptions,
    permissions,
    loading,
    roles,
    userProjectsOptions,
    userProjectsTotalItems,
    userProjects,
    dummyEdit
  } = storeToRefs(usersStore);

  const CreateNewUserForm = ref();
  async function submitFormToValidate() {
    if (CreateNewUserForm.value.isValid && userModalOptions.value) {
      if (onlyEdit.value && password.value) {
        if (userModalOptions.value?.user?.user_id !== undefined) {
          usersStore.updateUserPassword(userModalOptions.value.user.user_id, password.value);
        }
      }
      switch (true) {
        case userModalOptions.value.isEditing && !userModalOptions.value.isCreating:
          return usersStore.updateUser();
        case !userModalOptions.value.isEditing &&
          userModalOptions.value.isCreating &&
          password.value !== undefined:
          return usersStore.createUser(password.value);

        default:
          return 'Náhled uživatele';
      }
    } else {
      if (userModalOptions.value && userModalOptions.value.newUserData) {
        userModalOptions.value.newUserData.confirm = false;
      }
    }
  }
  const show2 = ref(false);
  const havePermision = ref<boolean>(false);
  const missingPermison = ref<string>();
  const hasCorrectRole = ref();
  onMounted(async () => {
    usersStore.getAllPermissions();
    usersStore.getAllRoles();
    if (!userModalOptions.value?.isEditing) {
      hasCorrectRole.value = false;
    }
    if (usersStore.user && userModalOptions.value?.isEditing) {
      const userPermissionIds =
        usersStore.user.permissions?.map((p) => p.system_permission_id) ?? [];
      hasCorrectRole.value = usersStore.hasPermissionByName(
        userPermissionIds,
        'edit_technological_department'
      );
    }
    if (onlyEdit.value || onlyCreate.value) {
      await checkRolesForPermision();
    }
    await checkPermision();
  });
  const checkPermision = async () => {
    if (isAllowed(['add_edit_roles_permissions'])) {
      havePermision.value = true;
    } else {
      missingPermison.value = 'add_edit_roles_permissions';
      havePermision.value = false;
    }
    if (!havePermision.value) {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description:
          'Nemáte oprávnění pro přidávání a editaci rolí a přístupů: ' + missingPermison.value + '.'
      });
    }
  };
  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    }
  });

  const showState = useVModel(props, 'show');
  const password = ref<string>('');
  const showTitle = computed(() => {
    if (userModalOptions.value === undefined) {
      return 'Náhled uživatele';
    }

    switch (true) {
      case userModalOptions.value.isEditing && !userModalOptions.value.isCreating:
        return 'Editace uživatele';
      case !userModalOptions.value.isEditing && userModalOptions.value.isCreating:
        return 'Nový uživatel';
      default:
        return 'Náhled uživatele';
    }
  });
  const showSuccessButtonTitle = computed(() => {
    if (userModalOptions.value === undefined) {
      return false;
    }

    switch (true) {
      case userModalOptions.value.isEditing && !userModalOptions.value.isCreating:
        return 'Upravit uživatele';
      case !userModalOptions.value.isEditing && userModalOptions.value.isCreating:
        return 'Přidat uživatele';
      default:
        return false;
    }
  });

  const onlyPreview = computed(() => {
    return (
      userModalOptions.value?.isCreating === false && userModalOptions.value?.isEditing === false
    );
  });

  const onlyEdit = computed(() => {
    return (
      userModalOptions.value?.isCreating === false && userModalOptions.value?.isEditing === true
    );
  });
  const onlyCreate = computed(() => {
    return (
      userModalOptions.value?.isCreating === true && userModalOptions.value?.isEditing === false
    );
  });
  const tab = ref('one');

  watch(
    userProjectsOptions,
    () => {
      if (loading.value === false) debouncedSearch();
    },
    { deep: true }
  );

  const debouncedSearch = useDebounceFn(async () => {
    if (loading.value === false && userModalOptions.value?.user?.user_id !== undefined) {
      await usersStore.getAllUserProjects(userModalOptions.value.user.user_id);
    }
  }, 350);

  watch(tab, (newVal) => {
    if (newVal === 'two') {
      debouncedSearch();
    }
  });

  const reload = async (user_id: number) => {
    const u = await usersStore.getUser(user_id);
    if (u) {
      user.value = u;
    }
  };

  const addUser = async (laborant_id: number) => {
    if (userModalOptions.value?.user?.user_id === undefined) return;

    await usersStore.addLaboratoryToUser(userModalOptions.value.user.user_id, laborant_id);
    await reload(userModalOptions.value.user.user_id);
  };

  const removeUser = async (laborant_id: number) => {
    if (userModalOptions.value?.user?.user_id === undefined) return;

    await usersStore.deleteLaboratoryFromUser(userModalOptions.value.user.user_id, laborant_id);
    await reload(userModalOptions.value.user.user_id);
  };

  const addedPermissions = ref<number[]>([]);
  const rolePermissionsMap = ref<Record<number, number[]>>({});

  const checkRolesForPermision = async () => {
    if (userModalOptions.value?.newUserData?.roles) {
      if (userModalOptions.value.newUserData.roles.length > 0) {
        userModalOptions.value.newUserData.roles.forEach((role) => {
          const rolePermissions = roles.value.find((r) => r.system_role_id === role);
          if (rolePermissions) {
            rolePermissionsMap.value[role] = rolePermissions.newPermissions;
            forEach(rolePermissions.newPermissions, (permission) => {
              if (!userModalOptions.value?.newUserData?.permissions?.includes(permission)) {
                userModalOptions.value?.newUserData?.permissions?.push(permission);
                addedPermissions.value.push(permission);
              }
              if (
                userModalOptions.value?.newUserData?.permissions === undefined &&
                userModalOptions.value?.newUserData
              ) {
                userModalOptions.value.newUserData.permissions = [permission];
                addedPermissions.value.push(permission);
              } else if (onlyEdit.value || dummyEdit) {
                addedPermissions.value.push(permission);
              }
            });
          }
        });
      }
    }
  };

  watchEffect(async () => {
    if (userModalOptions.value?.newUserData?.roles) {
      const currentRoles = userModalOptions.value.newUserData.roles;
      const previousRoles = Object.keys(rolePermissionsMap.value).map(Number);
      const removedRoles = previousRoles.filter((role) => !currentRoles.includes(role));
      removedRoles.forEach((role) => {
        const permissionsToRemove = rolePermissionsMap.value[role];
        if (userModalOptions.value?.newUserData?.permissions) {
          userModalOptions.value.newUserData.permissions =
            userModalOptions.value.newUserData.permissions.filter(
              (permission) => !permissionsToRemove.includes(permission)
            );
          addedPermissions.value = addedPermissions.value.filter(
            (permission) => !permissionsToRemove.includes(permission)
          );
          delete rolePermissionsMap.value[role];
        }
      });

      await checkRolesForPermision();
    }
  });

  const closablePermissions = computed(() => {
    if (userModalOptions.value?.newUserData?.permissions === undefined) return [];
    return userModalOptions.value?.newUserData?.permissions.map((permission) => {
      const isAddedPermission = addedPermissions.value.includes(permission);
      return {
        value: permission,
        closable: !isAddedPermission,
        color: isAddedPermission ? 'secondary' : 'primary'
      };
    });
  });

  const closeChipCustom = (item: number) => {
    const index = userModalOptions.value?.newUserData?.permissions?.indexOf(item);
    if (index !== undefined && index !== -1) {
      userModalOptions.value?.newUserData?.permissions?.splice(index, 1);
    }
  };

  const resetPassword = async () => {
    password.value = await usersStore.generatePassowrd();
  };
  const checkShortcuts = async () => {
    if (
      userModalOptions.value?.newUserData?.first_name &&
      userModalOptions.value?.newUserData?.last_name &&
      (userModalOptions.value?.newUserData.name_shortcut === '' ||
        userModalOptions.value?.newUserData.name_shortcut === null)
    ) {
      if (
        usersStore.hasPermissionByName(
          userModalOptions.value.newUserData.permissions,
          'edit_technological_department'
        )
      ) {
        userModalOptions.value.newUserData.name_shortcut = usersStore.generateInitials(
          userModalOptions.value.newUserData.first_name,
          userModalOptions.value.newUserData.last_name
        );
      }
    }
  };
  const calculateInitialsReadOnly = () => {
    if (onlyPreview.value) {
      return true;
    }

    if (
      userModalOptions.value?.user?.name_shortcut &&
      userModalOptions.value.user.name_shortcut.length > 0 &&
      onlyEdit.value
    ) {
      return true;
    }
    if (
      usersStore.hasPermissionByName(
        userModalOptions.value?.newUserData?.permissions,
        'edit_technological_department'
      ) &&
      onlyEdit.value
    ) {
      return false;
    }
    if (
      usersStore.hasPermissionByName(
        userModalOptions.value?.newUserData?.permissions,
        'edit_technological_department'
      ) &&
      onlyCreate.value
    ) {
      return false;
    }
    return true;
  };

  watch(
    () => userModalOptions.value?.newUserData,
    (newData) => {
      if (newData && userModalOptions.value?.isCreating) {
        localStorage.setItem('usersStore', JSON.stringify(newData));
      }
    },
    { deep: true }
  );
  watch(
    () => userModalOptions.value?.newUserData?.name_shortcut,
    (newData) => {
      if (
        newData &&
        userModalOptions.value &&
        userModalOptions.value.newUserData &&
        userModalOptions.value.newUserData.name_shortcut
      ) {
        userModalOptions.value.newUserData.name_shortcut = newData.toUpperCase();
      }
    }
  );
</script>

<template>
  <v-dialog v-model="showState" class="customer-modal" :z-index="1010">
    <v-card :loading="loading" flat>
      <v-tabs v-if="onlyEdit" v-model="tab">
        <v-tab value="one">Správa uživatele</v-tab>
        <v-tab value="two">Projekty</v-tab>
        <v-tab value="three">Laboratorní pracovníci</v-tab>
      </v-tabs>
      <v-tabs-window v-model="tab">
        <v-tabs-window-item value="one">
          <v-form
            v-if="userModalOptions?.newUserData"
            ref="CreateNewUserForm"
            class="createNewUserForm"
            :readonly="onlyPreview"
            autocomplete="off"
            @submit.prevent="submitFormToValidate"
          >
            <v-card-title :class="responsiveCardClass">
              <span class="text-h5">{{ showTitle }}</span>
            </v-card-title>
            <v-divider></v-divider>
            <v-card-text :class="responsiveCardClass">
              <v-row>
                <v-col>
                  <v-row>
                    <v-col cols="12" sm="4">
                      <v-label class="mb-2">Jméno</v-label>
                      <v-text-field
                        v-model="userModalOptions.newUserData.first_name"
                        :rules="itemRequiredRule"
                        single-line
                        placeholder="Zadejte jméno"
                        hide-details="auto"
                        variant="outlined"
                        required
                        rounded="sm"
                        @blur.prevent.stop="checkShortcuts()"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" sm="4">
                      <v-label class="mb-2">Příjmení</v-label>
                      <v-text-field
                        v-model="userModalOptions.newUserData.last_name"
                        :rules="lastRules"
                        single-line
                        placeholder="Zadejte příjmení"
                        hide-details="auto"
                        variant="outlined"
                        rounded="sm"
                        @blur.prevent.stop="checkShortcuts()"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" sm="4">
                      <v-label class="mb-2">Iniciály</v-label>
                      <v-text-field
                        v-model="userModalOptions.newUserData.name_shortcut"
                        :rules="
                          usersStore.hasPermissionByName(
                            userModalOptions.newUserData.permissions,
                            'edit_technological_department'
                          )
                            ? itemRequiredRule
                            : []
                        "
                        :readonly="calculateInitialsReadOnly()"
                        single-line
                        placeholder="Zadejte iniciály"
                        hide-details="auto"
                        variant="outlined"
                        required
                        rounded="sm"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12">
                      <v-label class="mb-2">Email</v-label>
                      <v-text-field
                        v-model="userModalOptions.newUserData.user_email"
                        :rules="emailRules"
                        single-line
                        hide-details="auto"
                        placeholder="Zadejte email uživatele"
                        variant="outlined"
                        rounded="sm"
                        autocomplete="off"
                        :autofill="false"
                        :autocorrect="false"
                        :spellcheck="false"
                      ></v-text-field>
                    </v-col>

                    <v-col v-if="userModalOptions.isCreating" cols="12">
                      <v-label class="mb-2">Heslo</v-label>
                      <v-text-field
                        v-model="password"
                        :append-icon="show2 ? 'mdi-eye' : 'mdi-eye-off'"
                        :rules="passwordRules"
                        single-line
                        hide-details="auto"
                        placeholder="Zadejte heslo uživatele"
                        required
                        variant="outlined"
                        rounded="sm"
                        autocomplete="off"
                        :autofill="false"
                        :autocorrect="false"
                        :spellcheck="false"
                        :type="show2 ? 'text' : 'password'"
                        @click:append="show2 = !show2"
                      ></v-text-field>
                    </v-col>
                    <v-col v-if="onlyEdit" cols="12" md="10">
                      <v-text-field
                        v-model="password"
                        single-line
                        hide-details="auto"
                        variant="outlined"
                        rounded="sm"
                        autocomplete="off"
                        :autofill="false"
                        :autocorrect="false"
                        :spellcheck="false"
                        :readonly="true"
                        placeholder="Vygenerované heslo"
                      ></v-text-field>
                    </v-col>
                    <v-col v-if="onlyEdit" cols="12" md="2">
                      <v-btn variant="flat" color="primary" @click.prevent="resetPassword()">
                        Resetovat heslo
                      </v-btn>
                    </v-col>
                    <v-col cols="12">
                      <v-label class="mb-2">Systémová role</v-label>
                      <v-autocomplete
                        v-model="userModalOptions.newUserData.roles"
                        :readonly="onlyPreview || !havePermision"
                        :clearable="!onlyPreview"
                        :disabled="!havePermision"
                        hide-details
                        required
                        rounded="sm"
                        :items="
                          roles.map((role) => {
                            return { value: role.system_role_id, title: role.translatedRole };
                          })
                        "
                        variant="outlined"
                        color="primary"
                        label="Zadejte název role"
                        single-line
                        multiple
                        class="autocomplete"
                        :no-data-text="'Žádná další políčka'"
                        :slim="true"
                        @blur.prevent.stop="checkShortcuts()"
                      >
                        <template #chip>
                          <v-chip
                            label
                            variant="tonal"
                            color="primary"
                            size="large"
                            class="my-1 text-subtitle-1 font-weight-regular"
                          ></v-chip>
                        </template>
                      </v-autocomplete>
                    </v-col>

                    <v-col cols="12">
                      <v-label class="mb-2">Specifická systémová oprávnění</v-label>
                      <v-autocomplete
                        v-model="userModalOptions.newUserData.permissions"
                        :readonly="!havePermision"
                        :disabled="!havePermision"
                        hide-details
                        required
                        rounded="sm"
                        :items="
                          permissions.map((permission) => {
                            return {
                              value: permission.system_permission_id,
                              title: permission.translatedPermission
                            };
                          })
                        "
                        variant="outlined"
                        color="primary"
                        label="Zadejte název oprávnění"
                        single-line
                        multiple
                        class="autocomplete"
                        :no-data-text="'Žádná další políčka'"
                        :slim="true"
                        @blur.prevent.stop="checkShortcuts()"
                      >
                        <template #chip="{ item }">
                          <v-chip
                            label
                            variant="tonal"
                            :color="
                              closablePermissions.find((p) => p.value === item.raw.value)?.color
                            "
                            size="large"
                            class="my-1 text-subtitle-1 font-weight-regular"
                            :closable="
                              closablePermissions.find((p) => p.value === item.raw.value)
                                ?.closable && !onlyPreview
                            "
                            @click:close="closeChipCustom(item.raw.value)"
                          >
                            {{ item.title }}
                          </v-chip>
                        </template>
                      </v-autocomplete>
                    </v-col>
                    <v-col v-if="!userModalOptions.isCreating" cols="12">
                      <v-label class="mb-2">Stav</v-label>
                      <v-autocomplete
                        v-model="userModalOptions.newUserData.status"
                        :readonly="onlyPreview"
                        :items="[
                          { value: 'active', title: 'Aktivní uživatel' },
                          { value: 'inactive', title: 'Neaktivní uživatel' }
                        ]"
                        rounded="sm"
                        color="primary"
                        single-line
                        hide-details
                        variant="outlined"
                        :no-data-text="'Žádná další políčka'"
                      ></v-autocomplete>
                    </v-col>
                    <v-col v-if="userModalOptions.isCreating" cols="12">
                      <div class="d-flex justify-space-between gap-2">
                        <div class="pb-4">
                          <h6 class="text-subtitle-1 mb-0">Potvrzení přidání uživatele</h6>
                          <p class="text-caption text-lightText mb-0 mr-5">
                            Uživatel bude vytvořen podle výše vyplněných údajů
                          </p>
                        </div>
                        <v-switch
                          v-model="userModalOptions.newUserData.confirm"
                          color="primary"
                          class="switchRight"
                          hide-details
                        ></v-switch>
                      </div>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions :class="responsiveCardClass">
              <v-spacer></v-spacer>
              <v-btn color="error" variant="text" @click="usersStore.resetModalUserData()">
                Zrušit
              </v-btn>
              <v-btn
                v-if="showSuccessButtonTitle"
                color="primary"
                variant="flat"
                type="submit"
                :loading="loading"
              >
                {{ showSuccessButtonTitle }}
              </v-btn>
            </v-card-actions>
          </v-form>
        </v-tabs-window-item>

        <v-tabs-window-item value="two">
          <v-card-title :class="responsiveCardClass">
            <span class="text-h5">Projekty</span>
          </v-card-title>
          <v-divider></v-divider>
          <v-card-text :class="responsiveCardClass">
            <CustomTable
              v-model:server-options="userProjectsOptions"
              :server-items-length="userProjectsTotalItems"
              :loading="loading"
              :headers="[
                { text: 'ID', value: 'project_id', sortable: false },
                { text: 'Název', value: 'name', sortable: false },
                { text: 'Vytvořeno', value: 'created_at', sortable: false },
                { text: 'Aktualizováno', value: 'updated_at', sortable: false },
                { text: 'Stav', value: 'status', sortable: false },
                { text: 'Zodpovědný uživatelé', value: 'responsible_users', sortable: false }
              ]"
              :items="userProjects"
              multi-sort
            >
              <template #item-created_at="{ created_at }">
                {{ toLocale(created_at) }}
              </template>

              <template #item-updated_at="{ updated_at }">
                {{ toLocale(updated_at) }}
              </template>

              <template
                #item-responsible_users="{ responsible_users }: { responsible_users: User[] }"
              >
                <v-chip
                  v-for="responsibleUser in responsible_users"
                  :key="responsibleUser.user_id"
                  class="mr-2"
                  color="primary"
                  size="small"
                  label
                >
                  {{ responsibleUser.getName }} ({{ responsibleUser.user_email }})
                </v-chip>
              </template>

              <template #item-status="{ status }">
                <v-chip v-if="status === 'active'" color="success" size="small" label>
                  Aktivní
                </v-chip>
                <v-chip v-if="status === 'inactive'" color="warning" size="small" label>
                  Neaktivní
                </v-chip>
                <v-chip v-if="status === 'deleted'" color="error" size="small" label>
                  Archivovaný
                </v-chip>
              </template>
            </CustomTable>
          </v-card-text>
        </v-tabs-window-item>

        <v-tabs-window-item value="three">
          <v-card-title :class="responsiveCardClass">
            <span class="text-h5">Laboratorní pracovníci</span>
          </v-card-title>
          <v-divider></v-divider>
          <v-card-text v-if="user" :class="responsiveCardClass">
            <UserSelect
              :users="user.userLaborants ?? []"
              @remove-user="removeUser"
              @add-user="addUser"
            ></UserSelect>
          </v-card-text>
        </v-tabs-window-item>
      </v-tabs-window>
    </v-card>
  </v-dialog>
</template>
