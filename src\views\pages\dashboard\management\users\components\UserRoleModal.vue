<script setup lang="ts">
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import type { Permission } from '@/stores/auth';
  import { useUsersStore } from '@/stores/users';
  import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons-vue';
  import { useVModel } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import { computed, nextTick, onMounted, reactive, ref, watch, watchEffect } from 'vue';

  export interface CreateUserRoleI {
    name: string;
    permissions: number[];
  }

  const ConfirmRef = ref<undefined | typeof ConfirmDlg>(undefined);

  // stores
  const usersStore = useUsersStore();
  const { permissions, roles, loading, role } = storeToRefs(usersStore);

  onMounted(() => {
    usersStore.getAllPermissions();
    usersStore.getAllRoles();
  });

  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    }
  });

  const showState = useVModel(props, 'show');
  const showUserRoleModalState = ref(false);
  const showEditUserRoleModalState = ref(false);

  const showTitle = computed(() => {
    return 'Uživatelské role';
  });

  const openedMenu = ref(true);
  const removeStandard = (v: number) => {
    usersStore.deleteSystemRole(v);
  };

  const setStandard = (id: number) => {
    usersStore.role = roles.value.find((role) => role.system_role_id === id);
  };

  const refresh = () => {
    roles.value.forEach((role) => {
      rolesSelections[role.system_role_id] = [...role.permissions.values()].map(
        (permission) => permission.system_permission_id
      );
    });
  };

  const saveRole = (id: number) => {
    usersStore.updateSystemRole(id, rolesSelections[id]);
  };

  const rolesSelections: Record<number, number[]> = reactive({});

  watch(roles, () => {
    refresh();
  });

  const createNewUserRole = reactive({
    name: '',
    permissions: []
  });

  const submitNewUserRole = async () => {
    const res = await usersStore.createNewUserRole(createNewUserRole);
    if (res) {
      showUserRoleModalState.value = false;
      resetNewUserRoleModal();
    }
  };

  const submitEditUserRole = async () => {
    if (!role.value) return;

    await usersStore.fullUpdateSystemRole(role.value);
    resetEditUserRoleModal();
  };

  const resetNewUserRoleModal = () => {
    showUserRoleModalState.value = false;

    createNewUserRole.name = '';
    createNewUserRole.permissions = [];
  };

  const resetEditUserRoleModal = async () => {
    showEditUserRoleModalState.value = false;

    await usersStore.getAllRoles();
    if (role.value) setStandard(role.value.system_role_id);

    await nextTick();

    refresh();
  };

  const _groupedPermissions = () => {
    const groups: {
      name: string;
      permissions: Permission[];
    }[] = [];
    for (const permission of [...permissions.value.values()]) {
      const prefix = permission.name.split('_')[0].trim();
      if (prefix && groups.find((group) => group.name === prefix) === undefined) {
        groups.push({ name: prefix, permissions: [] });
      }

      const group = groups.find((group) => group.name === prefix);
      if (group) {
        group.permissions.push(permission);
      }
    }

    return groups;
  };

  const groupedPermissions = computed(() => _groupedPermissions());

  const isTabletOrSmaller = ref(false);
  const navigationWidth = ref(325);
  const updateIsTabletOrSmaller = () => {
    isTabletOrSmaller.value = window.matchMedia('(max-width: 630px)').matches;
    navigationWidth.value = isTabletOrSmaller.value ? 200 : 325;
  };

  watchEffect(() => {
    updateIsTabletOrSmaller();
    const resizeListener = () => updateIsTabletOrSmaller();
    window.addEventListener('resize', resizeListener);

    return () => {
      window.removeEventListener('resize', resizeListener);
    };
  });
  const translateGroupName = (name: string) => {
    switch (name) {
      case 'view':
        return 'Zobrazení';
      case 'fill':
        return 'Vyplnění';
      case 'create':
        return 'Vytvoření';
      case 'add':
        return 'Přidání';
      case 'edit':
        return 'Editace';
      case 'request':
        return 'Žádost';
      case 'manage':
        return 'Správa';
      case 'sign':
        return 'Podpis';
      case 'reopen':
        return ' Znovuotevření';
      case 'deactivate':
        return 'Deaktivace';
      case 'close':
        return 'Deaktivace';
      case 'activate':
        return 'Znovuotevření';
      case 'access':
        return 'Přístup';
      case 'show':
        return 'Zobrazit';
      default:
        return name;
    }
  };
</script>
<template>
  <v-dialog v-model="showState" :z-index="1010" full-screen>
    <v-card class="pageSize" style="border-radius: 0 !important" :loading="loading">
      <v-card-title class="pa-5 rounded-0">
        <span class="text-h5" style="text-transform: capitalize">{{ showTitle }}</span>
      </v-card-title>
      <v-divider></v-divider>
      <v-card-text>
        <v-layout class="rounded border border-secondary border-md br-0" style="min-height: 70vh">
          <v-navigation-drawer
            v-model="openedMenu"
            left
            elevation="0"
            rail-width="60"
            mobile-breakpoint="lg"
            app
            class="leftSidebar"
            expand-on-hover
            :width="navigationWidth"
          >
            <!-- <perfect-scrollbar> -->
            <v-list aria-busy="true" aria-label="menu list" class="pb-0 pt-0">
              <v-list-item
                v-for="role of [...roles.values()]"
                :key="role.system_role_id"
                rounded="sm"
                class="mb-0 br-0 pt-6 pb-6 border-secondary"
                color="primary"
                :value="role"
                :border="'md'"
                density="comfortable"
                @click="setStandard(role.system_role_id)"
              >
                <v-list-item-title>{{ role.translatedRole }}</v-list-item-title>
                <template #append>
                  <v-btn
                    icon
                    variant="text"
                    color="error"
                    @click.prevent.stop="
                      async () => {
                        if (
                          await ConfirmRef?.open('Potvrzení', 'Opravdu chcete smazat roli?', {
                            color: 'error',
                            notclosable: true,
                            zIndex: 1011
                          })
                        ) {
                          removeStandard(role.system_role_id);
                        }
                      }
                    "
                  >
                    <v-icon :icon="'mdi-close'"></v-icon>
                  </v-btn>
                </template>
              </v-list-item>
            </v-list>
            <!-- </perfect-scrollbar> -->
          </v-navigation-drawer>

          <v-app-bar elevation="0" height="60">
            <v-btn
              v-if="openedMenu"
              class="text-secondary mr-3"
              color="darkText"
              icon
              rounded="sm"
              variant="text"
              size="small"
              @click.stop="openedMenu = !openedMenu"
            >
              <MenuFoldOutlined :style="{ fontSize: '16px' }" />
            </v-btn>
            <v-btn
              v-else
              class="text-secondary ms-3"
              color="darkText"
              icon
              rounded="sm"
              variant="text"
              size="small"
              @click.stop="openedMenu = !openedMenu"
            >
              <MenuUnfoldOutlined :style="{ fontSize: '16px' }" />
            </v-btn>
            <v-spacer />

            <v-btn variant="flat" color="primary" @click="showUserRoleModalState = true">
              Přidat uživatelskou roli
            </v-btn>
          </v-app-bar>

          <v-main class="d-flex align-center justify-center">
            <v-card v-if="role" class="pageSizeCustom">
              <v-card-title class="mt-5">
                {{ role.name }}
                <v-btn
                  icon
                  color="secondary"
                  variant="text"
                  @click="showEditUserRoleModalState = true"
                >
                  <v-icon>mdi-pencil</v-icon>
                </v-btn>
              </v-card-title>

              <v-form class="pageSizeCustomBottom">
                <v-card-text>
                  <v-row>
                    <v-col>
                      <v-row>
                        <v-col cols="12">
                          <span class="text-h5">Oprávnění</span>
                        </v-col>

                        <v-col
                          v-for="group in groupedPermissions"
                          :key="group.name"
                          cols="12"
                          sm="6"
                          md="3"
                        >
                          <span class="text-h5">{{ translateGroupName(group.name) }}</span>
                          <v-col
                            v-for="permission in group.permissions"
                            :key="permission.system_permission_id"
                            cols="12"
                          >
                            <v-checkbox
                              v-model="rolesSelections[role.system_role_id]"
                              :label="permission.translatedPermission"
                              :value="permission.system_permission_id"
                              hide-details
                              single-line
                              variant="outlined"
                              color="primary"
                              rounded="sm"
                            />
                          </v-col>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-card-text>

                <v-card-actions>
                  <v-spacer></v-spacer>
                  <v-btn color="error" variant="text" @click="refresh">Zrušit</v-btn>
                  <v-btn
                    color="primary"
                    variant="flat"
                    @click.prevent="saveRole(role.system_role_id)"
                  >
                    Uložit
                  </v-btn>
                </v-card-actions>
              </v-form>
            </v-card>

            <span v-else class="text-h4">Není vybrána uživatelská role</span>

            <v-dialog v-model="showUserRoleModalState" class="customer-modal" :z-index="1010">
              <v-card :loading="loading">
                <v-form
                  ref="CreateNewUserRoleForm"
                  class="CreateNewUserRoleForm"
                  @submit.prevent="submitNewUserRole"
                >
                  <v-card-title class="pa-5">
                    <span class="text-h5">Přidat uživatelskou roli</span>
                  </v-card-title>
                  <v-divider></v-divider>
                  <v-card-text>
                    <v-row>
                      <v-col>
                        <v-row>
                          <v-col cols="12">
                            <v-label class="mb-2">Název role</v-label>
                            <v-text-field
                              v-model="createNewUserRole.name"
                              single-line
                              placeholder="Zadejte název role"
                              hide-details="auto"
                              variant="outlined"
                              required
                              rounded="sm"
                            ></v-text-field>
                          </v-col>
                          <v-col cols="12">
                            <v-label class="mb-2">Systémová oprávnění ssd</v-label>

                            <v-autocomplete
                              v-model="createNewUserRole.permissions"
                              hide-details
                              required
                              rounded="sm"
                              :items="
                                permissions.map((permission) => {
                                  return {
                                    value: permission.system_permission_id,
                                    title: permission.translatedPermission
                                  };
                                })
                              "
                              variant="outlined"
                              color="primary"
                              label="Zadejte název oprávnění"
                              single-line
                              multiple
                              class="autocomplete"
                              :no-data-text="'Žádná další políčka'"
                              :slim="true"
                            >
                              <template #chip>
                                <v-chip
                                  label
                                  variant="tonal"
                                  color="primary"
                                  size="large"
                                  class="my-1 text-subtitle-1 font-weight-regular"
                                ></v-chip>
                              </template>
                            </v-autocomplete>
                          </v-col>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-card-text>
                  <v-divider></v-divider>
                  <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="error" variant="text" @click="resetNewUserRoleModal">
                      Zrušit
                    </v-btn>
                    <v-btn color="primary" variant="flat" type="submit" :loading="loading">
                      Přidat roli
                    </v-btn>
                  </v-card-actions>
                </v-form>
              </v-card>
            </v-dialog>

            <v-dialog
              v-if="showEditUserRoleModalState && role"
              v-model="showEditUserRoleModalState"
              class="customer-modal"
              :z-index="1010"
            >
              <v-card :loading="loading">
                <v-form
                  ref="CreateNewUserRoleForm"
                  class="CreateNewUserRoleForm"
                  @submit.prevent="submitEditUserRole"
                >
                  <v-card-title class="pa-5">
                    <span class="text-h5">Editovat uživatelskou roli</span>
                  </v-card-title>
                  <v-divider></v-divider>
                  <v-card-text>
                    <v-row>
                      <v-col>
                        <v-row>
                          <v-col cols="12">
                            <v-label class="mb-2">Název role</v-label>
                            <v-text-field
                              v-model="role.name"
                              single-line
                              placeholder="Zadejte název role"
                              hide-details="auto"
                              variant="outlined"
                              required
                              rounded="sm"
                            ></v-text-field>
                          </v-col>
                          <v-col cols="12">
                            <v-label class="mb-2">Systémová oprávnění ssd</v-label>

                            <v-autocomplete
                              v-model="role.newPermissions"
                              hide-details
                              required
                              rounded="sm"
                              :items="
                                permissions.map((permission) => {
                                  return {
                                    value: permission.system_permission_id,
                                    title: permission.translatedPermission
                                  };
                                })
                              "
                              variant="outlined"
                              color="primary"
                              label="Zadejte název oprávnění"
                              single-line
                              multiple
                              class="autocomplete"
                              :no-data-text="'Žádná další políčka'"
                              :slim="true"
                            >
                              <template #chip>
                                <v-chip
                                  label
                                  variant="tonal"
                                  color="primary"
                                  size="large"
                                  class="my-1 text-subtitle-1 font-weight-regular"
                                ></v-chip>
                              </template>
                            </v-autocomplete>
                          </v-col>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-card-text>
                  <v-divider></v-divider>
                  <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="error" variant="text" @click="resetEditUserRoleModal">
                      Zrušit
                    </v-btn>
                    <v-btn color="primary" variant="flat" type="submit" :loading="loading">
                      Upravit roli
                    </v-btn>
                  </v-card-actions>
                </v-form>
              </v-card>
            </v-dialog>
          </v-main>
        </v-layout>
      </v-card-text>
      <v-divider></v-divider>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn color="error" variant="text" @click="usersStore.showUserRoleModal = false">
          Zavřít
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>

  <ConfirmDlg ref="ConfirmRef" />
</template>
