<script setup lang="ts">
  import type { BreadcrumbI } from '@/components/shared/BaseBreadcrumb.vue';
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import TopPageBreadcrumb from '@/components/shared/TopPageBreadcrumb.vue';
  import UiParentCard from '@/components/shared/UiParentCard.vue';
  import LoaderWrapper from '@/layouts/dashboard/LoaderWrapper.vue';
  import { useLogsStore } from '@/stores/logs';
  import { useProjectsStore, type SearchResult, translateKey } from '@/stores/projects';
  import { isAllowed } from '@/utils/directive/isAllowed';
  import { SearchOutlined, FilterOutlined } from '@ant-design/icons-vue';
  import { useDebounceFn } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import { computed, onMounted, provide, ref, watch, onUnmounted } from 'vue';
  import { useRouter } from 'vue-router';
  import ChangelogPanel from '../../../../components/shared/ChangelogPanel.vue';
  import ProjectModal from './components/ProjectModal.vue';
  import ProjectRoleModal from './components/ProjectRoleModal.vue';
  import ProjectTable from './components/ProjectTable.vue';
  import { notification } from 'ant-design-vue';
  import { useUIStore } from '@/stores/ui';

  import FilterModal from '@/components/shared/ProjectFilter.vue';

  const ConfirmRef = ref<undefined | typeof ConfirmDlg>(undefined);

  provide('ConfirmRef', ConfirmRef);

  const searchColumns = [
    'status__project',
    'departments__number__project_department',
    'departments__status__project_department',
    'departments__analytical_requests__request_type__analytical_request',
    'departments__analytical_requests__samples__analysis_status__sample',
    'departments__chapters__status__chapter',
    'departments__batch_numbers__form__form_type__form',
    'departments__batch_numbers__batch_number',
    'departments__chapters__chemicals__shortcut__chemical',
    'departments__chapters__chemicals__cas__chemical',
    'departments__chapters__form__form_status__form'
  ];

  const searchTable = [
    'analytical_request',
    'batch_number',
    'chapter',
    'form',
    'method',
    'project_department',
    'sample',
    'standard',
    'project',
    'tag',
    'chemical',
    'project_chemical'
  ];

  onUnmounted(() => {
    allProjectsOptionsPageSearch.value.all_projects.search = '';
    emptySearchResults();
    results.clear();
  });

  const baseDataLoaded = ref(false);
  onMounted(async () => {
    allProjectsOptionsPageSearch.value.all_projects.search = '';
    start_date.value = undefined;
    end_date.value = undefined;
    allProjectsOptionsPageSearch.value.all_projects.search_columns = searchColumns;
    tables.value = searchTable;
    searchTables.value = [
      {
        default: true,
        table: 'analytical_request',
        canChange: true,
        translate: 'Analytická žádost'
      },
      {
        default: true,
        table: 'batch_number',
        canChange: true,
        translate: 'Číslo šarže'
      },
      {
        default: true,
        table: 'chapter',
        canChange: true,
        translate: 'Kapitola'
      },
      {
        default: true,
        table: 'form',
        canChange: true,
        translate: 'Formulář'
      },
      {
        default: true,
        table: 'method',
        canChange: true,
        translate: 'Metoda'
      },
      {
        default: true,
        table: 'project_department',
        canChange: true,
        translate: 'Oddělení'
      },
      {
        default: true,
        table: 'sample',
        canChange: true,
        translate: 'Vzorek'
      },
      {
        default: true,
        table: 'standard',
        canChange: true,
        translate: 'Standard'
      },
      {
        default: true,
        table: 'project',
        canChange: true,
        translate: 'Projekt'
      },
      {
        default: true,
        table: 'tag',
        canChange: true,
        translate: 'Štítek'
      },
      {
        default: true,
        table: 'chemical',
        canChange: true,
        translate: 'Chemikálie'
      },
      {
        default: true,
        table: 'project_chemical',
        canChange: true,
        translate: 'Chemikálie ve formuláři'
      }
    ];
    await showChangelog();
    await debouncedLoadFromServer(false);
    baseDataLoaded.value = true;
  });

  const checkPermision = (permision: string) => {
    return isAllowed([permision]);
  };
  const router = useRouter();
  const searchValue = ref('');
  const selectedProjects = ref([]);

  const logsStore = useLogsStore();
  const projectsStore = useProjectsStore();
  const {
    loading,
    loadingSearch,
    allProjectsOptionsPage,
    allProjectsOptionsPageSearch,
    extendsSearch,
    tables,
    searchTables,
    start_date,
    end_date
  } = storeToRefs(projectsStore);

  const loadFromServer = async (search: boolean = false) => {
    results.clear();
    if (search) {
      await projectsStore.getAllForSearch();
    } else {
      await projectsStore.projectPageGetAllProjects();
    }
  };

  watch(searchValue, async () => {
    debouncedSearch();
  });

  const uiStore = useUIStore();
  watch(loading, () => {
    uiStore.isLoading = loading.value;
  });

  const checkAdminPermission = () => {
    return isAllowed(['edit_all']);
  };

  type ActionFunction = () => void;
  const handleClick = (permision: string, action: ActionFunction, permisionDescription: string) => {
    if (checkAdminPermission()) {
      action();
      return;
    } else if (!isAllowed([permision])) {
      const nameOfPermission = permision;
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: permisionDescription + ' ' + nameOfPermission + '.'
      });
    } else {
      action();
    }
  };
  const havePermisionForChannelog = ref<boolean>(false);
  const showChangelog = async () => {
    if (isAllowed(['view_changelog'])) {
      havePermisionForChannelog.value = true;
    } else {
      havePermisionForChannelog.value = false;
    }
  };

  const emptySearchResults = () => {
    extendsSearch.value = {
      analytical_requests: [],
      analytical_techniques: [],
      batch_numbers: [],
      chapters: [],
      forms: [],
      methods: [],
      project_departments: [],
      projects: [],
      samples: [],
      standards: [],
      tags: [],
      chemicals: [],
      project_chemicals: []
    };
  };

  const debouncedSearch = useDebounceFn(() => {
    const search = searchValue.value.length < 2 ? undefined : searchValue.value;
    if (search) {
      let cleanedSearch = search.replace(/[():]/g, ' ');
      cleanedSearch = cleanedSearch.replace(/\s+/g, ' ').trim();
      const formattedSearch = cleanedSearch
        .split(' ')
        .filter((word) => word.length > 0)
        .map((word) => `${word}:*`)
        .join(' & ');

      allProjectsOptionsPageSearch.value.all_projects.search = formattedSearch;
    } else {
      emptySearchResults();
    }
  }, 500);

  const reload = async () => {
    await debouncedLoadFromServer(false);
  };

  const debouncedLoadFromServer = useDebounceFn(async (search: boolean = false) => {
    if (loading.value === false) {
      await loadFromServer(search);
    }
  }, 500);

  watch(
    () => [allProjectsOptionsPageSearch.value.all_projects.search],
    () => {
      if (
        !allProjectsOptionsPageSearch.value.all_projects.search ||
        (allProjectsOptionsPageSearch.value.all_projects.search ?? '').length < 2
      ) {
        emptySearchResults();
      } else {
        debouncedLoadFromServer(true);
      }
    }
  );

  watch(
    () => [
      allProjectsOptionsPage.value.my_projects.options.page,
      allProjectsOptionsPage.value.all_projects.options.page,
      allProjectsOptionsPage.value.active_project.options.page,

      allProjectsOptionsPage.value.my_projects.options.rowsPerPage,
      allProjectsOptionsPage.value.all_projects.options.rowsPerPage,
      allProjectsOptionsPage.value.active_project.options.rowsPerPage
    ],
    () => {
      debouncedLoadFromServer(false);
    }
  );

  watch(
    () => allProjectsOptionsPage.value.all_projects.results,
    () => {
      logsStore.setProjectTableAsActiveFilter(
        allProjectsOptionsPage.value.all_projects.results.map((project) => project.project_id)
      );
    }
  );

  const breadcrumbItems = computed<BreadcrumbI[]>(() => {
    return [
      {
        title: 'Projekty',
        disabled: true,
        href: router.resolve({ name: 'Projects' }).href
      }
    ];
  });

  const getTranslatedType = (type: string) => {
    switch (type) {
      case 'analytical':
        return 'Analytické oddělení';
      case 'synthetic':
        return 'Syntetické oddělení';
      case 'technical':
        return 'Technologické oddělení';
      default:
        return type;
    }
  };

  function findStructure(data: any, word: string): any | null {
    if (typeof data === 'object' && data !== null) {
      if (Array.isArray(data)) {
        return data.find((item) => findStructure(item, word));
      } else {
        for (const key in data) {
          if (
            typeof data[key] === 'string' &&
            data[key].toLowerCase().includes(word.toLowerCase())
          ) {
            return data;
          }
        }

        let matchedObject = null;

        // Check batch_number
        if (data.batch_number && typeof data.batch_number === 'object') {
          matchedObject = findStructure(data.batch_number, word);
          if (matchedObject) {
            return { ...data, batch_match: true };
          }
        }

        // Check analytical_request.batch_number
        if (
          data.analytical_request &&
          typeof data.analytical_request === 'object' &&
          data.analytical_request.batch_number &&
          typeof data.analytical_request.batch_number === 'object'
        ) {
          matchedObject = findStructure(data.analytical_request.batch_number, word);
          if (matchedObject) {
            return { ...data, analytical_request_batch_match: true };
          }
        }

        // Check form.batch_number
        if (data.form && typeof data.form === 'object' && data.form.batch_number) {
          matchedObject = findStructure(data.form.batch_number, word);
          if (matchedObject) {
            return { ...data, form_batch_match: true };
          }
        }

        // Check technique
        if (data.technique && typeof data.technique === 'object') {
          matchedObject = findStructure(data.technique, word);
          if (matchedObject) {
            return { ...data, technique_match: true };
          }
        }
      }
    }
    return null;
  }

  const filteredItems = computed<SearchResult[]>(() => {
    if (
      actualSearchValue.value === '' ||
      actualSearchValue.value === null ||
      actualSearchValue.value === undefined
    ) {
      return flattenedItems.value;
    }

    if (actualSearchValue.value.length < 2 && actualSearchValue.value.length > 0) return [];

    const seenBreadcrumbs = new Set<string>();
    const seenSearchIds = new Set<string>();

    return flattenedItems.value
      .map((item) => {
        const match = findStructure(item.search_object, actualSearchValue.value);
        if (match) {
          const updatedItem = { ...item };

          // Append batch_number.batch_number_id if found in batch_number
          if (match.batch_match && match.batch_number?.batch_number_id) {
            updatedItem.search_id = `${updatedItem.search_id}-${match.batch_number.batch_number_id}`;
          }

          // Append batch_number.batch_number_id if found in analytical_request.batch_number
          if (
            match.analytical_request_batch_match &&
            match.analytical_request?.batch_number?.batch_number_id
          ) {
            updatedItem.search_id = `${updatedItem.search_id}-${match.analytical_request.batch_number.batch_number_id}`;

            // Modify breadcrumb
            if (updatedItem.breadcrumb) {
              const sampleNumber = findSampleNumber(item.search_object);
              updatedItem.breadcrumb = updateBreadcrumbWithSampleNumber(
                updatedItem.breadcrumb,
                sampleNumber
              );
            }
          }

          // Append batch_number.batch_number_id if found in form.batch_number
          if (match.form_batch_match && match.form?.batch_number?.batch_number_id) {
            updatedItem.search_id = `${updatedItem.search_id}-${match.form.batch_number.batch_number_id}`;

            // Modify breadcrumb
            if (updatedItem.breadcrumb) {
              const sampleNumber = findSampleNumber(item.search_object);
              updatedItem.breadcrumb = updateBreadcrumbWithSampleNumber(
                updatedItem.breadcrumb,
                sampleNumber
              );
            }
          }

          // Append analytical_technique_id if found in technique
          if (match.technique_match && match.technique?.analytical_technique_id) {
            updatedItem.search_id = `${updatedItem.search_id}-${match.technique.analytical_technique_id}`;

            // Modify breadcrumb
            if (updatedItem.breadcrumb) {
              const sampleNumber = findSampleNumber(item.search_object);
              updatedItem.breadcrumb = updateBreadcrumb(updatedItem.breadcrumb, sampleNumber);
            }
          }

          return updatedItem;
        }
        return null;
      })
      .filter((item): item is SearchResult => item !== null) // Remove nulls
      .filter((item) => {
        if (seenBreadcrumbs.has(item.breadcrumb)) {
          return false;
        }
        seenBreadcrumbs.add(item.breadcrumb);
        return true;
      })
      .filter((item) => {
        if (seenSearchIds.has(item.search_id)) {
          return false;
        }
        seenSearchIds.add(item.search_id);
        return true;
      });
  });

  function updateBreadcrumb(breadcrumb: string, sampleNumber: string | null): string {
    if (!sampleNumber) return breadcrumb;
    // Split breadcrumb into parts
    const parts = breadcrumb.split(' → ');

    // Find the last part
    const lastPart = parts[parts.length - 1];

    // Extract content inside parentheses ()
    const match = lastPart.match(/\((.*?)\)$/);

    if (match) {
      const contentInsideParentheses = match[1]; // Get the extracted text

      let newContent = contentInsideParentheses; // Default to original
      if (contentInsideParentheses === 'Název') {
        newContent = 'Název techniky';
      } else if (contentInsideParentheses === 'Zkratka') {
        newContent = 'Zkratka techniky';
      }

      // Replace the old text with new
      const updatedLastPart = lastPart.replace(/\(.*?\)$/, `(${newContent})`);

      // Insert sample number as second to last item
      parts.splice(parts.length - 1, 0, sampleNumber);

      // Update the last part with the modified content
      parts[parts.length - 1] = updatedLastPart;

      return parts.join(' → ');
    }

    // If no parentheses found, still add the sample number as second to last
    parts.splice(parts.length - 1, 0, sampleNumber);
    return parts.join(' → ');
  }
  function updateBreadcrumbWithSampleNumber(
    breadcrumb: string,
    sampleNumber: string | null
  ): string {
    if (!sampleNumber) return breadcrumb; // No sample number, no change

    // Split breadcrumb into parts
    const parts = breadcrumb.split(' → ');

    // Find last part and check if it contains (Číslo šarže)
    if (parts.length > 1 && parts[parts.length - 1].includes('(Číslo šarže)')) {
      parts.splice(parts.length - 1, 0, sampleNumber); // Insert before last
    }

    return parts.join(' → ');
  }
  function findSampleNumber(searchObject: any): string | null {
    if (searchObject?.sample_number) {
      return searchObject.sample_number;
    }
    return null;
  }
  interface MatchedTerm {
    value: string;
    key: string;
  }

  const findMatchedTerm = (obj: any, searchTerm: string): MatchedTerm | null => {
    if (!obj || typeof obj !== 'object') return null;

    for (const key in obj) {
      if (
        typeof obj[key] === 'string' &&
        obj[key].toLowerCase().includes(searchTerm.toLowerCase())
      ) {
        return {
          value: obj[key],
          key: translateKey(key)
        };
      }
    }

    for (const key in obj) {
      if (typeof obj[key] === 'object' && obj[key] !== null) {
        const result = findMatchedTerm(obj[key], searchTerm);
        if (result) return result;
      }
    }

    return null;
  };

  const getBreadCrumb = (
    project?: any | null,
    department?: any | null,
    chapter?: any | null,
    batch?: any | null,
    form?: any | null,
    search_object?: any | null,
    search_object_key_name?: string
  ): string => {
    const path = ['(' + search_object_key_name + ')'];
    if (project) {
      path.push(project?.name);
    }
    if (department)
      path.push(department.shortcut + ' (' + getTranslatedType(department.type) + ' )');
    if (chapter) path.push(chapter.chapter_title);
    if (batch) path.push(batch.batch_number);
    if (form) path.push(form.form_name);
    if (search_object && actualSearchValue.value) {
      const matchedTerm = findMatchedTerm(search_object, actualSearchValue.value);
      if (matchedTerm) {
        path.push(`${matchedTerm.value} (${matchedTerm.key})`);
      }
    }
    return path.join(' → ');
  };

  const getBreadCrumbForSamples = (
    project?: any | null,
    department?: any | null,
    chapter?: any | null,
    search_object?: any | null,
    search_object_key_name?: string
  ): string => {
    const path = ['(' + search_object_key_name + ')'];
    if (project) {
      path.push(project);
    }
    if (department) path.push(department + ' (Analytické oddělení) ');
    if (chapter) path.push(chapter);
    if (search_object && search_object.technique && search_object.technique.shortcut) {
      path.push(search_object.technique.shortcut);
    }
    if (search_object && actualSearchValue.value) {
      const matchedTerm = findMatchedTerm(search_object, actualSearchValue.value);
      if (matchedTerm) {
        path.push(`${matchedTerm.value} (${matchedTerm.key})`);
      }
    }
    return path.join(' → ');
  };

  const getBreadCrumbForAnalyticalRequest = (
    project?: any | null,
    department?: any | null,
    chapter?: any | null,
    batch?: any | null,
    form?: any | null,
    search_object?: any | null,
    search_object_key_name?: string
  ): string => {
    const path = ['(' + search_object_key_name + ')'];
    if (project) {
      path.push(project?.project_name);
    }
    if (department) path.push(department.project_department_name);
    if (chapter) path.push(chapter.chapter_name);
    if (batch) path.push(batch.batch_number);
    if (form) path.push(form.form_name);
    if (search_object && actualSearchValue.value) {
      const matchedTerm = findMatchedTerm(search_object, actualSearchValue.value);
      if (matchedTerm) {
        path.push(`${matchedTerm.value} (${matchedTerm.key})`);
      }
    }
    return path.join(' → ');
  };

  const getBreadCrumbForAnalytical = (
    project?: any | null,
    department?: any | null,
    chapter?: any | null,
    search_object?: any | null,
    search_object_key_name?: string
  ): string => {
    const path = ['(' + search_object_key_name + ')'];
    if (project) {
      path.push(project?.name);
    }
    if (department) path.push(department);
    if (chapter) path.push(chapter);
    if (search_object && actualSearchValue.value) {
      const matchedTerm = findMatchedTerm(search_object, actualSearchValue.value);
      if (matchedTerm) {
        path.push(`${matchedTerm.value} (${matchedTerm.key})`);
      }
    }
    return path.join(' → ');
  };

  const highlightMatch = (text: string) => {
    const query = actualSearchValue.value;
    if (!query) return text;

    const parts = text.split(' → ');
    if (parts.length < 1) return text;

    const lastPart = parts[parts.length - 1];
    let processedLastPart = lastPart;

    // Truncate if longer than 30 characters
    if (lastPart.length > 30) {
      processedLastPart = lastPart.substring(0, 30) + '...';
    }

    if (lastPart.toLowerCase().includes(query.toLowerCase())) {
      parts[parts.length - 1] = `<span class="highlight">${processedLastPart}</span>`;
      return parts.join(' → ');
    }

    return text;
  };

  const results: Set<SearchResult> = new Set();

  const flattenedItems = computed<SearchResult[]>(() => {
    if (!extendsSearch.value) return [];

    const standardSampleNumbers = new Set<string>();
    if (extendsSearch.value.standards) {
      extendsSearch.value.standards
        .filter((standard) => !standard.sample_number.endsWith('E'))
        .forEach((standard) => {
          if (standard.sample_number) {
            standardSampleNumbers.add(standard.sample_number);
          }
        });
    }

    if (extendsSearch.value.projects) {
      extendsSearch.value.projects.forEach((project) => {
        const route = `/projekty/projekt/${project.project_id}`;
        results.add({
          search_id: `project-${project.project_id}-${route}`,
          breadcrumb: getBreadCrumb(project, null, null, null, null, project, 'Projekt'),
          route: route,
          search_object: project
        });
      });
    }

    if (extendsSearch.value.project_departments) {
      extendsSearch.value.project_departments.forEach((department) => {
        const route = `/projekty/projekt/${department.project_id}/oddeleni/${department.project_department_id}`;
        results.add({
          search_id: `department-${department.project_department_id}-${route}`,
          breadcrumb: getBreadCrumb(
            department,
            department,
            null,
            null,
            null,
            department,
            'Oddělení'
          ),
          route: route,
          search_object: department
        });
      });
    }

    if (extendsSearch.value.chapters) {
      extendsSearch.value.chapters.forEach((chapter) => {
        const route = `/projekty/projekt/${chapter.project_department?.project.project_id}/oddeleni/${chapter.department_id}/kapitola/${chapter.chapter_id}`;
        results.add({
          search_id: `chapter-${chapter.chapter_id}-${route}`,
          breadcrumb: getBreadCrumb(
            chapter.project_department?.project,
            chapter.project_department,
            chapter,
            null,
            null,
            chapter,
            'Kapitola'
          ),
          route: route,
          search_object: chapter
        });
      });
    }

    if (extendsSearch.value.forms) {
      extendsSearch.value.forms.forEach((form) => {
        const formType = switchFormTypes(form?.form_type ?? '');
        const route = `/projekty/projekt/${form?.chapter.project_department?.project.project_id}/oddeleni/${form?.chapter?.project_department?.project_department_id}/kapitola/${form.chapter_id}/${formType}/${form?.form_id}`;
        results.add({
          search_id: `form-${form.form_id}-${route}`,
          breadcrumb: getBreadCrumb(
            form?.chapter?.project_department?.project,
            form?.chapter?.project_department,
            form.chapter,
            form.batch_number,
            form,
            form,
            'Formulář ' + getTranslatedFormType(form?.form_type ?? '')
          ),
          route: route,
          search_object: form
        });
      });
    }

    if (extendsSearch.value.analytical_requests) {
      extendsSearch.value.analytical_requests
        .filter((request) => {
          if (request.sample_number && standardSampleNumbers.has(request.sample_number)) {
            return false;
          }
          return (
            request.form &&
            request.has_completed_samples === true &&
            !request.sample_number.endsWith('E')
          );
        })
        .forEach((request) => {
          const formType = switchFormTypes(request.form?.form_type ?? '');
          const route = `/projekty/projekt/${request.form?.chapter?.project_department?.project.project_id}/oddeleni/${request.form?.chapter?.project_department.project_department_id}/kapitola/${request.form?.chapter.chapter_id}/${formType}/${request.form?.form_id}`;
          results.add({
            search_id: `analytical-request-form-${request.analytical_request_id}-${route}`,
            breadcrumb: getBreadCrumb(
              request.form?.chapter?.project_department?.project,
              request.form?.chapter?.project_department,
              request.form?.chapter,
              request.batch_number,
              request.form,
              request,
              'Analytická žádost ve formuláři'
            ),
            route: route,
            search_object: request
          });
        });

      extendsSearch.value.analytical_requests
        .filter((request) => {
          if (request.sample_number && standardSampleNumbers.has(request.sample_number)) {
            return false;
          }
          return request.has_completed_samples === true && !request.sample_number.endsWith('E');
        })
        .forEach((request) => {
          const route = `/projekty/projekt/${request.project_id}/oddeleni/${request.project_department_id}/kapitola/${request.chapter_id}`;
          results.add({
            search_id: `analytical-request-${request.analytical_request_id}-${route}`,
            breadcrumb: getBreadCrumbForAnalyticalRequest(
              request,
              request,
              request,
              null,
              null,
              request,
              'Analytická žádost'
            ),
            route: route,
            search_object: request
          });
        });
    }

    if (extendsSearch.value.batch_numbers) {
      extendsSearch.value.batch_numbers.forEach((batch) => {
        const formType = switchFormTypes(batch.form?.form_type ?? '');
        const route = `/projekty/projekt/${batch.form?.chapter?.project_department?.project.project_id}/oddeleni/${batch.form?.chapter?.project_department.project_department_id}/kapitola/${batch.form?.chapter.chapter_id}/${formType}/${batch.form?.form_id}`;
        results.add({
          search_id: `batch-number-${batch.batch_number_id}-${route}`,
          breadcrumb: getBreadCrumb(
            batch.form?.chapter?.project_department?.project,
            batch.form?.chapter?.project_department,
            batch.form?.chapter,
            batch.batch_number,
            batch.form,
            batch,
            'Číslo šarže'
          ),
          route: route,
          search_object: batch
        });
      });
    }

    if (extendsSearch.value.methods) {
      extendsSearch.value.methods.forEach((method) => {
        const route = `/projekty/projekt/${method.project_id}/oddeleni/${method.department_id}/kapitola/${method.chapter_id}/technika/${method.technique_id}/metody/${method.method_id}/zmena`;
        results.add({
          search_id: `method-${method.method_id}-${route}`,
          breadcrumb: getBreadCrumbForAnalytical(
            method.project,
            'Analytické oddělení',
            'Vývoj metody',
            method,
            'Metoda'
          ),
          route: route,
          search_object: method
        });
      });
    }

    if (extendsSearch.value.samples) {
      if (extendsSearch.value.samples) {
        extendsSearch.value.samples
          .filter(
            (sample) =>
              !(sample.analytical_request?.type === NewAnalyticalRequestType.STANDARD_QC) &&
              !sample.sample_number.endsWith('E') &&
              sample.status !== 'created'
          )
          .forEach((sample) => {
            const sampleType =
              sample.analytical_request?.type === NewAnalyticalRequestType.STANDARD_RD
                ? 'rd'
                : 'qc-a-vt';
            const route = `/vzorky/${sampleType}/vzorek/${sample.sample_id}`;
            results.add({
              search_id: `sample-${sample.sample_id}-${route}`,
              breadcrumb: getBreadCrumbForSamples(
                getProjectDetails(
                  sample.analytical_request?.project_department?.project_id ?? 1,
                  sample.analytical_request?.type ?? NewAnalyticalRequestType.NORMAL
                ).projectName,
                getProjectDetails(
                  sample.analytical_request?.project_department?.project_id ?? 1,
                  sample.analytical_request?.type ?? NewAnalyticalRequestType.NORMAL
                ).departmentShortcut,
                getProjectDetails(
                  sample.analytical_request?.project_department?.project_id ?? 1,
                  sample.analytical_request?.type ?? NewAnalyticalRequestType.NORMAL
                ).chapterTitle,
                sample,
                'Vzorek'
              ),
              route: route,
              search_object: sample
            });
          });
      }
    }

    if (extendsSearch.value.standards) {
      extendsSearch.value.standards
        .filter((standard) => !standard.sample_number.endsWith('E'))
        .forEach((standard) => {
          const route = `/projekty/projekt/${standard.chapter?.project_department?.project_id}/oddeleni/${standard.chapter?.project_department?.project_department_id}/kapitola/${standard.chapter?.chapter_id}`;
          results.add({
            search_id: `standard-${standard.sample_id}-${route}`,
            breadcrumb: getBreadCrumb(
              standard.chapter?.project_department?.project,
              standard.chapter?.project_department,
              standard.chapter,
              null,
              null,
              standard,
              'Standard'
            ),
            route: route,
            search_object: standard
          });
        });
    }

    if (extendsSearch.value.standards) {
      if (extendsSearch.value.standards) {
        extendsSearch.value.standards
          .filter(
            (standard) => !standard.sample_number.endsWith('E') && standard.status !== 'created'
          )
          .forEach((standard) => {
            const sampleType =
              standard.analytical_request?.type === NewAnalyticalRequestType.STANDARD_RD
                ? 'rd'
                : 'qc-a-vt';
            const route = `/vzorky/${sampleType}/vzorek/${standard.sample_id}`;
            results.add({
              search_id: `sample-${standard.sample_id}-${route}`,
              breadcrumb: getBreadCrumbForSamples(
                getProjectDetails(
                  standard.analytical_request?.project_department?.project_id ?? 1,
                  standard.analytical_request?.type ?? NewAnalyticalRequestType.NORMAL
                ).projectName,
                getProjectDetails(
                  standard.analytical_request?.project_department?.project_id ?? 1,
                  standard.analytical_request?.type ?? NewAnalyticalRequestType.NORMAL
                ).departmentShortcut,
                getProjectDetails(
                  standard.analytical_request?.project_department?.project_id ?? 1,
                  standard.analytical_request?.type ?? NewAnalyticalRequestType.NORMAL
                ).chapterTitle,
                standard,
                'Vzorek'
              ),
              route: route,
              search_object: standard
            });
          });
      }
    }

    if (extendsSearch.value.project_chemicals) {
      extendsSearch.value.project_chemicals.forEach((project_chemical) => {
        const route = `/projekty/projekt/${project_chemical.project.project_id}`;
        results.add({
          search_id: `project-chemical-${project_chemical.project_chemical_id}`,
          breadcrumb: getBreadCrumb(
            project_chemical.project,
            null,
            null,
            null,
            null,
            project_chemical,
            'Chemikálie v projektu'
          ),
          route: route,
          search_object: project_chemical
        });
      });
    }

    if (extendsSearch.value.chemicals) {
      extendsSearch.value.chemicals.forEach((chemical) => {
        const formType = switchFormTypes(chemical.forms?.form_type ?? '');
        const route = `/projekty/projekt/${chemical.forms?.chapter?.project_department?.project.project_id}/oddeleni/${chemical.forms?.chapter?.project_department.project_department_id}/kapitola/${chemical.forms?.chapter.chapter_id}/${formType}/${chemical.forms?.form_id}`;
        results.add({
          search_id: `chemical-${chemical.chemical_id}`,
          breadcrumb: getBreadCrumb(
            chemical.forms?.chapter?.project_department?.project,
            chemical.forms?.chapter?.project_department,
            chemical.forms?.chapter,
            null,
            chemical.forms,
            chemical,
            'Chemikálie ve formuláři'
          ),
          route: route,
          search_object: chemical
        });
      });
    }
    if (extendsSearch.value.tags) {
      extendsSearch.value.tags.forEach((tag) => {
        tag.forms.forEach((form) => {
          const formType = switchFormTypes(form.form_type ?? '');
          const route = `/projekty/projekt/${form.chapter?.project_department?.project.project_id}/oddeleni/${form.chapter?.project_department.project_department_id}/kapitola/${form.chapter.chapter_id}/${formType}/${form.form_id}`;
          results.add({
            search_id: `tag-${tag.tag_id}-${route}`,
            breadcrumb: getBreadCrumb(
              form.chapter?.project_department?.project,
              form.chapter?.project_department,
              form.chapter,
              null,
              form,
              tag,
              'Štítek - formulář'
            ),
            route: route,
            search_object: tag
          });
        });
        tag.chapters.forEach((chapter) => {
          const route = `/projekty/projekt/${chapter.project_department?.project.project_id}/oddeleni/${chapter.project_department.project_department_id}/kapitola/${chapter.chapter_id}`;
          results.add({
            search_id: `tag-${tag.tag_id}-${route}`,
            breadcrumb: getBreadCrumb(
              chapter.project_department?.project,
              chapter.project_department,
              chapter,
              null,
              null,
              tag,
              'Štítek - kapitola'
            ),
            route: route,
            search_object: tag
          });
        });
      });
    }

    return Array.from(results);
  });

  const switchFormTypes = (form_type: string) => {
    switch (form_type) {
      case 'attempt':
        return 'pokus';
      case 'experiment':
        return 'experiment';
      case 'message':
        return 'sprava';
      case 'investigation':
        return 'setreni';
      default:
        return form_type;
    }
  };
  const getTranslatedFormType = (form_type: string) => {
    switch (form_type) {
      case 'attempt':
        return 'Pokus';
      case 'experiment':
        return 'Experiment';
      case 'message':
        return 'Zpráva';
      case 'investigation':
        return 'Šetreni';
    }
  };

  interface ComboboxItem {
    path: string;
    title: string;
    search_item: SearchResult;
    value: string;
  }

  const selectedItemToNavigate = ref<ComboboxItem | null>(null);

  const navigateToItem = (item: ComboboxItem | null) => {
    if (item && item.path) {
      emptySearchResults();
      router.push(item.path);
      searchValue.value = '';
      selectedItemToNavigate.value = null;
    }
  };

  const showFilterModal = ref(false);
  const toggleFilterModal = () => {
    showFilterModal.value = !showFilterModal.value;
  };

  const actualSearchValue = computed(() => {
    return searchValue.value;
  });

  const updateSearch = (value: string) => {
    searchValue.value = value;

    results.clear();
  };

  const updateSearchAndReset = (value: string) => {
    searchValue.value = value;
    results.clear();
    if (value) {
      emptySearchResults();
      debouncedSearch();
    }
  };
  enum NewAnalyticalRequestType {
    NORMAL = 'normal',
    STANDARD_QC = 'standard_qc',
    STANDARD_RD = 'standard_rd'
  }
  /*
  const findAnalyticalDepartmentId = (
    project_id: number | undefined,
    sample: number
  ): number | null => {
    if (typeof project_id !== 'number') return sample;

    const project = allProjectsOptionsPage.value.all_projects.results.find(
      (project) => project.project_id === project_id
    );

    if (project) {
      const analyticalDepartment = project.departments?.find(
        (department) => department.type === 'analytical'
      );

      return analyticalDepartment ? analyticalDepartment.project_department_id : null;
    }

    return null;
  };
*/
  const switchChapterNames = (type: string) => {
    switch (type) {
      case 'normal':
        return 'Vzorky RD';
      case 'standard_qc':
        return 'Vzorky QC a VT';
      case 'standard_rd':
        return 'Vzorky RD';
      default:
        return type;
    }
  };
  /*
  const findChapterIdByType = (
    type: NewAnalyticalRequestType,
    project_id: number
  ): number | null => {
    const project = allProjectsOptionsPage.value.all_projects.results.find(
      (project) => project.project_id === project_id
    );

    if (project) {
      for (const department of project.departments ?? []) {
        if (department.type === 'analytical') {
          const chapter = department.chapters?.find(
            (chapter) => chapter.chapter_title.trim() === switchChapterNames(type).trim()
          );
          if (chapter) {
            return chapter.chapter_id; // Return only chapter_id
          }
        }
      }
    }

    return null; // If no match is found
  };
*/
  const getProjectDetails = (
    project_id: number,
    type: NewAnalyticalRequestType
  ): {
    projectName: string | null;
    departmentShortcut: string | null;
    chapterTitle: string | null;
  } => {
    const project = allProjectsOptionsPage.value.all_projects.results.find(
      (project) => project.project_id === project_id
    );

    if (!project) return { projectName: null, departmentShortcut: null, chapterTitle: null };

    const projectName = project.name ?? null;

    const analyticalDepartment = project.departments?.find(
      (department) => department.type === 'analytical'
    );
    const departmentShortcut = analyticalDepartment?.shortcut ?? null;

    const chapterTitle = analyticalDepartment?.chapters?.find(
      (chapter) => chapter.chapter_title.trim() === switchChapterNames(type).trim()
    )
      ? switchChapterNames(type).trim()
      : null;

    return { projectName, departmentShortcut, chapterTitle };
  };

  const maxFromDate = computed(() => {
    if (end_date.value) {
      const toDate = new Date(end_date.value);
      toDate.setHours(23, 59, 59, 999);
      return toDate;
    }
    return null;
  });

  const minToDate = computed(() => {
    if (start_date.value) {
      const fromDate = new Date(start_date.value);
      fromDate.setHours(0, 0, 0, 0);
      return fromDate;
    }
    return null;
  });

  const onFromDateChange = (newFromDate: Date | null) => {
    if (newFromDate) {
      newFromDate.setHours(0, 0, 0, 0);
      if (end_date) {
        end_date.value = newFromDate;
      }
    }
  };

  const onToDateChange = (newToDate: Date | null) => {
    if (newToDate) {
      newToDate.setHours(23, 59, 59, 999);
      if (start_date.value) {
        start_date.value = newToDate;
      }
    }
  };

  watch([() => start_date.value, () => end_date.value], async ([newFrom, newTo]) => {
    if (newFrom && newTo) {
      const fromDate = new Date(newFrom);
      fromDate.setHours(0, 0, 0, 0);
      const toDate = new Date(newTo);
      toDate.setHours(23, 59, 59, 999);
      if (
        allProjectsOptionsPageSearch.value.all_projects.search &&
        (allProjectsOptionsPageSearch.value.all_projects.search ?? '').length > 2
      ) {
        debouncedLoadFromServer(true);
      }
    }
  });
</script>

<template>
  <LoaderWrapper v-if="!baseDataLoaded" />
  <template v-else>
    <TopPageBreadcrumb title="Seznam projektů" :_breadcrumbs="breadcrumbItems" />
    <v-row v-if="baseDataLoaded" class="justify-content-end">
      <v-col cols="12">
        <UiParentCard class="pa-0" :loading="loading">
          <template #action>
            <v-row justify="space-between" class="align-center">
              <v-col cols="12" md="5" class="d-flex align-center">
                <v-combobox
                  v-model:search="searchValue"
                  v-model="selectedItemToNavigate"
                  :items="
                    filteredItems.map((search_item, index) => {
                      return {
                        path: search_item.route,
                        title: search_item.breadcrumb,
                        search_item: search_item,
                        value: `${search_item.route}-${search_item.search_id ?? index}`
                      };
                    })
                  "
                  hide-details
                  rounded="sm"
                  variant="outlined"
                  single-line
                  :slim="true"
                  label="Zadejte hledaný výraz"
                  :loading="loadingSearch"
                  placeholder="Zadejte hledaný výraz"
                  color="primary"
                  class="autocomplete no-dropdown-arrow"
                  no-filter
                  hide-no-data
                  clearable
                  menu-icon=""
                  @update:model-value="navigateToItem"
                  @keydown.enter.prevent="navigateToItem(selectedItemToNavigate)"
                >
                  <template #prepend-inner>
                    <SearchOutlined class="text-lightText" />
                  </template>
                  <template #item="{ props, item }">
                    <v-list-item v-bind="props" :key="item.raw.value" :title="''">
                      <div class="player-wrapper pa-2">
                        <h6
                          class="text-subtitle-1 mb-0"
                          v-html="highlightMatch(item.raw.search_item.breadcrumb)"
                        ></h6>
                      </div>
                    </v-list-item>
                  </template>
                </v-combobox>

                <v-btn
                  v-if="baseDataLoaded"
                  class="ml-2"
                  variant="flat"
                  color="primary"
                  icon
                  rounded="sm"
                  size="default"
                  @click.prevent="toggleFilterModal"
                >
                  <FilterOutlined :style="{ fontSize: '18px' }" />
                </v-btn>
              </v-col>
              <v-col cols="12" md="2" class="d-flex align-center">
                <v-date-input
                  v-model="start_date"
                  density="compact"
                  single-line
                  hide-details="auto"
                  variant="outlined"
                  rounded="sm"
                  cancel-text="Zrušit"
                  ok-text="Potvrdit"
                  prepend-icon=""
                  clearable
                  label="Od: "
                  :max="maxFromDate"
                  @input="onFromDateChange"
                ></v-date-input>
              </v-col>
              <v-col cols="12" md="2" class="d-flex align-center">
                <v-date-input
                  v-model="end_date"
                  density="compact"
                  single-line
                  hide-details="auto"
                  variant="outlined"
                  rounded="sm"
                  cancel-text="Zrušit"
                  ok-text="Potvrdit"
                  prepend-icon=""
                  label="Do: "
                  clearable
                  :min="minToDate"
                  @input="onToDateChange"
                ></v-date-input>
              </v-col>
              <v-col cols="12" md="3">
                <div class="d-flex gap-2 justify-end flex-wrap">
                  <v-btn
                    :disabled="!havePermisionForChannelog"
                    variant="flat"
                    color="primary"
                    @click.prevent="
                      handleClick(
                        'view_changelog',
                        () => (logsStore.showDrawer = true),
                        'Nemáte oprávnění pro prohlížení changelogu:'
                      )
                    "
                  >
                    Změny
                  </v-btn>
                  <v-btn
                    variant="flat"
                    color="primary"
                    :disabled="checkPermision('add_project')"
                    @click.prevent="
                      handleClick(
                        'add_projects',
                        projectsStore.showNewProjectModal,
                        'Nemáte oprávnění pro přidávání nových projektů:'
                      )
                    "
                  >
                    Přidat projekt
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </template>

          <!-- Project table section -->
          <template
            v-if="
              allProjectsOptionsPage.my_projects.results.length === 0 &&
              allProjectsOptionsPage.active_project.results.length === 0 &&
              allProjectsOptionsPage.all_projects.results.length === 0
            "
          >
            <v-col cols="12" class="text-center">
              <span class="text-h5">Žádný projekt nebyl nalezen</span>
            </v-col>
          </template>
          <template v-else>
            <ProjectTable
              v-model:selected-projects="selectedProjects"
              title="Moje projekty"
              :list-projects="allProjectsOptionsPage.my_projects.results"
              :options="allProjectsOptionsPage.my_projects"
              @update-project="reload"
            />
            <ProjectTable
              v-model:selected-projects="selectedProjects"
              title="Aktivní projekty"
              :list-projects="allProjectsOptionsPage.active_project.results"
              :options="allProjectsOptionsPage.active_project"
              @update-project="reload"
            />

            <ProjectTable
              v-model:selected-projects="selectedProjects"
              title="Všechny projekty"
              :list-projects="allProjectsOptionsPage.all_projects.results"
              :options="allProjectsOptionsPage.all_projects"
              @update-project="reload"
            />
          </template>
        </UiParentCard>
      </v-col>
    </v-row>

    <ConfirmDlg ref="ConfirmRef" />
    <ProjectRoleModal v-model:show="projectsStore.showProjectRoleModal" />
    <ChangelogPanel v-if="logsStore.showDrawer" v-model:show="logsStore.showDrawer" />
    <ProjectModal
      v-model:show="projectsStore.showProjectModal"
      @update-project="reload"
      @create-project="reload"
    />
    <FilterModal
      v-if="baseDataLoaded"
      v-model:show="showFilterModal"
      :search-value="actualSearchValue"
      @update-search="updateSearch"
      @update-search-and-reset="updateSearchAndReset"
    />
  </template>
</template>
<style>
  .highlight {
    font-weight: bold;
    color: rgb(42, 161, 175);
  }
</style>
