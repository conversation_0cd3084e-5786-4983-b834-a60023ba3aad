<script setup lang="ts">
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import TagModal, { type TagListI } from '@/components/shared/TagModal.vue';
  import { useChaptersStore, type Chapter } from '@/stores/chapters';
  import { EllipsisOutlined } from '@ant-design/icons-vue';
  import { ref, type PropType } from 'vue';
  import { isAllowed } from '@/utils/directive/isAllowed';
  import { useProjectsStore } from '@/stores/projects';
  import { storeToRefs } from 'pinia';
  import { notification } from 'ant-design-vue';
  import Bruteforce from '@/components/shared/CloseSection.vue';
  import { itemRequiredRule } from '@/utils/formValidation.ts';
  import { FormType } from '@/stores/forms.ts';

  const projectsStore = useProjectsStore();
  const { department, project_permision } = storeToRefs(projectsStore);
  const ConfirmRef = ref<InstanceType<typeof ConfirmDlg> | null>(null);

  const emits = defineEmits(['reload']);

  const props = defineProps({
    chapter: {
      type: Object as PropType<Chapter>,
      required: true
    }
  });

  const showChapterTagsModal = ref(false);
  const chaptersStore = useChaptersStore();
  const showModal = ref(false);
  const formType = ref<FormType | null>(null);
  const exportToWord = ref(false);

  const toggleTagsModal = () => {
    showChapterTagsModal.value = !showChapterTagsModal.value;
  };

  const handleUpdateTags = async (tags: TagListI[]) => {
    await chaptersStore.updateTags(props.chapter, tags);
    emits('reload');
  };

  const selectedChapter = ref();
  const toggleModal = (isWord = false) => {
    exportToWord.value = isWord;
    showModal.value = true;
  }

  const handleExportChapter = async (isWord: boolean | null, defaultFormType: FormType = FormType.EXPERIMENT) => {
    if (!formType.value) {
      formType.value = defaultFormType;
    }
    if (isWord === null) {
      isWord = exportToWord.value;
    }

    selectedChapter.value = props.chapter.chapter_id;
    if (!selectedChapter.value) return;

    if (isWord) {
      await chaptersStore.exportChapterToWord(selectedChapter.value, formType.value);
    } else {
      await chaptersStore.exportChapter(selectedChapter.value, formType.value);
    }

    showModal.value = false;
  }
  const handleDeleteChapter = async () => {
    selectedChapter.value = props.chapter.chapter_id;
    if (!selectedChapter.value) return;
    if (checkAdminPermission()) {
      if (
        await ConfirmRef.value?.open('Opravdu chcete uzavřít kapitolu?', '', {
          color: 'error',
          notclosable: true,
          zIndex: 2400
        })
      ) {
        const res = await chaptersStore.closeChapter(props.chapter.chapter_id, false);
        if (res) emits('reload');
      }
      return;
    } else if (!project_permision.value) {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nejste přiřazený k danému projektu.'
      });
      return;
    }
    const res = await chaptersStore.checkIfCanCloseChapter(selectedChapter.value);
    if (res && res.count_of_missing_dependencies === 0) {
      if (
        await ConfirmRef.value?.open('Opravdu chcete uzavřít kapitolu?', '', {
          color: 'error',
          notclosable: true,
          zIndex: 2400
        })
      ) {
        const res = await chaptersStore.closeChapter(props.chapter.chapter_id, false);
        if (res) emits('reload');
      }
    } else if (res && res.count_of_missing_dependencies > 0) {
      notification.error({
        message: 'Nelze uzavřít oddělení',
        description: `Oddělení nelze uzavřít, protože chybí uzavřít ${res.count_of_missing_dependencies} závislostí.`
      });
      switchModal();
    }
  };

  const checkAdminPermission = () => {
    return isAllowed(['edit_all']);
  };
  const handleOpenChapter = async () => {
    if (checkAdminPermission()) {
      await chaptersStore.reactivateChapter(props.chapter.chapter_id);
      emits('reload');
      return;
    } else if (!project_permision.value) {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nejste přiřazený k danému projektu.'
      });
      return;
    }
    await chaptersStore.reactivateChapter(props.chapter.chapter_id);
    emits('reload');
  };

  const handleClick = (project_type: string | undefined, action: () => void) => {
    if (checkAdminPermission()) {
      action();
      return;
    }
    if (!project_permision.value) {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nejste přiřazený k danému projektu.'
      });
      return;
    }
    if (checkProjectTypePermisionsForEdit(project_type)) {
      action();
    }
  };

  defineExpose({
    toggleTagsModal,
    handleUpdateTags,
    handleDeleteChapter,
    handleOpenChapter
  });
  const havePermision = ref<boolean>(true);
  const missingPermison = ref<string>();

  const checkProjectTypePermisionsForEdit = (project_type: string | undefined) => {
    if (!project_type) return false;
    if (checkAdminPermission()) {
      return true;
    }
    if (!project_permision.value) {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nejste přiřazený k danému projektu.'
      });
      return;
    }
    if (project_type === 'Analytické oddělení') {
      if (isAllowed(['edit_analytical_department'])) {
        havePermision.value = true;
        return true;
      } else {
        missingPermison.value = 'edit_analytical_department';
        havePermision.value = false;
      }
    } else if (project_type === 'Syntetické oddělení') {
      if (isAllowed(['edit_syntetic_department'])) {
        havePermision.value = true;
        return true;
      } else {
        missingPermison.value = 'edit_syntetic_department';
        havePermision.value = false;
      }
    } else if (project_type === 'Technologické oddělení') {
      if (isAllowed(['edit_technological_department'])) {
        havePermision.value = true;
        return true;
      } else {
        missingPermison.value = 'edit_technological_department';
        havePermision.value = false;
      }
    }
    if (!havePermision.value) {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nemáte oprávnění pro editaci kapitoly: ' + missingPermison.value + '.'
      });
    }
    return false;
  };

  const openBruteForce = ref(false);
  const switchModal = () => {
    openBruteForce.value = !openBruteForce.value;
  };
  const closeAndReload = () => {
    openBruteForce.value = false;
    emits('reload');
  };
</script>

<template>
  <v-menu>
    <template #activator="{ props }">
      <v-btn
        size="x-small"
        v-bind="props"
        variant="text"
        style="height: auto"
        @click.prevent.stop="props.isActive = true"
      >
        <EllipsisOutlined :style="{ fontSize: '28px' }" />
      </v-btn>
    </template>

    <v-list elevation="24" density="compact" class="py-0">
      <v-list-item :value="chapter.chapter_id + '_tags'">
        <v-list-item-title @click="handleClick(department?.getName, toggleTagsModal)">
          Správa štítků
        </v-list-item-title>
      </v-list-item>
      <v-list-item :value="chapter.chapter_id + '_update_name'">
        <v-list-item-title
          @click="
            handleClick(department?.getName, () => chaptersStore.showEditModal(chapter.chapter_id))
          "
        >
          Aktualizovat název
        </v-list-item-title>
      </v-list-item>
      <v-list-item
        v-if="department?.isSynthetic() || department?.isTechnical()"
        :value="chapter.chapter_id + '_export'">
        <v-list-item-title
          @click="
            handleClick(department?.getName, () => department?.isTechnical() ? toggleModal() : handleExportChapter(false, FormType.EXPERIMENT))
          "
        >
          Exportovat to PDF
        </v-list-item-title>
      </v-list-item>
      <v-list-item
        v-if="department?.isSynthetic() || department?.isTechnical()"
        :value="chapter.chapter_id + '_export_to_word'">
        <v-list-item-title
          @click="
            handleClick(department?.getName, () => department?.isTechnical() ? toggleModal(true) : handleExportChapter(true))
          "
        >
          Exportovat do Wordu
        </v-list-item-title>
      </v-list-item>
      <v-list-item v-if="chapter.isActive" :value="chapter.chapter_id + '_close'">
        <v-list-item-title @click.prevent="handleClick(department?.getName, handleDeleteChapter)">
          Uzavřít kapitolu
        </v-list-item-title>
      </v-list-item>
      <v-list-item v-if="!chapter.isActive" :value="chapter.chapter_id + '_open'">
        <v-list-item-title @click.prevent="handleClick(department?.getName, handleOpenChapter)">
          Otevřít kapitolu
        </v-list-item-title>
      </v-list-item>
    </v-list>
  </v-menu>

  <TagModal
    v-if="chapter.tagIsLoaded"
    v-model:show="showChapterTagsModal"
    :tags="chapter.tagLists"
    @update-tags="handleUpdateTags"
  />
  <Bruteforce
    v-if="openBruteForce"
    :id="selectedChapter"
    v-model:show="openBruteForce"
    :what-to-close="'chapter'"
    @update="closeAndReload"
  />
  <ConfirmDlg ref="ConfirmRef" />

  <v-dialog v-model="showModal" class="customer-modal" :z-index="1010">
    <v-card>
      <v-card-title class="pa-5">
        <span class="text-h5">Vyberte typ formuláře</span>
      </v-card-title>
      <v-divider></v-divider>
      <v-card-text>
        <v-row>
          <v-col>
            <v-row>
              <v-col cols="12">
                <v-label class="mb-2">Typ formuláře</v-label>
                <v-select
                  v-model="formType"
                  :rules="itemRequiredRule"
                  item-text="title"
                  item-value="value"
                  placeholder="Vyberte šablonu"
                  hide-details="auto"
                  variant="outlined"
                  rounded="sm"
                  density="compact"
                  clearable
                  :items="[
                    {
                      title: 'Pokus',
                      value: FormType.ATTEMPT
                    },
                    {
                      title: 'Zpráva',
                      value: FormType.MESSAGE
                    },
                    {
                      title: 'Šetření',
                      value: FormType.INVESTIGATION
                    }
                  ]"
                ></v-select>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-card-text>
      <v-divider></v-divider>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn
          color="error"
          variant="text"
          @click="showModal=false"
        >
          Zrušit
        </v-btn>
        <v-btn color="primary" variant="flat" type="button" @click="handleExportChapter(null)">Exportovat</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>
