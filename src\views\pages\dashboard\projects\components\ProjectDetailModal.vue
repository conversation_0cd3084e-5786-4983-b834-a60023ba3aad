<template>
  <LoaderWrapper v-if="!project" />
  <template v-else>
    <v-dialog v-model="showState" class="customer-modal" :z-index="1010">
      <v-card :loading="loading">
        <v-card-title class="pa-5">
          <span class="text-h5">Nastavení projektu</span>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-card flat>
            <v-tabs v-model="tab" density="compact">
              <v-tab value="one">Uživatelé projektu</v-tab>
              <v-tab value="two">Analytické techniky</v-tab>
              <v-tab value="three">Přehled</v-tab>
            </v-tabs>

            <v-card-text class="pa-0">
              <v-tabs-window v-model="tab">
                <v-tabs-window-item value="one">
                  <UserSelect
                    v-if="havePermisionUser"
                    :users="projectUsers"
                    @remove-user="removeProjectUser"
                    @add-user="addProjectUser"
                  ></UserSelect>
                </v-tabs-window-item>
                <v-tabs-window-item value="two">
                  <v-row class="pt-4">
                    <v-col cols="12">
                      <TechniquesSelect
                        v-model:selected-techniques="selectedAnalyticalTechnique"
                        :readonly="!havePermisionTechnique"
                        :filter-options="filterOptions"
                      >
                        <template #append>
                          <v-btn
                            v-if="project.project_id"
                            variant="flat"
                            color="primary"
                            :disabled="
                              selectedAnalyticalTechnique === null || !havePermisionTechnique
                            "
                            @click.prevent="addProjectAnalyticalTechnique()"
                          >
                            Přidat techniku
                          </v-btn>
                        </template>
                      </TechniquesSelect>
                    </v-col>
                  </v-row>
                  <v-row style="max-height: 360px; overflow-y: auto" class="mt-4">
                    <v-col
                      v-for="projectAnalyticalTechnique in projectAnalyticalTechniques"
                      :key="projectAnalyticalTechnique.analytical_technique_id"
                      cols="12"
                    >
                      <v-card
                        variant="outlined"
                        class="customListCard card-hover-border bg-containerBg"
                        :subtitle="`Status: ${projectAnalyticalTechnique.getStatus} | Typ: ${translateType(projectAnalyticalTechnique.type)}`"
                        :title="`${projectAnalyticalTechnique.name} (${projectAnalyticalTechnique.shortcut})`"
                        pa-2
                      >
                        <template #append>
                          <v-menu>
                            <template #activator="{ props }">
                              <v-btn
                                size="x-small"
                                v-bind="props"
                                variant="text"
                                style="height: auto"
                                @click.prevent.stop="props.isActive = true"
                              >
                                <EllipsisOutlined :style="{ fontSize: '28px' }" />
                              </v-btn>
                            </template>
                            <v-list elevation="24" density="compact" class="py-0">
                              <v-list-item
                                v-for="(item, index) in technicActionDD"
                                :key="index"
                                :value="index"
                                @click="handleClickTable(item, projectAnalyticalTechnique)"
                              >
                                <v-list-item-title>{{ item.title }}</v-list-item-title>
                              </v-list-item>
                            </v-list>
                          </v-menu>
                        </template>
                      </v-card>
                    </v-col>
                  </v-row>
                </v-tabs-window-item>

                <v-tabs-window-item value="three">
                  <v-row align="center" class="mb-2">
                    <v-col cols="12" md="2">
                      <v-label style="margin-left: 1em"><h3>Přehledy</h3></v-label>
                    </v-col>
                    <v-spacer></v-spacer>
                    <v-col cols="12" md="3" class="d-flex align-center">
                      <v-label class="mb-0 mr-2">Od</v-label>
                      <v-date-input
                        v-model="from_datetime"
                        density="compact"
                        single-line
                        hide-details="auto"
                        variant="outlined"
                        rounded="sm"
                        cancel-text="Zrušit"
                        ok-text="Potvrdit"
                        prepend-icon=""
                        :max="maxFromDate"
                        @input="onFromDateChange"
                      ></v-date-input>
                    </v-col>
                    <v-col cols="12" md="3" class="d-flex align-center">
                      <v-label class="mb-0 mr-2">Do</v-label>
                      <v-date-input
                        v-model="to_datetime"
                        density="compact"
                        single-line
                        hide-details="auto"
                        variant="outlined"
                        rounded="sm"
                        cancel-text="Zrušit"
                        ok-text="Potvrdit"
                        prepend-icon=""
                        :min="minToDate"
                        @input="onToDateChange"
                      ></v-date-input>
                    </v-col>
                  </v-row>
                  <v-divider></v-divider>
                  <v-row class="mb-4">
                    <v-col cols="12">
                      <CustomTable
                        :loading="loading"
                        :headers="headers"
                        :items="[...historyItems.values()]"
                        :show-header="false"
                        :show-index="false"
                        :hide-footer="false"
                      >
                        <template #item-technique="{ technique }">
                          {{ technique.name }}
                        </template>

                        <template #item-samples="{ samples }">({{ samples.length }})</template>

                        <template #expand="sample">
                          <CustomTable
                            :headers="headersExtended"
                            :items="sample.samples"
                            :hide-footer="true"
                            hide-rows-per-page
                            table-class-name="customize-table customize"
                            :rows-per-page="sample.samples.length"
                          >
                            <template #item-sequence_name="{ sequence_name }">
                              <template v-if="sequence_name">
                                {{ sequence_name }}
                              </template>
                              <template v-else>/</template>
                            </template>

                            <template #item-sample_number="{ sample_number }">
                              {{ sample_number }}
                            </template>

                            <template #item-reanalyse_at="{ reanalyse_at }">
                              {{ toLocale(reanalyse_at) }}
                            </template>

                            <template #item-analytical_request="{ analytical_request }">
                              {{ analytical_request.batch_number?.batch_number }}
                            </template>

                            <template #item-user="{ user }">
                              {{ user.first_name }} {{ user.last_name }}
                            </template>
                          </CustomTable>
                        </template>
                      </CustomTable>
                    </v-col>
                  </v-row>
                </v-tabs-window-item>
              </v-tabs-window>
            </v-card-text>
          </v-card>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" variant="text" @click="projectsStore.showProjectDetailModal = false">
            Zavřít
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <ConfirmDlg ref="ConfirmRef" />
    <ProjectUserModal v-model:show="projectsStore.showProjectDetailUserRolesModal" />
  </template>
</template>
<script setup lang="ts">
  import { useAuthStore } from '@/stores/auth';
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import TechniquesSelect from '@/components/shared/TechniquesSelect.vue';
  import UserSelect from '@/components/shared/UserSelectExtended.vue';
  import LoaderWrapper from '@/layouts/dashboard/LoaderWrapper.vue';
  import { ProjectUser, useProjectsStore } from '@/stores/projects';
  import { Technique } from '@/stores/techniques';
  import { useUIStore } from '@/stores/ui';
  import { EllipsisOutlined } from '@ant-design/icons-vue';
  import { useVModel } from '@vueuse/core';
  import { notification } from 'ant-design-vue';
  import { storeToRefs } from 'pinia';
  import { onMounted, ref, watch, computed } from 'vue';
  import { useRoute } from 'vue-router';
  import ProjectUserModal from './ProjectUserModal.vue';
  import { isAllowed } from '@/utils/directive/isAllowed';
  import { toLocale } from '@/utils/locales';
  import type { Header } from 'vue3-easy-data-table';
  import CustomTable from '@/components/shared/CustomTable.vue';

  const emit = defineEmits(['update:show', 'updateProject']);
  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    }
  });

  const ConfirmRef = ref<undefined | typeof ConfirmDlg>(undefined);
  const showState = useVModel(props, 'show');

  const route = useRoute();
  const uiStore = useUIStore();
  const projectsStore = useProjectsStore();
  const authStore = useAuthStore();

  const { user } = storeToRefs(authStore);
  const {
    loading,
    project,
    projectUser,
    from_datetime,
    to_datetime,
    historyItems,
    projectUsers: project_users
  } = storeToRefs(projectsStore);

  const selectedUser = ref<number | undefined>(undefined);
  const selectedAnalyticalTechnique = ref<number | undefined>(undefined);

  const projectUsers = ref<ProjectUser[]>([]);
  const projectAnalyticalTechniques = ref<Technique[]>([]);

  const tab = ref('one');
  watch(loading, () => {
    uiStore.isLoading = loading.value;
  });

  onMounted(async () => {
    await loadExecute();
    projectUsers.value = project_users.value;
  });

  const loadExecute = async () => {
    from_datetime.value = new Date();
    to_datetime.value = new Date();
    const project_id = route.params.project_id as string;
    const projectId = parseInt(project_id);

    if (projectId) {
      if (project.value) {
        if (project.value.project_id !== projectId) {
          await projectsStore.getProjectById(projectId);
        }
      } else {
        await projectsStore.getProjectById(projectId);
      }
    }

    if (project.value) {
      projectAnalyticalTechniques.value =
        (await projectsStore.getProjectAnalyticalTechniques(project.value.project_id)) ?? [];
    }
  };

  const removeProjectUser = async (id: number) => {
    if (!project.value) return;

    const res = await projectsStore.removeProjectUser(project.value.project_id, id);
    if (res) {
      projectUsers.value = (await projectsStore.getProjectUsers(project.value.project_id)) ?? [];
      if (user.value && id === user.value.user_id) {
        showState.value = false;
        emit('updateProject');
      } else {
        await loadExecute();
      }
    }
  };

  const addProjectUser = async (user_id: number) => {
    if (project.value && user_id) {
      const res = await projectsStore.addProjectUser(project.value.project_id, user_id);
      if (res) {
        projectUsers.value = (await projectsStore.getProjectUsers(project.value.project_id)) ?? [];
        selectedUser.value = undefined;
      }
    }
  };

  const removeProjectAnalyticalTechnique = async (id: number) => {
    if (!project.value) return;

    if (
      await ConfirmRef.value?.open('Opravdu chcete odstranit techniku?', '', {
        color: 'error',
        notclosable: true,
        zIndex: 2400
      })
    ) {
      const res = await projectsStore.removeProjectAnalyticalTechnique(
        project.value.project_id,
        id
      );
      if (res) {
        await loadExecute();
      }
    }
  };

  const addProjectAnalyticalTechnique = async () => {
    if (project.value && selectedAnalyticalTechnique.value) {
      const res = await projectsStore.addProjectAnalyticalTechnique(
        project.value.project_id,
        selectedAnalyticalTechnique.value
      );
      if (res) {
        await loadExecute();
        selectedAnalyticalTechnique.value = undefined;
      }
    }
  };

  const showProjectDetailModal = async (user_id: number) => {
    if (project.value) {
      const _projectUser = await projectsStore.getProjectUser(project.value.project_id, user_id);

      if (_projectUser) {
        projectUser.value = {
          project_id: _projectUser.project_id,
          user: _projectUser.user,
          roles: _projectUser.roles.map((role) => role.project_role_id),
          permissions: _projectUser.permissions.map(
            (permission) => permission.project_permission_id
          )
        };
        projectsStore.showProjectDetailUserRolesModal = true;
      } else {
        notification.error({
          message: 'Chyba',
          description: 'Nepodařilo se načíst detail uživatele projektu.'
        });
      }
    }
  };

  // dropdown data
  const actionDD = ref([
    {
      title: 'Oprávnění uživatele',
      action: showProjectDetailModal
    }
  ]);
  const havePermisionUser = ref<boolean>(false);
  const missingPermisonUser = ref<string>();
  const havePermisionTechnique = ref<boolean>(false);
  const missingPermisonTechnique = ref<string>();
  watch(showState, async (newValue) => {
    if (newValue) {
      await loadExecute();
      await checkPermision();
    }
  });
  const checkPermision = async () => {
    if (isAllowed(['manage_project_users'])) {
      havePermisionUser.value = true;
    } else {
      missingPermisonUser.value = 'manage_project_users';
      havePermisionUser.value = false;
    }
    if (isAllowed(['manage_project_techniques'])) {
      havePermisionTechnique.value = true;
    } else {
      missingPermisonTechnique.value = 'manage_project_techniques';
      havePermisionTechnique.value = false;
    }
    if (!havePermisionUser.value || !havePermisionTechnique.value) {
      const buildMessage = () => {
        let message = '';
        if (!havePermisionUser.value) {
          message += 'správa uživatelů, ';
        }
        if (!havePermisionTechnique.value) {
          message += 'správa technik, ';
        }
        return message;
      };
    }
  };
  export type ActionItem = {
    title: string;
    action: (project_id: number) => void;
  };
  const technicActionDD = ref([
    { title: 'Odebrat techniku', action: removeProjectAnalyticalTechnique }
  ]);

  const handleClickTable = (item: ActionItem, projectAnalyticalTechnique: Technique) => {
    if (item.title === 'Odebrat techniku') {
      if (!isAllowed(['manage_project_techniques'])) {
        const nameOfPermission = 'manage_project_techniques';
        notification.error({
          message: 'Nedostatečné oprávnění',
          description: 'Nemáte oprávnění pro odebrání technik.' + ' ' + nameOfPermission + '.'
        });
      } else {
        item.action(projectAnalyticalTechnique.analytical_technique_id);
      }
    } else {
      item.action(projectAnalyticalTechnique.analytical_technique_id);
    }
  };

  const headers: Header[] = [
    { text: 'Název techniky', value: 'technique', sortable: false },
    { text: 'Počet vzorků', value: 'samples', sortable: false }
  ];

  const headersExtended: Header[] = [
    { text: 'Název vzorku', value: 'sequence_name', sortable: false },
    { text: 'Číslo vzorku', value: 'sample_number', sortable: false },
    { text: 'Číslo šarže', value: 'analytical_request', sortable: false },
    { text: 'Analýza', value: 'reanalyse_at', sortable: false },
    { text: 'Vytvořil', value: 'user', sortable: false }
  ];

  const maxFromDate = computed(() => {
    if (to_datetime.value) {
      const toDate = new Date(to_datetime.value);
      return toDate;
    }
    return null;
  });

  const minToDate = computed(() => {
    if (from_datetime.value) {
      const fromDate = new Date(from_datetime.value);
      return fromDate;
    }
    return null;
  });

  const onFromDateChange = (newFromDate: Date | null) => {
    if (newFromDate) {
      newFromDate.setHours(0, 0, 0, 0);

      if (to_datetime.value) {
        to_datetime.value = newFromDate;
      }
    }
  };
  const onToDateChange = (newToDate: Date | null) => {
    if (newToDate) {
      newToDate.setHours(0, 0, 0, 0);
      if (from_datetime.value) {
        to_datetime.value = newToDate;
      }
    }
  };

  watch([() => from_datetime.value, () => to_datetime.value], async ([newFrom, newTo]) => {
    if (newFrom && newTo) {
      const fromDate = new Date(newFrom);
      fromDate.setHours(0, 0, 0, 0);
      const toDate = new Date(newTo);
      toDate.setHours(23, 59, 59, 999);
      await projectsStore.getHistoryUsageProject(fromDate, toDate);
    }
  });
  watch(
    () => tab.value,
    async (newTab, oldTab) => {
      if (newTab) {
        if (newTab === 'three') {
          if (await checkPermisionHistory()) {
            if (from_datetime.value && to_datetime.value) {
              const fromDate = new Date(from_datetime.value);
              fromDate.setHours(0, 0, 0, 0);
              const toDate = new Date(to_datetime.value);
              toDate.setHours(23, 59, 59, 999);
              await projectsStore.getHistoryUsageProject(fromDate, toDate);
            }
          } else {
            tab.value = oldTab;
          }
        }
      }
    }
  );

  const havePermisionHistory = ref<boolean>(true);
  const missingPermisonHistory = ref<string>();
  const checkPermisionHistory = async () => {
    if (isAllowed(['view_project_overviews_statistics'])) {
      havePermisionHistory.value = true;
      return true;
    } else {
      missingPermisonHistory.value = 'view_project_overviews_statistics';
      havePermisionHistory.value = false;
    }
    if (!havePermisionHistory.value) {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description:
          'Nemáte dostanečné oprávnení pro prohlížení historie projektu: ' +
          missingPermisonHistory.value +
          '.'
      });
    }
    return false;
  };

  const translateType = (type: string): string => {
    const translations: { [key: string]: string } = {
      external: 'externí',
      internal: 'interní'
    };
    return translations[type] || type;
  };

  const filterOptions = computed(() => {
    if (project.value?.departments?.find((department) => department.type === 'analytical')) {
      return [{ column: 'status', value: 'active' }];
    } else {
      return [
        { column: 'status', value: 'active' },
        { column: 'type', value: 'external' }
      ];
    }
  });
</script>
<style scoped>
  :deep(.v-tabs-window) {
    overflow: hidden !important;
  }
  :deep(.customize) {
    --da0d4328: auto !important;
  }
</style>
