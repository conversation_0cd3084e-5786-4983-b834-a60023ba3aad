<script setup lang="ts">
  import UsersSelect from '@/components/shared/UsersSelect.vue';
  import { useProjectsStore } from '@/stores/projects';
  import { useUsersStore } from '@/stores/users';
  import { itemRequiredRule, itemShortcutRequiredRule } from '@/utils/formValidation';
  import { useVModel } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import { computed, ref } from 'vue';
  const emit = defineEmits(['update:show', 'createProject', 'updateProject']);

  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    }
  });

  const projectsStore = useProjectsStore();
  const { modalOptions, loading } = storeToRefs(projectsStore);

  const usersStore = useUsersStore();
  const { loading: userLoading } = storeToRefs(usersStore);

  const CreateColumnForm = ref();
  async function submitFormToValidate() {
    /*
  if(modalOptions.value?.baseData?.status !== modalOptions.value?.newData?.status ){
    if(modalOptions.value?.isEditing)
    {
    if(modalOptions.value?.newData?.status === 'closed'){
      const check = await projectsStore.checkIfCanClose();
    {
   if(check && check.count_of_missing_dependencies === 0){
   const res = await projectsStore.updateProjectStatus(false);
   {
      if(res){
        emit('updateProject');
      }
    }
   }
   else if (check && check.count_of_missing_dependencies > 0){
    notification.error({
      message: 'Nelze uzavřít projekt',
      description: `Projekt nelze uzavřít, protože chybí ${check.count_of_missing_dependencies} závislostí.`
    });
    switchModal();
   }
 }
}
else 
{
  const res = await projectsStore.updateProjectStatus(false);
   {
      if(res){
        emit('updateProject');
      }
    }}
  }
  }
  */
    if (CreateColumnForm.value.isValid && modalOptions.value) {
      switch (true) {
        case modalOptions.value.isEditing && !modalOptions.value.isCreating:
          return projectsStore.updateProject().then((res) => {
            if (res) {
              emit('updateProject');
            }
          });
        case !modalOptions.value.isEditing && modalOptions.value.isCreating:
          return projectsStore.createProject().then((res) => {
            if (res) {
              emit('createProject');
            }
          });

        default:
          return 'Náhled projektu';
      }
    } else {
      if (modalOptions.value && modalOptions.value.newData) {
        modalOptions.value.newData.confirm = false;
      }
    }
  }

  const showState = useVModel(props, 'show');
  const showTitle = computed(() => {
    if (modalOptions.value === undefined) {
      return 'Náhled projektu';
    }

    switch (true) {
      case modalOptions.value.isEditing && !modalOptions.value.isCreating:
        return 'Editace projektu';
      case !modalOptions.value.isEditing && modalOptions.value.isCreating:
        return 'Nový projekt';
      default:
        return 'Náhled projektu';
    }
  });
  const showSuccessButtonTitle = computed(() => {
    if (modalOptions.value === undefined) {
      return false;
    }

    switch (true) {
      case modalOptions.value.isEditing && !modalOptions.value.isCreating:
        return 'Upravit projekt';
      case !modalOptions.value.isEditing && modalOptions.value.isCreating:
        return 'Přidat projekt';
      default:
        return false;
    }
  });

  const onlyPreview = computed(() => {
    return (
      (modalOptions.value?.isCreating === false && modalOptions.value?.isEditing === false) ||
      modalOptions.value?.newData?.status === 'closed' ||
      modalOptions.value?.newData?.status === 'deactivated'
    );
  });
  const disableUpdate = computed(() => {
    return (
      modalOptions.value?.newData?.status === 'closed' ||
      modalOptions.value?.newData?.status === 'deactivated'
    );
  });
  /*
const statusType = computed(() => {
  const status = modalOptions.value?.newData?.status;
  return status === 'closed' || status === 'deactivated';
});
*/
</script>
<template>
  <v-dialog v-model="showState" class="customer-modal" :z-index="1010">
    <v-card :loading="loading || userLoading">
      <v-form
        v-if="modalOptions?.newData"
        ref="CreateColumnForm"
        class="createColumnForm"
        :readonly="onlyPreview"
        @submit.prevent="submitFormToValidate"
      >
        <v-card-title class="pa-5">
          <span class="text-h5">{{ showTitle }}</span>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col>
              <v-row>
                <v-col cols="12">
                  <v-label class="mb-2">Název projektu</v-label>
                  <v-text-field
                    v-model="modalOptions.newData.name"
                    :rules="itemRequiredRule"
                    single-line
                    hide-details="auto"
                    variant="outlined"
                    required
                    rounded="sm"
                  ></v-text-field>
                </v-col>
                <v-col cols="12">
                  <v-label class="mb-2">Odpovědná osoba</v-label>
                  <UsersSelect
                    v-model:selected-users="modalOptions.newData.responsible_users"
                    :readonly="onlyPreview"
                    :clearable="!onlyPreview"
                    multiple
                    :users="modalOptions.baseData?.responsible_users ?? []"
                    :what-to-show="['user_email']"
                  ></UsersSelect>
                </v-col>
                <v-col cols="12">
                  <v-row justify="space-between" class="align-center v-full">
                    <v-checkbox
                      v-model="modalOptions.newData.synthetic_department"
                      :readonly="modalOptions.baseData?.hasSyntheticDepartment ?? false"
                      single-line
                      label="Syntetické oddělení"
                      hide-details="auto"
                      variant="outlined"
                      color="primary"
                      rounded="sm"
                    />
                    <v-checkbox
                      v-model="modalOptions.newData.analytical_department"
                      :readonly="modalOptions.baseData?.hasAnalyticalDepartment ?? false"
                      single-line
                      label="Analytické oddělení"
                      hide-details="auto"
                      variant="outlined"
                      color="primary"
                      rounded="sm"
                      class="justify-align: center"
                    />
                    <v-checkbox
                      v-model="modalOptions.newData.technological_department"
                      :readonly="modalOptions.baseData?.hasTechnicalDepartment ?? false"
                      single-line
                      label="Technologické oddělení"
                      hide-details="auto"
                      color="primary"
                      variant="outlined"
                      rounded="sm"
                    />
                  </v-row>
                </v-col>
                <v-col
                  v-if="
                    modalOptions.newData.synthetic_department ||
                    modalOptions.newData.analytical_department
                  "
                  cols="12"
                  class="border-primary rounded-sm border-md mt-4"
                >
                  <v-label class="mb-2">
                    {{
                      modalOptions.newData.synthetic_department &&
                      modalOptions.newData.analytical_department
                        ? 'Analytické a syntetické oddělení'
                        : modalOptions.newData.synthetic_department
                          ? 'Syntetické oddělení'
                          : modalOptions.newData.analytical_department
                            ? 'Analytické oddělení'
                            : ''
                    }}
                  </v-label>
                  <v-row>
                    <v-col cols="12" md="6">
                      <v-label class="mb-2">Zkratka projektu</v-label>
                      <v-text-field
                        v-model="modalOptions.newData.synthetic_department_shortcut"
                        :rules="
                          modalOptions.newData.synthetic_department ||
                          modalOptions.newData.analytical_department
                            ? itemShortcutRequiredRule
                            : undefined
                        "
                        single-line
                        hide-details="auto"
                        variant="outlined"
                        rounded="sm"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="6">
                      <v-label class="mb-2">Identifikační číslo</v-label>
                      <v-text-field
                        v-model="modalOptions.newData.synthetic_department_number"
                        :rules="
                          modalOptions.newData.synthetic_department ||
                          modalOptions.newData.analytical_department
                            ? itemRequiredRule
                            : undefined
                        "
                        single-line
                        hide-details="auto"
                        variant="outlined"
                        rounded="sm"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-col>
                <v-col
                  v-if="modalOptions.newData.technological_department"
                  cols="12"
                  class="border-primary rounded-sm border-md mt-4"
                >
                  <v-label class="mb-2">Technologické oddělení</v-label>
                  <v-row>
                    <v-col cols="12" md="6">
                      <v-label class="mb-2">Výrobní zkratka</v-label>
                      <v-text-field
                        v-model="modalOptions.newData.technological_department_shortcut"
                        :rules="
                          modalOptions.newData.analytical_department ? itemRequiredRule : undefined
                        "
                        single-line
                        hide-details="auto"
                        variant="outlined"
                        rounded="sm"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" md="6">
                      <v-label class="mb-2">Výrobní číslo</v-label>
                      <v-text-field
                        v-model="modalOptions.newData.technological_department_number"
                        :rules="
                          modalOptions.newData.analytical_department ? itemRequiredRule : undefined
                        "
                        single-line
                        hide-details="auto"
                        variant="outlined"
                        rounded="sm"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-col>

                <!--
                <v-col v-if="!modalOptions.isCreating" cols="12">
                  <v-label class="mb-2">Status</v-label>
                  <v-autocomplete
                    :readonly="onlyPreview"
                    v-model="modalOptions.newData.status"
                    :items="[
                      { value: 'active', title: 'Aktivní' },
                      { value: 'deactivated', title: 'Neaktivní'},
                      { value: 'closed', title: 'Ukončený' }
                    ]"
                    rounded="sm"
                    color="primary"
                    single-line
                    hide-details
                    variant="outlined"
                    :no-data-text="'Žádná další políčka'"
                  ></v-autocomplete>
                </v-col>
              -->
                <v-col v-if="modalOptions.isCreating" cols="12">
                  <div class="d-flex justify-space-between gap-2">
                    <div class="pb-4">
                      <h6 class="text-subtitle-1 mb-0">Potvrzení přidání</h6>
                    </div>
                    <v-switch
                      v-model="modalOptions.newData.confirm"
                      color="primary"
                      class="switchRight"
                      hide-details
                    ></v-switch>
                  </div>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" variant="text" @click="projectsStore.resetModal()">Zrušit</v-btn>
          <v-btn
            v-if="showSuccessButtonTitle"
            color="primary"
            variant="flat"
            type="submit"
            :loading="loading"
            :disabled="disableUpdate"
          >
            {{ showSuccessButtonTitle }}
          </v-btn>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>
