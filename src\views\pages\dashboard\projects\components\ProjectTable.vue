<template>
  <section>
    <v-row>
      <v-col cols="12">
        <span class="text-h5">
          {{ title }} -
          <v-chip color="primary">{{ optionsState.totalItems }}</v-chip>
        </span>
      </v-col>
    </v-row>
    <v-row>
      <template v-if="optionsState.totalItems === 0">
        <v-col cols="12" class="text-center">
          <span class="text-h5">Žádný projekt nebyl nalezen</span>
        </v-col>
      </template>
      <template v-else>
        <v-col v-for="project in projects" :key="project.project_id" cols="12" md="4" xl="3" sm="6">
          <v-card
            variant="outlined"
            :color="
              project.status === 'active'
                ? 'primary'
                : project.status === 'closed'
                  ? 'error'
                  : 'warning'
            "
            :class="'card-hover-border bg-containerBg'"
            link
            @click.stop="redirectToProjectDetail(project.project_id)"
          >
            <v-card-text>
              <div class="d-flex align-start">
                <div>
                  <h4 class="text-h5">
                    {{ project.name }} ({{ projectStore.getProjectStatus(project.status) }})
                  </h4>
                </div>
                <div class="ml-auto">
                  <v-menu>
                    <template #activator="{ props }">
                      <v-btn size="small" v-bind="props" variant="tonal" color="secondary">
                        <EllipsisOutlined :style="{ fontSize: '20px' }" />
                      </v-btn>
                    </template>
                    <v-list elevation="24" density="compact" class="py-0">
                      <v-list-item
                        v-for="(item, index) in getActionDD(project)"
                        :key="index"
                        :value="index"
                        color="secondary"
                      >
                        <v-list-item-title class="text-h6" @click="handleClick(item, project)">
                          {{ item.title }}
                        </v-list-item-title>
                      </v-list-item>
                    </v-list>
                  </v-menu>
                </div>
              </div>
              <div class="d-flex align-center justify-space-between">
                <h4 class="text-h5 mb-0">Zkratka:</h4>
                {{ project.getShortcuts }}
              </div>
            </v-card-text>
          </v-card>
        </v-col>
      </template>
    </v-row>
    <v-row v-if="(optionsState?.totalItems ?? 0) > 0" class="justify-space-between mt-5">
      <v-col cols="12" lg="5" class="text-left">
        <v-pagination
          v-if="optionsState.totalItems && optionsState.options?.page"
          v-model="optionsState.options.page"
          density="compact"
          active-color="primary"
          class="left-pagination"
          :length="Math.ceil(optionsState.totalItems / selectedRow.value)"
          rounded="sm"
        ></v-pagination>
      </v-col>
      <v-col cols="12" lg="5" class="text-right">
        <v-menu transition="scale-transition">
          <template #activator="{ props }">
            <v-btn color="inherit" variant="text" v-bind="props">
              {{ selectedRow.title }}
              <template #append>
                <ChevronDownIcon size="20" stroke-width="1.5" />
              </template>
            </v-btn>
          </template>

          <v-list>
            <v-list-item v-for="(row, i) in rows" :key="i" :value="i">
              <v-list-item-title @click="selectedRow = row">{{ row.title }}</v-list-item-title>
            </v-list-item>
          </v-list>
        </v-menu>
      </v-col>
    </v-row>
  </section>
  <Bruteforce
    v-if="openBruteForce"
    :id="selectedProjectId"
    v-model:show="openBruteForce"
    :what-to-close="deactivateOrClose"
    @update="closeAndReload"
  />
</template>

<script lang="ts" setup>
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import { SimpleProject, useProjectsStore, type PaginatorRequestDataI } from '@/stores/projects';
  import { useUsersStore } from '@/stores/users';
  import { EllipsisOutlined } from '@ant-design/icons-vue';
  import { useVModel } from '@vueuse/core';
  import { computed, inject, ref, watch, type PropType, type Ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { isAllowed } from '@/utils/directive/isAllowed';
  import { notification } from 'ant-design-vue';
  import Bruteforce from '@/components/shared/CloseSection.vue';
  import { storeToRefs } from 'pinia';

  const projectsStore = useProjectsStore();
  const usersStore = useUsersStore();
  const { user } = storeToRefs(usersStore);

  const emit = defineEmits(['update:selectedProjects', 'updateProject']);
  const ConfirmRef = inject('ConfirmRef') as Ref<undefined | typeof ConfirmDlg>;
  const deactivateOrClose = ref('project');
  const props = defineProps({
    title: {
      type: String,
      required: true
    },
    listProjects: {
      type: Array as () => SimpleProject[],
      required: true
    },
    selectedProjects: {
      type: Array,
      required: true
    },
    options: {
      type: Object as PropType<PaginatorRequestDataI>,
      required: true
    }
  });

  const optionsState = useVModel(props, 'options');

  const router = useRouter();
  const projectStore = useProjectsStore();

  const selectedRow = ref({ title: '25 Projektů na stránku', value: 25 });
  const rows = ref([
    { title: '25 Projektů na stránku', value: 25 },
    { title: '50 Projektů na stránku', value: 50 },
    { title: '100 Projektů na stránku', value: 100 }
  ]);

  watch(selectedRow, () => {
    if (optionsState.value.options) {
      optionsState.value.options.rowsPerPage = selectedRow.value.value;
      optionsState.value.options.page = 1;
    }
  });

  const closeProject = async (id: number) => {
    if (
      await ConfirmRef.value?.open('Potvrzení', 'Opravdu chcete uzavřít projekt?', {
        color: 'error',
        notclosable: true
      })
    ) {
      const res = await projectStore.closeProject(id, false);
      if (res) emit('updateProject');
    }
  };

  const reactivateProject = async (id: number) => {
    const res = await projectStore.reactivateProject(id);
    if (res) emit('updateProject');
  };

  const closeAndReload = () => {
    openBruteForce.value = false;
    emit('updateProject');
  };
  export type ActionItem = {
    title: string;
    action: (project_id: number) => void;
  };

  const getActionDD = (project: SimpleProject) => {
    if (project.status === 'closed') {
      return [
        { title: 'Zobrazit náhled', action: projectStore.showPreviewModal },
        { title: 'Editovat projekt', action: projectStore.showEditModal },
        { title: 'Otevřít projekt', action: reactivateProject }
      ];
    }
    if (project.status === 'deactivated') {
      return [
        { title: 'Zobrazit náhled', action: projectStore.showPreviewModal },
        { title: 'Editovat projekt', action: projectStore.showEditModal },
        { title: 'Uzavřít projekt', action: closeProject },
        { title: 'Otevřít projekt', action: reactivateProject }
      ];
    }
    return [
      { title: 'Zobrazit náhled', action: projectStore.showPreviewModal },
      { title: 'Editovat projekt', action: projectStore.showEditModal },
      { title: 'Uzavřít projekt', action: closeProject }
    ];
  };

  const projects = computed(() => props.listProjects);

  const redirectToProjectDetail = (id: number) => {
    router.push(
      router.resolve({
        name: 'ProjectDetail',
        params: { project_id: id.toString() }
      })
    );
  };

  const selectedProjectId = ref();
  const handleClick = async (item: ActionItem, project: SimpleProject) => {
    selectedProjectId.value = project.project_id;
    if (!selectedProjectId.value) return;
    if (checkAdminPermission()) {
      if (item.title === 'Uzavřít projekt') {
        deactivateOrClose.value = 'project';
        const res = await projectsStore.checkIfCanCloseProject(project.project_id);
        if (res && res.count_of_missing_dependencies === 0) {
          item.action(project.project_id);
        } else if (res && res.count_of_missing_dependencies > 0) {
          notification.error({
            message: 'Nelze uzavřít projekt',
            description: `Projekt nelze uzavřít, protože chybí uzavřít ${res.count_of_missing_dependencies} závislostí.`
          });
          switchModal();
        }
      } else if (item.title === 'Editovat') {
        item.action(project.project_id);
      } else if (item.title === 'Otevřít projekt') {
        item.action(project.project_id);
      } else {
        item.action(project.project_id);
      }
      return;
    }

    if (item.title !== 'Zobrazit náhled') {
      if (
        project.responsible_users &&
        project.responsible_users.length > 0 &&
        user.value &&
        typeof user.value.user_id === 'number' &&
        !project.responsible_users.some(
          (responsibleUser) => user.value && responsibleUser.user_id === user.value.user_id
        )
      ) {
        notification.error({
          message: 'Nedostatečné oprávnění',
          description: 'Nejste přiřazený k danému projektu.'
        });
        return;
      }
    }

    if (item.title === 'Uzavřít projekt') {
      deactivateOrClose.value = 'project';
      if (!isAllowed(['deactivate_project'])) {
        notification.error({
          message: 'Nedostatečné oprávnění',
          description: 'Nemáte oprávnění pro uzavření projektu.'
        });
      } else {
        const res = await projectsStore.checkIfCanCloseProject(project.project_id);
        if (res && res.count_of_missing_dependencies === 0) {
          item.action(project.project_id);
        } else if (res && res.count_of_missing_dependencies > 0) {
          notification.error({
            message: 'Nelze uzavřít projekt',
            description: `Projekt nelze uzavřít, protože chybí uzavřít ${res.count_of_missing_dependencies} závislostí.`
          });
          switchModal();
        }
      }
    } else if (item.title === 'Editovat') {
      if (!isAllowed(['edit_project'])) {
        notification.error({
          message: 'Nedostatečné oprávnění',
          description: 'Nemáte oprávnění pro editaci projektu.'
        });
      } else {
        item.action(project.project_id);
      }
    } else if (item.title === 'Otevřít projekt') {
      if (!isAllowed(['reopen_project'])) {
        notification.error({
          message: 'Nedostatečné oprávnění',
          description: 'Nemáte oprávnění pro reaktivaci projektu.'
        });
      } else {
        item.action(project.project_id);
      }
    } else {
      item.action(project.project_id);
    }
  };

  const openBruteForce = ref(false);
  const switchModal = () => {
    openBruteForce.value = !openBruteForce.value;
  };

  const checkAdminPermission = () => {
    return isAllowed(['edit_all']);
  };
</script>
