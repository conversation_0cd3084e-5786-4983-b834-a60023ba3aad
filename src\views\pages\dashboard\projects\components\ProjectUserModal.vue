<script setup lang="ts">
  import { responsiveCardClass } from '@/config';
  import { useProjectsStore } from '@/stores/projects';
  import { useVModel } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import { onMounted, ref } from 'vue';

  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    }
  });

  const projectsStore = useProjectsStore();
  const showState = useVModel(props, 'show');
  const { permissions, loading, roles, projectUser } = storeToRefs(projectsStore);

  const CreateNewUserForm = ref();
  async function submitFormToValidate() {
    if (CreateNewUserForm.value.isValid && projectUser.value) {
      projectsStore.updateProjectUserRoleAndPermissions(
        projectUser.value.project_id,
        projectUser.value.user.user_id,
        projectUser.value.roles,
        projectUser.value.permissions
      );
    }
  }

  onMounted(() => {
    projectsStore.getProjectPermissions();
    projectsStore.getProjectRoles();
  });
</script>
<template>
  <v-dialog v-if="projectUser" v-model="showState" class="customer-modal" :z-index="1010">
    <v-card :loading="loading">
      <v-form
        ref="CreateNewUserForm"
        class="createNewUserForm"
        @submit.prevent="submitFormToValidate"
      >
        <v-card-title :class="responsiveCardClass">
          <span class="text-h5">
            Oprávnění uživatele v projektu ({{ projectUser.user.user_email }})
          </span>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text :class="responsiveCardClass">
          <v-row>
            <v-col>
              <v-row>
                <v-col cols="12">
                  <v-label class="mb-2">Role v projektu</v-label>
                  <v-autocomplete
                    v-model="projectUser.roles"
                    :clearable="true"
                    hide-details
                    required
                    rounded="sm"
                    :items="
                      roles.map((role) => {
                        return { value: role.project_role_id, title: role.translatedRole };
                      })
                    "
                    variant="outlined"
                    color="primary"
                    label="Zadejte název role"
                    single-line
                    multiple
                    class="autocomplete"
                    :no-data-text="'Žádná další políčka'"
                    :slim="true"
                  >
                    <template #chip>
                      <v-chip
                        label
                        variant="tonal"
                        color="primary"
                        size="large"
                        class="my-1 text-subtitle-1 font-weight-regular"
                      ></v-chip>
                    </template>
                  </v-autocomplete>
                </v-col>

                <v-col cols="12">
                  <v-label class="mb-2">Specifická projektová oprávnění</v-label>

                  <v-autocomplete
                    v-model="projectUser.permissions"
                    :clearable="true"
                    hide-details
                    required
                    rounded="sm"
                    :items="
                      permissions.map((permission) => {
                        return {
                          value: permission.project_permission_id,
                          title: permission.translatedPermission
                        };
                      })
                    "
                    variant="outlined"
                    color="primary"
                    label="Zadejte název oprávnění"
                    single-line
                    multiple
                    class="autocomplete"
                    :no-data-text="'Žádná další políčka'"
                    :slim="true"
                  >
                    <template #chip>
                      <v-chip
                        label
                        variant="tonal"
                        color="primary"
                        size="large"
                        class="my-1 text-subtitle-1 font-weight-regular"
                      ></v-chip>
                    </template>
                  </v-autocomplete>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions :class="responsiveCardClass">
          <v-spacer></v-spacer>
          <v-btn color="error" variant="text" @click="showState = false">Zrušit</v-btn>
          <v-btn color="primary" variant="flat" type="submit" :loading="loading">Upravit</v-btn>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>
