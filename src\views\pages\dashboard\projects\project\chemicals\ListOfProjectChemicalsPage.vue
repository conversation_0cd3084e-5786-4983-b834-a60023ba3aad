<script setup lang="ts">
  import type { BreadcrumbI } from '@/components/shared/BaseBreadcrumb.vue';
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import CustomTable from '@/components/shared/CustomTable.vue';
  import TopPageBreadcrumb from '@/components/shared/TopPageBreadcrumb.vue';
  import LoaderWrapper from '@/layouts/dashboard/LoaderWrapper.vue';
  import { useProjectsChemicalsStore, type ProjectChemical } from '@/stores/projectChemicals';
  import { useProjectsStore } from '@/stores/projects';
  import { EditOutlined, EyeOutlined, SearchOutlined } from '@ant-design/icons-vue';
  import { useDebounceFn } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import { computed, onBeforeMount, onBeforeUnmount, onMounted, ref, watch } from 'vue';
  import { onBeforeRouteUpdate, useRoute, useRouter } from 'vue-router';
  import type { Header } from 'vue3-easy-data-table';
  import ProjectChemicalModal from './components/ProjectChemicalModal.vue';
  import { notification } from 'ant-design-vue';
  import { isAllowed } from '@/utils/directive/isAllowed';
  const ConfirmRef = ref<undefined | typeof ConfirmDlg>(undefined);

  const route = useRoute();
  const router = useRouter();
  const projectsStore = useProjectsStore();
  const { loading: projectLoading, project } = storeToRefs(projectsStore);

  const projectChemicals = useProjectsChemicalsStore();
  const { options, loading, chemicals, search, totalItems } = storeToRefs(projectChemicals);

  const loadFromServer = async () => {
    projectChemicals.search_type = 'OR';
    await projectChemicals.getAll(undefined, undefined, undefined, null);
  };

  const debouncedSearch = useDebounceFn(async () => {
    if (loading.value === false) await loadFromServer();
  }, 500);

  onMounted(async () => {
    projectChemicals.setParamsFromLocation();
    await loadExecute();
  });

  watch(
    options,
    () => {
      if (loading.value === false) debouncedSearch();
    },
    { deep: true }
  );

  watch(search, () => {
    if (loading.value === false) debouncedSearch();
  });

  onBeforeRouteUpdate((to, from, next) => {
    if (to.path === from.path && to.query !== from.query) {
      projectChemicals.setParamsFromLocation();
      debouncedSearch();
    }

    next();
  });

  onBeforeUnmount(() => {
    projectChemicals.search = '';
  });

  const loadExecute = async () => {
    const project_id = route.params.project_id as string;
    const projectId = parseInt(project_id);
    if (projectId) {
      const res = await projectsStore.getProjectById(projectId);
      if (res) {
        await projectChemicals.getAll(undefined, undefined, undefined, res.project_id);
        projectChemicals.project_id = projectId;
        projectChemicals.project = project.value;
      }
    }
  };

  const headers: Header[] = [
    { text: 'ID', value: 'project_chemical_id', sortable: true },
    { text: 'CAS', value: 'csa', sortable: true },
    { text: 'Zkratka', value: 'shortcut', sortable: true },
    { text: 'Celý název', value: 'name', sortable: true },
    { text: 'Hustota', value: 'density', sortable: true },
    { text: 'M', value: 'molar_mass', sortable: false },
    { text: 'Akce', value: 'action' }
  ];

  const breadcrumbItems = computed<BreadcrumbI[]>(() => {
    return [
      {
        title: 'Projekty',
        disabled: false,
        href: router.resolve({ name: 'Projects' }).href
      },
      {
        title: project.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'ProjectDetail',
          params: { project_id: project.value?.project_id }
        }).href
      },
      {
        title: 'Správa chemikálií',
        disabled: true,
        href: router.resolve({
          name: 'ListOfProjectChemicals',
          params: { project_id: project.value?.project_id }
        }).href
      }
    ];
  });
  type ActionFunction = () => void;
  const handleClick = (action: ActionFunction) => {
    if (!isAllowed(['manage_project_chemicals'])) {
      return;
    } else {
      action();
    }
  };
  const getRowClass = (item: ProjectChemical, rowNumber: number) => {
    return item.status === 'deleted' ? 'deleted-row' : '';
  };
</script>
<template>
  <LoaderWrapper v-if="!project" />
  <template v-else>
    <TopPageBreadcrumb title="Správa chemikálií" :_breadcrumbs="breadcrumbItems" />
    <v-row>
      <v-col cols="12" md="12">
        <v-card elevation="0" variant="outlined" class="withbg pageSize">
          <v-card-item>
            <v-row justify="space-between" class="align-center">
              <v-col cols="12" md="9">
                <v-text-field
                  v-model="search"
                  type="text"
                  variant="outlined"
                  persistent-placeholder
                  placeholder="Hledat chemikálii"
                  hide-details
                >
                  <template #prepend-inner>
                    <SearchOutlined :style="{ fontSize: '14px' }" />
                  </template>
                </v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <div class="d-flex gap-2 justify-end flex-wrap">
                  <v-btn
                    variant="flat"
                    color="primary"
                    :disabled="!isAllowed(['manage_project_chemicals'])"
                    @click.prevent="handleClick(projectChemicals.showNewChemicalModal)"
                  >
                    Přidat chemikálii
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </v-card-item>
          <v-divider></v-divider>
          <v-card-text>
            <CustomTable
              v-model:server-options="options"
              :loading="loading || projectLoading"
              :server-items-length="totalItems"
              :headers="headers"
              :items="chemicals"
              :get-row-class="getRowClass"
              multi-sort
            >
              <template #item-action="{ project_chemical_id }">
                <div class="operation-wrapper">
                  <v-btn
                    icon
                    color="secondary"
                    variant="text"
                    rounded="sm"
                    @click.prevent="projectChemicals.showPreviewModal(project_chemical_id)"
                  >
                    <EyeOutlined />
                  </v-btn>
                  <v-btn
                    icon
                    color="primary"
                    variant="text"
                    rounded="sm"
                    @click.prevent="
                      handleClick(() => projectChemicals.showEditModal(project_chemical_id))
                    "
                  >
                    <EditOutlined />
                  </v-btn>
                </div>
              </template>
            </CustomTable>
          </v-card-text>
        </v-card>
      </v-col>

      <ConfirmDlg ref="ConfirmRef" />
      <ProjectChemicalModal v-model:show="projectChemicals.showChemicalModal" />
    </v-row>
  </template>
</template>
<style lang="less">
  .deleted-row {
    background-color: rgba(0, 0, 0, 0.02);
    opacity: 0.6;
  }
</style>
