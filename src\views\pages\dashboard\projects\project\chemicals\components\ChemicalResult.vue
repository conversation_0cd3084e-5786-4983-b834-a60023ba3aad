<template>
  <CustomTable
    id="chemicalsTable"
    :key="modelValueState.length"
    table-class-name="customize-table customize"
    :headers="
      headers.filter((header) => header.value !== 'action' && header.value !== 'chemical_id')
    "
    :items="[result]"
    :rows-per-page="1"
    :show-header="false"
    :show-index="false"
    :hide-footer="true"
  >
    <template #item-name="{ name }: { name: string }">
      {{ name }}
    </template>

    <template #item-notes="{ notes }: { notes: string }">
      {{ notes }}
    </template>

    <template #item-density="{ density }: { density: number }">
      {{ roundTableData(density) }}
    </template>

    <template #item-molar_mass="{ molar_mass }: { molar_mass: number }">
      {{ roundTableData(molar_mass) }}
    </template>

    <template #item-grams="{ grams }: { grams: number }">
      {{ roundTableData(grams) }}
    </template>

    <template #item-moles="{ moles }: { moles: number }">
      {{ roundTableData(moles) }}
    </template>

    <template #item-equivalent="{ equivalent }: { equivalent: number }">
      {{ roundTableData(equivalent) }}
    </template>

    <template #item-volume_ml="{ volume_ml }: { volume_ml: number }">
      {{ roundTableData(volume_ml) }}
    </template>

    <template #item-concentration="{ concentration }: { concentration: number }">
      {{ roundTableData(concentration) }}
    </template>
  </CustomTable>
</template>

<script setup lang="ts">
  import CustomTable from '@/components/shared/CustomTable.vue';
  import { Chemical } from '@/stores/chemicals';
  import { roundTableData } from '@/stores/columns';
  import { useVModel } from '@vueuse/core';
  import type { Header } from 'vue3-easy-data-table';

  const props = defineProps({
    result: {
      type: Object as () => Chemical,
      required: true
    },
    headers: {
      type: Array as () => Array<Header>,
      required: true
    },
    modelValue: {
      type: Array,
      required: true
    }
  });

  const modelValueState = useVModel(props, 'modelValue');
</script>
