<script lang="ts" setup>
  import type { ChemicalType } from '@/stores/chemicals';

  export interface ChemicalOptions {
    isSave: boolean;
    isEdit: boolean;
    cellData: {
      name: {
        editable: boolean;
        isEdited: boolean;
        valueBeforeEdit?: string | number;
      };
      density: {
        editable: boolean;
        isEdited: boolean;
        valueBeforeEdit?: string | number;
      };
      molar_mass: {
        editable: boolean;
        isEdited: boolean;
        valueBeforeEdit?: string | number;
      };
      notes: {
        editable: boolean;
        isEdited: boolean;
        valueBeforeEdit?: string | number;
      };
      grams: {
        editable: boolean;
        isEdited: boolean;
        valueBeforeEdit?: string | number;
      };
      moles: {
        editable: boolean;
        isEdited: boolean;
        valueBeforeEdit?: string | number;
      };
      equivalent: {
        editable: boolean;
        isEdited: boolean;
        valueBeforeEdit?: string | number;
      };
      volume_ml: {
        editable: boolean;
        isEdited: boolean;
        valueBeforeEdit?: string | number;
      };
      concentration: {
        editable: boolean;
        isEdited: boolean;
        valueBeforeEdit?: string | number;
      };
    };
  }

  export interface ChemicalTableChemicalDataI {
    chemical_id: number;
    name: string;
    density: number;
    molar_mass: number;
    notes: string;
    grams: number;
    moles: number;
    equivalent: number;
    volume_ml: number;
    concentration: number | string;
    type: ChemicalType;

    options: ChemicalOptions;
  }
</script>

<style lang="less" scoped></style>
