<template>
  {{ value }}
  <component :is="editable ? 'v-text-field' : 'div'" v-bind="fieldProps" v-if="value !== undefined">
    <template v-if="!editable">{{ value }}</template>
  </component>
</template>

<script setup lang="ts">
  defineProps({
    value: {
      type: [String, Number],
      required: true
    },
    editable: {
      type: Boolean,
      required: true
    },
    fieldProps: {
      type: Object,
      required: true
    }
  });
</script>
