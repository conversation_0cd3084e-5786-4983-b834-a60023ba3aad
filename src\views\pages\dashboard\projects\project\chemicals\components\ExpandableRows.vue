<template>
  <v-row
    v-if="projectChemicalForAutocompleteOptions.results.length > 0"
    style="max-height: 260px; overflow-y: auto"
    class="mt-4"
  >
    <v-col
      v-for="projectChemical in projectChemicalForAutocompleteOptions.results"
      :key="projectChemical.project_chemical_id"
      cols="12"
    >
      <v-card
        density="compact"
        variant="outlined"
        class="customListCard card-hover-border bg-containerBg"
        :title="`${projectChemical.name} (${projectChemical.shortcut})`"
        :subtitle="`Name: ${projectChemical.name}, Density: ${projectChemical.density}, Molar mass: ${projectChemical.molar_mass}, Density: ${projectChemical.density}`"
        pa-1
        @click.prevent="$emit('fillFromProjectChemical', projectChemical)"
      ></v-card>
    </v-col>
  </v-row>
  <div v-else style="padding: 15px"><PERSON><PERSON><PERSON><PERSON> v<PERSON>ky</div>
</template>

<script setup lang="ts">
  import type { ProjectChemical } from '@/stores/projectChemicals';
  import type { PaginatorRequestDataI } from '@/stores/projects';

  defineProps({
    projectChemicalForAutocompleteOptions: {
      type: Object as () => PaginatorRequestDataI<ProjectChemical>,
      required: true
    }
  });
  defineEmits(['fillFromProjectChemical']);
</script>
