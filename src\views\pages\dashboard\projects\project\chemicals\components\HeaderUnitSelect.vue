<template>
  <div class="unit-header">
    <v-select
      v-model="selectedUnit"
      class="compact-select"
      :items="items"
      variant="plain"
      density="compact"
      style="max-width: 100px"
      hide-details
      dense
    ></v-select>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';

  const selectedUnit = defineModel('selectedUnit', { required: true });
  defineProps({
    label: {
      type: String,
      required: true
    },
    items: {
      type: Array as () => Array<{ title: string; value: string }>,
      required: true
    }
  });
</script>

<style lang="less">
  .unit-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .compact-select {
      font-size: 10px !important;
    }

    .compact-select .v-field__append-inner {
      display: none !important;
    }
    .compact-select .v-select__selections {
      padding-top: 0;
      padding-bottom: 0;
    }

    .compact-select .v-field__input {
      display: flex;
      justify-content: flex-end;
    }
  }
</style>
