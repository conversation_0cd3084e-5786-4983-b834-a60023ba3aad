<script setup lang="ts">
  import CustomNumberInput from '@/components/shared/CustomNumberInput.vue';
  import { useProjectsChemicalsStore } from '@/stores/projectChemicals';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { useVModel } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import { computed, ref } from 'vue';

  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    }
  });

  // stores
  const projectChemicals = useProjectsChemicalsStore();
  const { loading, modalOptions } = storeToRefs(projectChemicals);

  const CreateColumnForm = ref();
  async function submitFormToValidate() {
    if (CreateColumnForm.value.isValid && modalOptions.value) {
      switch (true) {
        case modalOptions.value.isEditing && !modalOptions.value.isCreating:
          return projectChemicals.updateChemical();
        case !modalOptions.value.isEditing && modalOptions.value.isCreating:
          return projectChemicals.createChemical();

        default:
          return 'Náhled chemikálie';
      }
    }
  }

  const showState = useVModel(props, 'show');
  const showTitle = computed(() => {
    if (modalOptions.value === undefined) {
      return 'Náhled chemikálie';
    }

    switch (true) {
      case modalOptions.value.isEditing && !modalOptions.value.isCreating:
        return 'Editace chemikálie';
      case !modalOptions.value.isEditing && modalOptions.value.isCreating:
        return 'Nová chemikálie';
      default:
        return 'Náhled chemikálie';
    }
  });
  const showSuccessButtonTitle = computed(() => {
    if (modalOptions.value === undefined) {
      return false;
    }

    switch (true) {
      case modalOptions.value.isEditing && !modalOptions.value.isCreating:
        return 'Upravit chemikálii';
      case !modalOptions.value.isEditing && modalOptions.value.isCreating:
        return 'Přidat chemikálii';
      default:
        return false;
    }
  });

  const onlyPreview = computed(() => {
    return modalOptions.value?.isCreating === false && modalOptions.value?.isEditing === false;
  });
</script>
<template>
  <v-dialog v-model="showState" class="customer-modal" :z-index="1010">
    <v-card :loading="loading">
      <v-form
        v-if="modalOptions?.newData"
        ref="CreateColumnForm"
        class="createColumnForm"
        :readonly="onlyPreview"
        @submit.prevent="submitFormToValidate"
      >
        <v-card-title class="pa-5">
          <span class="text-h5">{{ showTitle }}</span>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col>
              <v-row>
                <v-col cols="12" md="6">
                  <v-label class="mb-2">CAS</v-label>
                  <v-text-field
                    v-model="modalOptions.newData.csa"
                    single-line
                    placeholder="Zadejte cas"
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></v-text-field>
                </v-col>

                <v-col cols="12" md="6">
                  <v-label class="mb-2">Zkratka</v-label>
                  <v-text-field
                    v-model="modalOptions.newData.shortcut"
                    :rules="itemRequiredRule"
                    single-line
                    placeholder="Zadejte zkratku"
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></v-text-field>
                </v-col>

                <v-col cols="12" md="6">
                  <v-label class="mb-2">Název</v-label>
                  <v-text-field
                    v-model="modalOptions.newData.name"
                    :rules="itemRequiredRule"
                    single-line
                    placeholder="Zadejte název"
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></v-text-field>
                </v-col>
                <v-col cols="12" md="6">
                  <v-label class="mb-2">Hustota (g/cm³)</v-label>
                  <CustomNumberInput
                    v-model="modalOptions.newData.density"
                    single-line
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                    :only-preview="onlyPreview"
                  ></CustomNumberInput>
                </v-col>

                <v-col cols="12" md="6">
                  <v-label class="mb-2">Molární hmotnost (g/mol)</v-label>
                  <CustomNumberInput
                    v-model="modalOptions.newData.molar_mass"
                    single-line
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                    :only-preview="onlyPreview"
                  ></CustomNumberInput>
                </v-col>

                <v-col v-if="!modalOptions.isCreating" cols="12">
                  <v-label class="mb-2">Status</v-label>
                  <v-autocomplete
                    v-model="modalOptions.newData.status"
                    :readonly="onlyPreview"
                    :items="[
                      { value: 'active', title: 'Aktivní' },
                      { value: 'deleted', title: 'Odstraněný' }
                    ]"
                    rounded="sm"
                    color="primary"
                    single-line
                    hide-details
                    variant="outlined"
                    :no-data-text="'Žádná další políčka'"
                  ></v-autocomplete>
                </v-col>
                <v-col v-if="modalOptions.isCreating" cols="12">
                  <div class="d-flex justify-space-between gap-2">
                    <div class="pb-4">
                      <h6 class="text-subtitle-1 mb-0">Potvrzení přidání</h6>
                    </div>
                    <v-switch
                      v-model="modalOptions.newData.confirm"
                      color="primary"
                      class="switchRight"
                      hide-details
                    ></v-switch>
                  </div>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" variant="text" @click="projectChemicals.resetModal()">Zrušit</v-btn>
          <v-btn
            v-if="showSuccessButtonTitle"
            color="primary"
            variant="flat"
            type="submit"
            :loading="loading"
          >
            {{ showSuccessButtonTitle }}
          </v-btn>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>
