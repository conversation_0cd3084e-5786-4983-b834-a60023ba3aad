<template>
  <div v-if="isEditable" class="operation-wrapper">
    <v-btn
      icon
      color="success"
      variant="text"
      rounded="sm"
      @click.prevent="$emit('calculateRowData', index - 1)"
    >
      <CalculatorOutlined />
    </v-btn>
    <v-btn
      icon
      color="error"
      variant="text"
      rounded="sm"
      @click.prevent="
        async () => {
          if (
            await ConfirmRef?.open('Potvrzení', 'Opravdu chcete smazat řádek?', {
              color: 'error',
              notclosable: true,
              zIndex: 2400
            })
          ) {
            modelValueState.splice(index - 1, 1);
          }
        }
      "
    >
      <DeleteOutlined />
    </v-btn>

    <ConfirmDlg ref="ConfirmRef" />
  </div>
</template>

<script setup lang="ts">
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import { CalculatorOutlined, DeleteOutlined } from '@ant-design/icons-vue';
  import { useVModel } from '@vueuse/core';
  import { ref } from 'vue';

  const ConfirmRef = ref<undefined | typeof ConfirmDlg>(undefined);

  const props = defineProps({
    index: {
      type: Number,
      required: true
    },
    isEditable: {
      type: Boolean,
      required: true
    },
    modelValue: {
      type: Array,
      required: true
    }
  });

  const modelValueState = useVModel(props, 'modelValue');
  defineEmits(['calculateRowData']);
</script>
