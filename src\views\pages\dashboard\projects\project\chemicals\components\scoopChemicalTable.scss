.customize-table {
  --easy-table-header-font-size: 14px;
  --easy-table-header-height: 20px;
  --easy-table-header-item-padding: 4px 4px;

  --easy-table-body-row-height: auto;
  --easy-table-body-row-font-size: 14px;
  --easy-table-body-item-padding: 4px;

  --easy-table-rows-per-page-selector-width: 70px;
  --easy-table-rows-per-page-selector-option-padding: 10px;

  .v-field__input {
    min-width: 140px;
  }

  .vue3-easy-data-table__main {
    --da0d4328: auto !important;
  }

  .unit-header {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

:deep(.customize) {
  thead {
    display: none !important;
  }

  --da0d4328: auto !important;
}
