export const baseColumns = [
  'name',
  'concentration',
  'density',
  'molar_mass',
  'grams',
  'moles',
  'volume_ml',
  'equivalent',
  'notes'
];
export const columns = [
  /* 'status', */
  ...baseColumns,
  'action'
];

// Function to calculate moles, mass, or molar mass based on available values
export function convertToBaseUnit(value: number, unit: string, type: string): number {
  switch (type) {
    case 'density':
      if (unit === 'g') return value * 1000; // g/cm³ to Kg/m³
      return value; // already in Kg/m³
    case 'molar_mass':
      if (unit === 'g') return value / 1000; // g/mol to Kg/mol
      return value; // already in Kg/mol
    case 'volume':
      if (unit === 'ml') return value / 1000; // ml to L
      if (unit === 'ul') return value / 1000000; // µl to L
      return value; // already in L
    case 'mass':
      if (unit === 'g') return value / 1000; // g to Kg
      if (unit === 'mg') return value / 1000000; // mg to Kg
      return value; // already in Kg
    case 'moles':
      if (unit === 'mmol') return value / 1000; // mmol to mol
      return value; // already in mol
    default:
      return value; // no conversion
  }
}

export function convertFromBaseUnit(value: number, unit: string, type: string): number {
  switch (type) {
    case 'density':
      if (unit === 'g') return value / 1000; // Kg/m³ to g/cm³
      return value; // already in Kg/m³
    case 'molar_mass':
      if (unit === 'g') return value * 1000;
      return value; // already in Kg/mol
    case 'volume':
      if (unit === 'ml') return value * 1000;
      if (unit === 'ul') return value * 1000000;
      return value; // already in L
    case 'mass':
      if (unit === 'g') return value * 1000;
      if (unit === 'mg') return value * 1000000;
      return value; // already in Kg
    case 'moles':
      if (unit === 'mmol') return value * 1000; // mol to mmol
      return value; // already in mol
    default:
      return value; // no conversion
  }
}

export const headers = [
  // {
  //   title: 'Stav',
  //   key: 'status'
  // },
  {
    title: 'Chemikálie',
    key: 'name'
  },
  {
    title: 'Obsah (%)',
    key: 'concentration'
  },
  {
    title: 'p',
    key: 'density'
  },
  {
    title: 'M',
    key: 'molar_mass'
  },
  {
    title: 'm',
    key: 'grams'
  },
  {
    title: 'n',
    key: 'moles'
  },
  {
    title: 'V',
    key: 'volume_ml'
  },
  {
    title: 'ekv.',
    key: 'equivalent'
  },
  {
    title: 'Poznámka',
    key: 'notes'
  },
  {
    title: 'Akce',
    key: 'action'
  }
];
