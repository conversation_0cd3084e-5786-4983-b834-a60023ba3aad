<template>
  <LoaderWrapper
    v-if="!baseDataLoaded || project === null || department === null || chapter === null"
  />
  <template v-else>
    <TopPageBreadcrumb :title="chapter.chapter_title" :_breadcrumbs="breadcrumbItems" />
    <v-row>
      <v-col cols="12" md="12">
        <AnalyticalDepartment
          v-if="department.type === DepartmentEnum.ANALYTICAL"
          :chapter="chapter"
          :project="project"
          :department="department"
          @reload-chapter="reloadChapter"
          @process-file-uploads="(fileIds: number[]) => processFileUploads(fileIds)"
        />
        <SyntheticDepartment
          v-if="department.type === DepartmentEnum.SYNTHETIC"
          :chapter="chapter"
          :project="project"
          :department="department"
          @reload-chapter="reloadChapter"
          @process-file-uploads="(fileIds: number[]) => processFileUploads(fileIds)"
        />
        <TechnicalDepartment
          v-if="department.type === DepartmentEnum.TECHNICAL"
          :chapter="chapter"
          :project="project"
          :department="department"
          @reload-chapter="reloadChapter"
          @process-file-uploads="(fileIds: number[]) => processFileUploads(fileIds)"
        />
      </v-col>
    </v-row>
  </template>
</template>
<script setup lang="ts">
  import type { BreadcrumbI } from '@/components/shared/BaseBreadcrumb.vue';
  import TopPageBreadcrumb from '@/components/shared/TopPageBreadcrumb.vue';
  import LoaderWrapper from '@/layouts/dashboard/LoaderWrapper.vue';
  import { useChaptersStore } from '@/stores/chapters';
  import { useFilesStore } from '@/stores/files';
  import { DepartmentEnum, useProjectsStore } from '@/stores/projects';
  import { useUIStore } from '@/stores/ui';
  import { storeToRefs } from 'pinia';
  import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import AnalyticalDepartment from './components/AnalyticalDepartment.vue';
  import SyntheticDepartment from './components/SyntheticDepartment.vue';
  import TechnicalDepartment from './components/TechnicalDepartment.vue';
  import { notification } from 'ant-design-vue';

  const baseDataLoaded = ref(false);

  const route = useRoute();
  const router = useRouter();
  const uiStore = useUIStore();
  const filesStore = useFilesStore();
  const chaptersStore = useChaptersStore();
  const projectsStore = useProjectsStore();
  const { loading, project, department, chapter } = storeToRefs(projectsStore);

  onBeforeUnmount(() => {
    uiStore.isLoading = false;
    project.value = null;
    department.value = null;
    chapter.value = null;
  });

  watch(loading, () => {
    uiStore.isLoading = loading.value;
  });

  const reloadChapter = async () => {
    if (department.value)
      await projectsStore.getDepartmentById(department.value.project_department_id);
    if (chapter.value) await projectsStore.getChapterById(chapter.value.chapter_id);
  };

  const processFileUploads = async (fileIds: number[] | undefined) => {
    if (!fileIds || !chapter.value?.chapter_id) {
      notification.error({
        message: 'Nepodařilo se nahrát soubory',
        description:
          'Kapitola: ' +
          chapter.value?.chapter_title +
          ' nebyla nalezena nebo nebyly vybrány žádné soubory: ' +
          (fileIds?.join(', ') ?? '/')
      });
      return;
    }

    await chaptersStore.processMultipleFiles(chapter.value.chapter_id, fileIds);
    await reloadChapter();
  };

  onMounted(async () => {
    loadExecute();
  });

  const chapterSearch = ref<string | undefined>(undefined);

  const project_id = computed(() => route.params.project_id as string);
  const projectId = computed(() => parseInt(project_id.value));

  const project_department_id = computed(() => route.params.project_department_id as string);
  const projectDepartmentId = computed(() => parseInt(project_department_id.value));

  const chapter_id = computed(() => route.params.chapter_id as string);
  const chapterId = computed(() => parseInt(chapter_id.value));

  const loadExecute = async () => {
    baseDataLoaded.value = false;
    chapterSearch.value = undefined;

    if (projectId.value) {
      if (project.value) {
        if (project.value.project_id !== projectId.value) {
          await projectsStore.getProjectById(projectId.value);
        }
      } else {
        await projectsStore.getProjectById(projectId.value);
      }
    }

    if (projectDepartmentId.value) {
      if (department.value) {
        if (department.value.project_department_id !== projectDepartmentId.value) {
          await projectsStore.getDepartmentById(projectDepartmentId.value);
        }
      } else {
        await projectsStore.getDepartmentById(projectDepartmentId.value);
      }
    }

    if (chapterId.value) {
      await projectsStore.getChapterById(chapterId.value);
    }

    if (
      department.value &&
      project.value &&
      chapter.value &&
      project.value.departments.find(
        (d) =>
          department.value && d.project_department_id === department.value.project_department_id
      ) &&
      department.value.chapters.find(
        (c) => chapter.value && c.chapter_id === chapter.value.chapter_id
      )
    ) {
      baseDataLoaded.value = true;
    } else {
      if (project.value && department.value) {
        router.push({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value.project_id.toString(),
            project_department_id: department.value.project_department_id.toString()
          }
        });
      } else if (project.value) {
        router.push({
          name: 'ProjectDetail',
          params: { project_id: project.value.project_id.toString() }
        });
      } else {
        router.push({ name: 'ListOfProjects' });
      }
    }
  };

  watch([project_id, project_department_id, chapter_id], () => {
    loadExecute();
  });

  const breadcrumbItems = computed<BreadcrumbI[]>(() => {
    return [
      {
        title: 'Projekty',
        disabled: false,
        href: router.resolve({ name: 'Projects' }).href
      },
      {
        title: project.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'ProjectDetail',
          params: { project_id: project.value?.project_id }
        }).href
      },
      {
        title: department.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id
          }
        }).href
      },
      {
        title: chapter.value?.chapter_title ?? '',
        disabled: true,
        href: router.resolve({
          name: 'ChapterDetail',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id,
            chapter_id: chapter.value?.chapter_id
          }
        }).href
      }
    ];
  });
</script>
