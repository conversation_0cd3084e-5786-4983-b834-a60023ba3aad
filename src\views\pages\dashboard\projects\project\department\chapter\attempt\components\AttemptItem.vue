<template>
  <v-card
    variant="outlined"
    :color="
      isParentClosed
        ? 'error'
        : attempt.isLocked
          ? 'warning'
          : attempt.isActive
            ? 'primary'
            : 'error'
    "
    :class="'card-hover-border bg-containerBg' + (attempt.isActive ? '' : ' strikethrough-tex')"
    :subtitle="`Štítky: ${attempt.tags && attempt.tags.length > 0 ? attempt.joinTags : '<PERSON>z <PERSON>'}`"
    :title="formattedTitle"
    :disabled="!checkProjectTypePermisions()"
    @click.prevent.stop="openAttempt"
  >
    <template #prepend>
      <LockOutlined v-if="attempt.isLocked" :style="{ fontSize: '28px' }" />
      <ExperimentOutlined v-else :style="{ fontSize: '28px' }" />
      <div v-if="!isTabletOrSmaller">
        <div v-if="attempt.batch_number" class="absolute-center">
          <p class="v-card-title">{{ attempt.batch_number.batch_number }}</p>
        </div>
        <div v-else class="absolute-center">
          <p class="v-card-title">Pokus nemá číslo šarže</p>
        </div>
        <div class="absolute-center-2">
          <p>{{ formattedDescription }}</p>
        </div>
      </div>
    </template>
    <template #append>
      <v-menu>
        <template #activator="{ props }">
          <v-btn
            size="x-small"
            v-bind="props"
            variant="text"
            style="height: auto"
            @click.prevent.stop="props.isActive = true"
          >
            <EllipsisOutlined :style="{ fontSize: '28px' }" />
          </v-btn>
        </template>

        <v-list elevation="24" density="compact" class="py-0">
          <v-list-item v-if="!isParentClosed" :value="attempt.form_id + '_tags'">
            <v-list-item-title @click="handleClick(toggleTagsModal, 'tags')">
              Správa štítků
            </v-list-item-title>
          </v-list-item>
          <v-list-item v-if="!isParentClosed" :value="attempt.form_id + '_copy'">
            <v-list-item-title @click="openCopyModal">Kopírovat pokus</v-list-item-title>
          </v-list-item>
          <v-list-item v-if="!isParentClosed" :value="attempt.form_id + '_pdf'">
            <v-list-item-title @click="exportAsPdf()">Exportovat jako PDF</v-list-item-title>
          </v-list-item>
          <v-list-item v-if="!isParentClosed" :value="attempt.form_id + '_word'">
            <v-list-item-title @click="exportAsWord()">Exportovat jako Word</v-list-item-title>
          </v-list-item>
          <v-list-item
            v-if="attempt.isActive && !isParentClosed"
            :value="attempt.form_id + '_close'"
          >
            <v-list-item-title @click.prevent="handleClick(closeForm, 'close')">
              Uzavřít pokus
            </v-list-item-title>
          </v-list-item>
          <v-list-item
            v-if="!attempt.isActive && !isParentClosed && !openPermissions"
            :value="attempt.form_id + '_ask_reopen'"
          >
            <v-list-item-title @click.prevent="askForReopen()">
              Požádat o otevření
            </v-list-item-title>
          </v-list-item>
          <v-list-item
            v-if="!attempt.isActive && !isParentClosed && openPermissions"
            :value="attempt.form_id + '_reopen'"
          >
            <v-list-item-title @click.prevent="handleClick(reopenForm, 'open')">
              Otevřít pokus
            </v-list-item-title>
          </v-list-item>
          <v-list-item v-if="!isParentClosed" :value="attempt.form_id + '_move'">
            <v-list-item-title @click="openMoveModal">Přesunout do jiné kapitoly</v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>
    </template>
  </v-card>
  <ConfirmDlg ref="ConfirmRef" />
  <TagModal
    v-if="attempt.tagIsLoaded"
    v-model:show="showExperimentTagsModal"
    :tags="attempt.tagLists"
    @update-tags="handleUpdateTags"
  />
  <CopyAttemptModal
    v-model:show="showCopyModal"
    :default-name="copyFormName"
    :loading="copyLoading"
    @confirm="handleCopyAttempt"
  />
  <MoveToChapterModal
    v-model:show="showMoveModal"
    :chapters="chapters"
    :loading="moveLoading"
    @confirm="handleMoveToChapter"
  />
</template>
<script lang="ts" setup>
  import TagModal, { type TagListI } from '@/components/shared/TagModal.vue';
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import { useAuthStore } from '@/stores/auth';
  import { type Attempt, useAttemptsStore } from '@/stores/attempts';
  import { useFormsStore } from '@/stores/forms';
  import { UserLockTableName, useUserLock } from '@/stores/userLock';
  import { EllipsisOutlined, ExperimentOutlined, LockOutlined } from '@ant-design/icons-vue';
  import { ref, onMounted, onUnmounted, computed } from 'vue';
  import { notification } from 'ant-design-vue';
  import { useRouter, useRoute } from 'vue-router';
  import { isAllowed } from '@/utils/directive/isAllowed';
  import { useProjectsStore } from '@/stores/projects';
  import { storeToRefs } from 'pinia';
  import CopyAttemptModal from './CopyAttemptModal.vue';
  import MoveToChapterModal from '@/components/shared/MoveToChapterModal.vue';
  const ConfirmRef = ref<InstanceType<typeof ConfirmDlg> | null>(null);
  const projectsStore = useProjectsStore();
  const { project_permision } = storeToRefs(projectsStore);
  const emits = defineEmits(['reload']);

  const props = defineProps<{
    attempt: Attempt;
    isParentClosed: boolean;
  }>();

  const router = useRouter();
  const route = useRoute();
  const auth = useAuthStore();
  const userLock = useUserLock();
  const formsStore = useFormsStore();
  const attemptsStore = useAttemptsStore();

  const { user } = storeToRefs(auth);

  const project_department_id = computed(() => route.params.project_department_id as string);
  const projectDepartmentId = computed(() => parseInt(project_department_id.value));

  const exportAsPdf = async () => {
    formsStore.form_id = props.attempt.form_id;
    const res = await formsStore.exportFormAsPdf(props.attempt?.form_name);
  };

  const exportAsWord = async () => {
    formsStore.form_id = props.attempt.form_id;
    const res = await formsStore.exportFormAsWord(props.attempt?.form_name);
  };

  const redirectToAttemptDetailLink = (id: number) => {
    return router.resolve({
      name: 'Attempt',
      params: { form_id: id.toString() }
    });
  };

  const openAttempt = async () => {
    if (props.attempt.isLocked) {
      if (
        auth.user?.user_id &&
        props.attempt.edited_by?.user_id &&
        (await ConfirmRef.value?.open(
          'Pokus je uzamčen',
          `Právě edituje uživatel ${props.attempt.edited_by?.user?.getName} (${props.attempt.edited_by?.user?.user_email}). Chcete pokračovat?`,
          {
            color: 'error',
            notclosable: true,
            zIndex: 2400
          }
        ))
      ) {
        await userLock.unlockRowForUser(
          props.attempt.edited_by.user_id,
          UserLockTableName.ATTEMPT,
          props.attempt.form_id
        );
        await userLock.lockRowForUser(
          auth.user.user_id,
          UserLockTableName.ATTEMPT,
          props.attempt.form_id
        );
        router.push(redirectToAttemptDetailLink(props.attempt.form_id));
      }
    } else {
      router.push(redirectToAttemptDetailLink(props.attempt.form_id));
    }
  };
  const checkProjectTypePermisions = () => {
    return (
      isAllowed(['view_technological_department']) || isAllowed(['edit_technological_department'])
    );
  };
  const isTabletOrSmaller = ref(false);

  const updateIsTabletOrSmaller = () => {
    isTabletOrSmaller.value = window.matchMedia('(max-width: 768px)').matches;
  };

  onMounted(() => {
    updateIsTabletOrSmaller();
    updateLenght();
    window.addEventListener('resize', updateIsTabletOrSmaller);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', updateIsTabletOrSmaller);
  });

  const showExperimentTagsModal = ref(false);

  const toggleTagsModal = () => {
    showExperimentTagsModal.value = !showExperimentTagsModal.value;
  };

  const handleUpdateTags = async (tags: TagListI[]) => {
    await attemptsStore.updateTags(props.attempt, tags);
    emits('reload');
  };

  const closeForm = async () => {
    if (
      await ConfirmRef.value?.open('Opravdu chcete uzavřít pokus?', '', {
        color: 'error',
        notclosable: true,
        zIndex: 2400
      })
    ) {
      const res = await attemptsStore.closeAttempt(props.attempt.form_id, false);
      if (res) emits('reload');
    }
  };
  const reopenForm = async () => {
    await attemptsStore.reactivateAttempt(props.attempt.form_id);
    emits('reload');
  };

  const checkAdminSignPermission = () => {
    return isAllowed(['sign_experiments']);
  };

  const checkAdminOpenPermission = () => {
    return isAllowed(['reopen_experiments']);
  };

  const havePermision = ref<boolean>(true);
  const missingPermison = ref<string>();
  const checkProjectTypePermisionsForEdit = () => {
    if (isAllowed(['edit_technological_department']) || isAllowed(['edit_all'])) {
      havePermision.value = true;
      return true;
    } else {
      missingPermison.value = 'edit_technological_department';
      havePermision.value = false;
    }

    if (!havePermision.value) {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nemáte oprávnění pro editaci pokusu: ' + missingPermison.value + '.'
      });
    }
    return false;
  };

  const checkIfCurrentUserIsOwner = () => {
    if (props.attempt?.owner === null || user.value === null) {
      return false;
    }
    return props.attempt?.owner?.user_id === user.value?.user_id;
  };

  const checkIfCurrentUserIsCollaborator = () => {
    if (props.attempt?.collaborators === null && user.value === null) {
      return false;
    }
    return (
      props.attempt?.collaborators.some(
        (collaborator) => collaborator.user_id === user.value?.user_id
      ) || checkIfCurrentUserIsOwner()
    );
  };

  const askForReopen = async () => {
    if (props.attempt.form_id) {
      await formsStore.requestReopenForm(props.attempt.form_id);
    }
  };

  const openPermissions = computed(() => {
    if (checkAdminPermission()) {
      return true;
    } else if (
      checkIfCurrentUserIsCollaborator() &&
      isAllowed(['edit_technological_department']) &&
      checkAdminOpenPermission()
    ) {
      return true;
    }
    return false;
  });

  const handleClick = (action: () => void, type: string) => {
    if (checkAdminPermission()) {
      action();
      return;
    } else if (
      type === 'close' &&
      checkAdminSignPermission() &&
      checkIfCurrentUserIsCollaborator() &&
      isAllowed(['edit_technological_department'])
    ) {
      action();
      return;
    } else if (
      type === 'open' &&
      checkAdminOpenPermission() &&
      checkIfCurrentUserIsCollaborator() &&
      isAllowed(['edit_technological_department'])
    ) {
      action();
      return;
    } else if (type === 'close') {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nemáte oprávnění uzavřít pokus.'
      });
      return;
    } else if (type === 'open') {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nemáte oprávnění otevřít pokus.'
      });
      return;
    }
    if (checkProjectTypePermisionsForEdit()) {
      action();
    }
  };

  defineExpose({
    toggleTagsModal,
    handleUpdateTags,
    closeForm,
    reopenForm
  });

  const maxLegth = ref(40);

  const formattedTitle = computed(() => {
    if (!props.attempt.form_name) return '';
    return props.attempt.form_name.length > maxLegth.value
      ? `${props.attempt.form_name.slice(0, maxLegth.value)}...`
      : props.attempt.form_name;
  });

  const formattedDescription = computed(() => {
    if (!props.attempt.batch_description) return '';
    return props.attempt.batch_description.length > maxLegth.value
      ? `${props.attempt.batch_description.slice(0, maxLegth.value)}...`
      : props.attempt.batch_description;
  });
  const updateLenght = () => {
    if (window.innerWidth < 1465) {
      maxLegth.value = 20;
    } else {
      maxLegth.value = 40;
    }
  };

  const checkAdminPermission = () => {
    return isAllowed(['edit_all']);
  };

  const showCopyModal = ref(false);
  const copyFormName = ref('');
  const copyLoading = computed(() => attemptsStore.loading);
  function openCopyModal() {
    copyFormName.value = `Kopie – ${props.attempt.form_name}`;
    showCopyModal.value = true;
  }
  async function handleCopyAttempt(newName: string) {
    await attemptsStore.copyAttempt(props.attempt.form_id, { form_name: newName });
  }

  const showMoveModal = ref(false);
  const chapters = ref<any[]>([]);
  const moveLoading = ref(false);

  async function openMoveModal() {
    moveLoading.value = true;
    const department = await projectsStore.getDepartment(projectDepartmentId.value);
    chapters.value = (department?.chapters?.filter((c: any) => c.chapter_id !== props.attempt.chapter_id)
      .map((c: any) => ({ chapter_id: c.chapter_id, chapter_title: c.chapter_title, status: c.status }))
    ) || [];
    moveLoading.value = false;
    showMoveModal.value = true;
  }

  async function handleMoveToChapter(chapter_id: number) {
    moveLoading.value = true;
    formsStore.form_id = props.attempt.form_id;
    const res = await formsStore.moveToDifferentChapter(chapter_id);
    moveLoading.value = false;
    if (res) emits('reload');
  }
</script>
<style scoped lang="scss">
  .absolute-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }

  .absolute-center-2 {
    position: absolute;
    top: 75%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }
</style>
