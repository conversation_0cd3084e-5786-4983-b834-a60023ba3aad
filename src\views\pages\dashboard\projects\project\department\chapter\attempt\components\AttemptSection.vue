<template>
  <section class="my-5">
    <span class="text-h4">Pokusy</span>
  </section>
  <v-row v-if="attempts.length > 0">
    <v-col
      v-for="attempt in attempts
        .filter((chapter) =>
          attemptSearch
            ? chapter.form_name
                .toUpperCase()
                .includes(attemptSearch.toUpperCase().replace(/\s+/g, '')) ||
              chapter.joinTags
                .toUpperCase()
                .includes(attemptSearch.toUpperCase().replace(/\s+/g, ''))
            : true
        )
        .sort((a, b) => {
          const statusOrder = { created: 1, in_progress: 2, signed: 3, canceled: 4, completed: 5 };
          if (statusOrder[a.status] !== statusOrder[b.status]) {
            return statusOrder[a.status] - statusOrder[b.status];
          }
          return a.created_at > b.created_at ? -1 : 1;
        })"
      :key="attempt.form_id"
      cols="12"
    >
      <AttemptItem
        :attempt="attempt"
        :is-parent-closed="isParentClosed"
        @reload="$emit('reload')"
      />
    </v-col>
  </v-row>
  <NotFoundItem v-else>
    <template #notFound>Nebyly nalezeny žádné pokusy</template>
  </NotFoundItem>
</template>

<script lang="ts" setup>
  import NotFoundItem from '@/components/shared/NotFoundItem.vue';
  import type { Attempt } from '@/stores/attempts';
  import AttemptItem from './AttemptItem.vue';

  defineEmits(['reload']);
  defineProps<{
    attempts: Attempt[];
    attemptSearch: string | undefined;
    isParentClosed: boolean;
  }>();
</script>
