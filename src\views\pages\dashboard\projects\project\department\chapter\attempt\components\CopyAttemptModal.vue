<template>
  <v-dialog v-model="showState" class="customer-modal" :z-index="1010">
    <v-card>
      <v-form ref="formRef" @submit.prevent="onSubmit">
        <v-card-title class="pa-5">
          <span class="text-h5">Kopírovat pokus</span>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col cols="12">
              <v-label class="mb-2">Název nového pokusu</v-label>
              <v-text-field
                v-model="formName"
                :rules="[v => !!v || 'Název je povinný']"
                single-line
                placeholder="Zadejte název pokusu"
                hide-details="auto"
                variant="outlined"
                rounded="sm"
                autofocus
              ></v-text-field>
            </v-col>
            <v-col cols="12">
              <div class="d-flex justify-space-between gap-2">
                <div class="pb-4">
                  <h6 class="text-subtitle-1 mb-0">Potvrdit kopírování</h6>
                </div>
                <v-switch v-model="confirmChecked" color="primary" class="switchRight" hide-details></v-switch>
              </div>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" variant="text" @click="showState = false">Zrušit</v-btn>
          <v-btn color="primary" variant="flat" type="submit" :loading="loading">
            Kopírovat
          </v-btn>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import { useVModel } from '@vueuse/core';
const emit = defineEmits(['update:show', 'confirm']);
import { notification } from 'ant-design-vue';
const props = defineProps({
  show: { type: Boolean, required: true },
  defaultName: { type: String, required: true },
  loading: { type: Boolean, default: false }
});
const showState = useVModel(props, 'show');
const formName = ref(props.defaultName);
const confirmChecked = ref(false);
const formRef = ref();
watch(() => props.defaultName, (val) => { formName.value = val; });
function onSubmit() {
  if (!formName.value) return;
  if (!confirmChecked.value) {
        notification.error({ message: 'Musíte potvrdit souhlas s kopírováním pokusu' });
        return false;
      }
  emit('confirm', formName.value);
  showState.value = false;
}
</script>
<style scoped>
</style>