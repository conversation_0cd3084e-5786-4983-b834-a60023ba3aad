<script setup lang="ts">
  import { useAttemptsStore } from '@/stores/attempts';
  import { useAttemptTemplates, AttemptTemplateStatus } from '@/stores/attemptTemplates';
  import { useInvestigationTemplates } from '@/stores/investigationsTemplates';
  import { FormType, useFormsStore } from '@/stores/forms';
  import { useInvestigationsStore } from '@/stores/investigations';
  import { useMessagesStore } from '@/stores/messages';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { useVModel } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import { computed, onMounted, ref, watch, reactive } from 'vue';
  import { useRoute } from 'vue-router';
  import { type GetAllOptions } from '@/stores/projects';
  const emit = defineEmits(['update:show', 'createForm']);
  const route = useRoute();
  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    }
  });

  const showState = useVModel(props, 'show');
  const formType = ref<FormType | null>(null);

  const formsStore = useFormsStore();
  const { modalOptions } = storeToRefs(formsStore);

  const attemptsStore = useAttemptsStore();
  const messagesStore = useMessagesStore();
  const investigationsStore = useInvestigationsStore();

  const CreateInstrumentForm = ref();
  async function submitFormToValidate() {
    if (CreateInstrumentForm.value.isValid) {
      if (attemptsStore.modalOptions && modalOptions.value?.newData.attempt) {
        attemptsStore.modalOptions.newData = modalOptions.value.newData.attempt;
        const res = await attemptsStore.createAttempt();
        if (res) {
          //emit('createForm');
          formsStore.resetNewFormModalWithSelectFormTypeForm();
        }
        // else {
        //   notification.error({
        //     message: 'Chyba',
        //     description: 'Při vytváření pokusu došlo k chybě'
        //   });
        // }
      } else if (investigationsStore.modalOptions && modalOptions.value?.newData.investigation) {
        investigationsStore.modalOptions.newData = modalOptions.value.newData.investigation;
        const res = await investigationsStore.createInvestigation();
        if (res) {
          //emit('createForm');
          formsStore.resetNewFormModalWithSelectFormTypeForm();
        }
        // else {
        //   notification.error({
        //     message: 'Chyba',
        //     description: 'Při vytváření šetření došlo k chybě'
        //   });
        // }
      } else if (messagesStore.modalOptions && modalOptions.value?.newData.message) {
        messagesStore.modalOptions.newData = modalOptions.value.newData.message;
        const res = await messagesStore.createMessage();
        if (res) {
          //emit('createForm');
          formsStore.resetNewFormModalWithSelectFormTypeForm();
        }
        // else {
        //   notification.error({
        //     message: 'Chyba',
        //     description: 'Při vytváření zprávy došlo k chybě'
        //   });
        // }
      }
    } else {
      if (modalOptions.value && modalOptions.value.newData) {
        if (modalOptions.value.newData.attempt) {
          modalOptions.value.newData.attempt.confirm = false;
        }
        if (modalOptions.value.newData.investigation) {
          modalOptions.value.newData.investigation.confirm = false;
        }
        if (modalOptions.value.newData.message) {
          modalOptions.value.newData.message.confirm = false;
        }
      }
    }
  }

  const setSelectedFormTYpe = async () => {
    if (!modalOptions.value || !formType.value) return;

    switch (formType.value) {
      case FormType.ATTEMPT: {
        await attemptsStore.showNewAttemptModal(modalOptions.value.baseData.chapter.chapter_id);
        modalOptions.value.newData.attempt = attemptsStore.modalOptions?.newData;
        modalOptions.value.newData.message = undefined;
        modalOptions.value.newData.investigation = undefined;

        break;
      }
      case FormType.MESSAGE: {
        await messagesStore.showNewMessageModal(modalOptions.value.baseData.chapter.chapter_id);
        modalOptions.value.newData.message = messagesStore.modalOptions?.newData;
        modalOptions.value.newData.attempt = undefined;
        modalOptions.value.newData.investigation = undefined;

        break;
      }
      case FormType.INVESTIGATION: {
        await investigationsStore.showNewInvestigationModal(
          modalOptions.value.baseData.chapter.chapter_id
        );
        modalOptions.value.newData.investigation = investigationsStore.modalOptions?.newData;
        modalOptions.value.newData.attempt = undefined;
        modalOptions.value.newData.message = undefined;

        break;
      }
      default:
        break;
    }
  };

  watch(formType, async () => {
    if (formType.value) {
      await setSelectedFormTYpe();
    }
  });

  const showNextPage = computed(() => {
    if (!modalOptions.value) return false;

    switch (formType.value) {
      case FormType.ATTEMPT:
        return modalOptions.value.newData.attempt !== undefined;
      case FormType.MESSAGE:
        return modalOptions.value.newData.message !== undefined;
      case FormType.INVESTIGATION:
        return modalOptions.value.newData.investigation !== undefined;
      default:
        return false;
    }
  });

  const project_id = computed(() => route.params.project_id as string);
  const projectId = computed(() => parseInt(project_id.value));

  const attemptTemplatesStore = useAttemptTemplates();
  const { attemptTemplates } = storeToRefs(attemptTemplatesStore);
  const investigationTemplatesStore = useInvestigationTemplates();
  const { investigationTemplates } = storeToRefs(investigationTemplatesStore);
  onMounted(async () => {
    const optionsAttemptTemplates = reactive<GetAllOptions>({
      search: '',
      totalItems: undefined,
      options: {
        page: 1,
        rowsPerPage: 100,
        sortBy: ['status', 'updated_at'],
        sortType: ['asc']
      },
      search_columns: ['template_name'],
      fixedFilterOptions: [
        {
          column: 'status',
          value: AttemptTemplateStatus.ACTIVE
        },
        {
          column: 'project_id',
          value: projectId.value ?? null
        }
      ]
    });
    await attemptTemplatesStore.getAttemptTemplates(true, optionsAttemptTemplates);
    // investigationTemplatesStore.getInvestigationTemplates();
  });
</script>
<template>
  <v-dialog v-model="showState" class="customer-modal" :z-index="1010">
    <v-card>
      <v-form
        v-if="modalOptions"
        ref="CreateInstrumentForm"
        class="createInstrumentForm"
        @submit.prevent="submitFormToValidate"
      >
        <v-card-title class="pa-5">
          <span class="text-h5">Přidat nový formulář</span>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col>
              <v-row>
                <v-col cols="12">
                  <v-label class="mb-2">Vyberte typ formuláře</v-label>
                  <v-select
                    v-model="formType"
                    :rules="itemRequiredRule"
                    item-text="title"
                    item-value="value"
                    placeholder="Vyberte šablonu"
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                    density="compact"
                    clearable
                    :items="[
                      {
                        title: 'Pokus',
                        value: FormType.ATTEMPT
                      },
                      {
                        title: 'Zpráva',
                        value: FormType.MESSAGE
                      },
                      {
                        title: 'Šetření',
                        value: FormType.INVESTIGATION
                      }
                    ]"
                  ></v-select>
                </v-col>
              </v-row>

              <template v-if="showNextPage">
                <v-row v-if="modalOptions.newData.attempt">
                  <v-col cols="12">
                    <v-label class="mb-2">Název formuláře</v-label>
                    <v-text-field
                      v-model="modalOptions.newData.attempt.form_name"
                      :rules="itemRequiredRule"
                      single-line
                      placeholder="Zadejte název formuláře"
                      hide-details="auto"
                      variant="outlined"
                      rounded="sm"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12">
                    <v-label class="mb-2">Šablona</v-label>
                    <v-select
                      v-model="modalOptions.newData.attempt.attempt_template_id"
                      :items="
                        attemptTemplates.map((attemptTemplate) => ({
                          id: attemptTemplate.attempt_template_id,
                          title: attemptTemplate.template_name
                        }))
                      "
                      item-text="name"
                      item-value="id"
                      placeholder="Vyberte šablonu"
                      hide-details="auto"
                      variant="outlined"
                      rounded="sm"
                      density="compact"
                      clearable
                    ></v-select>
                  </v-col>

                  <v-col cols="12">
                    <div class="d-flex justify-space-between gap-2">
                      <div class="pb-4">
                        <h6 class="text-subtitle-1 mb-0">Potvrzení přidání</h6>
                      </div>
                      <v-switch
                        v-model="modalOptions.newData.attempt.confirm"
                        color="primary"
                        class="switchRight"
                        hide-details
                      ></v-switch>
                    </div>
                  </v-col>
                </v-row>

                <v-row v-if="modalOptions.newData.investigation">
                  <v-col cols="12">
                    <v-label class="mb-2">Název formuláře</v-label>
                    <v-text-field
                      v-model="modalOptions.newData.investigation.form_name"
                      :rules="itemRequiredRule"
                      single-line
                      placeholder="Zadejte název formuláře"
                      hide-details="auto"
                      variant="outlined"
                      rounded="sm"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12">
                    <div class="d-flex justify-space-between gap-2">
                      <div class="pb-4">
                        <h6 class="text-subtitle-1 mb-0">Potvrzení přidání</h6>
                      </div>
                      <v-switch
                        v-model="modalOptions.newData.investigation.confirm"
                        color="primary"
                        class="switchRight"
                        hide-details
                      ></v-switch>
                    </div>
                  </v-col>
                </v-row>

                <v-row v-if="modalOptions.newData.message">
                  <v-col cols="12">
                    <v-label class="mb-2">Název formuláře</v-label>
                    <v-text-field
                      v-model="modalOptions.newData.message.form_name"
                      :rules="itemRequiredRule"
                      single-line
                      placeholder="Zadejte název formuláře"
                      hide-details="auto"
                      variant="outlined"
                      rounded="sm"
                    ></v-text-field>
                  </v-col>

                  <v-col cols="12">
                    <div class="d-flex justify-space-between gap-2">
                      <div class="pb-4">
                        <h6 class="text-subtitle-1 mb-0">Potvrzení přidání</h6>
                      </div>
                      <v-switch
                        v-model="modalOptions.newData.message.confirm"
                        color="primary"
                        class="switchRight"
                        hide-details
                      ></v-switch>
                    </div>
                  </v-col>
                </v-row>
              </template>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="error"
            variant="text"
            @click="formsStore.resetNewFormModalWithSelectFormTypeForm()"
          >
            Zrušit
          </v-btn>
          <v-btn :disabled="messagesStore.loading" color="primary" variant="flat" type="submit">Přidat</v-btn>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>
