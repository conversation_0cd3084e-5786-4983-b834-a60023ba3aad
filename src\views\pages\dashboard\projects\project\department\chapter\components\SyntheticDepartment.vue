<template>
  <UiParentCard class="pa-0" :loading="!baseDataLoaded">
    <template #action>
      <v-row justify="space-between" class="align-center">
        <v-col cols="12" md="4" class="d-flex align-center">
          <v-combobox
            v-model:search="searchValue"
            v-model="selectedItemToNavigate"
            :items="
              filteredItems.map((search_item, index) => {
                return {
                  path: search_item.route,
                  title: search_item.breadcrumb,
                  search_item: search_item,
                  value: `${search_item.route}-${search_item.search_id ?? index}`
                };
              })
            "
            hide-details
            rounded="sm"
            variant="outlined"
            single-line
            :slim="true"
            label="Zadejte hledaný výraz"
            placeholder="Zadejte hledaný výraz"
            :loading="loadingSearch"
            color="primary"
            class="autocomplete no-dropdown-arrow"
            no-filter
            hide-no-data
            clearable
            menu-icon=""
            @update:model-value="navigateToItem"
            @keydown.enter.prevent="navigateToItem(selectedItemToNavigate)"
          >
            <template #prepend-inner>
              <SearchOutlined class="text-lightText" />
            </template>
            <template #item="{ props, item }">
              <v-list-item v-bind="props" :key="item.raw.value" :title="''">
                <div class="player-wrapper pa-2">
                  <h6
                    class="text-subtitle-1 mb-0"
                    v-html="highlightMatch(item.raw.search_item.breadcrumb)"
                  ></h6>
                </div>
              </v-list-item>
            </template>
          </v-combobox>

          <v-btn
            v-if="baseDataLoaded"
            class="ml-2"
            variant="flat"
            color="primary"
            icon
            rounded="sm"
            size="default"
            @click.prevent="toggleFilterModal"
          >
            <FilterOutlined :style="{ fontSize: '18px' }" />
          </v-btn>
        </v-col>
        <v-col cols="12" md="2" class="d-flex align-center">
          <v-date-input
            v-model="start_date"
            density="compact"
            single-line
            hide-details="auto"
            variant="outlined"
            rounded="sm"
            cancel-text="Zrušit"
            ok-text="Potvrdit"
            prepend-icon=""
            clearable
            label="Od: "
            :max="maxFromDate"
            @input="onFromDateChange"
          ></v-date-input>
        </v-col>
        <v-col cols="12" md="2" class="d-flex align-center">
          <v-date-input
            v-model="end_date"
            density="compact"
            single-line
            hide-details="auto"
            variant="outlined"
            rounded="sm"
            cancel-text="Zrušit"
            ok-text="Potvrdit"
            prepend-icon=""
            label="Do: "
            clearable
            :min="minToDate"
            @input="onToDateChange"
          ></v-date-input>
        </v-col>
        <v-col cols="12" md="4">
          <div class="d-flex gap-2 justify-end flex-wrap">
            <v-btn
              :disabled="!havePermisionForChannelog"
              variant="flat"
              color="primary"
              @click.prevent="showLogs"
            >
              Změny
            </v-btn>
            <v-btn
              v-if="chapter.parent_chapter_id === null"
              variant="flat"
              color="primary"
              :loading="filesLoading"
              :disabled="!checkReadOnly() || isParentClosed || !project_permision"
              @click.prevent="filesStore.showFileUploaderModal = true"
            >
              Nahrát soubory
            </v-btn>
            <v-btn
              v-if="chapter.parent_chapter_id === null"
              variant="flat"
              color="primary"
              :loading="experimentsLoading"
              :disabled="!checkReadOnly() || isParentClosed || !project_permision"
              @click.prevent="experimentsStore.showNewExperimentModal(chapter.chapter_id)"
            >
              Přidat experiment
            </v-btn>
            <v-btn
              v-if="chapter.parent_chapter_id !== null"
              variant="flat"
              color="primary"
              :loading="chapterLoading"
              :disabled="!checkReadOnly() || isParentClosed || !project_permision"
              @click.prevent="
                chaptersStore.showNewChapterModal(
                  department.project_department_id,
                  chapter.chapter_id
                )
              "
            >
              Přidat kapitolu
            </v-btn>
          </div>
        </v-col>
      </v-row>
    </template>

    <ChapterSection
      v-if="chapter.parent_chapter_id !== null"
      :chapters="chapter.sub_chapters"
      :chapter-search="chapterSearch"
      :is-parent-closed="isParentClosed"
      @reload="emits('reloadChapter')"
    />
    <ExperimentSection
      v-if="
        chapter.parent_chapter_id === null &&
        baseDataLoaded &&
        experiments &&
        experiments.length > 0
      "
      :key="'ExperimentSection' + refreshKey"
      :experiments="experiments"
      :experiment-search="chapterSearch"
      :is-parent-closed="isParentClosed"
      @reload="emits('reloadChapter')"
    />
    <template v-if="checkReadOnly()">
      <FileSection
        v-if="chapter.parent_chapter_id === null"
        :is-read-only="!checkProjectTypePermisions() || isParentClosed || !project_permision"
        :files="chapter.files"
        :file-search="chapterSearch"
        :custom-remove-file="true"
        @reload="emits('reloadChapter')"
        @file-remove="
          async (file_id: number) => {
            const res = await chaptersStore.deleteFileFromChapter(chapter.chapter_id, file_id);
            if (res) {
              emits('reloadChapter');
            }
          }
        "
      />
    </template>
  </UiParentCard>

  <ChangelogPanel v-if="logsStore.showDrawer" v-model:show="logsStore.showDrawer" />
  <FileUploaderModal
    v-if="chapter.parent_chapter_id === null"
    v-model:show="filesStore.showFileUploaderModal"
    @submit-files="(fileIds: number[]) => emits('processFileUploads', fileIds)"
  />
  <ExperimentModal
    v-if="chapter.parent_chapter_id === null"
    v-model:show="experimentsStore.showExperimentModal"
    @create-experiment="emits('reloadChapter')"
  />
  <ChapterModal
    v-if="chapter.parent_chapter_id !== null"
    v-model:show="chaptersStore.showChapterModal"
    @create-chapter="emits('reloadChapter')"
    @update-chapter="emits('reloadChapter')"
  />

  <FilterModal
    v-if="baseDataLoaded"
    v-model:show="showFilterModal"
    :search-value="actualSearchValue"
    @update-search="updateSearch"
    @update-search-and-reset="updateSearchAndReset"
  />
</template>
<script setup lang="ts">
  import { useProjectsStore } from '@/stores/projects';
  import ChangelogPanel from '@/components/shared/ChangelogPanel.vue';
  import FileSection from '@/components/shared/file/FileSection.vue';
  import FileUploaderModal from '@/components/shared/file/FileUploaderModal.vue';
  import UiParentCard from '@/components/shared/UiParentCard.vue';
  import type { Attempt } from '@/stores/attempts';
  import { useChaptersStore, type Chapter } from '@/stores/chapters';
  import { Experiment, useExperimentsStore } from '@/stores/experiments';
  import { useFilesStore } from '@/stores/files';
  import { useFormsStore } from '@/stores/forms';
  import type { Investigation } from '@/stores/investigations';
  import { useLogsStore } from '@/stores/logs';
  import type { Message } from '@/stores/messages';
  import {
    type Department,
    type Project,
    type SearchResult,
    translateKey
  } from '@/stores/projects';
  import { SearchOutlined, FilterOutlined } from '@ant-design/icons-vue';
  import { storeToRefs } from 'pinia';
  import { computed, onMounted, ref, watch, onUnmounted } from 'vue';
  import ChapterModal from '../../components/ChapterModal.vue';
  import ChapterSection from '../../components/ChapterSection.vue';
  import ExperimentModal from '../experiment/components/ExperimentModal.vue';
  import ExperimentSection from '../experiment/components/ExperimentSection.vue';
  import { notification } from 'ant-design-vue';
  import { isAllowed } from '@/utils/directive/isAllowed';
  import { useRoute, useRouter } from 'vue-router';
  import { useDebounceFn, useVModel } from '@vueuse/core';

  import FilterModal from '@/components/shared/ProjectFilter.vue';
  import { setPageTitle } from '@/utils/title';

  const emits = defineEmits(['reloadChapter', 'processFileUploads']);
  const props = defineProps<{
    chapter: Chapter;
    project: Project;
    department: Department;
  }>();
  const route = useRoute();
  const router = useRouter();
  const logsStore = useLogsStore();
  const filesStore = useFilesStore();
  const formsStore = useFormsStore();
  const chaptersStore = useChaptersStore();
  const experimentsStore = useExperimentsStore();
  const projectsStore = useProjectsStore();

  const {
    project_permision,
    allProjectsOptionsPageSearch,
    loading,
    loadingSearch,
    extendsSearch,
    tables,
    searchTables,
    start_date,
    end_date
  } = storeToRefs(projectsStore);

  const havePermision = ref<boolean>(true);
  const missingPermison = ref<string>();
  const checkPermision = () => {
    if (isAllowed(['view_changelog_syntetic']) || isAllowed(['edit_all'])) {
      havePermision.value = true;
      return true;
    } else {
      missingPermison.value = 'view_changelog_syntetic';
      havePermision.value = false;
    }
    if (!havePermision.value) {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nemáte oprávnění pro prohlížení changelogu: ' + missingPermison.value + '.'
      });
    }
    return false;
  };

  const showLogs = () => {
    if (checkPermision()) {
      if (props.chapter.forms.length > 0) {
        logsStore.setExperimentTableAsActiveFilter(props.chapter.forms.map((form) => form.form_id));
      }
      if (props.chapter.forms.length > 0) {
        logsStore.setFormStatusAsActiveFilter(
          props.chapter.forms.map((form) => form.form_id),
          true
        );
      }

      if (props.chapter.files.length > 0) {
        logsStore.setFileTableAsActiveFilter(
          props.chapter.files.map((file) => file.file_id),
          true
        );
      }

      logsStore.showDrawer = true;
    }
  };

  const { loading: filesLoading } = storeToRefs(filesStore);
  const { loading: chapterLoading } = storeToRefs(chaptersStore);
  const { loading: experimentsLoading } = storeToRefs(experimentsStore);

  const forms = ref<(Experiment | Message | Investigation | Attempt)[]>([]);
  const chapterSearch = ref<string | undefined>(undefined);
  const baseDataLoaded = ref(false);
  const refreshKey = ref(0);

  const experiments = computed(() => {
    return forms.value.filter((form) => form.form_type === 'experiment') as Experiment[];
  });

  const loadExecute = useDebounceFn(async () => {
    forms.value = [];
    for (const form of props.chapter.forms) {
      forms.value.push(form);
    }
    refreshKey.value += 1;

    setPageTitle(props.chapter.chapter_title);
  }, 350);

  const getForm = async (form_id: number) => {
    await formsStore.getForm(form_id);
  };
  const checkAdminViewPermission = () => {
    return isAllowed(['view_all']);
  };

  const searchTable = ['analytical_request', 'batch_number', 'form', 'method', 'chemical', 'tag'];

  onMounted(async () => {
    start_date.value = undefined;
    end_date.value = undefined;
    emptySearchResults();
    if (
      (await checkProjectTypePermisions()) ||
      checkAdminViewPermission() ||
      checkAdminPermission()
    ) {
      await checkParentClosure();
      await showChangelog();
      await loadExecute();
    } else {
      notification.error({
        message: 'Chyba',
        description: 'Nemáte oprávnění pro zobrazení této stránky'
      });
      router.push({ name: 'ListOfProjects' });
    }
    if (props.chapter.forms.length === forms.value.length) {
      prepareSearchParams();
      baseDataLoaded.value = true;
    }
    tables.value = searchTable;
    searchTables.value = [
      {
        default: true,
        table: 'analytical_request',
        canChange: true,
        translate: 'Analytická žádost'
      },
      {
        default: true,
        table: 'batch_number',
        canChange: true,
        translate: 'Číslo šarže'
      },
      {
        default: true,
        table: 'form',
        canChange: true,
        translate: 'Formulář'
      },
      {
        default: true,
        table: 'method',
        canChange: true,
        translate: 'Metoda'
      },
      {
        default: true,
        table: 'sample',
        canChange: true,
        translate: 'Vzorek'
      },
      {
        default: true,
        table: 'project_chemical',
        canChange: true,
        translate: 'Chemikálie ve formuláři'
      }
    ];
  });
  const havePermisionForChannelog = ref<boolean>(false);
  const showChangelog = async () => {
    if (isAllowed(['view_changelog_syntetic'])) {
      havePermisionForChannelog.value = true;
    } else {
      havePermisionForChannelog.value = false;
    }
  };
  watch(
    () => props.chapter.forms.length,
    async () => {
      await loadExecute();
    },
    {
      deep: true
    }
  );

  const checkProjectTypePermisions = async () => {
    return isAllowed(['view_syntetic_department']) || isAllowed(['edit_syntetic_department']);
  };
  const checkReadOnly = () => {
    return isAllowed(['edit_syntetic_department']);
  };
  const checkParentClosure = async () => {
    if (
      props.project?.status === 'closed' ||
      props.department?.status === 'closed' ||
      props.chapter?.status === 'closed'
    ) {
      isParentClosed.value = true;
    }
  };
  const checkAdminPermission = () => {
    return isAllowed(['edit_all']);
  };
  const isParentClosed = ref(false);
  watch(
    () => [allProjectsOptionsPageSearch.value.all_projects.search],
    () => {
      if (
        !allProjectsOptionsPageSearch.value.all_projects.search ||
        (allProjectsOptionsPageSearch.value.all_projects.search ?? '').length < 2
      ) {
        emptySearchResults();
      } else {
        debouncedLoadFromServer(true);
      }
    }
  );

  const searchValue = ref('');

  onUnmounted(() => {
    allProjectsOptionsPageSearch.value.all_projects.search = '';
    emptySearchResults();
    results.clear();
  });

  watch(searchValue, async () => {
    debouncedSearch();
  });

  const debouncedSearch = useDebounceFn(() => {
    const search = searchValue.value.length < 2 ? undefined : searchValue.value;
    if (search) {
      let cleanedSearch = search.replace(/[():]/g, ' ');
      cleanedSearch = cleanedSearch.replace(/\s+/g, ' ').trim();
      const formattedSearch = cleanedSearch
        .split(' ')
        .filter((word) => word.length > 0)
        .map((word) => `${word}:*`)
        .join(' & ');

      allProjectsOptionsPageSearch.value.all_projects.search = formattedSearch;
    } else {
      emptySearchResults();
    }
  }, 500);

  const debouncedLoadFromServer = useDebounceFn(async (search: boolean = false) => {
    if (loading.value === false) {
      await loadFromServer(search);
    }
  }, 500);

  const loadFromServer = async (search: boolean = false) => {
    results.clear();
    await projectsStore.getAllForSearch();
  };

  const getTranslatedType = (type: string) => {
    switch (type) {
      case 'analytical':
        return 'Analytické oddělení';
      case 'synthetic':
        return 'Syntetické oddělení';
      case 'technical':
        return 'Technologické oddělení';
      default:
        return type;
    }
  };

  function findStructure(data: any, word: string): any | null {
    if (typeof data === 'object' && data !== null) {
      if (Array.isArray(data)) {
        return data.find((item) => findStructure(item, word));
      } else {
        for (const key in data) {
          if (
            typeof data[key] === 'string' &&
            data[key].toLowerCase().includes(word.toLowerCase())
          ) {
            return data;
          }
        }

        let matchedObject = null;

        // Check batch_number
        if (data.batch_number && typeof data.batch_number === 'object') {
          matchedObject = findStructure(data.batch_number, word);
          if (matchedObject) {
            return { ...data, batch_match: true };
          }
        }

        // Check analytical_request.batch_number
        if (
          data.analytical_request &&
          typeof data.analytical_request === 'object' &&
          data.analytical_request.batch_number &&
          typeof data.analytical_request.batch_number === 'object'
        ) {
          matchedObject = findStructure(data.analytical_request.batch_number, word);
          if (matchedObject) {
            return { ...data, analytical_request_batch_match: true };
          }
        }

        // Check form.batch_number
        if (data.form && typeof data.form === 'object' && data.form.batch_number) {
          matchedObject = findStructure(data.form.batch_number, word);
          if (matchedObject) {
            return { ...data, form_batch_match: true };
          }
        }

        // Check technique
        if (data.technique && typeof data.technique === 'object') {
          matchedObject = findStructure(data.technique, word);
          if (matchedObject) {
            return { ...data, technique_match: true };
          }
        }
      }
    }
    return null;
  }

  const filteredItems = computed<SearchResult[]>(() => {
    if (
      actualSearchValue.value === '' ||
      actualSearchValue.value === null ||
      actualSearchValue.value === undefined
    ) {
      return flattenedItems.value;
    }

    if (actualSearchValue.value.length < 2 && actualSearchValue.value.length > 0) return [];

    const seenBreadcrumbs = new Set<string>();
    const seenSearchIds = new Set<string>();

    return flattenedItems.value
      .map((item) => {
        const match = findStructure(item.search_object, actualSearchValue.value);
        if (match) {
          const updatedItem = { ...item };

          // Append batch_number.batch_number_id if found in batch_number
          if (match.batch_match && match.batch_number?.batch_number_id) {
            updatedItem.search_id = `${updatedItem.search_id}-${match.batch_number.batch_number_id}`;
          }

          // Append batch_number.batch_number_id if found in analytical_request.batch_number
          if (
            match.analytical_request_batch_match &&
            match.analytical_request?.batch_number?.batch_number_id
          ) {
            updatedItem.search_id = `${updatedItem.search_id}-${match.analytical_request.batch_number.batch_number_id}`;

            // Modify breadcrumb
            if (updatedItem.breadcrumb) {
              const sampleNumber = findSampleNumber(item.search_object);
              updatedItem.breadcrumb = updateBreadcrumbWithSampleNumber(
                updatedItem.breadcrumb,
                sampleNumber
              );
            }
          }

          // Append batch_number.batch_number_id if found in form.batch_number
          if (match.form_batch_match && match.form?.batch_number?.batch_number_id) {
            updatedItem.search_id = `${updatedItem.search_id}-${match.form.batch_number.batch_number_id}`;

            // Modify breadcrumb
            if (updatedItem.breadcrumb) {
              const sampleNumber = findSampleNumber(item.search_object);
              updatedItem.breadcrumb = updateBreadcrumbWithSampleNumber(
                updatedItem.breadcrumb,
                sampleNumber
              );
            }
          }

          // Append analytical_technique_id if found in technique
          if (match.technique_match && match.technique?.analytical_technique_id) {
            updatedItem.search_id = `${updatedItem.search_id}-${match.technique.analytical_technique_id}`;

            // Modify breadcrumb
            if (updatedItem.breadcrumb) {
              const sampleNumber = findSampleNumber(item.search_object);
              updatedItem.breadcrumb = updateBreadcrumb(updatedItem.breadcrumb, sampleNumber);
            }
          }

          return updatedItem;
        }
        return null;
      })
      .filter((item): item is SearchResult => item !== null) // Remove nulls
      .filter((item) => {
        if (seenBreadcrumbs.has(item.breadcrumb)) {
          return false;
        }
        seenBreadcrumbs.add(item.breadcrumb);
        return true;
      })
      .filter((item) => {
        if (seenSearchIds.has(item.search_id)) {
          return false;
        }
        seenSearchIds.add(item.search_id);
        return true;
      });
  });

  function updateBreadcrumb(breadcrumb: string, sampleNumber: string | null): string {
    if (!sampleNumber) return breadcrumb;
    // Split breadcrumb into parts
    const parts = breadcrumb.split(' → ');

    // Find the last part
    const lastPart = parts[parts.length - 1];

    // Extract content inside parentheses ()
    const match = lastPart.match(/\((.*?)\)$/);

    if (match) {
      const contentInsideParentheses = match[1]; // Get the extracted text

      let newContent = contentInsideParentheses; // Default to original
      if (contentInsideParentheses === 'Název') {
        newContent = 'Název techniky';
      } else if (contentInsideParentheses === 'Zkratka') {
        newContent = 'Zkratka techniky';
      }

      // Replace the old text with new
      const updatedLastPart = lastPart.replace(/\(.*?\)$/, `(${newContent})`);

      // Insert sample number as second to last item
      parts.splice(parts.length - 1, 0, sampleNumber);

      // Update the last part with the modified content
      parts[parts.length - 1] = updatedLastPart;

      return parts.join(' → ');
    }

    // If no parentheses found, still add the sample number as second to last
    parts.splice(parts.length - 1, 0, sampleNumber);
    return parts.join(' → ');
  }
  function updateBreadcrumbWithSampleNumber(
    breadcrumb: string,
    sampleNumber: string | null
  ): string {
    if (!sampleNumber) return breadcrumb; // No sample number, no change

    // Split breadcrumb into parts
    const parts = breadcrumb.split(' → ');

    // Find last part and check if it contains (Číslo šarže)
    if (parts.length > 1 && parts[parts.length - 1].includes('(Číslo šarže)')) {
      parts.splice(parts.length - 1, 0, sampleNumber); // Insert before last
    }

    return parts.join(' → ');
  }
  function findSampleNumber(searchObject: any): string | null {
    if (searchObject?.sample_number) {
      return searchObject.sample_number;
    }
    return null;
  }

  interface MatchedTerm {
    value: string;
    key: string;
  }

  const findMatchedTerm = (obj: any, searchTerm: string): MatchedTerm | null => {
    if (!obj || typeof obj !== 'object') return null;

    for (const key in obj) {
      if (
        typeof obj[key] === 'string' &&
        obj[key].toLowerCase().includes(searchTerm.toLowerCase())
      ) {
        return {
          value: obj[key],
          key: translateKey(key)
        };
      }
    }

    for (const key in obj) {
      if (typeof obj[key] === 'object' && obj[key] !== null) {
        const result = findMatchedTerm(obj[key], searchTerm);
        if (result) return result;
      }
    }

    return null;
  };

  const getBreadCrumbForSamples = (
    project?: any | null,
    department?: any | null,
    chapter?: any | null,
    search_object?: any | null,
    search_object_key_name?: string
  ): string => {
    const path = ['(' + search_object_key_name + ')'];
    if (project) {
      path.push(project);
    }
    if (department) path.push(department + ' (Analytické oddělení) ');
    if (chapter) path.push(chapter);
    if (search_object && search_object.technique && search_object.technique.shortcut) {
      path.push(search_object.technique.shortcut);
    }
    if (search_object && actualSearchValue.value) {
      const matchedTerm = findMatchedTerm(search_object, actualSearchValue.value);
      if (matchedTerm) {
        path.push(`${matchedTerm.value} (${matchedTerm.key})`);
      }
    }
    return path.join(' → ');
  };

  const getBreadCrumbForAnalyticalRequest = (
    project?: any | null,
    department?: any | null,
    chapter?: any | null,
    batch?: any | null,
    form?: any | null,
    search_object?: any | null,
    search_object_key_name?: string
  ): string => {
    const path = ['(' + search_object_key_name + ')'];
    if (project) {
      path.push(project?.project_name);
    }
    if (department) path.push(department.project_department_name);
    if (chapter) path.push(chapter.chapter_name);
    if (batch) path.push(batch.batch_number);
    if (form) path.push(form.form_name);
    if (search_object && actualSearchValue.value) {
      const matchedTerm = findMatchedTerm(search_object, actualSearchValue.value);
      if (matchedTerm) {
        path.push(`${matchedTerm.value} (${matchedTerm.key})`);
      }
    }
    return path.join(' → ');
  };

  const getBreadCrumb = (
    project?: any | null,
    department?: any | null,
    chapter?: any | null,
    batch?: any | null,
    form?: any | null,
    search_object?: any | null,
    search_object_key_name?: string
  ): string => {
    const path = ['(' + search_object_key_name + ')'];
    if (project) {
      path.push(project?.name);
    }
    if (department)
      path.push(department.shortcut + ' (' + getTranslatedType(department.type) + ' )');
    if (chapter) path.push(chapter.chapter_title);
    if (batch) path.push(batch.batch_number);
    if (form) path.push(form.form_name);
    if (search_object && actualSearchValue.value) {
      const matchedTerm = findMatchedTerm(search_object, actualSearchValue.value);
      if (matchedTerm) {
        path.push(`${matchedTerm.value} (${matchedTerm.key})`);
      }
    }
    return path.join(' → ');
  };

  const getBreadCrumbForAnalytical = (
    project?: any | null,
    department?: any | null,
    chapter?: any | null,
    search_object?: any | null,
    search_object_key_name?: string
  ): string => {
    const path = ['(' + search_object_key_name + ')'];
    if (project) {
      path.push(project?.name);
    }
    if (department) path.push(department);
    if (chapter) path.push(chapter);
    if (search_object && actualSearchValue.value) {
      const matchedTerm = findMatchedTerm(search_object, actualSearchValue.value);
      if (matchedTerm) {
        path.push(`${matchedTerm.value} (${matchedTerm.key})`);
      }
    }
    return path.join(' → ');
  };

  const results: Set<SearchResult> = new Set();
  const flattenedItems = computed<SearchResult[]>(() => {
    if (!extendsSearch.value) return [];

    if (extendsSearch.value.forms) {
      extendsSearch.value.forms
        .filter(
          (form) =>
            form.chapter?.project_department?.project_id === props.project.project_id &&
            form.chapter?.project_department?.project_department_id ===
              props.department.project_department_id &&
            form.chapter_id === props.chapter.chapter_id
        )
        .forEach((form) => {
          const formType = switchFormTypes(form?.form_type ?? '');
          const route = `/projekty/projekt/${form?.chapter?.project_department?.project.project_id}/oddeleni/${form?.chapter?.project_department?.project_department_id}/kapitola/${form.chapter_id}/${formType}/${form?.form_id}`;
          results.add({
            search_id: `form-${form.form_id}-${route}`,
            breadcrumb: getBreadCrumb(
              form?.chapter?.project_department?.project,
              form?.chapter?.project_department,
              form.chapter,
              form.batch_number,
              form,
              form,
              'Formulář ' + getTranslatedFormType(form?.form_type ?? '')
            ),
            route: route,
            search_object: form
          });
        });
    }

    if (extendsSearch.value.analytical_requests) {
      extendsSearch.value.analytical_requests
        .filter(
          (request) =>
            request.form &&
            request.form.chapter?.project_department?.project_id === props.project.project_id &&
            request.form.chapter?.project_department?.project_department_id ===
              props.department.project_department_id &&
            request.form.chapter?.chapter_id === props.chapter.chapter_id &&
            request.has_completed_samples === true
        )
        .forEach((request) => {
          const formType = switchFormTypes(request.form?.form_type ?? '');
          const route = `/projekty/projekt/${request.form?.chapter?.project_department?.project.project_id}/oddeleni/${request.form?.chapter?.project_department.project_department_id}/kapitola/${request.form?.chapter.chapter_id}/${formType}/${request.form?.form_id}`;
          results.add({
            search_id: `analytical-request-form-${request.analytical_request_id}-${route}`,
            breadcrumb: getBreadCrumb(
              request.form?.chapter?.project_department?.project,
              request.form?.chapter?.project_department,
              request.form?.chapter,
              request.batch_number,
              request.form,
              request,
              'Analytická žádost ve formuláři'
            ),
            route: route,
            search_object: request
          });
        });
    }

    if (extendsSearch.value.batch_numbers) {
      extendsSearch.value.batch_numbers
        .filter(
          (batch) =>
            batch.form?.chapter?.project_department?.project.project_id ===
              props.project.project_id &&
            batch.form?.chapter?.project_department?.project.project_id ===
              props.department.project_department_id &&
            batch.form?.chapter?.chapter_id === props.chapter.chapter_id
        )
        .forEach((batch) => {
          const formType = switchFormTypes(batch.form?.form_type ?? '');
          const route = `/projekty/projekt/${batch.form?.chapter?.project_department?.project.project_id}/oddeleni/${batch.form?.chapter?.project_department.project_department_id}/kapitola/${batch.form?.chapter.chapter_id}/${formType}/${batch.form?.form_id}`;
          results.add({
            search_id: `batch-number-${batch.batch_number_id}-${route}`,
            breadcrumb: getBreadCrumb(
              batch.form?.chapter?.project_department?.project,
              batch.form?.chapter?.project_department,
              batch.form?.chapter,
              batch.batch_number,
              batch.form,
              batch,
              'Číslo šarže'
            ),
            route: route,
            search_object: batch
          });
        });
    }

    if (extendsSearch.value.methods) {
      extendsSearch.value.methods
        .filter(
          (method) =>
            method.project_id === props.project.project_id &&
            method.department_id === props.department.project_department_id &&
            method.chapter_id === props.chapter.chapter_id
        )
        .forEach((method) => {
          const route = `/projekty/projekt/${method.project_id}/oddeleni/${method.department_id}/kapitola/${method.chapter_id}/technika/${method.technique_id}/metody/${method.method_id}/zmena`;
          results.add({
            search_id: `method-${method.method_id}-${route}`,
            breadcrumb: getBreadCrumbForAnalytical(
              method.project,
              'Analytické oddělení',
              'Vývoj metody',
              method,
              'Metoda'
            ),
            route: route,
            search_object: method
          });
        });
    }
    if (extendsSearch.value.chemicals) {
      extendsSearch.value.chemicals
        .filter(
          (chemical) =>
            chemical.forms?.chapter?.project_department?.project_id === props.project.project_id &&
            chemical.forms?.chapter?.project_department?.project_department_id ===
              props.department.project_department_id &&
            chemical.forms?.chapter?.chapter_id === props.chapter.chapter_id
        )
        .forEach((chemical) => {
          const formType = switchFormTypes(chemical.forms?.form_type ?? '');
          const route = `/projekty/projekt/${chemical.forms?.chapter?.project_department?.project.project_id}/oddeleni/${chemical.forms?.chapter?.project_department.project_department_id}/kapitola/${chemical.forms?.chapter.chapter_id}/${formType}/${chemical.forms?.form_id}`;
          results.add({
            search_id: `chemical-${chemical.chemical_id}`,
            breadcrumb: getBreadCrumb(
              chemical.forms?.chapter?.project_department?.project,
              chemical.forms?.chapter?.project_department,
              chemical.forms?.chapter,
              null,
              chemical.forms,
              chemical,
              'Chemikálie ve formuláři'
            ),
            route: route,
            search_object: chemical
          });
        });
    }

    if (extendsSearch.value.tags) {
      extendsSearch.value.tags.forEach((tag) => {
        tag.forms.forEach((form) => {
          const formType = switchFormTypes(form.form_type ?? '');
          const route = `/projekty/projekt/${form.chapter?.project_department?.project.project_id}/oddeleni/${form.chapter?.project_department.project_department_id}/kapitola/${form.chapter.chapter_id}/${formType}/${form.form_id}`;
          results.add({
            search_id: `tag-${tag.tag_id}-${route}`,
            breadcrumb: getBreadCrumb(
              form.chapter?.project_department?.project,
              form.chapter?.project_department,
              form.chapter,
              null,
              form,
              tag,
              'Štítek - formulář'
            ),
            route: route,
            search_object: tag
          });
        });
      });
    }
    return Array.from(results);
  });

  const switchFormTypes = (form_type: string) => {
    switch (form_type) {
      case 'attempt':
        return 'pokus';
      case 'experiment':
        return 'experiment';
      case 'message':
        return 'sprava';
      case 'investigation':
        return 'setreni';
      default:
        return form_type;
    }
  };

  const getTranslatedFormType = (form_type: string) => {
    switch (form_type) {
      case 'attempt':
        return 'Pokus';
      case 'experiment':
        return 'Experiment';
      case 'message':
        return 'Zpráva';
      case 'investigation':
        return 'Šetreni';
    }
  };

  const highlightMatch = (text: string) => {
    const query = actualSearchValue.value;
    if (!query) return text;

    const parts = text.split(' → ');
    if (parts.length < 1) return text;

    const lastPart = parts[parts.length - 1];
    let processedLastPart = lastPart;

    // Truncate if longer than 30 characters
    if (lastPart.length > 30) {
      processedLastPart = lastPart.substring(0, 30) + '...';
    }

    if (lastPart.toLowerCase().includes(query.toLowerCase())) {
      parts[parts.length - 1] = `<span class="highlight">${processedLastPart}</span>`;
      return parts.join(' → ');
    }

    return text;
  };

  const displaySearchValue = ref('');
  interface ComboboxItem {
    path: string;
    title: string;
    search_item: SearchResult;
    value: string;
  }

  const selectedItemToNavigate = ref<ComboboxItem | null>(null);

  const navigateToItem = (item: ComboboxItem | null) => {
    if (item && item.path) {
      emptySearchResults();
      router.push(item.path);
      searchValue.value = '';
      displaySearchValue.value = '';
      selectedItemToNavigate.value = null;
    }
  };

  const savedSearchValue = ref('');

  const showFilterModal = ref(false);
  const toggleFilterModal = () => {
    showFilterModal.value = !showFilterModal.value;
  };

  const actualSearchValue = computed(() => {
    return searchValue.value || displaySearchValue.value;
  });

  const updateSearch = (value: string) => {
    searchValue.value = value;
    displaySearchValue.value = value;
    results.clear();
  };

  const updateSearchAndReset = (value: string) => {
    searchValue.value = value;
    displaySearchValue.value = value;
    results.clear();
    if (value) {
      emptySearchResults();
      debouncedSearch();
    }
  };

  const emptySearchResults = () => {
    extendsSearch.value = {
      analytical_requests: [],
      analytical_techniques: [],
      batch_numbers: [],
      chapters: [],
      forms: [],
      methods: [],
      project_departments: [],
      projects: [],
      samples: [],
      standards: [],
      tags: [],
      chemicals: [],
      project_chemicals: []
    };
  };

  const prepareSearchParams = () => {
    if (
      props.project &&
      props.project.project_id &&
      props.department.project_department_id &&
      props.department.type &&
      props.chapter.chapter_id
    ) {
      allProjectsOptionsPageSearch.value.all_projects.search = '';
      allProjectsOptionsPageSearch.value.all_projects.search_columns = [
        'departments__analytical_requests__request_type__analytical_request',
        'departments__batch_numbers__form__form_type__form',
        'departments__batch_numbers__batch_number',
        'departments__chapters__chemicals__shortcut__chemical',
        'departments__chapters__chemicals__cas__chemical',
        'departments__chapters__form__form_status__form'
      ];
      allProjectsOptionsPageSearch.value.all_projects.filterOptions = [
        {
          column: 'project_id',
          value: props.project.project_id
        },
        {
          column: 'departments__project_department_id',
          value: props.department.project_department_id
        },
        {
          column: 'departments__type',
          value: props.department.type
        },
        {
          column: 'departments__chapters__chapter_id',
          value: props.chapter.chapter_id
        }
      ];
    }
  };
  const maxFromDate = computed(() => {
    if (end_date.value) {
      const toDate = new Date(end_date.value);
      toDate.setHours(23, 59, 59, 999);
      return toDate;
    }
    return null;
  });

  const minToDate = computed(() => {
    if (start_date.value) {
      const fromDate = new Date(start_date.value);
      fromDate.setHours(0, 0, 0, 0);
      return fromDate;
    }
    return null;
  });

  const onFromDateChange = (newFromDate: Date | null) => {
    if (newFromDate) {
      newFromDate.setHours(0, 0, 0, 0);
      if (end_date) {
        end_date.value = newFromDate;
      }
    }
  };

  const onToDateChange = (newToDate: Date | null) => {
    if (newToDate) {
      newToDate.setHours(23, 59, 59, 999);
      if (start_date.value) {
        start_date.value = newToDate;
      }
    }
  };

  watch([() => start_date.value, () => end_date.value], async ([newFrom, newTo]) => {
    if (newFrom && newTo) {
      const fromDate = new Date(newFrom);
      fromDate.setHours(0, 0, 0, 0);
      const toDate = new Date(newTo);
      toDate.setHours(23, 59, 59, 999);
      if (
        allProjectsOptionsPageSearch.value.all_projects.search &&
        (allProjectsOptionsPageSearch.value.all_projects.search ?? '').length > 2
      ) {
        debouncedLoadFromServer(true);
      }
    }
  });
</script>
<style>
  .highlight {
    font-weight: bold;
    color: rgb(42, 161, 175);
  }
</style>
