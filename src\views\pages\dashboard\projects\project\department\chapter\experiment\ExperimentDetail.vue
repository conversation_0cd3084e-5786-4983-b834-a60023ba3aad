<script setup lang="ts">
  import type { BreadcrumbI } from '@/components/shared/BaseBreadcrumb.vue';
  import ChangelogPanel from '@/components/shared/ChangelogPanel.vue';
  import CustomChemicalTable from '@/components/shared/chemicalTable/CustomChemicalTable.vue';
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import CustomNumberInput from '@/components/shared/CustomNumberInput.vue';
  import EditorTextarea from '@/components/shared/EditorTextareaWebSocket.vue';
  import FileSection from '@/components/shared/file/FileSection.vue';
  import FileUploader from '@/components/shared/file/FileUploader.vue';
  import TopPageBreadcrumb from '@/components/shared/TopPageBreadcrumb.vue';
  import UiParentCard from '@/components/shared/UiParentCard.vue';
  import LoaderWrapper from '@/layouts/dashboard/LoaderWrapper.vue';
  import { ChemicalType, type NewChemicalDataI, type ChemicalDto } from '@/stores/chemicals';
  import {
    Conclusion,
    ConclusionType,
    useExperimentsStore,
    type ExperimentUpdateDataI,
    type SaveExperimentAsTemplateDto
  } from '@/stores/experiments';
  import { File, useFilesStore } from '@/stores/files';
  import { useFormsStore } from '@/stores/forms';
  import { useLogsStore } from '@/stores/logs';
  import { useAuthStore } from '@/stores/auth';
  import { useProjectsStore } from '@/stores/projects';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { toLocale, toTimeLocale } from '@/utils/locales';
  import { notification } from 'ant-design-vue';
  import { storeToRefs } from 'pinia';
  import type { EditorManager, RawEditorOptions } from 'tinymce';
  import { computed, onBeforeUnmount, onMounted, ref, watch, onUnmounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import AddConclusionModal from './components/AddConclusionModal.vue';
  import AddPeopleToExperimentModal from './components/AddPeopleToExperimentModal.vue';
  import AnalyticalRequestsSection from '@/stores/analyticalRequests/components/AnalyticalRequestsSection.vue';
  import TemplateRename from '@/components/shared/NameNewTemplate.vue';
  import { CalculatorOutlined } from '@ant-design/icons-vue';
  import { isAllowed } from '@/utils/directive/isAllowed';
  import CopyExperimentModal from './components/CopyExperimentModal.vue';
  import isEqual from 'lodash/isEqual';
  import { setPageTitle } from '@/utils/title';

  // WebSocket imports
  import ActiveUsersIndicator from '@/components/shared/websocket/ActiveUsersIndicator.vue';
  import FieldLockWrapper from '@/components/shared/websocket/FieldLockWrapper.vue';
  import { useFormWebSocket } from '@/composables/useFormWebSocket';

  const ConfirmRef = ref<undefined | typeof ConfirmDlg>(undefined);
  const ChemicalTableRef = ref<undefined | typeof CustomChemicalTable>(undefined);

  const route = useRoute();
  const router = useRouter();
  const logsStore = useLogsStore();
  const filesStore = useFilesStore();
  const formsStore = useFormsStore();
  const authStore = useAuthStore();
  const projectsStore = useProjectsStore();
  const experimentsStore = useExperimentsStore();

  const { project, department, chapter, project_permision } = storeToRefs(projectsStore);
  const { experiment } = storeToRefs(experimentsStore);
  const { loading, completeBatches } = storeToRefs(formsStore);
  const { user } = storeToRefs(authStore);

  const baseDataLoaded = ref(false);
  const isParentClosed = ref(false);

  // WebSocket setup
  const formId = computed(() => experiment.value?.form_id || null);
  const webSocket = useFormWebSocket({
    formId,
    autoConnect: true,
    enableNotifications: true
  });

  const checkAdminPermission = () => {
    return false;
  };

  const checkAdminpermissionNew = () => {
    return isAllowed(['edit_all']);
  };

  const checkAdminViewPermission = () => {
    return isAllowed(['view_all']);
  };

  onMounted(async () => {
    if (checkProjectTypePermisions() || checkAdminPermission() || checkAdminViewPermission()) {
      await showChangelog();
      await loadExecuteWithLocalStorageCheck();
    } else {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nemáte oprávnění pro prohlížení této stránky.'
      });
      router.push({ name: 'ListOfProjects' });
    }
    await checkParentClosure();
    if (checkAdminpermissionNew() && !isParentClosed.value && !isReadOnly.value) {
      saveEvery5Minutes();
    }
  });

  const havePermisionForChannelog = ref<boolean>(false);
  const showChangelog = async () => {
    if (isAllowed(['view_changelog_syntetic'])) {
      havePermisionForChannelog.value = true;
    } else {
      havePermisionForChannelog.value = false;
    }
  };

  const havePermision = ref<boolean>(true);
  const missingPermison = ref<string>();
  const checkPermision = () => {
    if (isAllowed(['view_changelog_syntetic'])) {
      havePermision.value = true;
      return true;
    } else {
      missingPermison.value = 'view_changelog_syntetic';
      havePermision.value = false;
    }
    if (!havePermision.value) {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nemáte oprávnění pro prohlížení changelogu: ' + missingPermison.value + '.'
      });
    }
    return false;
  };

  const showLogs = () => {
    if (checkPermision()) {
      if (experiment.value?.experiment_id) {
        const config = [
          {
            table_name: ['form', 'experiment'],
            table_primary_key: [experiment.value.experiment_id]
          },
          experiment.value?.conclusions.length
            ? {
                table_name: ['conclusion'],
                table_primary_key: experiment.value.conclusions.map((c) => c.conclusion_id)
              }
            : {},
          experiment.value?.files.length
            ? {
                table_name: ['file'],
                table_primary_key: experiment.value.files.map((file) => file.file_id)
              }
            : {}
        ];
        logsStore.setMultiColumnsAsActiveFilter(config);
      } else {
        notification.error({ message: 'Nepodařilo se načíst data pro zobrazení změn' });
      }
      logsStore.showDrawer = true;
    }
  };

  const chapterSearch = ref<string | undefined>(undefined);
  const forceRefreshKey = ref(0);

  const project_id = computed(() => route.params.project_id as string);
  const projectId = computed(() => parseInt(project_id.value));

  const project_department_id = computed(() => route.params.project_department_id as string);
  const projectDepartmentId = computed(() => parseInt(project_department_id.value));

  const chapter_id = computed(() => route.params.chapter_id as string);
  const chapterId = computed(() => parseInt(chapter_id.value));

  const form_id = computed(() => route.params.form_id as string);
  const experimentId = computed(() => parseInt(form_id.value));

  const redirectToNewAnalyticalRequest = async () => {
    if (project.value && department.value && chapter.value && experiment.value) {
      const go = router.resolve({
        name: 'NewAnalyticalRequest',
        params: {
          project_id: project.value.project_id,
          project_department_id: department.value.project_department_id,
          chapter_id: chapter.value.chapter_id,
          form_id: experiment.value.form_id
        }
      });

      if (!isReadOnly.value) {
        if (
          isUpdateDataSameAsOriginal.value ||
          (await ConfirmRef.value?.open(
            'Potvrzení',
            'Opravdu chcete pokračovat bez uložení změn?',
            {
              color: 'error',
              notclosable: true,
              zIndex: 2400,
              onOk: () => {
                alert('ok');
                router.push(go.fullPath);
              }
            }
          ))
        ) {
          router.push(go.fullPath);
        }
      } else {
        router.push(go);
      }
    } else {
      notification.error({ message: 'Nepodařilo se přesměrovat na nový analytický požadavek' });
    }
  };
  const redirectToNewStadardRdAnalyticalRequest = async () => {
    if (project.value && department.value && chapter.value && experiment.value) {
      const go = router.resolve({
        name: 'NewAnalyticalRequestStandard',
        params: {
          project_id: project.value.project_id,
          project_department_id: department.value.project_department_id,
          chapter_id: chapter.value.chapter_id,
          form_id: experiment.value.form_id
        }
      });

      if (!isReadOnly.value) {
        if (
          isUpdateDataSameAsOriginal.value ||
          (await ConfirmRef.value?.open(
            'Potvrzení',
            'Opravdu chcete pokračovat bez uložení změn?',
            {
              color: 'error',
              notclosable: true,
              zIndex: 2400,
              onOk: () => {
                alert('ok');
                router.push(go.fullPath);
              }
            }
          ))
        ) {
          router.push(go.fullPath);
        }
      } else {
        router.push(go);
      }
    } else {
      notification.error({ message: 'Nepodařilo se přesměrovat na nový analytický požadavek' });
    }
  };

  const redirectToInsertAnalyticalRequest = async () => {
    if (project.value && department.value && chapter.value && experiment.value) {
      const go = router.resolve({
        name: 'InsertAnalyticalRequest',
        params: {
          project_id: project.value.project_id,
          project_department_id: department.value.project_department_id,
          chapter_id: chapter.value.chapter_id,
          form_id: experiment.value.form_id
        }
      });

      if (!isReadOnly.value) {
        if (
          isUpdateDataSameAsOriginal.value ||
          (await ConfirmRef.value?.open(
            'Potvrzení',
            'Opravdu chcete pokračovat bez uložení změn?',
            {
              color: 'error',
              notclosable: true,
              zIndex: 2400,
              onOk: () => {
                router.push(go.fullPath);
              }
            }
          ))
        ) {
          router.push(go.fullPath);
        }
      } else {
        router.push(go);
      }
    } else {
      notification.error({ message: 'Nepodařilo se přesměrovat na nový analytický požadavek' });
    }
  };

  const isUpdateDataSameAsOriginal = computed(() => {
    if (updateData.value && experiment.value && ChemicalTableRef.value) {
      const currentChemicals = ChemicalTableRef.value?.getAllChemicalInBaseUnitToUpdateModel();
      const originalChemicals = experiment.value.chemicals.map((ch) => ({
        name: ch.name,
        density: ch.density,
        molar_mass: ch.molar_mass,
        notes: ch.notes,
        grams: ch.grams,
        moles: ch.moles,
        equivalent: ch.equivalent,
        volume_ml: ch.volume_ml,
        concentration: ch.concentration,
        type: ch.type
      }));
      const chemicalsEqual = isEqual(currentChemicals, originalChemicals);
      return (
        chemicalsEqual &&
        updateData.value.form_name === experiment.value.form_name &&
        updateData.value.batch_description === experiment.value.batch_description &&
        updateData.value.reaction_procedure === experiment.value.reaction_procedure &&
        updateData.value.product_weight === experiment.value.product_weight &&
        updateData.value.yield_percentage === experiment.value.yield_percentage &&
        updateData.value.content_percentage === experiment.value.content_percentage &&
        updateData.value.conclusion_text ===
          experiment.value?.conclusions.find(
            (conclusion) => conclusion.type === ConclusionType.MAIN
          )?.conclusion_text
      );
    }
    return false;
  });

  const loadExecuteWithLocalStorageCheck = async () => {
    baseDataLoaded.value = false;
    chapterSearch.value = undefined;
    if (projectId.value) {
      if (project.value) {
        if (project.value.project_id !== projectId.value) {
          await projectsStore.getProjectById(projectId.value);
        }
      } else {
        await projectsStore.getProjectById(projectId.value);
      }
    }

    if (projectDepartmentId.value) {
      if (department.value) {
        if (department.value.project_department_id !== projectDepartmentId.value) {
          await projectsStore.getDepartmentById(projectDepartmentId.value);
        }
      } else {
        await projectsStore.getDepartmentById(projectDepartmentId.value);
      }
    }

    if (chapterId.value) {
      if (chapter.value) {
        if (chapter.value.chapter_id !== chapterId.value) {
          await projectsStore.getChapterById(chapterId.value);
        }
      } else {
        await projectsStore.getChapterById(chapterId.value);
      }
    }

    if (experimentId.value) {
      await experimentsStore.getExperimentById(experimentId.value);
      if (experiment.value?.batch_number.batch_number) {
        setPageTitle(experiment.value.batch_number?.batch_number);
      }
    }

    if (
      department.value &&
      project.value &&
      chapter.value &&
      experiment.value &&
      project.value.departments.find(
        (d) =>
          department.value && d.project_department_id === department.value.project_department_id
      ) &&
      department.value.chapters.find(
        (c) => chapter.value && c.chapter_id === chapter.value.chapter_id
      ) &&
      experiment.value.chapter_id === chapter.value.chapter_id
    ) {
      updateData.value = {
        form_name: experiment.value.form_name,
        reaction_scheme_file_id: experiment.value.reaction_scheme_file_id,
        batch_description: experiment.value.batch_description,
        reaction_procedure: experiment.value.reaction_procedure,
        product_weight: experiment.value.product_weight,
        yield_percentage: experiment.value.yield_percentage,
        content_percentage: experiment.value?.content_percentage,
        conclusion_text:
          experiment.value?.conclusions.find(
            (conclusion) => conclusion.type === ConclusionType.MAIN
          )?.conclusion_text ?? '',
        chemicals: experiment.value.chemicals
          .filter((ch) => ch.type === ChemicalType.CHEMICAL)
          .map(
            (ch) =>
              ({
                name: ch.name,
                density: ch.density,
                molar_mass: ch.molar_mass,
                notes: ch.notes,
                grams: ch.grams,
                moles: ch.moles,
                equivalent: ch.equivalent,
                volume_ml: ch.volume_ml,
                concentration: ch.concentration,
                type: ch.type
              }) as NewChemicalDataI
          )
      };
      formsStore.form_id = experiment.value.form_id;
      forceRefreshKey.value++;
    } else {
      if (project.value && department.value && chapter.value) {
        router.push({
          name: 'ChapterDetail',
          params: {
            project_id: project.value.project_id.toString(),
            chapter_id: chapter.value.chapter_id.toString(),
            project_department_id: department.value.project_department_id.toString()
          }
        });
      } else if (project.value && department.value) {
        router.push({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value.project_id.toString(),
            project_department_id: department.value.project_department_id.toString()
          }
        });
      } else if (project.value) {
        router.push({
          name: 'ProjectDetail',
          params: { project_id: project.value.project_id.toString() }
        });
      } else {
        router.push({ name: 'ListOfProjects' });
      }
    }
    await formsStore.getForm(parseInt(form_id.value));
    //await checkLocalStorage();
    baseDataLoaded.value = true;
  };

  const loadDataForCopyRedirect = async (copy_id: number) => {
    baseDataLoaded.value = false;
    chapterSearch.value = undefined;
    if (copy_id) {
      await experimentsStore.getExperimentById(copy_id);
    }

    if (
      experiment.value &&
      chapter.value &&
      experiment.value.chapter_id === chapter.value.chapter_id
    ) {
      updateData.value = {
        form_name: experiment.value.form_name,
        reaction_scheme_file_id: experiment.value.reaction_scheme_file_id,
        batch_description: experiment.value.batch_description,
        reaction_procedure: experiment.value.reaction_procedure,
        product_weight: experiment.value.product_weight,
        yield_percentage: experiment.value.yield_percentage,
        content_percentage: experiment.value?.content_percentage,
        conclusion_text:
          experiment.value?.conclusions.find(
            (conclusion) => conclusion.type === ConclusionType.MAIN
          )?.conclusion_text ?? '',
        chemicals: experiment.value.chemicals
          .filter((ch) => ch.type === ChemicalType.CHEMICAL)
          .map(
            (ch) =>
              ({
                name: ch.name,
                density: ch.density,
                molar_mass: ch.molar_mass,
                notes: ch.notes,
                grams: ch.grams,
                moles: ch.moles,
                equivalent: ch.equivalent,
                volume_ml: ch.volume_ml,
                concentration: ch.concentration,
                type: ch.type
              }) as NewChemicalDataI
          )
      };
      formsStore.form_id = experiment.value.form_id;
      forceRefreshKey.value++;
    } else {
      if (project.value && department.value && chapter.value) {
        router.push({
          name: 'ChapterDetail',
          params: {
            project_id: project.value.project_id.toString(),
            chapter_id: chapter.value.chapter_id.toString(),
            project_department_id: department.value.project_department_id.toString()
          }
        });
      } else if (project.value && department.value) {
        router.push({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value.project_id.toString(),
            project_department_id: department.value.project_department_id.toString()
          }
        });
      } else if (project.value) {
        router.push({
          name: 'ProjectDetail',
          params: { project_id: project.value.project_id.toString() }
        });
      } else {
        router.push({ name: 'ListOfProjects' });
      }
    }
    await formsStore.getForm(parseInt(form_id.value));
    baseDataLoaded.value = true;
  };

  const checkIfCurrentUserIsOwner = () => {
    if (isAllowed(['edit_all'])) {
      return true;
    }
    if (experiment.value?.owner === null || user.value === null) {
      return false;
    }
    return experiment.value?.owner?.user_id === user.value?.user_id;
  };

  const checkIfCurrentUserIsCollaborator = () => {
    if (isAllowed(['edit_all'])) {
      return true;
    }
    if (experiment.value?.collaborators === null && user.value === null) {
      return false;
    }
    return (
      experiment.value?.collaborators.some(
        (collaborator) => collaborator.user_id === user.value?.user_id
      ) || checkIfCurrentUserIsOwner()
    );
  };

  const checkIfCurrentUserIsCollaboratorWithoutEditAll = () => {
    if (experiment.value?.collaborators === null && user.value === null) {
      return false;
    }
    return (
      experiment.value?.collaborators.some(
        (collaborator) => collaborator.user_id === user.value?.user_id
      ) || checkIfCurrentUserIsOwner()
    );
  };

  const loadExecute = async () => {
    baseDataLoaded.value = false;
    chapterSearch.value = undefined;

    if (projectId.value) {
      if (project.value) {
        if (project.value.project_id !== projectId.value) {
          await projectsStore.getProjectById(projectId.value);
        }
      } else {
        await projectsStore.getProjectById(projectId.value);
      }
    }

    if (projectDepartmentId.value) {
      if (department.value) {
        if (department.value.project_department_id !== projectDepartmentId.value) {
          await projectsStore.getDepartmentById(projectDepartmentId.value);
        }
      } else {
        await projectsStore.getDepartmentById(projectDepartmentId.value);
      }
    }

    if (chapterId.value) {
      if (chapter.value) {
        if (chapter.value.chapter_id !== chapterId.value) {
          await projectsStore.getChapterById(chapterId.value);
        }
      } else {
        await projectsStore.getChapterById(chapterId.value);
      }
    }

    if (experimentId.value) {
      await experimentsStore.getExperimentById(experimentId.value);
    }

    if (
      department.value &&
      project.value &&
      chapter.value &&
      experiment.value &&
      project.value.departments.find(
        (d) =>
          department.value && d.project_department_id === department.value.project_department_id
      ) &&
      department.value.chapters.find(
        (c) => chapter.value && c.chapter_id === chapter.value.chapter_id
      ) &&
      experiment.value.chapter_id === chapter.value.chapter_id
    ) {
      updateData.value = {
        form_name: experiment.value.form_name,
        reaction_scheme_file_id: experiment.value.reaction_scheme_file_id,
        batch_description: experiment.value.batch_description,
        reaction_procedure: experiment.value.reaction_procedure,
        product_weight: experiment.value.product_weight,
        yield_percentage: experiment.value.yield_percentage,
        content_percentage: experiment.value?.content_percentage,
        conclusion_text:
          experiment.value?.conclusions.find(
            (conclusion) => conclusion.type === ConclusionType.MAIN
          )?.conclusion_text ?? '',
        chemicals: experiment.value.chemicals
          .filter((ch) => ch.type === ChemicalType.CHEMICAL)
          .map(
            (ch) =>
              ({
                name: ch.name,
                density: ch.density,
                molar_mass: ch.molar_mass,
                notes: ch.notes,
                grams: ch.grams,
                moles: ch.moles,
                equivalent: ch.equivalent,
                volume_ml: ch.volume_ml,
                concentration: ch.concentration,
                type: ch.type
              }) as NewChemicalDataI
          )
      };
      formsStore.form_id = experiment.value.form_id;
      forceRefreshKey.value++;
    } else {
      if (project.value && department.value && chapter.value) {
        router.push({
          name: 'ChapterDetail',
          params: {
            project_id: project.value.project_id.toString(),
            chapter_id: chapter.value.chapter_id.toString(),
            project_department_id: department.value.project_department_id.toString()
          }
        });
      } else if (project.value && department.value) {
        router.push({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value.project_id.toString(),
            project_department_id: department.value.project_department_id.toString()
          }
        });
      } else if (project.value) {
        router.push({
          name: 'ProjectDetail',
          params: { project_id: project.value.project_id.toString() }
        });
      } else {
        router.push({ name: 'ListOfProjects' });
      }
    }
    await formsStore.getForm(parseInt(form_id.value));
    baseDataLoaded.value = true;
  };

  watch([project_id, project_department_id, chapter_id], async () => {
    await loadExecuteWithLocalStorageCheck();
  });

  const breadcrumbItems = computed<BreadcrumbI[]>(() => {
    return [
      {
        title: 'Projekty',
        disabled: false,
        href: router.resolve({ name: 'Projects' }).href
      },
      {
        title: project.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'ProjectDetail',
          params: { project_id: project.value?.project_id }
        }).href
      },
      {
        title: department.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id
          }
        }).href
      },
      {
        title: chapter.value?.chapter_title ?? '',
        disabled: false,
        href: router.resolve({
          name: 'ChapterDetail',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id,
            chapter_id: chapter.value?.chapter_id
          }
        }).href
      },
      {
        title: experiment.value?.form_name ?? '',
        disabled: true,
        href: router.resolve({
          name: 'Experiment',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id,
            chapter_id: chapter.value?.chapter_id,
            form_id: experiment.value?.form_id
          }
        }).href
      }
    ];
  });

  const updateData = ref<ExperimentUpdateDataI | undefined>(undefined);
  const reloadPageWithoutSave = () => {
    if (isUpdateDataSameAsOriginal.value) {
      router.push({
        name: 'ChapterDetail',
        params: {
          project_id: project.value?.project_id,
          project_department_id: department.value?.project_department_id,
          chapter_id: chapter.value?.chapter_id
        }
      });
    } else {
      loadExecute();
    }
  };
  const goBack = () => {
    router.push({
      name: 'ChapterDetail',
      params: {
        project_id: project.value?.project_id,
        project_department_id: department.value?.project_department_id,
        chapter_id: chapter.value?.chapter_id
      }
    });
  };
  const CreateSyntheticTemplateForm = ref();
  const isInProgress = ref(false);
  async function submitFormToValidate() {
    if (CreateSyntheticTemplateForm.value.isValid && updateData.value && !isInProgress.value) {
      // Unlock all fields before submitting
      webSocket.webSocketStore.unlockAllMyFields();

      isInProgress.value = true;
      baseDataLoaded.value = false;
      const chemicals: NewChemicalDataI[] =
        ChemicalTableRef.value?.getAllChemicalInBaseUnitToUpdateModel();
      if (updateData.value && chemicals) {
        updateData.value.chemicals = chemicals;
        updateData.value.chemicals.forEach((chemical) => {
          if (chemical.concentration)
            chemical.concentration = validateInput(chemical.concentration);
        });
      }
      const res = await experimentsStore.updateExperiment(updateData.value);
      if (res) {
        await clearLocalStorage();
        loadExecute();
      }
    }
    isInProgress.value = false;
    baseDataLoaded.value = true;
  }

  const exportFormAsPdf = async () => {
    const res = await formsStore.exportFormAsPdf(experiment.value?.form_name);
    if (res) {
      notification.success({ message: 'Experiment byl úspěšně exportován' });
    } else {
      notification.error({ message: 'Nepodařilo se exportovat experiment' });
    }
  };

  const exportFormAsWord = async () => {
    const res = await formsStore.exportFormAsWord(experiment.value?.form_name);
    if (res) {
      notification.success({ message: 'Experiment byl úspěšně exportován' });
    } else {
      notification.error({ message: 'Nepodařilo se exportovat experiment' });
    }
  };

  const saveExperimentAsTemplate = async (templateName: string) => {
    showTemplateModal.value = false;
    await experimentsStore.saveExperimentAsTemplate({
      template_name: templateName
    } as SaveExperimentAsTemplateDto);
  };
  const showTemplateModal = ref(false);
  const toggleTemplateModal = () => {
    showTemplateModal.value = !showTemplateModal.value;
  };

  const scrollToHash = () => {
    const hash = window.location.hash;
    if (hash) {
      const element = document.querySelector(hash);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    }
  };

  onMounted(() => {
    window.addEventListener('hashchange', scrollToHash);
    scrollToHash();
  });

  onBeforeUnmount(() => {
    window.removeEventListener('hashchange', scrollToHash);
  });

  const selectedReactionSchemeFile = ref<File[]>([]);
  const selectedReactionSchemeFileUrl = ref<string | undefined>(undefined);
  const getReactionSchemeFileId = async (fileId: number) => {
    selectedReactionSchemeFileUrl.value = await filesStore.getFileUrl(fileId);
  };

  watch(selectedReactionSchemeFile, (files) => {
    if (updateData.value) {
      updateData.value.reaction_scheme_file_id = files[0].file_id;
    }
  });

  watch(
    () => updateData.value?.reaction_scheme_file_id,
    async (fileId) => {
      if (fileId) {
        getReactionSchemeFileId(fileId);
      }
    }
  );

  const getAllFiles = async () => {
    const files = await formsStore.getAllFiles();
    if (experiment.value && files !== false) {
      experiment.value.files = files;
    }
  };

  const isReadOnly = computed(() => {
    return experiment.value?.isReadOnly ?? true;
  });

  const isCompleted = computed(() => {
    return experiment.value?.isCompleted ?? false;
  });

  const other_conclusions = computed<Conclusion[]>(
    () =>
      experiment.value?.conclusions.filter(
        (conclusion) => conclusion.type === ConclusionType.ADDITIONAL
      ) ?? []
  );
  const triggerSuccessNotification = (message: string) => {
    notification.success({
      message: message
    });
  };

  const triggerErrorNotification = (message: string) => {
    notification.error({
      message: message
    });
  };

  const calculateResult = async () => {
    let calculationTriggered = false;
    const chemicals: NewChemicalDataI[] =
      ChemicalTableRef.value?.getAllChemicalInBaseUnitToUpdateModel();
    const lastRow = chemicals.find((chemical) => chemical.type === ChemicalType.RESULT);
    const gramsBase = ref(lastRow?.grams);
    if (!updateData.value?.product_weight) {
      triggerErrorNotification('Nepodařilo se dopočítat údaje.');
      return;
    }
    if (!updateData.value?.content_percentage) updateData.value.content_percentage = 100;
    if (updateData.value?.content_percentage && updateData.value?.content_percentage > 100) {
      triggerErrorNotification('Nepodařilo se dopočítat údaje.');
      return;
    }
    if (updateData.value?.content_percentage && updateData.value?.content_percentage <= 0) {
      updateData.value.content_percentage = 100;
    }
    if (
      updateData.value?.product_weight &&
      updateData.value?.content_percentage &&
      gramsBase.value
    ) {
      const concentrationBase = ref(updateData.value?.content_percentage / 100);
      if (
        updateData.value.yield_percentage === 0 ||
        updateData.value.yield_percentage === null ||
        updateData.value.yield_percentage === undefined
      ) {
        if (currentMass.value === 'g') {
          calculationTriggered = true;
          updateData.value.yield_percentage = dynamicRound(
            ((concentrationBase.value * updateData.value?.product_weight) / gramsBase.value) * 100
          );
        } else {
          calculationTriggered = true;
          updateData.value.yield_percentage = dynamicRound(
            ((concentrationBase.value * updateData.value?.product_weight) /
              (gramsBase.value / 1000)) *
              100
          );
        }
      }
    }
    if (calculationTriggered) {
      triggerSuccessNotification('Údaje byli úspěšně dopočítany.');
    } else {
      triggerErrorNotification('Nepodařilo se dopočítat údaje.');
    }
  };

  const validateInput = (concentration: number | string) => {
    const input = concentration.toString();
    if (input) {
      if (/^\d+([.,]\d+)?$/.test(input)) {
        return parseFloat(input.replace(',', '.'));
      } else {
        return -1;
      }
    } else {
      return -1;
    }
  };
  function dynamicRound(number: number): number {
    return parseFloat(number.toFixed(2));
  }
  const currentMass = ref('g');
  const changeMass = async (mass: string) => {
    currentMass.value = mass;
  };

  function convertMass(value: number, fromUnit: string, toUnit: string): number {
    const massConversions: Record<string, number> = {
      g: 1,
      kg: 1000,
      mg: 0.001
    };

    if (massConversions[fromUnit] && massConversions[toUnit]) {
      return value * (massConversions[fromUnit] / massConversions[toUnit]);
    }

    return value;
  }

  watch(currentMass, (newUnit, oldUnit) => {
    updateMassUnit(newUnit, oldUnit);
  });

  function updateMassUnit(newUnit: string, oldUnit: string) {
    if (updateData.value?.product_weight) {
      updateData.value.product_weight = convertMass(
        updateData.value.product_weight,
        oldUnit,
        newUnit
      );
    }
    currentMass.value = newUnit;
  }

  const checkProjectTypePermisions = () => {
    return isAllowed(['view_syntetic_department']) || isAllowed(['edit_syntetic_department']);
  };
  const checkReadOnly = () => {
    return isAllowed(['edit_syntetic_department']);
  };

  const computedChemicals = computed(() => {
    return ChemicalTableRef.value?.getAllChemicalInBaseUnitToUpdateModel();
  });
  const combinedData = computed(() => ({
    ...updateData.value,
    chemicals: { ...computedChemicals.value }
  }));
  watch(
    combinedData,
    (newVal) => {
      if (!isReadOnly.value) {
        if (experimentId.value && baseDataLoaded.value) {
          const experimentFormStore = JSON.parse(
            localStorage.getItem('experimentFormStore') || '{}'
          );
          experimentFormStore[experimentId.value] = newVal;
          localStorage.setItem('experimentFormStore', JSON.stringify(experimentFormStore));
        }
      }
    },
    { deep: true }
  );

  const checkLocalStorage = async () => {
    if (experimentId.value) {
      const experimentFormStore = JSON.parse(localStorage.getItem('experimentFormStore') || '{}');
      if (experimentFormStore[experimentId.value]) {
        updateData.value = {
          form_name: experimentFormStore[experimentId.value].form_name,
          reaction_scheme_file_id: experimentFormStore[experimentId.value].reaction_scheme_file_id,
          batch_description: experimentFormStore[experimentId.value].batch_description,
          reaction_procedure: experimentFormStore[experimentId.value].reaction_procedure,
          product_weight: experimentFormStore[experimentId.value].product_weight,
          yield_percentage: experimentFormStore[experimentId.value].yield_percentage,
          content_percentage: experimentFormStore[experimentId.value].content_percentage,
          conclusion_text: experimentFormStore[experimentId.value].conclusion_text,
          chemicals: experimentFormStore[experimentId.value].chemicals
        };
      }
    }
  };
  const clearLocalStorage = async () => {
    const experimentFormStore = JSON.parse(localStorage.getItem('experimentFormStore') || '{}');
    if (experimentId.value && experimentFormStore[experimentId.value]) {
      delete experimentFormStore[experimentId.value];
      localStorage.setItem('experimentFormStore', JSON.stringify(experimentFormStore));
    }
    const experimentChemicalsFormStore = JSON.parse(
      localStorage.getItem('experimentChemicalsFormStore') || '{}'
    );
    if (experimentId.value && experimentChemicalsFormStore[experimentId.value]) {
      delete experimentChemicalsFormStore[experimentId.value];
      localStorage.setItem(
        'experimentChemicalsFormStore',
        JSON.stringify(experimentChemicalsFormStore)
      );
    }
  };

  const haveOnlyViewPermision = () => {
    if (isAllowed(['edit_syntetic_department'])) {
      return false;
    } else if (isAllowed(['view_syntetic_department'])) {
      return true;
    }
  };
  const haveOnlyEditPermision = () => {
    if (isAllowed(['edit_syntetic_department'])) {
      return true;
    }
  };

  const checkParentClosure = async () => {
    if (
      project.value?.status === 'deactivated' ||
      project.value?.status === 'closed' ||
      department.value?.status === 'closed' ||
      chapter.value?.status === 'closed' ||
      haveOnlyViewPermision() ||
      !checkIfCurrentUserIsCollaborator()
    ) {
      isParentClosed.value = true;
    } else {
      isParentClosed.value = false;
    }
  };

  const checkIfParentHaveAnalyticalDepartment = () => {
    if (project.value?.departments.find((d) => d.type === 'analytical')) {
      return true;
    }
    return false;
  };

  const askForReopen = async () => {
    if (experiment.value && experiment.value.form_id) {
      await formsStore.requestReopenForm(experiment.value.form_id);
    }
  };

  const openPermissions = computed(() => {
    if (checkAdminpermissionNew()) {
      return true;
    } else if (
      checkIfCurrentUserIsCollaboratorWithoutEditAll() &&
      haveOnlyEditPermision() &&
      checkAdminOpenPermission()
    ) {
      return true;
    }
    return false;
  });

  const reopenForm = async () => {
    if (checkAdminpermissionNew()) {
      if (experiment.value) {
        const res = await experimentsStore.reactivateExperiment(experimentId.value);
        if (res) {
          await loadExecute();
          await checkParentClosure();
        }
      }
    } else if (
      checkIfCurrentUserIsCollaboratorWithoutEditAll() &&
      haveOnlyEditPermision() &&
      checkAdminOpenPermission()
    ) {
      if (experiment.value) {
        const res = await experimentsStore.reactivateExperiment(experimentId.value);
        if (res) {
          await loadExecute();
          await checkParentClosure();
        }
      }
    } else {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nemáte oprávnění otevřít experiment.'
      });
    }
  };

  const signAndCloseForm = async () => {
    if (checkAdminpermissionNew()) {
      const res = await formsStore.signAndCloseForm();
      if (res) {
        await clearLocalStorage();
        loadExecute();
      }
    } else if (
      checkIfCurrentUserIsCollaboratorWithoutEditAll() &&
      haveOnlyEditPermision() &&
      checkAdminSignPermission()
    ) {
      if (experiment.value) {
        const res = await formsStore.signAndCloseForm();
        if (res) {
          await clearLocalStorage();
          loadExecute();
        }
      }
    } else {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nemáte oprávnění uzavřít experiment.'
      });
    }
  };

  const checkAdminSignPermission = () => {
    return isAllowed(['sign_experiments']);
  };

  const checkAdminOpenPermission = () => {
    return isAllowed(['reopen_experiments']);
  };

  //trigger save to db every 5 minutes automaticly

  let saveInterval: number;

  const saveEvery5Minutes = () => {
    saveInterval = setInterval(async () => {
      if (!isUpdateDataSameAsOriginal.value) {
        await submitFormToValidate();
      }
    }, 300000);
  };

  onUnmounted(() => {
    clearInterval(saveInterval);
  });

  const showCopyModal = ref(false);
  const copyFormName = ref('');
  const copyLoading = computed(() => experimentsStore.loading);
  function openCopyModal() {
    copyFormName.value = `Kopie – ${experiment.value?.form_name ?? ''}`;
    showCopyModal.value = true;
  }
  async function handleCopyExperiment(newName: string) {
    if (experiment.value?.experiment_id !== undefined) {
      const res = await experimentsStore.copyExperiment(experiment.value.experiment_id, {
        form_name: newName
      });
      if (res && typeof res === 'object' && 'form_id' in res) {
        await loadDataForCopyRedirect(res.form_id);
      }
    }
    showCopyModal.value = false;
  }
</script>
<template>
  <LoaderWrapper v-if="!project || !department || !chapter || !experiment" />
  <template v-else>
    <TopPageBreadcrumb :title="experiment.form_name" :_breadcrumbs="breadcrumbItems" />
    <v-row class="justify-content-end">
      <v-col cols="12">
        <UiParentCard class="pa-0" :loading="loading || !baseDataLoaded">
          <template #action>
            <v-row justify="space-between" class="align-center">
              <v-col cols="12" md="10">
                <div class="d-flex gap-2 justify-start flex-wrap">
                  <v-btn
                    v-if="checkIfParentHaveAnalyticalDepartment()"
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="!checkAdminPermission() && (!baseDataLoaded || isParentClosed)"
                    @click.prevent="redirectToNewAnalyticalRequest"
                  >
                    Generovat požadavek
                  </v-btn>
                  <v-btn
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="!checkAdminPermission() && (!baseDataLoaded || isParentClosed)"
                    @click.prevent="exportFormAsPdf"
                  >
                    Exportovat do PDF
                  </v-btn>
                  <v-btn
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="!checkAdminPermission() && (!baseDataLoaded || isParentClosed)"
                    @click.prevent="exportFormAsWord"
                  >
                    Exportovat do Wordu
                  </v-btn>
                  <v-btn
                    v-if="!isReadOnly && experiment?.status !== 'canceled'"
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="
                      !checkAdminPermission() && (!baseDataLoaded || isReadOnly || isParentClosed)
                    "
                    @click.prevent="
                      async () => {
                        if (!isReadOnly) {
                          if (
                            await ConfirmRef?.open(
                              'Potvrzení',
                              'Opravdu chcete uložit změny a podepsat experiment ?',
                              {
                                color: 'error',
                                notclosable: true,
                                zIndex: 2400
                              }
                            )
                          ) {
                            signAndCloseForm();
                          }
                        } else {
                          loadExecute();
                        }
                      }
                    "
                  >
                    Podepsat
                  </v-btn>

                  <v-btn
                    v-if="
                      (experiment?.status === 'canceled' || experiment?.status === 'signed') &&
                      !openPermissions
                    "
                    size="small"
                    variant="flat"
                    color="primary"
                    @click.prevent="askForReopen"
                  >
                    Požádat o otevření
                  </v-btn>
                  <v-btn
                    v-else-if="experiment?.status === 'canceled' || experiment?.status === 'signed'"
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="!checkAdminPermission() && (!baseDataLoaded || isParentClosed)"
                    @click.prevent="reopenForm"
                  >
                    Otevřít experiment
                  </v-btn>

                  <v-btn
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="!baseDataLoaded || isParentClosed"
                    @click.prevent="toggleTemplateModal"
                  >
                    Vytvořit šablonu
                  </v-btn>

                  <v-btn
                    :disabled="
                      (!checkAdminPermission() && !havePermisionForChannelog) || !baseDataLoaded
                    "
                    variant="flat"
                    color="primary"
                    size="small"
                    @click.prevent="showLogs"
                  >
                    Změny
                  </v-btn>

                  <v-btn
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="!checkAdminPermission() && (!baseDataLoaded || isParentClosed)"
                    @click.prevent="redirectToInsertAnalyticalRequest"
                  >
                    Vložit analýzu
                  </v-btn>

                  <v-btn
                    size="small"
                    variant="flat"
                    color="primary"
                    href="#fileSection"
                    :disabled="!checkAdminPermission() && (!baseDataLoaded || isParentClosed)"
                  >
                    Nahrát soubor
                  </v-btn>

                  <v-btn
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="
                      !checkAdminPermission() &&
                      (!baseDataLoaded || isParentClosed || !checkIfCurrentUserIsOwner())
                    "
                    @click.prevent="formsStore.showInviteModal = true"
                  >
                    Přizvat uživatele
                  </v-btn>

                  <v-btn
                    v-if="isReadOnly"
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="!checkAdminPermission() && (!baseDataLoaded || isParentClosed)"
                    @click.prevent="formsStore.showAddConclusionModal = true"
                  >
                    Přidat závěr
                  </v-btn>

                  <v-btn
                    v-if="isCompleted"
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="!checkAdminPermission() && (!baseDataLoaded || isParentClosed)"
                    @click.prevent="redirectToNewStadardRdAnalyticalRequest"
                  >
                    Prohlásit za standard
                  </v-btn>

                  <v-btn
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="!baseDataLoaded || isParentClosed"
                    @click.prevent="openCopyModal"
                  >
                    Kopírovat experiment
                  </v-btn>
                </div>
              </v-col>
              <v-col cols="12" md="2">
                <div class="d-flex gap-2 justify-end flex-wrap">
                  <v-btn
                    v-if="!isReadOnly"
                    size="small"
                    variant="flat"
                    color="error"
                    :disabled="!baseDataLoaded"
                    @click.prevent="
                      async () => {
                        if (!isReadOnly) {
                          if (
                            isUpdateDataSameAsOriginal ||
                            (await ConfirmRef?.open(
                              'Potvrzení',
                              'Opravdu chcete zrušit změny a načíst data ze serveru?',
                              {
                                color: 'error',
                                notclosable: true,
                                zIndex: 2400
                              }
                            ))
                          ) {
                            reloadPageWithoutSave();
                          }
                        } else {
                          reloadPageWithoutSave();
                        }
                      }
                    "
                  >
                    {{ isReadOnly ? 'Zpět' : isUpdateDataSameAsOriginal ? 'Zpět' : 'Zrušit' }}
                  </v-btn>
                  <v-btn
                    v-if="isReadOnly"
                    size="small"
                    variant="flat"
                    color="error"
                    :disabled="!baseDataLoaded"
                    @click.prevent="goBack()"
                  >
                    Zpět
                  </v-btn>
                  <v-btn
                    v-if="!isReadOnly"
                    size="small"
                    variant="flat"
                    color="primary"
                    type="submit"
                    form="experiment-edit-form"
                    :disabled="!checkAdminPermission() && (!baseDataLoaded || isParentClosed)"
                  >
                    Uložit
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </template>
          <v-row justify="space-between" class="align-center">
            <v-col cols="12" class="d-flex gap-2 justify-end flex-wrap v-card-subtitle">
              Poslední změna: {{ toTimeLocale(experiment.updated_at) }}
            </v-col>
          </v-row>
          <v-form
            v-if="updateData"
            id="experiment-edit-form"
            ref="CreateSyntheticTemplateForm"
            :readonly="!checkAdminPermission() && (isReadOnly || isParentClosed)"
            @submit.prevent="submitFormToValidate"
          >
            <!-- Active Users Indicator -->
            <v-row v-if="webSocket.isConnected()">
              <v-col cols="12" class="d-flex justify-end align-center">
                <ActiveUsersIndicator />
              </v-col>
            </v-row>

            <v-row>
              <v-col
                v-if="
                  experiment.owner && (experiment.owner.first_name || experiment.owner.last_name)
                "
                cols="12"
                md="6"
              >
                <v-label class="mb-2">Vlastník</v-label>
                <div>
                  <v-chip class="ma-1" color="primary" variant="tonal" size="small">
                    {{ experiment.owner.first_name }} {{ experiment.owner.last_name }}
                    <span v-if="experiment.owner.user_email" class="ml-2 text-caption">
                      ({{ experiment.owner.user_email }})
                    </span>
                  </v-chip>
                </div>
              </v-col>
              <v-col v-if="experiment.authors && experiment.authors.length > 0" cols="12" md="6">
                <v-label class="mb-2">Spoluautoři</v-label>
                <div>
                  <v-chip
                    v-for="author in experiment.authors"
                    :key="author.user_id"
                    class="ma-1"
                    color="primary"
                    variant="tonal"
                    size="small"
                  >
                    {{ author.first_name }} {{ author.last_name }}
                    <span v-if="author.user_email" class="ml-2 text-caption">
                      ({{ author.user_email }})
                    </span>
                  </v-chip>
                </div>
              </v-col>
              <v-col cols="12" md="6">
                <v-label class="mb-2">Status</v-label>
                <v-text-field
                  v-model="experiment.statusText"
                  single-line
                  placeholder="Status"
                  hide-details="auto"
                  variant="plain"
                  rounded="sm"
                  :readonly="true"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-label class="mb-2">Název</v-label>
                <FieldLockWrapper field-name="form_name" :disabled="isReadOnly || isParentClosed">
                  <v-text-field
                    v-model="updateData.form_name"
                    :rules="!isReadOnly && !isParentClosed ? itemRequiredRule : []"
                    single-line
                    placeholder="Zadejte název"
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></v-text-field>
                </FieldLockWrapper>
              </v-col>
              <v-col cols="12" md="6">
                <v-label class="mb-2">Šarže</v-label>
                <v-text-field
                  v-model="experiment.batch_number.batch_number"
                  single-line
                  hide-details="auto"
                  variant="outlined"
                  rounded="sm"
                  :readonly="true"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-label class="mb-2">Popis šarže</v-label>
                <FieldLockWrapper
                  field-name="batch_description"
                  :disabled="isReadOnly || isParentClosed"
                >
                  <v-text-field
                    v-model="updateData.batch_description"
                    :rules="!isReadOnly && !isParentClosed ? itemRequiredRule : []"
                    single-line
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></v-text-field>
                </FieldLockWrapper>
              </v-col>

              <v-col cols="12">
                <v-label class="mb-2">Schéma reakce</v-label>
                <div
                  v-if="updateData.reaction_scheme_file_id || isReadOnly || isParentClosed"
                  class="image-container"
                >
                  <v-img
                    :src="selectedReactionSchemeFileUrl"
                    :height="400"
                    aspect-ratio="16/9"
                    contain
                    rounded
                    class="image"
                  ></v-img>
                  <v-btn
                    :disabled="!checkAdminPermission() && (isReadOnly || isParentClosed)"
                    absolute
                    top
                    right
                    color="error"
                    class="floating-button"
                    @click="
                      async () => {
                        if (updateData) updateData.reaction_scheme_file_id = null;
                      }
                    "
                  >
                    <v-icon>mdi-close</v-icon>
                  </v-btn>
                </div>

                <FileUploader
                  v-else
                  v-model="selectedReactionSchemeFile"
                  :process-save-file="
                    async (file_id: number) => {
                      await formsStore.addFileToForm(file_id);
                      if (updateData) updateData.reaction_scheme_file_id = file_id;
                    }
                  "
                  :process-remove-file="
                    async (file_id: number) => {
                      await formsStore.deleteFileFromForm(file_id);
                      if (updateData) updateData.reaction_scheme_file_id = null;
                    }
                  "
                  :disabled="!checkAdminPermission() && (isReadOnly || isParentClosed)"
                  :uppy-options="{
                    height: 250
                  }"
                />
              </v-col>

              <v-col cols="12" class="mb-4">
                <v-label class="mb-2">Tabulka chemikálií</v-label>

                <CustomChemicalTable
                  ref="ChemicalTableRef"
                  :init-value="experiment.chemicals"
                  :form-id="experiment.form_id"
                  :is-editable="checkAdminPermission() || (!isReadOnly && !isParentClosed)"
                  :enable-web-socket="true"
                />
              </v-col>
              <v-col cols="12">
                <v-label class="mb-2">Postup reakce</v-label>

                <EditorTextarea
                  v-model="updateData.reaction_procedure"
                  :show-edit-button="false"
                  :disabled="!checkAdminPermission() && (isReadOnly || isParentClosed)"
                  :config="
                    !checkAdminPermission() && (isReadOnly || isParentClosed)
                      ? ({ statusbar: true, resize: true } as EditorManager & RawEditorOptions)
                      : ({ statusbar: true, resize: true, min_height: 200 } as EditorManager &
                          RawEditorOptions)
                  "
                  :enable-web-socket="true"
                  field-name="reaction_procedure"
                ></EditorTextarea>
              </v-col>
              <v-col cols="12">
                <v-label class="mb-8">Výtěžek</v-label>

                <v-row>
                  <v-col cols="12" md="4">
                    <v-label class="mb-2">Obsah (%)</v-label>
                    <FieldLockWrapper
                      field-name="content_percentage"
                      :disabled="isReadOnly || isParentClosed"
                    >
                      <CustomNumberInput
                        v-model="updateData.content_percentage"
                        single-line
                        hide-details="auto"
                        variant="outlined"
                        rounded="sm"
                        :is-required="true"
                      ></CustomNumberInput>
                    </FieldLockWrapper>
                  </v-col>

                  <v-col cols="12" md="4">
                    <v-label class="mb-2">Produkt ({{ currentMass }})</v-label>
                    <FieldLockWrapper
                      field-name="product_weight"
                      :disabled="isReadOnly || isParentClosed"
                    >
                      <CustomNumberInput
                        v-model="updateData.product_weight"
                        single-line
                        hide-details="auto"
                        variant="outlined"
                        rounded="sm"
                        :is-required="true"
                      ></CustomNumberInput>
                    </FieldLockWrapper>
                  </v-col>

                  <v-col cols="10" md="3">
                    <v-label class="mb-2">Výtěžek (%)</v-label>
                    <FieldLockWrapper
                      field-name="yield_percentage"
                      :disabled="isReadOnly || isParentClosed"
                    >
                      <CustomNumberInput
                        v-model="updateData.yield_percentage"
                        single-line
                        hide-details="auto"
                        variant="outlined"
                        rounded="sm"
                        :is-required="true"
                      ></CustomNumberInput>
                    </FieldLockWrapper>
                  </v-col>
                  <v-col cols="2" md="1">
                    <v-label class="mb-5"></v-label>
                    <div class="action">
                      <div class="operation-wrapper">
                        <v-btn
                          icon
                          color="success"
                          variant="text"
                          rounded="sm"
                          :disabled="!checkAdminPermission() && (!baseDataLoaded || isParentClosed)"
                          @click.prevent="calculateResult"
                        >
                          <CalculatorOutlined />
                        </v-btn>
                      </div>
                    </div>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12">
                <v-row>
                  <v-col cols="12">
                    <v-label class="mb-2">Závěr</v-label>
                    <EditorTextarea
                      v-model="updateData.conclusion_text"
                      :disabled="!checkAdminPermission() && (isReadOnly || isParentClosed)"
                      :show-edit-button="false"
                      :config="
                        !checkAdminPermission() && (isReadOnly || isParentClosed)
                          ? ({ statusbar: true, resize: true } as EditorManager & RawEditorOptions)
                          : ({ statusbar: true, resize: true, min_height: 200 } as EditorManager &
                              RawEditorOptions)
                      "
                      :enable-web-socket="true"
                      field-name="conclusion_text"
                      :table-primary-key="
                        experiment?.conclusions.find((c) => c.type === 'main')?.conclusion_id
                      "
                    ></EditorTextarea>
                  </v-col>

                  <v-col v-if="other_conclusions.length > 0" cols="12">
                    <v-label class="mb-2">Ostatní závěry</v-label>
                  </v-col>
                  <v-col v-for="(conclusion, index) in other_conclusions" :key="index" cols="12">
                    <v-label class="mb-2">Vytvořeno: {{ toLocale(conclusion.created_at) }}</v-label>

                    <EditorTextarea
                      v-model="conclusion.conclusion_text"
                      :disabled="!checkAdminPermission() && (isReadOnly || isParentClosed)"
                      :show-edit-button="false"
                      :config="
                        !checkAdminPermission() && (isReadOnly || isParentClosed)
                          ? ({ statusbar: true, resize: true } as EditorManager & RawEditorOptions)
                          : ({ statusbar: true, resize: true, min_height: 200 } as EditorManager &
                              RawEditorOptions)
                      "
                      :enable-web-socket="true"
                      field-name="conclusion_text"
                      :table-primary-key="conclusion.conclusion_id"
                    ></EditorTextarea>
                  </v-col>
                </v-row>
                <AnalyticalRequestsSection
                  :batch-number="completeBatches || experiment.batch_number"
                  @reload="loadExecute"
                />

                <section class="my-5">
                  <span class="text-h4">Přílohy</span>
                </section>
                <v-row>
                  <v-col v-if="!checkAdminPermission() && (isReadOnly || isParentClosed)" cols="12">
                    <FileUploader
                      v-model="experiment.files"
                      :disabled="true"
                      :process-save-file="formsStore.addFileToForm"
                      :process-remove-file="formsStore.deleteFileFromForm"
                      :uppy-options="{
                        height: 250
                      }"
                    />
                  </v-col>
                  <v-col v-else cols="12">
                    <FileUploader
                      v-model="experiment.files"
                      :process-save-file="formsStore.addFileToForm"
                      :process-remove-file="formsStore.deleteFileFromForm"
                      :uppy-options="{
                        height: 250
                      }"
                    />
                  </v-col>
                </v-row>

                <FileSection
                  :is-read-only="isReadOnly"
                  :files="experiment.files"
                  :custom-remove-file="true"
                  @reload="getAllFiles"
                  @file-remove="
                    async (file_id: number) => {
                      const res = await formsStore.deleteFileFromForm(file_id);
                      if (res) {
                        getAllFiles();
                      }
                    }
                  "
                />
              </v-col>
            </v-row>
          </v-form>
        </UiParentCard>
      </v-col>

      <ConfirmDlg ref="ConfirmRef" />
      <ChangelogPanel v-if="logsStore.showDrawer" v-model:show="logsStore.showDrawer" />
      <AddPeopleToExperimentModal
        v-if="formsStore.showInviteModal"
        v-model:show="formsStore.showInviteModal"
        @reload="loadExecute"
      />
      <AddConclusionModal
        v-if="formsStore.showAddConclusionModal"
        v-model:show="formsStore.showAddConclusionModal"
        @reload="loadExecute"
      />
      <TemplateRename
        v-if="baseDataLoaded"
        v-model:show="showTemplateModal"
        :experiment="experiment"
        @update-name="saveExperimentAsTemplate"
        @reload="loadExecute"
      />
      <CopyExperimentModal
        v-model:show="showCopyModal"
        :default-name="copyFormName"
        :loading="copyLoading"
        @confirm="handleCopyExperiment"
      />
    </v-row>
  </template>
</template>
<style scoped>
  :deep(.uppy-Dashboard .uppy-Dashboard-inner) {
    height: 250px !important;
    min-height: 250px !important;
  }
</style>
