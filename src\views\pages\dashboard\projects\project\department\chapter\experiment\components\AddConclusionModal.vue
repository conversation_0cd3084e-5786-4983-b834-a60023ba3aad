<script setup lang="ts">
  import EditorTextarea from '@/components/shared/EditorTextarea.vue';
  import { useFormsStore, type AddConclusionDto } from '@/stores/forms';
  import { useVModel } from '@vueuse/core';
  import { notification } from 'ant-design-vue';
  import type { EditorManager, RawEditorOptions } from 'tinymce';
  import { ref } from 'vue';

  const emits = defineEmits(['reload']);

  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    }
  });

  const formsStore = useFormsStore();
  const data = ref<AddConclusionDto>({
    conclusion_text: ''
  });
  const CreateColumnForm = ref();
  async function submitFormToValidate() {
    if (data.value && data.value.conclusion_text.length > 0) {
      const res = await formsStore.addConclusionToForm(data.value);
      if (res) {
        formsStore.showAddConclusionModal = false;
        emits('reload');
      }
    } else {
      notification.error({
        message: '<PERSON><PERSON><PERSON>',
        description: '<PERSON><PERSON><PERSON><PERSON><PERSON> pr<PERSON>',
        duration: 5
      });
    }
  }

  const showState = useVModel(props, 'show');
</script>
<template>
  <v-dialog v-model="showState" class="customer-modal" :z-index="1010">
    <v-card>
      <v-form
        ref="CreateColumnForm"
        class="createColumnForm"
        @submit.prevent="submitFormToValidate"
      >
        <v-card-title class="pa-5">
          <span class="text-h5">Přidat další závěr</span>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col>
              <v-row>
                <v-col cols="12">
                  <v-label class="mb-2">Závěr</v-label>
                  <EditorTextarea
                    v-model="data.conclusion_text"
                    :show-edit-button="false"
                    :config="
                      {
                        statusbar: true,
                        resize: true,
                        min_height: 200
                      } as EditorManager & RawEditorOptions
                    "
                  ></EditorTextarea>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" variant="text" @click="formsStore.showAddConclusionModal = false">
            Zrušit
          </v-btn>
          <v-btn color="primary" variant="flat" type="submit">Přidat</v-btn>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>
