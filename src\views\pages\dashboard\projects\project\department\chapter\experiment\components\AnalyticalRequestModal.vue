<script setup lang="ts">
  import { useChaptersStore } from '@/stores/chapters';
  import { useProjectsStore } from '@/stores/projects';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { useVModel } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import { computed, ref } from 'vue';

  const emit = defineEmits(['update:show', 'createChapter', 'updateChapter']);

  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    }
  });

  // stores
  const chaptersStore = useChaptersStore();
  const { modalOptions, loading } = storeToRefs(chaptersStore);

  const projectStore = useProjectsStore();
  const { project, department, chapter } = storeToRefs(projectStore);

  const CreateInstrumentForm = ref();
  async function submitFormToValidate() {
    if (CreateInstrumentForm.value.isValid && modalOptions.value) {
      switch (true) {
        case modalOptions.value.isEditing && !modalOptions.value.isCreating:
          return chaptersStore.updateChapter().then((res) => {
            if (res) {
              emit('updateChapter');
            }
          });
        case !modalOptions.value.isEditing && modalOptions.value.isCreating:
          return chaptersStore.createChapter().then((res) => {
            if (res) {
              emit('createChapter');
            }
          });

        default:
          return 'Náhled kapitoly';
      }
    } else {
      if (modalOptions.value && modalOptions.value.newData) {
        modalOptions.value.newData.confirm = false;
      }
    }
  }

  const showState = useVModel(props, 'show');
  const showTitle = computed(() => {
    if (modalOptions.value === undefined) {
      return 'Náhled kapitoly';
    }

    switch (true) {
      case modalOptions.value.isEditing && !modalOptions.value.isCreating:
        return 'Editace kapitoly';
      case !modalOptions.value.isEditing && modalOptions.value.isCreating:
        return 'Nová kapitola';
      default:
        return 'Náhled kapitoly';
    }
  });
  const showSuccessButtonTitle = computed(() => {
    if (modalOptions.value === undefined) {
      return false;
    }

    switch (true) {
      case modalOptions.value.isEditing && !modalOptions.value.isCreating:
        return 'Upravit kapitolu';
      case !modalOptions.value.isEditing && modalOptions.value.isCreating:
        return 'Přidat kapitolu';
      default:
        return false;
    }
  });

  const onlyPreview = computed(() => {
    return modalOptions.value?.isCreating === false && modalOptions.value?.isEditing === false;
  });
</script>
<template>
  <v-dialog v-model="showState" class="customer-modal" :z-index="1010">
    <v-card :loading="loading">
      <v-form
        v-if="modalOptions?.newData"
        ref="CreateInstrumentForm"
        class="createInstrumentForm"
        :readonly="onlyPreview"
        @submit.prevent="submitFormToValidate"
      >
        <v-card-title class="pa-5">
          <span class="text-h5">
            {{ showTitle }}
          </span>
          <br />
          <div class="d-flex justify-content gap-2">
            <small v-if="project && project.getName" class="text-caption">
              <strong>Projekt:</strong>
              {{ project.getName }}
            </small>
            <small v-if="department && department.getName" class="text-caption">
              <strong>Oddělení:</strong>
              {{ department.getName }}
            </small>
            <small v-if="chapter && chapter.chapter_title" class="text-caption">
              <strong>Kapitola:</strong>
              {{ chapter.chapter_title }}
            </small>
          </div>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col>
              <v-row>
                <v-col>
                  <v-label class="mb-2">Název kapitoly</v-label>
                  <v-text-field
                    v-model="modalOptions.newData.chapter_title"
                    :rules="itemRequiredRule"
                    single-line
                    placeholder="Zadejte název kapitoly"
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></v-text-field>
                </v-col>
                <v-col v-if="modalOptions.isCreating" cols="12">
                  <div class="d-flex justify-space-between gap-2">
                    <div class="pb-4">
                      <h6 class="text-subtitle-1 mb-0">Potvrzení přidání</h6>
                    </div>
                    <v-switch
                      v-model="modalOptions.newData.confirm"
                      color="primary"
                      class="switchRight"
                      hide-details
                    ></v-switch>
                  </div>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" variant="text" @click="chaptersStore.resetModal()">Zrušit</v-btn>
          <v-btn
            v-if="showSuccessButtonTitle"
            color="primary"
            variant="flat"
            type="submit"
            :loading="loading"
          >
            {{ showSuccessButtonTitle }}
          </v-btn>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>
