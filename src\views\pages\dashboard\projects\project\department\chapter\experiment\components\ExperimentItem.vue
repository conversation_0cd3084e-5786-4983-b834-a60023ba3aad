<template>
  <v-card
    variant="outlined"
    :color="
      isParentClosed
        ? 'error'
        : experiment.isCompleted
          ? 'warning'
          : experiment.isActive
            ? 'primary'
            : 'error'
    "
    :class="'card-hover-border bg-containerBg'"
    :subtitle="`Štítky: ${experiment.tags && experiment.tags.length > 0 ? experiment.joinTags : '<PERSON><PERSON>'}`"
    :title="formattedTitle"
    :disabled="!checkProjectTypePermisions()"
    @click.prevent.stop="openExperiment"
  >
    <template #prepend>
      <LockOutlined v-if="experiment.isLocked" :style="{ fontSize: '28px' }" />
      <ExperimentOutlined v-else :style="{ fontSize: '28px' }" />
    </template>
    <div v-if="!isTabletOrSmaller">
      <div v-if="experiment.batch_number" class="absolute-center">
        <p class="v-card-title">{{ experiment.batch_number.batch_number }}</p>
      </div>
      <div v-else class="absolute-center">
        <p class="v-card-title">Experiment nem<PERSON> čís<PERSON></p>
      </div>
      <div class="absolute-center-2">
        <p>{{ formattedDescription }}</p>
      </div>
    </div>
    <template #append>
      <v-menu>
        <template #activator="{ props }">
          <v-btn
            size="x-small"
            v-bind="props"
            variant="text"
            style="height: auto"
            @click.prevent.stop="props.isActive = true"
          >
            <EllipsisOutlined :style="{ fontSize: '28px' }" />
          </v-btn>
        </template>

        <v-list elevation="24" density="compact" class="py-0">
          <v-list-item v-if="!isParentClosed" :value="experiment.form_id + '_tags'">
            <v-list-item-title @click="handleClick(toggleTagsModal, 'tags')">
              Správa štítků
            </v-list-item-title>
          </v-list-item>
          <v-list-item v-if="!isParentClosed" :value="experiment.form_id + '_copy'">
            <v-list-item-title @click="openCopyModal">Kopírovat experiment</v-list-item-title>
          </v-list-item>
          <v-list-item v-if="!isParentClosed" :value="experiment.form_id + '_export_pdf'">
            <v-list-item-title @click="exportAsPdf()">Exportovat jako PDF</v-list-item-title>
          </v-list-item>
          <v-list-item v-if="!isParentClosed" :value="experiment.form_id + '_export_word'">
            <v-list-item-title @click="exportAsWord()">Exportovat jako Word</v-list-item-title>
          </v-list-item>
          <v-list-item
            v-if="experiment.isActive && !isParentClosed"
            :value="experiment.form_id + '_close'"
          >
            <v-list-item-title @click.prevent="handleClick(closeForm, 'close')">
              Uzavřít experiment
            </v-list-item-title>
          </v-list-item>
          <v-list-item
            v-if="!experiment.isActive && !isParentClosed && !openPermissions"
            :value="experiment.form_id + '_ask_reopen'"
          >
            <v-list-item-title @click.prevent="askForReopen()">
              Požádat o otevření
            </v-list-item-title>
          </v-list-item>
          <v-list-item
            v-if="!experiment.isActive && !isParentClosed && openPermissions"
            :value="experiment.form_id + '_reopen'"
          >
            <v-list-item-title @click.prevent="handleClick(reopenForm, 'open')">
              Otevřít experiment
            </v-list-item-title>
          </v-list-item>
          <v-list-item v-if="!isParentClosed" :value="experiment.form_id + '_move'">
            <v-list-item-title @click="openMoveModal">Přesunout do jiné kapitoly</v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>
    </template>
  </v-card>
  <ConfirmDlg ref="ConfirmRef" />
  <TagModal
    v-if="experiment.tagIsLoaded"
    v-model:show="showExperimentTagsModal"
    :tags="experiment.tagLists"
    @update-tags="handleUpdateTags"
  />
  <CopyExperimentModal
    v-model:show="showCopyModal"
    :default-name="copyFormName"
    :loading="copyLoading"
    @confirm="handleCopyExperiment"
  />
  <MoveToChapterModal
    v-model:show="showMoveModal"
    :chapters="chapters"
    :loading="moveLoading"
    @confirm="handleMoveToChapter"
  />
</template>

<script lang="ts" setup>
  //    :subtitle="`Status: ${experiment.statusText}`"
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import TagModal, { type TagListI } from '@/components/shared/TagModal.vue';
  import { useAuthStore } from '@/stores/auth';
  import { type Experiment, useExperimentsStore } from '@/stores/experiments';
  import { useFormsStore } from '@/stores/forms';
  import { UserLockTableName, useUserLock } from '@/stores/userLock';
  import { EllipsisOutlined, ExperimentOutlined, LockOutlined } from '@ant-design/icons-vue';
  import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  
  import { isAllowed } from '@/utils/directive/isAllowed';
  import { notification } from 'ant-design-vue';
  import { useProjectsStore } from '@/stores/projects';
  import { storeToRefs } from 'pinia';
  import CopyExperimentModal from './CopyExperimentModal.vue';
  import MoveToChapterModal from '@/components/shared/MoveToChapterModal.vue';

  const ConfirmRef = ref<InstanceType<typeof ConfirmDlg> | null>(null);

  const emits = defineEmits(['reload']);
  const props = defineProps<{
    experiment: Experiment;
    isParentClosed: boolean;
  }>();

  const router = useRouter();
  const route = useRoute();

  const project_department_id = computed(() => route.params.project_department_id as string);
  const projectDepartmentId = computed(() => parseInt(project_department_id.value));


  const projectsStore = useProjectsStore();
  const auth = useAuthStore();
  const userLock = useUserLock();
  const formsStore = useFormsStore();
  const experimenStore = useExperimentsStore();

  const { user } = storeToRefs(auth);
  const { project_permision } = storeToRefs(projectsStore);

  const isTabletOrSmaller = ref(false);

  const updateIsTabletOrSmaller = () => {
    isTabletOrSmaller.value = window.matchMedia('(max-width: 768px)').matches;
  };

  onMounted(() => {
    updateIsTabletOrSmaller();
    updateLenght();
    window.addEventListener('resize', updateIsTabletOrSmaller);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', updateIsTabletOrSmaller);
  });

  const exportAsPdf = async () => {
    formsStore.form_id = props.experiment.form_id;
    const res = await formsStore.exportFormAsPdf(props.experiment?.form_name);
  };

  const exportAsWord = async () => {
    formsStore.form_id = props.experiment.form_id;
    const res = await formsStore.exportFormAsWord(props.experiment?.form_name);
  };

  const redirectToExperimentDetailLink = (id: number) => {
    return router.resolve({
      name: 'Experiment',
      params: { form_id: id.toString() }
    });
  };

  const openExperiment = async () => {
    if (props.experiment.isLocked) {
      if (
        auth.user?.user_id &&
        props.experiment.edited_by?.user_id &&
        (await ConfirmRef.value?.open(
          'Experiment je uzamčen',
          `Právě edituje uživatel ${props.experiment.edited_by?.user?.getName} (${props.experiment.edited_by?.user?.user_email}). Chcete pokračovat?`,
          {
            color: 'error',
            notclosable: true,
            zIndex: 2400
          }
        ))
      ) {
        await userLock.unlockRowForUser(
          props.experiment.edited_by.user_id,
          UserLockTableName.EXPERIMENT,
          props.experiment.form_id
        );
        await userLock.lockRowForUser(
          auth.user.user_id,
          UserLockTableName.EXPERIMENT,
          props.experiment.form_id
        );
        router.push(redirectToExperimentDetailLink(props.experiment.form_id));
      }
    } else {
      router.push(redirectToExperimentDetailLink(props.experiment.form_id));
    }
  };

  const checkProjectTypePermisions = () => {
    return isAllowed(['view_syntetic_department']) || isAllowed(['edit_syntetic_department']);
  };

  const showExperimentTagsModal = ref(false);

  const toggleTagsModal = () => {
    showExperimentTagsModal.value = !showExperimentTagsModal.value;
  };

  const handleUpdateTags = async (tags: TagListI[]) => {
    await experimenStore.updateTags(props.experiment, tags);
    emits('reload');
  };

  const closeForm = async () => {
    if (
      await ConfirmRef.value?.open('Opravdu chcete uzavřít experiment?', '', {
        color: 'error',
        notclosable: true,
        zIndex: 2400
      })
    ) {
      const res = await experimenStore.closeExperiment(props.experiment.form_id, false);
      if (res) emits('reload');
    }
  };
  const reopenForm = async () => {
    await experimenStore.reactivateExperiment(props.experiment.form_id);
    emits('reload');
  };
  const askForReopen = async () => {
    if (props.experiment.form_id) {
      await formsStore.requestReopenForm(props.experiment.form_id);
    }
  };
  const havePermision = ref<boolean>(true);
  const missingPermison = ref<string>();
  const checkProjectTypePermisionsForEdit = () => {
    if (isAllowed(['edit_syntetic_department']) || isAllowed(['edit_all'])) {
      havePermision.value = true;
      return true;
    } else {
      missingPermison.value = 'edit_syntetic_department';
      havePermision.value = false;
    }

    if (!havePermision.value) {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nemáte oprávnění pro editaci experimentu: ' + missingPermison.value + '.'
      });
    }
    return false;
  };

  const checkAdminPermission = () => {
    return isAllowed(['edit_all']);
  };

  const checkAdminSignPermission = () => {
    return isAllowed(['sign_experiments']);
  };

  const checkAdminOpenPermission = () => {
    return isAllowed(['reopen_experiments']);
  };

  const checkIfCurrentUserIsOwner = () => {
    if (props.experiment?.owner === null || user.value === null) {
      return false;
    }
    return props.experiment?.owner?.user_id === user.value?.user_id;
  };

  const checkIfCurrentUserIsCollaborator = () => {
    if (props.experiment?.collaborators === null && user.value === null) {
      return false;
    }
    return (
      props.experiment?.collaborators.some(
        (collaborator) => collaborator.user_id === user.value?.user_id
      ) || checkIfCurrentUserIsOwner()
    );
  };

  const openPermissions = computed(() => {
    if (checkAdminPermission()) {
      return true;
    } else if (
      checkIfCurrentUserIsCollaborator() &&
      isAllowed(['edit_syntetic_department']) &&
      checkAdminOpenPermission()
    ) {
      return true;
    }
    return false;
  });

  const handleClick = (action: () => void, type: string) => {
    if (checkAdminPermission()) {
      action();
      return;
    } else if (
      type === 'close' &&
      checkAdminSignPermission() &&
      checkIfCurrentUserIsCollaborator() &&
      isAllowed(['edit_syntetic_department'])
    ) {
      action();
      return;
    } else if (
      type === 'open' &&
      checkAdminOpenPermission() &&
      checkIfCurrentUserIsCollaborator() &&
      isAllowed(['edit_syntetic_department'])
    ) {
      action();
      return;
    } else if (type === 'close') {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nemáte oprávnění uzavřít experiment.'
      });
      return;
    } else if (type === 'open') {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nemáte oprávnění otevřít experiment.'
      });
      return;
    }
    if (checkProjectTypePermisionsForEdit()) {
      action();
    }
  };

  defineExpose({
    toggleTagsModal,
    handleUpdateTags,
    closeForm,
    reopenForm
  });

  const maxLegth = ref(40);

  const formattedTitle = computed(() => {
    if (!props.experiment.form_name) return '';
    return props.experiment.form_name.length > maxLegth.value
      ? `${props.experiment.form_name.slice(0, maxLegth.value)}...`
      : props.experiment.form_name;
  });

  const formattedDescription = computed(() => {
    if (!props.experiment.batch_description) return '';
    return props.experiment.batch_description.length > maxLegth.value
      ? `${props.experiment.batch_description.slice(0, maxLegth.value)}...`
      : props.experiment.batch_description;
  });
  const updateLenght = () => {
    if (window.innerWidth < 1465) {
      maxLegth.value = 20;
    } else {
      maxLegth.value = 40;
    }
  };

  const showCopyModal = ref(false);
  const copyFormName = ref('');
  const copyLoading = computed(() => experimenStore.loading);
  async function openCopyModal() {
    copyFormName.value = `Kopie – ${props.experiment.form_name}`;
    showCopyModal.value = true;
  }
  async function handleCopyExperiment(newName: string) {
    await experimenStore.copyExperiment(props.experiment.form_id, { form_name: newName });
  }

  const showMoveModal = ref(false);
  const chapters = ref<any[]>([]);
  const moveLoading = ref(false);

  async function openMoveModal() {
    moveLoading.value = true;
    const department = await projectsStore.getDepartment(projectDepartmentId.value);
    chapters.value = (department?.chapters?.filter((c: any) => c.chapter_id !== props.experiment.chapter_id)
      .map((c: any) => ({ chapter_id: c.chapter_id, chapter_title: c.chapter_title, status: c.status }))
    ) || [];
    moveLoading.value = false;
    showMoveModal.value = true;
  }

  async function handleMoveToChapter(chapter_id: number) {
    moveLoading.value = true;
    formsStore.form_id = props.experiment.form_id;
    const res = await formsStore.moveToDifferentChapter(chapter_id);
    moveLoading.value = false;
    if (res) emits('reload');
  }
</script>

<style scoped>
  .absolute-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }
  .absolute-center-2 {
    position: absolute;
    top: 75%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }
</style>
