<script setup lang="ts">
  import { useChaptersStore } from '@/stores/chapters';
  import { useExperimentsStore } from '@/stores/experiments';
  import { ExperimentTemplateStatus, useExperimentTemplates } from '@/stores/experimentTemplates';
  import { useProjectsStore, type GetAllOptions } from '@/stores/projects';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { useVModel } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import { computed, onMounted, ref } from 'vue';
  import type { ServerOptions } from 'vue3-easy-data-table';

  const emit = defineEmits(['update:show', 'createExperiment']);

  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    }
  });

  // stores
  const chaptersStore = useChaptersStore();
  const { chapter } = storeToRefs(chaptersStore);

  const projectsStore = useProjectsStore();
  const { project } = storeToRefs(projectsStore);

  const experimentTemplateStore = useExperimentTemplates();
  const { experimentTemplates, loading: experimentTemplateLoading } =
    storeToRefs(experimentTemplateStore);

  const experimentsStore = useExperimentsStore();
  const { modalOptions, loading: experimentsLoading } = storeToRefs(experimentsStore);

  const CreateInstrumentForm = ref();
  async function submitFormToValidate() {
    if (CreateInstrumentForm.value.isValid && modalOptions.value) {
      return experimentsStore.createExperiment().then((res) => {
        if (res) {
          // emit('createExperiment');
        }
      });
    } else {
      if (modalOptions.value && modalOptions.value.newData) {
        modalOptions.value.newData.confirm = false;
      }
    }
  }

  const showState = useVModel(props, 'show');
  const onlyPreview = computed(() => {
    return modalOptions.value?.isCreating === false && modalOptions.value?.isEditing === false;
  });

  onMounted(() => {
    const experimentTemplateGetAllOptions = {
      search: '',
      totalItems: 0,
      options: {
        page: 1,
        rowsPerPage: 100,
        sortBy: ['status', 'experiment_template_id'],
        sortDesc: ['asc']
      } as ServerOptions,
      fixedFilterOptions: [
        {
          column: 'project_id',
          value: project.value?.project_id ?? null
        },
        {
          column: 'status',
          value: ExperimentTemplateStatus.ACTIVE
        }
      ],
      search_columns: []
    } as GetAllOptions;
    experimentTemplateStore.getExperimentTemplates(true, experimentTemplateGetAllOptions);
  });
</script>
<template>
  <v-dialog v-model="showState" class="customer-modal" :z-index="1010">
    <v-card :loading="experimentsLoading || experimentTemplateLoading">
      <v-form
        v-if="modalOptions?.newData"
        ref="CreateInstrumentForm"
        class="createInstrumentForm"
        :readonly="onlyPreview"
        @submit.prevent="submitFormToValidate"
      >
        <v-card-title class="pa-5">
          <span class="text-h5">Přidat experiment</span>
          <br />
          <div class="d-flex justify-content gap-2">
            <small v-if="chapter && chapter.chapter_title" class="text-caption">
              <strong>Kapitola:</strong>
              {{ modalOptions.newData.chapter_id && chapter ? chapter.chapter_title : 'Nevybráno' }}
            </small>
          </div>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col>
              <v-row>
                <v-col cols="12">
                  <v-label class="mb-2">Název experimentu</v-label>
                  <v-text-field
                    v-model="modalOptions.newData.form_name"
                    :rules="itemRequiredRule"
                    single-line
                    placeholder="Zadejte název experimentu"
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></v-text-field>
                </v-col>

                <v-col cols="12">
                  <v-label class="mb-2">Šablona</v-label>
                  <v-select
                    v-model="modalOptions.newData.experiment_template_id"
                    :items="
                      experimentTemplates.map((experimentTemplate) => ({
                        id: experimentTemplate.experiment_template_id,
                        title: experimentTemplate.template_name
                      }))
                    "
                    item-text="name"
                    item-value="id"
                    placeholder="Vyberte šablonu"
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                    density="compact"
                    clearable
                  ></v-select>
                </v-col>
                <v-col v-if="modalOptions.isCreating" cols="12">
                  <div class="d-flex justify-space-between gap-2">
                    <div class="pb-4">
                      <h6 class="text-subtitle-1 mb-0">Potvrzení přidání</h6>
                    </div>
                    <v-switch
                      v-model="modalOptions.newData.confirm"
                      color="primary"
                      class="switchRight"
                      hide-details
                    ></v-switch>
                  </div>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" variant="text" @click="experimentsStore.resetModal()">Zrušit</v-btn>
          <v-btn color="primary" variant="flat" type="submit" :loading="experimentsLoading">
            Přidat experiment
          </v-btn>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>
