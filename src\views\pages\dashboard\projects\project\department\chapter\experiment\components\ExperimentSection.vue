<template>
  <section class="my-5">
    <span class="text-h4">Experimenty</span>
  </section>
  <v-row v-if="experiments.length > 0">
    <v-col
      v-for="experiment in experiments
        .filter((chapter) =>
          experimentSearch
            ? chapter.form_name
                .toUpperCase()
                .includes(experimentSearch.toUpperCase().replace(/\s+/g, '')) ||
              chapter.joinTags
                .toUpperCase()
                .includes(experimentSearch.toUpperCase().replace(/\s+/g, ''))
            : true
        )
        .sort((a, b) => {
          const statusOrder = { created: 1, in_progress: 2, signed: 3, canceled: 4 };
          if (statusOrder[a.status] !== statusOrder[b.status]) {
            return statusOrder[a.status] - statusOrder[b.status];
          }
          return a.created_at > b.created_at ? -1 : 1;
        })"
      :key="experiment.form_id"
      cols="12"
    >
      <ExperimentItem
        :experiment="experiment"
        :is-parent-closed="isParentClosed"
        @reload="$emit('reload')"
      />
    </v-col>
  </v-row>
  <NotFoundItem v-else>
    <template #notFound>Nebyly nalezeny žádné experimenty</template>
  </NotFoundItem>
</template>

<script lang="ts" setup>
  import NotFoundItem from '@/components/shared/NotFoundItem.vue';
  import type { Experiment } from '@/stores/experiments';
  import ExperimentItem from './ExperimentItem.vue';

  defineEmits(['reload']);
  defineProps<{
    experiments: Experiment[];
    experimentSearch: string | undefined;
    isParentClosed: boolean;
  }>();
</script>
