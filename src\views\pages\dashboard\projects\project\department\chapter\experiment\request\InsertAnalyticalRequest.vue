<script setup lang="ts">
  import type { BreadcrumbI } from '@/components/shared/BaseBreadcrumb.vue';
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import FileUploader from '@/components/shared/file/FileUploader.vue';
  import TopPageBreadcrumb from '@/components/shared/TopPageBreadcrumb.vue';
  import UiParentCard from '@/components/shared/UiParentCard.vue';
  import LoaderWrapper from '@/layouts/dashboard/LoaderWrapper.vue';
  import {
    NewAnalyticalRequestType,
    useAnalyticalRequestsStore,
    type AnalyticalRequestModalNewPostDataI
  } from '@/stores/analyticalRequests/analyticalRequests';
  import { Attempt } from '@/stores/attempts';
  import { Experiment } from '@/stores/experiments';
  import { FormType, useFormsStore, type InsertAnalysisDto } from '@/stores/forms';
  import { Investigation } from '@/stores/investigations';
  import { Message } from '@/stores/messages';
  import { useProjectsStore } from '@/stores/projects';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { storeToRefs } from 'pinia';
  import { computed, onMounted, ref, watch, reactive } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import InsertAnalysisParametersFormTable from './components/InsertAnalysisParametersFormTable.vue';
  import FileSection from '@/components/shared/file/FileSection.vue';
  import { useFilesStore, type File } from '@/stores/files';
  import { useDebounceFn } from '@vueuse/core';
  import type { AnalyticalRequestDto } from '@/stores/analyticalRequests/analyticalRequests';
  import type { GetAllOptions } from '@/stores/projects';
  import { notification } from 'ant-design-vue';
  import { setPageTitle } from '@/utils/title';

  const filesStore = useFilesStore();

  const ConfirmRef = ref<undefined | typeof ConfirmDlg>(undefined);

  const route = useRoute();
  const router = useRouter();
  const formsStore = useFormsStore();
  const projectsStore = useProjectsStore();
  const analyticalRequestsStore = useAnalyticalRequestsStore();
  const { project, department, chapter } = storeToRefs(projectsStore);
  const { analyticalRequests, loadingSamples } = storeToRefs(analyticalRequestsStore);

  const baseDataLoaded = ref(false);
  const newGenerateAnalyticalRequestData = ref<InsertAnalysisDto | null>(null);
  const form = ref<Experiment | Message | Investigation | Attempt | undefined>(undefined);

  onMounted(async () => {
    await loadExecute();
    // Fetch initial samples after base data is loaded
    if (baseDataLoaded.value) {
      await debouncedSampleSearch();
    } else {
      // If base data isn't loaded yet, set up a watcher to call it once loaded
      const unwatch = watch(baseDataLoaded, async (newVal) => {
        if (newVal) {
          await debouncedSampleSearch();
          unwatch(); // Remove the watcher after it's triggered
        }
      });
    }
  });

  const chapterSearch = ref<string | undefined>(undefined);

  const project_id = computed(() => route.params.project_id as string);
  const projectId = computed(() => parseInt(project_id.value));

  const project_department_id = computed(() => route.params.project_department_id as string);
  const projectDepartmentId = computed(() => parseInt(project_department_id.value));

  const chapter_id = computed(() => route.params.chapter_id as string);
  const chapterId = computed(() => parseInt(chapter_id.value));

  const form_id = computed(() => route.params.form_id as string | undefined);
  const formId = computed(() => (form_id.value ? parseInt(form_id.value) : null));

  const type = computed(() => route.params.type as string | undefined);

  const SampleOptions = reactive<GetAllOptions>({
    search: '',
    options: {
      page: 1,
      rowsPerPage: 25,
      sortBy: [],
      sortType: []
    },
    search_columns: ['sample_number'],
    fixedFilterOptions: []
  });

  const searchValueSample = ref('');

  const getProjectDepartmentIdForSampleSearch = (): number | null => {
    if (!type.value || !project.value?.departments) {
      return projectDepartmentId.value;
    }

    try {
      if (type.value === 'rd') {
        const syntheticDepartment = project.value.departments.find(
          (dept) => dept.type === 'synthetic'
        );
        if (syntheticDepartment) {
          return syntheticDepartment.project_department_id;
        }
      } else if (type.value === 'qc-a-vt') {
        const technicalDepartment = project.value.departments.find(
          (dept) => dept.type === 'technical'
        );
        if (technicalDepartment) {
          return technicalDepartment.project_department_id;
        }
      }
    } catch (error) {
      console.warn('Error determining project_department_id for sample search:', error);
    }

    return projectDepartmentId.value;
  };

  const debouncedSampleSearch = useDebounceFn(async () => {
    const searchDepartmentId = getProjectDepartmentIdForSampleSearch();

    if (searchDepartmentId) {
      SampleOptions.search = searchValueSample.value;
      SampleOptions.fixedFilterOptions = [
        {
          column: 'project_department_id',
          value: searchDepartmentId
        }
      ];
      await analyticalRequestsStore.getAnalyticalRequests(true, SampleOptions, 'OR');
    } else {
      SampleOptions.search = searchValueSample.value;
      SampleOptions.fixedFilterOptions = [];
      await analyticalRequestsStore.getAnalyticalRequests(true, SampleOptions, 'OR');
    }
  }, 350);

  watch(searchValueSample, () => {
    if (searchValueSample.value) {
      debouncedSampleSearch();
    }
  });

  const loadExecute = async () => {
    baseDataLoaded.value = false;
    chapterSearch.value = undefined;

    if (projectId.value) {
      if (project.value) {
        if (project.value.project_id !== projectId.value) {
          await projectsStore.getProjectById(projectId.value);
        }
      } else {
        await projectsStore.getProjectById(projectId.value);
      }
    }

    if (projectDepartmentId.value) {
      if (department.value) {
        if (department.value.project_department_id !== projectDepartmentId.value) {
          await projectsStore.getDepartmentById(projectDepartmentId.value);
        }
      } else {
        await projectsStore.getDepartmentById(projectDepartmentId.value);
      }
    }

    if (chapterId.value) {
      if (chapter.value) {
        if (chapter.value.chapter_id !== chapterId.value) {
          await projectsStore.getChapterById(chapterId.value);
        }
      } else {
        await projectsStore.getChapterById(chapterId.value);
      }
    }

    if (formId.value) {
      const _form = await formsStore.getForm(formId.value);
      if (_form) {
        form.value = _form;
      }
      if (form.value?.batch_number.batch_number) {
        setPageTitle(form.value.batch_number?.batch_number);
      }
    }

    if (
      department.value &&
      project.value &&
      chapter.value &&
      project.value.departments.find(
        (d) =>
          department.value && d.project_department_id === department.value.project_department_id
      ) &&
      department.value.chapters.find(
        (c) => chapter.value && c.chapter_id === chapter.value.chapter_id
      )
    ) {
      const department_id =
        typeof chapter.value.department_id === 'number'
          ? chapter.value.department_id
          : chapter.value.department_id.project_department_id;
      //const sampleNumber = await projectsStore.getProjectDepartmentSampleNumbers(department_id);

      newGenerateAnalyticalRequestData.value = {
        name: '',
        sample_number: null,
        samples: [],
        files_ids: [],
        files: []
      };

      formsStore.form_id = form.value?.form_id;
      baseDataLoaded.value = true;
    } else {
      if (project.value && department.value && chapter.value) {
        router.push({
          name: 'ChapterDetail',
          params: {
            project_id: project.value.project_id.toString(),
            chapter_id: chapter.value.chapter_id.toString(),
            project_department_id: department.value.project_department_id.toString()
          }
        });
      } else if (project.value && department.value) {
        router.push({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value.project_id.toString(),
            project_department_id: department.value.project_department_id.toString()
          }
        });
      } else if (project.value) {
        router.push({
          name: 'ProjectDetail',
          params: { project_id: project.value.project_id.toString() }
        });
      } else {
        router.push({ name: 'ListOfProjects' });
      }
    }
  };

  watch([project_id, project_department_id, chapter_id], () => {
    loadExecute();
  });

  const breadcrumbItems = computed<BreadcrumbI[]>(() => {
    return [
      {
        title: 'Projekty',
        disabled: false,
        href: router.resolve({ name: 'Projects' }).href
      },
      {
        title: project.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'ProjectDetail',
          params: { project_id: project.value?.project_id }
        }).href
      },
      {
        title: department.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id
          }
        }).href
      },
      {
        title: chapter.value?.chapter_title ?? '',
        disabled: false,
        href: router.resolve({
          name: 'ChapterDetail',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id,
            chapter_id: chapter.value?.chapter_id
          }
        }).href
      },
      ...(form.value?.form_name
        ? [
            {
              title: form.value.form_name,
              disabled: false,
              href: router.resolve({
                name: nameOfRouteBeforeByFormType.value,
                params: {
                  project_id: project.value?.project_id,
                  project_department_id: department.value?.project_department_id,
                  chapter_id: chapter.value?.chapter_id,
                  form_id: form.value.form_id
                }
              }).href
            }
          ]
        : []),
      {
        title: 'Vložit analýzu',
        disabled: true,
        href: '#'
      }
    ];
  });

  const reloadPageWithoutSave = () => {
    // window reload current page
    // window.location.reload();
    loadExecute();
  };

  const goBack = () => {
    router.push({
      name: nameOfRouteBeforeByFormType.value,
      params: {
        project_id: project.value?.project_id,
        project_department_id: department.value?.project_department_id,
        chapter_id: chapter.value?.chapter_id,
        form_id: form.value?.form_id
      }
    });
  };

  const CreateSyntheticTemplateForm = ref();
  const isInProgress = ref(false);
  async function submitFormToValidate() {
    if (
      CreateSyntheticTemplateForm.value.isValid &&
      newGenerateAnalyticalRequestData.value &&
      !isInProgress.value
    ) {
      isInProgress.value = true;
      newGenerateAnalyticalRequestData.value.files_ids =
        newGenerateAnalyticalRequestData.value.files.map((f) => f.file_id);

      if (formsStore.form_id) {
        const res = await formsStore.insertAnalysis(newGenerateAnalyticalRequestData.value);
        if (res) {
          router.push({
            name: nameOfRouteBeforeByFormType.value,
            params: {
              project_id: project.value?.project_id,
              project_department_id: department.value?.project_department_id,
              chapter_id: chapter.value?.chapter_id,
              form_id: form.value?.form_id
            }
          });
        }
      }

      if (type.value) {
        const data = {
          project_id: projectId.value,
          type: NewAnalyticalRequestType.NORMAL, // type.value === 'rd' ? NewAnalyticalRequestType.STANDARD_RD : NewAnalyticalRequestType.STANDARD_QC,
          project_department_id: projectDepartmentId.value,
          sample_number: newGenerateAnalyticalRequestData.value.sample_number,
          name: newGenerateAnalyticalRequestData.value.name,
          samples: newGenerateAnalyticalRequestData.value.samples,
          files_ids: newGenerateAnalyticalRequestData.value.files_ids
        } as AnalyticalRequestModalNewPostDataI;

        const res = await analyticalRequestsStore.createAnalyticalRequest(data);
        if (res) {
          router.push({
            name: nameOfRouteBeforeByFormType.value,
            params: {
              project_id: project.value?.project_id,
              project_department_id: department.value?.project_department_id,
              chapter_id: chapter.value?.chapter_id,
              form_id: form.value?.form_id
            }
          });
        }
      }
    }
    isInProgress.value = false;
  }

  watch(
    () => newGenerateAnalyticalRequestData.value?.files,
    () => {
      if (newGenerateAnalyticalRequestData.value) {
        newGenerateAnalyticalRequestData.value.files_ids =
          newGenerateAnalyticalRequestData.value.files.map((f) => f.file_id);
      }
    }
  );

  const nameOfRouteBeforeByFormType = computed(() => {
    if (form.value) {
      switch (form.value.form_type) {
        case FormType.EXPERIMENT:
          return 'Experiment';
        case FormType.MESSAGE:
          return 'Message';
        case FormType.INVESTIGATION:
          return 'Investigation';
        case FormType.ATTEMPT:
          return 'Attempt';
      }
    }

    if (type.value) {
      return 'ChapterDetail';
    }

    return 'ListOfProjects';
  });

  const getAllFiles = async () => {
    const files = await formsStore.getAllFiles();
    if (newGenerateAnalyticalRequestData.value && files !== false) {
      newGenerateAnalyticalRequestData.value.files = files;
    }
  };
</script>
<template>
  <LoaderWrapper
    v-if="!project || !department || !chapter || newGenerateAnalyticalRequestData === null"
  />
  <template v-else>
    <TopPageBreadcrumb title="Vložit analýzu" :_breadcrumbs="breadcrumbItems" />
    <v-row class="justify-content-end">
      <v-col cols="12">
        <UiParentCard class="pa-0" :loading="!baseDataLoaded">
          <template #action>
            <v-row justify="space-between" class="align-center">
              <v-col cols="12">
                <div class="d-flex gap-2 justify-end flex-wrap">
                  <v-btn
                    v-if="project?.project_id"
                    variant="flat"
                    color="error"
                    :disabled="!baseDataLoaded"
                    @click.prevent="goBack"
                  >
                    Zpět
                  </v-btn>
                  <v-btn
                    v-if="project?.project_id"
                    variant="flat"
                    color="primary"
                    :disabled="!baseDataLoaded"
                    type="submit"
                    form="form-edit-form"
                  >
                    Uložit analýzu
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </template>

          <v-form
            id="form-edit-form"
            ref="CreateSyntheticTemplateForm"
            @submit.prevent="submitFormToValidate"
          >
            <v-row>
              <v-col cols="12" md="6">
                <v-label class="mb-2">Název vzorku</v-label>
                <v-text-field
                  v-model="newGenerateAnalyticalRequestData.name"
                  :rules="itemRequiredRule"
                  single-line
                  placeholder="Zadejte název"
                  hide-details="auto"
                  variant="outlined"
                  rounded="sm"
                ></v-text-field>
              </v-col>

              <v-col cols="12" md="6">
                <v-label class="mb-2">Číslo vzorku</v-label>
                <v-autocomplete
                  v-model="newGenerateAnalyticalRequestData.sample_number"
                  v-model:search="searchValueSample"
                  :rules="itemRequiredRule"
                  rounded="sm"
                  :items="analyticalRequests"
                  variant="outlined"
                  color="primary"
                  item-title="sample_number"
                  item-value="sample_number"
                  label="Vyberte číslo vzorku"
                  single-line
                  class="autocomplete"
                  no-data-text="Žádná další políčka"
                  :slim="true"
                  :loading="loadingSamples"
                >
                  <template #chip>
                    <v-chip
                      label
                      variant="tonal"
                      color="primary"
                      size="large"
                      class="my-1 text-subtitle-1 font-weight-regular"
                    ></v-chip>
                  </template>

                  <template #item="{ props, item }">
                    <v-list-item v-bind="props" :title="''">
                      <div class="player-wrapper pa-2">
                        <h6 class="text-subtitle-1 mb-0">
                          {{ item.raw.sample_number }}
                        </h6>
                      </div>
                    </v-list-item>
                  </template>
                </v-autocomplete>
              </v-col>

              <v-col cols="12">
                <v-label class="mb-2">Zvolte techniky</v-label>
                <InsertAnalysisParametersFormTable
                  v-model:="newGenerateAnalyticalRequestData.samples"
                  :project_id="department.project_id"
                ></InsertAnalysisParametersFormTable>
              </v-col>

              <v-col cols="12">
                <v-label class="mb-2">Přílohy</v-label>
                <FileUploader v-model="newGenerateAnalyticalRequestData.files" />
              </v-col>
              <v-col cols="12">
                <FileSection
                  :files="newGenerateAnalyticalRequestData.files"
                  :custom-remove-file="true"
                  @reload="getAllFiles"
                  @file-remove="
                    async (file_id: number) => {
                      const res = await filesStore.deleteFile(file_id);
                      if (res) {
                        getAllFiles();
                      }
                    }
                  "
                />
              </v-col>
            </v-row>
          </v-form>
        </UiParentCard>
      </v-col>

      <ConfirmDlg ref="ConfirmRef" />
    </v-row>
  </template>
</template>
