<script setup lang="ts">
  import type { BreadcrumbI } from '@/components/shared/BaseBreadcrumb.vue';
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import FileUploader from '@/components/shared/file/FileUploader.vue';
  import TopPageBreadcrumb from '@/components/shared/TopPageBreadcrumb.vue';
  import UiParentCard from '@/components/shared/UiParentCard.vue';
  import LoaderWrapper from '@/layouts/dashboard/LoaderWrapper.vue';
  import { NewAnalyticalRequestType } from '@/stores/analyticalRequests/analyticalRequests';
  import { Attempt } from '@/stores/attempts';
  import { Experiment } from '@/stores/experiments';
  import { FormType, useFormsStore, type NewGenerateAnalyticalRequestDto } from '@/stores/forms';
  import { Investigation } from '@/stores/investigations';
  import { Message } from '@/stores/messages';
  import { useProjectsStore } from '@/stores/projects';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { storeToRefs } from 'pinia';
  import { computed, onMounted, ref, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import ParametersFormTable from './components/ParametersFormTable.vue';
  import FileSection from '@/components/shared/file/FileSection.vue';
  import { useFilesStore, type File } from '@/stores/files';
  import { setPageTitle } from '@/utils/title';

  const filesStore = useFilesStore();
  const ConfirmRef = ref<undefined | typeof ConfirmDlg>(undefined);

  const route = useRoute();
  const router = useRouter();
  const formsStore = useFormsStore();
  const projectsStore = useProjectsStore();
  const { project, department, chapter } = storeToRefs(projectsStore);

  const baseDataLoaded = ref(false);
  const newGenerateAnalyticalRequestData = ref<NewGenerateAnalyticalRequestDto | null>(null);
  const form = ref<Experiment | Message | Investigation | Attempt | undefined>(undefined);

  // Define requestType and set its default value to 'NORMAL'
  const requestType = ref<'NORMAL' | 'STANDARD_RD' | 'STANDARD_QC'>(
    (route.query.requestType as 'STANDARD_RD' | 'STANDARD_QC') || 'NORMAL'
  );

  // Watch for changes in the route's query parameters to update requestType
  watch(
    () => route.query.requestType,
    (newType) => {
      requestType.value = (newType as 'STANDARD_RD' | 'STANDARD_QC') || 'NORMAL';
    },
    { immediate: true } // Ensure the watcher runs immediately on component mount
  );

  // Computed property
  const pageTitle = computed(() => {
    if (requestType.value === 'NORMAL') {
      return 'Generovat analytický požadavek';
    } else {
      return 'Prohlásit za standard';
    }
  });

  onMounted(async () => {
    loadExecute();
  });

  const chapterSearch = ref<string | undefined>(undefined);

  const project_id = computed(() => route.params.project_id as string);
  const projectId = computed(() => parseInt(project_id.value));

  const project_department_id = computed(() => route.params.project_department_id as string);
  const projectDepartmentId = computed(() => parseInt(project_department_id.value));

  const chapter_id = computed(() => route.params.chapter_id as string);
  const chapterId = computed(() => parseInt(chapter_id.value));

  const form_id = computed(() => route.params.form_id as string);
  const formId = computed(() => parseInt(form_id.value));

  const loadExecute = async () => {
    baseDataLoaded.value = false;
    chapterSearch.value = undefined;

    if (projectId.value) {
      if (project.value) {
        if (project.value.project_id !== projectId.value) {
          await projectsStore.getProjectById(projectId.value);
        }
      } else {
        await projectsStore.getProjectById(projectId.value);
      }
    }

    if (projectDepartmentId.value) {
      if (department.value) {
        if (department.value.project_department_id !== projectDepartmentId.value) {
          await projectsStore.getDepartmentById(projectDepartmentId.value);
        }
      } else {
        await projectsStore.getDepartmentById(projectDepartmentId.value);
      }
    }

    if (chapterId.value) {
      if (chapter.value) {
        if (chapter.value.chapter_id !== chapterId.value) {
          await projectsStore.getChapterById(chapterId.value);
        }
      } else {
        await projectsStore.getChapterById(chapterId.value);
      }
    }

    if (formId.value) {
      const _form = await formsStore.getForm(formId.value);
      if (_form) {
        form.value = _form;
      }
      if (form.value?.batch_number.batch_number) {
        setPageTitle(form.value.batch_number?.batch_number);
      }
    }

    if (
      department.value &&
      project.value &&
      chapter.value &&
      form.value &&
      project.value.departments.find(
        (d) =>
          department.value && d.project_department_id === department.value.project_department_id
      ) &&
      department.value.chapters.find(
        (c) => chapter.value && c.chapter_id === chapter.value.chapter_id
      ) &&
      form.value.chapter_id === chapter.value.chapter_id
    ) {
      newGenerateAnalyticalRequestData.value = {
        name: '',
        files_ids: [],
        samples: [],
        files: []
      };

      formsStore.form_id = form.value.form_id;
      baseDataLoaded.value = true;
    } else {
      if (project.value && department.value && chapter.value) {
        router.push({
          name: 'ChapterDetail',
          params: {
            project_id: project.value.project_id.toString(),
            chapter_id: chapter.value.chapter_id.toString(),
            project_department_id: department.value.project_department_id.toString()
          }
        });
      } else if (project.value && department.value) {
        router.push({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value.project_id.toString(),
            project_department_id: department.value.project_department_id.toString()
          }
        });
      } else if (project.value) {
        router.push({
          name: 'ProjectDetail',
          params: { project_id: project.value.project_id.toString() }
        });
      } else {
        router.push({ name: 'ListOfProjects' });
      }
    }
  };

  watch([project_id, project_department_id, chapter_id], () => {
    loadExecute();
  });

  const breadcrumbItems = computed<BreadcrumbI[]>(() => {
    return [
      {
        title: 'Projekty',
        disabled: false,
        href: router.resolve({ name: 'Projects' }).href
      },
      {
        title: project.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'ProjectDetail',
          params: { project_id: project.value?.project_id }
        }).href
      },
      {
        title: department.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id
          }
        }).href
      },
      {
        title: chapter.value?.chapter_title ?? '',
        disabled: false,
        href: router.resolve({
          name: 'ChapterDetail',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id,
            chapter_id: chapter.value?.chapter_id
          }
        }).href
      },
      {
        title: form.value?.form_name ?? '',
        disabled: false,
        href: router.resolve({
          name: nameOfRouteBeforeByFormType.value,
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id,
            chapter_id: chapter.value?.chapter_id,
            form_id: form.value?.form_id
          }
        }).href
      },
      {
        title: pageTitle.value ?? '',
        disabled: true,
        href: router.resolve({
          name: 'NewAnalyticalRequest',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id,
            chapter_id: chapter.value?.chapter_id,
            form_id: form.value?.form_id
          }
        }).href
      }
    ];
  });

  const goBack = () => {
    router.push({
      name: nameOfRouteBeforeByFormType.value,
      params: {
        project_id: project.value?.project_id,
        project_department_id: department.value?.project_department_id,
        chapter_id: chapter.value?.chapter_id,
        form_id: form.value?.form_id
      }
    });
  };
  const CreateSyntheticTemplateForm = ref();

  const isInProgress = ref(false);
  async function submitFormToValidate() {
    if (
      CreateSyntheticTemplateForm.value.isValid &&
      newGenerateAnalyticalRequestData.value &&
      !isInProgress.value
    ) {
      isInProgress.value = true;
      newGenerateAnalyticalRequestData.value.files_ids =
        newGenerateAnalyticalRequestData.value.files.map((f) => f.file_id);

      let res;
      if (requestType.value === 'NORMAL') {
        res = await formsStore.generateAnalyticalRequest(
          newGenerateAnalyticalRequestData.value,
          NewAnalyticalRequestType.NORMAL
        );
      } else if (requestType.value === 'STANDARD_RD') {
        res = await formsStore.generateAnalyticalRequest(
          newGenerateAnalyticalRequestData.value,
          NewAnalyticalRequestType.STANDARD_RD
        );
      } else if (requestType.value === 'STANDARD_QC') {
        res = await formsStore.generateAnalyticalRequest(
          newGenerateAnalyticalRequestData.value,
          NewAnalyticalRequestType.STANDARD_QC
        );
      }

      if (res) {
        router.push({
          name: nameOfRouteBeforeByFormType.value,
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id,
            chapter_id: chapter.value?.chapter_id,
            form_id: form.value?.form_id
          }
        });
      }
    }
    isInProgress.value = false;
  }

  watch(
    () => newGenerateAnalyticalRequestData.value?.files,
    () => {
      if (newGenerateAnalyticalRequestData.value) {
        newGenerateAnalyticalRequestData.value.files_ids =
          newGenerateAnalyticalRequestData.value.files.map((f) => f.file_id);
      }
    }
  );

  const nameOfRouteBeforeByFormType = computed(() => {
    if (form.value) {
      switch (form.value.form_type) {
        case FormType.EXPERIMENT:
          return 'Experiment';
        case FormType.MESSAGE:
          return 'Message';
        case FormType.INVESTIGATION:
          return 'Investigation';
        case FormType.ATTEMPT:
          return 'Attempt';
      }
    }
    return 'ListOfProjects';
  });

  const getAllFiles = async () => {
    const files = await formsStore.getAllFiles();
    if (newGenerateAnalyticalRequestData.value && files !== false) {
      newGenerateAnalyticalRequestData.value.files = files;
    }
  };
</script>
<template>
  <LoaderWrapper
    v-if="!project || !department || !chapter || !form || newGenerateAnalyticalRequestData === null"
  />
  <template v-else>
    <TopPageBreadcrumb :title="pageTitle" :_breadcrumbs="breadcrumbItems" />
    <v-row class="justify-content-end">
      <v-col cols="12">
        <UiParentCard class="pa-0" :loading="!baseDataLoaded">
          <template #action>
            <v-row justify="space-between" class="align-center">
              <v-col cols="12">
                <div class="d-flex gap-2 justify-end flex-wrap">
                  <v-btn
                    v-if="project?.project_id"
                    variant="flat"
                    color="error"
                    :disabled="!baseDataLoaded"
                    @click.prevent="goBack"
                  >
                    Zpět
                  </v-btn>
                  <v-btn
                    v-if="project?.project_id"
                    variant="flat"
                    color="primary"
                    :disabled="!baseDataLoaded"
                    type="submit"
                    form="form-edit-form"
                  >
                    Odeslat analytický požadavek
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </template>

          <v-form
            id="form-edit-form"
            ref="CreateSyntheticTemplateForm"
            @submit.prevent="submitFormToValidate"
          >
            <v-row>
              <v-col cols="12" md="6">
                <v-label class="mb-2">Šarže</v-label>
                <v-text-field
                  v-model="form.batch_number.batch_number"
                  single-line
                  hide-details="auto"
                  variant="outlined"
                  rounded="sm"
                  :readonly="true"
                ></v-text-field>
              </v-col>

              <v-col cols="12" md="6">
                <v-label class="mb-2">Název</v-label>
                <v-text-field
                  v-model="newGenerateAnalyticalRequestData.name"
                  :rules="itemRequiredRule"
                  single-line
                  placeholder="Zadejte název"
                  hide-details="auto"
                  variant="outlined"
                  rounded="sm"
                ></v-text-field>
              </v-col>

              <v-col cols="12">
                <v-label class="mb-2">Zvolte techniky</v-label>
                <ParametersFormTable
                  v-model:="newGenerateAnalyticalRequestData.samples"
                  :project_id="department.project_id"
                ></ParametersFormTable>
              </v-col>

              <v-col cols="12">
                <v-label class="mb-2">Přílohy</v-label>
                <FileUploader
                  v-model="newGenerateAnalyticalRequestData.files"
                  :uppy-options="{
                    restrictions: {
                      maxNumberOfFiles: 10,
                      minNumberOfFiles: 0,
                      allowedFileTypes: null
                    }
                  }"
                />
                <FileSection
                  :files="newGenerateAnalyticalRequestData.files"
                  :custom-remove-file="true"
                  @reload="getAllFiles"
                  @file-remove="
                    async (file_id: number) => {
                      const res = await filesStore.deleteFile(file_id);
                      if (res) {
                        getAllFiles();
                      }
                    }
                  "
                />
              </v-col>
            </v-row>
          </v-form>
        </UiParentCard>
      </v-col>

      <ConfirmDlg ref="ConfirmRef" />
    </v-row>
  </template>
</template>
