<template>
  <template v-for="(field, index) in parametersState" :key="index">
    <v-row>
      <v-col md="11" cols="9">
        <v-autocomplete
          v-model="field.technique_id"
          v-model:search="techniqueSearch"
          :clearable="true"
          hide-details
          :rules="itemRequiredRule"
          rounded="sm"
          :items="
            flattenedTechniques.map((technique) => {
              return {
                value: technique.analytical_technique_id,
                title: `${technique.name} (${technique.type})`,
                technique: technique
              };
            })
          "
          variant="outlined"
          color="primary"
          label="Vyberte techniku"
          single-line
          class="autocomplete"
          :no-data-text="'Žádná další políčka'"
          :slim="true"
          :loading="techniqueLoading"
        >
          <template #chip>
            <v-chip
              label
              variant="tonal"
              color="primary"
              size="large"
              class="my-1 text-subtitle-1 font-weight-regular"
            ></v-chip>
          </template>

          <template #item="{ props, item }">
            <v-list-item v-bind="props" :title="''">
              <div class="player-wrapper pa-2">
                <h6 class="text-subtitle-1 mb-0">
                  {{ item.raw.technique.parent_technique_id === null ? 'Hlavní' : 'Sub' }} -
                  {{ item.raw.technique.name }} ({{ item.raw.technique.shortcut }}) -
                  <v-chip
                    v-if="item.raw.technique.status === 'active'"
                    color="success"
                    size="small"
                    label
                  >
                    Aktivní
                  </v-chip>
                  <v-chip
                    v-if="item.raw.technique.status === 'inactive'"
                    color="warning"
                    size="small"
                    label
                  >
                    Neaktivní
                  </v-chip>
                  <v-chip
                    v-if="item.raw.technique.status === 'deleted'"
                    color="error"
                    size="small"
                    label
                  >
                    Odstraněno
                  </v-chip>
                </h6>
                <small class="text-h6 text-lightText">
                  Typ: {{ translateType(item.raw.technique.type) }} | Vytvořena:
                  {{ toLocale(item.raw.technique.created_at) }}
                </small>
              </div>
            </v-list-item>
          </template>
        </v-autocomplete>
      </v-col>
      <v-col v-if="parametersState.length > 1 && index >= 1" md="1" cols="3">
        <div class="d-flex justify-end align-center gap-2 h-100">
          <v-btn
            variant="flat"
            color="error"
            density="compact"
            icon="mdi-close"
            @click="parametersState.splice(index, 1)"
          />
        </div>
      </v-col>
      <v-col cols="12">
        <v-label class="mb-2">Poznámka k technice</v-label>
        <EditorTextarea
          v-model="field.notes"
          :show-edit-button="false"
          :config="
            {
              statusbar: true,
              min_height: 200
            } as EditorManager & RawEditorOptions
          "
        ></EditorTextarea>
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="12">
        <v-divider class="my-5"></v-divider>
      </v-col>
    </v-row>
  </template>
  <v-row>
    <v-col cols="12">
      <v-btn @click="addField">Přidat techniku</v-btn>
    </v-col>
  </v-row>
</template>

<script setup lang="ts">
  import EditorTextarea from '@/components/shared/EditorTextarea.vue';
  import { useProjectsStore } from '@/stores/projects';
  import {
    Technique,
    TechniqueType,
    useTechniquesStore,
    type TechniqueI
  } from '@/stores/techniques';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { toLocale } from '@/utils/locales';
  import { useVModel } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import { useDebounceFn } from '@vueuse/core';
  import type { EditorManager, RawEditorOptions } from 'tinymce';
  import { onMounted, ref, watch } from 'vue';
  export interface AnalyticalRequestParameterI {
    technique_id: number | undefined;
    notes: string | undefined;
  }

  const props = defineProps<{
    modelValue: AnalyticalRequestParameterI[];
    project_id: number;
  }>();

  const flattenedTechniques = ref<any[]>([]);
  const projectsStore = useProjectsStore();
  const parametersState = useVModel(props, 'modelValue');
  const techniquesStore = useTechniquesStore();
  const {
    techniques,
    loading: techniqueLoading,
    search: searchTechnique
  } = storeToRefs(techniquesStore);

  onMounted(async () => {
    if (parametersState.value.length === 0) {
      addField();
    }
    await techniquesStore.getAll(TechniqueType.ALL);
  });

  const addField = () => {
    parametersState.value.push({ technique_id: undefined, notes: undefined });
  };

  const techniqueSearch = ref('');

  watch(techniqueSearch, () => {
    debouncedProjectTechniqueSearch();
  });

  const debouncedProjectTechniqueSearch = useDebounceFn(async () => {
    if (techniqueLoading.value === false && techniqueSearch.value !== '') {
      searchTechnique.value = techniqueSearch.value;
      await techniquesStore.getAll(TechniqueType.ALL);
    }
  }, 350);

  watch(
    techniques,
    (newTechniques) => {
      flattenedTechniques.value = flattenTechniques([...newTechniques.values()]);
    },
    { immediate: true }
  );

  function flattenTechniques(techniquesArray: TechniqueI[]): TechniqueI[] {
    const result: TechniqueI[] = [];

    techniquesArray.forEach((technique) => {
      result.push(technique);
      if (technique.sub_analytical_techniques && technique.sub_analytical_techniques.length > 0) {
        result.push(...flattenTechniques(technique.sub_analytical_techniques));
      }
    });

    return result;
  }

  const translateType = (type: string): string => {
    const translations: { [key: string]: string } = {
      external: 'externí',
      internal: 'interní'
    };
    return translations[type] || type;
  };
</script>
