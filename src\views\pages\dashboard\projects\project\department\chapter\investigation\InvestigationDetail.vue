<script setup lang="ts">
  import type { BreadcrumbI } from '@/components/shared/BaseBreadcrumb.vue';
  import ChangelogPanel from '@/components/shared/ChangelogPanel.vue';
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import EditorTextarea from '@/components/shared/EditorTextareaWebSocket.vue';
  import FileSection from '@/components/shared/file/FileSection.vue';
  import FileUploader from '@/components/shared/file/FileUploader.vue';
  import TopPageBreadcrumb from '@/components/shared/TopPageBreadcrumb.vue';
  import UiParentCard from '@/components/shared/UiParentCard.vue';
  import AddPeopleToInvestigationModal from './components/AddPeopleToInvestigationModal.vue';
  import LoaderWrapper from '@/layouts/dashboard/LoaderWrapper.vue';
  import AnalyticalRequestsSection from '@/stores/analyticalRequests/components/AnalyticalRequestsSection.vue';
  import { Conclusion, ConclusionType } from '@/stores/experiments';
  import { useFormsStore } from '@/stores/forms';
  import {
    useInvestigationsStore,
    type InvestigationUpdateDataI,
    type SaveInvestigationAsTemplateDto
  } from '@/stores/investigations';
  import { useLogsStore } from '@/stores/logs';
  import { useProjectsStore } from '@/stores/projects';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { toLocale, toTimeLocale } from '@/utils/locales';
  import { useAuthStore } from '@/stores/auth';
  import { notification } from 'ant-design-vue';
  import { storeToRefs } from 'pinia';
  import type { EditorManager, RawEditorOptions } from 'tinymce';
  import { computed, onBeforeUnmount, onMounted, onUnmounted, ref, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import AddConclusionModal from '../experiment/components/AddConclusionModal.vue';
  import TemplateRename from '@/components/shared/NameNewTemplate.vue';
  import { isAllowed } from '@/utils/directive/isAllowed';
  import { setPageTitle } from '@/utils/title';

  // WebSocket imports
  import ActiveUsersIndicator from '@/components/shared/websocket/ActiveUsersIndicator.vue';
  import FieldLockWrapper from '@/components/shared/websocket/FieldLockWrapper.vue';
  import { useFormWebSocket } from '@/composables/useFormWebSocket';
  import { useRadioGroupWebSocket } from '@/composables/useRadioGroupWebSocket';

  const ConfirmRef = ref<undefined | typeof ConfirmDlg>(undefined);

  const route = useRoute();
  const router = useRouter();
  const logsStore = useLogsStore();
  const formsStore = useFormsStore();
  const projectsStore = useProjectsStore();
  const authStore = useAuthStore();
  const investigationsStore = useInvestigationsStore();

  const { project, department, chapter, project_permision } = storeToRefs(projectsStore);
  const { investigation } = storeToRefs(investigationsStore);
  const { loading, completeBatches } = storeToRefs(formsStore);
  const { user } = storeToRefs(authStore);

  const baseDataLoaded = ref(false);
  const isParentClosed = ref(false);

  // WebSocket setup
  const formId = computed(() => investigation.value?.form_id || null);
  const webSocket = useFormWebSocket({
    formId,
    autoConnect: true,
    enableNotifications: true
  });

  // Radio group WebSocket setup
  const impactOnQualityRadio = useRadioGroupWebSocket({
    fieldName: 'impact_on_quality',
    enabled: true,
    disabled: computed(() => isReadOnly.value || isParentClosed.value)
  });

  const impactOnYieldRadio = useRadioGroupWebSocket({
    fieldName: 'impact_on_yield',
    enabled: true,
    disabled: computed(() => isReadOnly.value || isParentClosed.value)
  });

  const checkAdminViewPermission = () => {
    return isAllowed(['view_all']);
  };

  onMounted(async () => {
    if (checkProjectTypePermisions() || checkAdminPermission() || checkAdminViewPermission()) {
      await showChangelog();
      await loadExecuteWithLocalStorageCheck();
    } else {
      notification.error({
        message: 'Chyba',
        description: 'Nemáte oprávnění pro prohlížení této stránky.'
      });
      router.push({ name: 'ListOfProjects' });
    }
    await checkParentClosure();
    if (checkAdminpermissionNew() && !isReadOnly.value && !isParentClosed.value) {
      saveEvery5Minutes();
    }
  });

  const havePermisionForChannelog = ref<boolean>(false);
  const showChangelog = async () => {
    if (isAllowed(['view_changelog_technological'])) {
      havePermisionForChannelog.value = true;
    } else {
      havePermisionForChannelog.value = false;
    }
  };
  const havePermision = ref<boolean>(true);
  const missingPermison = ref<string>();
  const checkPermision = () => {
    if (isAllowed(['view_changelog_technological'])) {
      havePermision.value = true;
      return true;
    } else {
      missingPermison.value = 'view_changelog_technological';
      havePermision.value = false;
    }
    if (!havePermision.value) {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nemáte oprávnění pro prohlížení changelogu: ' + missingPermison.value + '.'
      });
    }
    return false;
  };

  const showLogs = () => {
    if (checkPermision()) {
      if (investigation.value?.investigation_id) {
        const config = [
          {
            table_name: ['form', 'attempt'],
            table_primary_key: [investigation.value.investigation_id]
          },
          investigation.value?.conclusions.length
            ? {
                table_name: ['conclusion'],
                table_primary_key: investigation.value.conclusions.map((c) => c.conclusion_id)
              }
            : {},
          investigation.value?.files.length
            ? {
                table_name: ['file'],
                table_primary_key: investigation.value.files.map((file) => file.file_id)
              }
            : {}
        ];
        logsStore.setMultiColumnsAsActiveFilter(config);
      } else {
        notification.error({ message: 'Nepodařilo se načíst data pro zobrazení změn' });
      }

      logsStore.showDrawer = true;
    }
  };
  const chapterSearch = ref<string | undefined>(undefined);

  const project_id = computed(() => route.params.project_id as string);
  const projectId = computed(() => parseInt(project_id.value));

  const project_department_id = computed(() => route.params.project_department_id as string);
  const projectDepartmentId = computed(() => parseInt(project_department_id.value));

  const chapter_id = computed(() => route.params.chapter_id as string);
  const chapterId = computed(() => parseInt(chapter_id.value));

  const form_id = computed(() => route.params.form_id as string);
  const investigationId = computed(() => parseInt(form_id.value));

  const redirectToNewAnalyticalRequest = async () => {
    if (project.value && department.value && chapter.value && investigation.value) {
      const go = router.resolve({
        name: 'NewAnalyticalRequest',
        params: {
          project_id: project.value.project_id,
          project_department_id: department.value.project_department_id,
          chapter_id: chapter.value.chapter_id,
          form_id: investigation.value.form_id
        }
      });

      if (!isReadOnly.value) {
        if (
          isUpdateDataSameAsOriginal.value ||
          (await ConfirmRef.value?.open(
            'Potvrzení',
            'Opravdu chcete pokračovat bez uložení změn?',
            {
              color: 'error',
              notclosable: true,
              zIndex: 2400,
              onOk: () => {
                alert('ok');
                router.push(go.fullPath);
              }
            }
          ))
        ) {
          router.push(go.fullPath);
        }
      } else {
        router.push(go);
      }
    } else {
      notification.error({ message: 'Nepodařilo se přesměrovat na nový analytický požadavek' });
    }
  };
  const redirectToNewStadardRdAnalyticalRequest = async () => {
    if (project.value && department.value && chapter.value && investigation.value) {
      const go = router.resolve({
        name: 'NewAnalyticalRequest',
        params: {
          project_id: project.value.project_id,
          project_department_id: department.value.project_department_id,
          chapter_id: chapter.value.chapter_id,
          form_id: investigation.value.form_id
        },
        query: {
          requestType: 'STANDARD_RD'
        }
      });

      if (!isReadOnly.value) {
        if (
          isUpdateDataSameAsOriginal.value ||
          (await ConfirmRef.value?.open(
            'Potvrzení',
            'Opravdu chcete pokračovat bez uložení změn?',
            {
              color: 'error',
              notclosable: true,
              zIndex: 2400,
              onOk: () => {
                alert('ok');
                router.push(go.fullPath);
              }
            }
          ))
        ) {
          router.push(go.fullPath);
        }
      } else {
        router.push(go);
      }
    } else {
      notification.error({ message: 'Nepodařilo se přesměrovat na nový analytický požadavek' });
    }
  };

  const redirectToInsertAnalyticalRequest = async () => {
    if (project.value && department.value && chapter.value && investigation.value) {
      const go = router.resolve({
        name: 'InsertAnalyticalRequest',
        params: {
          project_id: project.value.project_id,
          project_department_id: department.value.project_department_id,
          chapter_id: chapter.value.chapter_id,
          form_id: investigation.value.form_id
        }
      });

      if (!isReadOnly.value) {
        if (
          isUpdateDataSameAsOriginal.value ||
          (await ConfirmRef.value?.open(
            'Potvrzení',
            'Opravdu chcete pokračovat bez uložení změn?',
            {
              color: 'error',
              notclosable: true,
              zIndex: 2400,
              onOk: () => {
                router.push(go.fullPath);
              }
            }
          ))
        ) {
          router.push(go.fullPath);
        }
      } else {
        router.push(go);
      }
    } else {
      notification.error({ message: 'Nepodařilo se přesměrovat na nový analytický požadavek' });
    }
  };

  const isUpdateDataSameAsOriginal = computed(() => {
    if (updateData.value && investigation.value) {
      return (
        updateData.value.form_name === investigation.value.form_name &&
        updateData.value.batch_description === investigation.value.batch_description &&
        updateData.value.affected_batch_number === investigation.value.affected_batch_number &&
        updateData.value?.impact_on_quality === investigation.value.impact_on_quality &&
        updateData.value?.impact_on_yield === investigation.value.impact_on_yield &&
        updateData.value?.problem_description === investigation.value.problem_description &&
        updateData.value?.investigation_in_production ===
          investigation.value.investigation_in_production &&
        updateData.value?.investigation_in_laboratory ===
          investigation.value.investigation_in_laboratory &&
        updateData.value?.recommendations === investigation.value.recommendations &&
        updateData.value?.batch_number === investigation.value.batch_number.batch_number &&
        updateData.value.conclusion_text ===
          investigation.value?.conclusions.find(
            (investigation) => investigation.type === ConclusionType.MAIN
          )?.conclusion_text
      );
    }

    return false;
  });

  const checkIfCurrentUserIsOwner = () => {
    if (isAllowed(['edit_all'])) {
      return true;
    }
    if (investigation.value?.owner === null || user.value === null) {
      return false;
    }
    return investigation.value?.owner?.user_id === user.value?.user_id;
  };

  const checkIfCurrentUserIsCollaborator = () => {
    if (isAllowed(['edit_all'])) {
      return true;
    }
    if (investigation.value?.collaborators === null && user.value === null) {
      return false;
    }
    return (
      investigation.value?.collaborators.some(
        (collaborator) => collaborator.user_id === user.value?.user_id
      ) || checkIfCurrentUserIsOwner()
    );
  };

  const loadExecute = async () => {
    baseDataLoaded.value = false;
    chapterSearch.value = undefined;

    if (projectId.value) {
      if (project.value) {
        if (project.value.project_id !== projectId.value) {
          await projectsStore.getProjectById(projectId.value);
        }
      } else {
        await projectsStore.getProjectById(projectId.value);
      }
    }

    if (projectDepartmentId.value) {
      if (department.value) {
        if (department.value.project_department_id !== projectDepartmentId.value) {
          await projectsStore.getDepartmentById(projectDepartmentId.value);
        }
      } else {
        await projectsStore.getDepartmentById(projectDepartmentId.value);
      }
    }

    if (chapterId.value) {
      if (chapter.value) {
        if (chapter.value.chapter_id !== chapterId.value) {
          await projectsStore.getChapterById(chapterId.value);
        }
      } else {
        await projectsStore.getChapterById(chapterId.value);
      }
    }

    if (investigationId.value) {
      await investigationsStore.getInvestigationById(investigationId.value);
      if (investigation.value?.batch_number.batch_number) {
        setPageTitle(investigation.value.batch_number?.batch_number);
      }
    }

    if (
      department.value &&
      project.value &&
      chapter.value &&
      investigation.value &&
      project.value.departments.find(
        (d) =>
          department.value && d.project_department_id === department.value.project_department_id
      ) &&
      department.value.chapters.find(
        (c) => chapter.value && c.chapter_id === chapter.value.chapter_id
      ) &&
      investigation.value.chapter_id === chapter.value.chapter_id
    ) {
      updateData.value = {
        affected_batch_number: investigation.value.affected_batch_number,
        form_name: investigation.value.form_name,
        batch_description: investigation.value.batch_description,
        impact_on_quality: investigation.value.impact_on_quality,
        impact_on_yield: investigation.value.impact_on_yield,
        problem_description: investigation.value.problem_description,
        recommendations: investigation.value.recommendations,
        investigation_in_production: investigation.value.investigation_in_production,
        investigation_in_laboratory: investigation.value.investigation_in_laboratory,
        batch_number: investigation.value.batch_number.batch_number,
        conclusion_text:
          investigation.value?.conclusions.find(
            (conclusion) => conclusion.type === ConclusionType.MAIN
          )?.conclusion_text ?? '',
        chemicals: []
      };

      formsStore.form_id = investigation.value.form_id;
      baseDataLoaded.value = true;
    } else {
      if (project.value && department.value && chapter.value) {
        router.push({
          name: 'ChapterDetail',
          params: {
            project_id: project.value.project_id.toString(),
            chapter_id: chapter.value.chapter_id.toString(),
            project_department_id: department.value.project_department_id.toString()
          }
        });
      } else if (project.value && department.value) {
        router.push({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value.project_id.toString(),
            project_department_id: department.value.project_department_id.toString()
          }
        });
      } else if (project.value) {
        router.push({
          name: 'ProjectDetail',
          params: { project_id: project.value.project_id.toString() }
        });
      } else {
        router.push({ name: 'ListOfProjects' });
      }
    }
  };

  const loadExecuteWithLocalStorageCheck = async () => {
    baseDataLoaded.value = false;
    chapterSearch.value = undefined;

    if (projectId.value) {
      if (project.value) {
        if (project.value.project_id !== projectId.value) {
          await projectsStore.getProjectById(projectId.value);
        }
      } else {
        await projectsStore.getProjectById(projectId.value);
      }
    }

    if (projectDepartmentId.value) {
      if (department.value) {
        if (department.value.project_department_id !== projectDepartmentId.value) {
          await projectsStore.getDepartmentById(projectDepartmentId.value);
        }
      } else {
        await projectsStore.getDepartmentById(projectDepartmentId.value);
      }
    }

    if (chapterId.value) {
      if (chapter.value) {
        if (chapter.value.chapter_id !== chapterId.value) {
          await projectsStore.getChapterById(chapterId.value);
        }
      } else {
        await projectsStore.getChapterById(chapterId.value);
      }
    }

    if (investigationId.value) {
      await investigationsStore.getInvestigationById(investigationId.value);
    }

    if (
      department.value &&
      project.value &&
      chapter.value &&
      investigation.value &&
      project.value.departments.find(
        (d) =>
          department.value && d.project_department_id === department.value.project_department_id
      ) &&
      department.value.chapters.find(
        (c) => chapter.value && c.chapter_id === chapter.value.chapter_id
      ) &&
      investigation.value.chapter_id === chapter.value.chapter_id
    ) {
      updateData.value = {
        affected_batch_number: investigation.value.affected_batch_number,
        form_name: investigation.value.form_name,
        batch_description: investigation.value.batch_description,
        impact_on_quality: investigation.value.impact_on_quality,
        impact_on_yield: investigation.value.impact_on_yield,
        problem_description: investigation.value.problem_description,
        recommendations: investigation.value.recommendations,
        investigation_in_production: investigation.value.investigation_in_production,
        investigation_in_laboratory: investigation.value.investigation_in_laboratory,
        batch_number: investigation.value.batch_number.batch_number,
        conclusion_text:
          investigation.value?.conclusions.find(
            (conclusion) => conclusion.type === ConclusionType.MAIN
          )?.conclusion_text ?? '',
        chemicals: []
      };

      formsStore.form_id = investigation.value.form_id;
    } else {
      if (project.value && department.value && chapter.value) {
        router.push({
          name: 'ChapterDetail',
          params: {
            project_id: project.value.project_id.toString(),
            chapter_id: chapter.value.chapter_id.toString(),
            project_department_id: department.value.project_department_id.toString()
          }
        });
      } else if (project.value && department.value) {
        router.push({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value.project_id.toString(),
            project_department_id: department.value.project_department_id.toString()
          }
        });
      } else if (project.value) {
        router.push({
          name: 'ProjectDetail',
          params: { project_id: project.value.project_id.toString() }
        });
      } else {
        router.push({ name: 'ListOfProjects' });
      }
    }
    //await checkLocalStorage();
    baseDataLoaded.value = true;

    // Set up radio group WebSocket handlers after data is loaded
    setupRadioGroupHandlers();
  };

  watch([project_id, project_department_id, chapter_id], () => {
    loadExecuteWithLocalStorageCheck();
  });

  const breadcrumbItems = computed<BreadcrumbI[]>(() => {
    return [
      {
        title: 'Projekty',
        disabled: false,
        href: router.resolve({ name: 'Projects' }).href
      },
      {
        title: project.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'ProjectDetail',
          params: { project_id: project.value?.project_id }
        }).href
      },
      {
        title: department.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id
          }
        }).href
      },
      {
        title: chapter.value?.chapter_title ?? '',
        disabled: false,
        href: router.resolve({
          name: 'ChapterDetail',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id,
            chapter_id: chapter.value?.chapter_id
          }
        }).href
      },
      {
        title: investigation.value?.form_name ?? '',
        disabled: true,
        href: router.resolve({
          name: 'Investigation',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id,
            chapter_id: chapter.value?.chapter_id,
            form_id: investigation.value?.form_id
          }
        }).href
      }
    ];
  });

  const updateData = ref<InvestigationUpdateDataI | undefined>(undefined);

  // Set up radio group WebSocket handlers after updateData is available
  const setupRadioGroupHandlers = () => {
    if (updateData.value) {
      // Set up watchers for remote updates
      impactOnQualityRadio.watchRemoteUpdates(
        computed({
          get: () => updateData.value?.impact_on_quality,
          set: (value) => {
            if (updateData.value) {
              updateData.value.impact_on_quality = value;
            }
          }
        })
      );

      impactOnYieldRadio.watchRemoteUpdates(
        computed({
          get: () => updateData.value?.impact_on_yield,
          set: (value) => {
            if (updateData.value) {
              updateData.value.impact_on_yield = value;
            }
          }
        })
      );
    }
  };

  const reloadPageWithoutSave = () => {
    // window reload current page
    // window.location.reload();
    loadExecute();
  };

  const redirectToChapterDetail = () => {
    if (project.value && department.value && chapter.value) {
      router.push({
        name: 'ChapterDetail',
        params: {
          project_id: project.value.project_id.toString(),
          project_department_id: department.value.project_department_id.toString(),
          chapter_id: chapter.value.chapter_id.toString()
        }
      });
    } else {
      router.push({ name: 'ListOfProjects' });
    }
  };

  const CreateSyntheticTemplateForm = ref();
  async function submitFormToValidate() {
    if (CreateSyntheticTemplateForm.value.isValid && updateData.value) {
      // Unlock all fields before submitting
      webSocket.webSocketStore.unlockAllMyFields();

      baseDataLoaded.value = false;
      const res = await investigationsStore.updateInvestigation(updateData.value);
      if (res) {
        clearLocalStorage();
        loadExecute();
      }
    }
    baseDataLoaded.value = true;
  }

  const exportFormAsPdf = async () => {
    const res = await formsStore.exportFormAsPdf(investigation.value?.form_name);
    if (res) {
      notification.success({ message: 'Šetření bylo úspěšně exportováno' });
    } else {
      notification.error({ message: 'Nepodařilo se exportovat šetření' });
    }
  };

  const exportFormAsWord = async () => {
    const res = await formsStore.exportFormAsWord(investigation.value?.form_name);
    if (res) {
      notification.success({ message: 'Šetření bylo úspěšně exportováno' });
    } else {
      notification.error({ message: 'Nepodařilo se exportovat šetření' });
    }
  };

  const scrollToHash = () => {
    const hash = window.location.hash;
    if (hash) {
      const element = document.querySelector(hash);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    }
  };

  // WebSocket-enhanced file operations
  const addFileToFormWithWebSocket = async (file_id: number) => {
    console.log('📁 Adding file with WebSocket notification (Investigation):', file_id);

    // Perform the actual file operation
    const result = await formsStore.addFileToForm(file_id);

    // Send WebSocket notification to other users
    if (webSocket.isConnected()) {
      console.log('📤 Sending WebSocket notification for files update (add):', file_id);
      webSocket.webSocketStore.sendRealTimeUpdate('files', investigation.value?.files || []);
    }

    return result;
  };

  const deleteFileFromFormWithWebSocket = async (file_id: number) => {
    console.log('🗑️ Deleting file with WebSocket notification (Investigation):', file_id);

    // Perform the actual file operation
    const result = await formsStore.deleteFileFromForm(file_id);

    // Send WebSocket notification to other users
    if (webSocket.isConnected()) {
      console.log('📤 Sending WebSocket notification for files update (delete):', file_id);
      webSocket.webSocketStore.sendRealTimeUpdate('files', investigation.value?.files || []);
    }

    return result;
  };

  const getAllFiles = async () => {
    const files = await formsStore.getAllFiles();
    if (investigation.value && files !== false) {
      investigation.value.files = files;
    }
  };

  // Watch for incoming WebSocket file updates from other users
  watch(
    () => webSocket.webSocketStore.fieldUpdates['files'],
    (newUpdate) => {
      if (
        newUpdate &&
        newUpdate.userId !== webSocket.webSocketStore.currentUserId &&
        investigation.value
      ) {
        console.log('📥 Received files update from another user (Investigation):', newUpdate.value);
        investigation.value.files = newUpdate.value || [];
      }
    },
    { deep: true }
  );

  onMounted(() => {
    window.addEventListener('hashchange', scrollToHash);
    scrollToHash();
  });

  onBeforeUnmount(() => {
    window.removeEventListener('hashchange', scrollToHash);
  });

  const other_conclusions = computed<Conclusion[]>(
    () =>
      investigation.value?.conclusions.filter(
        (conclusion) => conclusion.type === ConclusionType.ADDITIONAL
      ) ?? []
  );

  const isReadOnly = computed(() => {
    return investigation.value?.isReadOnly ?? true;
  });
  const showTemplateModal = ref(false);
  const toggleTemplateModal = () => {
    showTemplateModal.value = !showTemplateModal.value;
  };
  const saveExperimentAsTemplate = async (templateName: string) => {
    showTemplateModal.value = false;
    await investigationsStore.saveInvestigationAsTemplate({
      template_name: templateName
    } as SaveInvestigationAsTemplateDto);
  };
  const checkProjectTypePermisions = () => {
    return (
      isAllowed(['view_technological_department']) || isAllowed(['edit_technological_department'])
    );
  };

  const handleCancelClick = async () => {
    if (!isUpdateDataSameAsOriginal.value) {
      const confirmed =
        ConfirmRef.value &&
        (await ConfirmRef.value.open(
          'Potvrzení',
          'Opravdu chcete zrušit změny a načíst data ze serveru?',
          {
            color: 'error',
            notclosable: true,
            zIndex: 2400
          }
        ));
      if (confirmed) {
        reloadPageWithoutSave();
      }
    } else {
      redirectToChapterDetail();
    }
  };

  const combinedData = computed(() => ({
    ...updateData.value
  }));
  watch(
    combinedData,
    (newVal) => {
      if (!isReadOnly.value) {
        if (investigationId.value && baseDataLoaded.value) {
          const investigationFormStore = JSON.parse(
            localStorage.getItem('investigationFormStore') || '{}'
          );
          investigationFormStore[investigationId.value] = newVal;
          localStorage.setItem('investigationFormStore', JSON.stringify(investigationFormStore));
        }
      }
    },
    { deep: true }
  );

  const checkLocalStorage = async () => {
    if (investigationId.value) {
      const investigationFormStore = JSON.parse(
        localStorage.getItem('investigationFormStore') || '{}'
      );
      if (investigationFormStore[investigationId.value]) {
        updateData.value = {
          affected_batch_number:
            investigationFormStore[investigationId.value].affected_batch_number,
          form_name: investigationFormStore[investigationId.value].form_name,
          batch_description: investigationFormStore[investigationId.value].batch_description,
          impact_on_quality: investigationFormStore[investigationId.value].impact_on_quality,
          impact_on_yield: investigationFormStore[investigationId.value].impact_on_yield,
          problem_description: investigationFormStore[investigationId.value].problem_description,
          recommendations: investigationFormStore[investigationId.value].recommendations,
          investigation_in_production:
            investigationFormStore[investigationId.value].investigation_in_production,
          investigation_in_laboratory:
            investigationFormStore[investigationId.value].investigation_in_laboratory,
          batch_number: investigationFormStore[investigationId.value].batch_number,
          conclusion_text: investigationFormStore[investigationId.value].conclusion_text,
          chemicals: []
        };
      }
    }
  };
  const clearLocalStorage = async () => {
    const investigationFormStore = JSON.parse(
      localStorage.getItem('investigationFormStore') || '{}'
    );
    if (investigationId.value && investigationFormStore[investigationId.value]) {
      delete investigationFormStore[investigationId.value];
      localStorage.setItem('investigationFormStore', JSON.stringify(investigationFormStore));
    }
  };
  const haveOnlyViewPermision = () => {
    if (isAllowed(['edit_technological_department'])) {
      return false;
    } else if (isAllowed(['view_technological_department'])) {
      return true;
    }
  };
  const checkParentClosure = async () => {
    if (
      project.value?.status === 'deactivated' ||
      project.value?.status === 'closed' ||
      department.value?.status === 'closed' ||
      chapter.value?.status === 'closed' ||
      haveOnlyViewPermision() ||
      !checkIfCurrentUserIsCollaborator()
    ) {
      isParentClosed.value = true;
    } else {
      isParentClosed.value = false;
    }
  };

  const checkIfParentHaveAnalyticalDepartment = () => {
    if (project.value?.departments.find((d) => d.type === 'analytical')) {
      return true;
    }
    return false;
  };

  const haveOnlyEditPermision = () => {
    if (isAllowed(['edit_technological_department'])) {
      return true;
    }
  };

  const openPermissions = computed(() => {
    if (checkAdminpermissionNew()) {
      return true;
    } else if (
      checkIfCurrentUserIsCollaboratorWithoutEditAll() &&
      haveOnlyEditPermision() &&
      checkAdminOpenPermission()
    ) {
      return true;
    }
    return false;
  });

  const checkIfCurrentUserIsCollaboratorWithoutEditAll = () => {
    if (investigation.value?.collaborators === null && user.value === null) {
      return false;
    }
    return (
      investigation.value?.collaborators.some(
        (collaborator) => collaborator.user_id === user.value?.user_id
      ) || checkIfCurrentUserIsOwner()
    );
  };

  const askForReopen = async () => {
    if (investigation.value && investigation.value.form_id) {
      await formsStore.requestReopenForm(investigation.value.form_id);
    }
  };

  const reopenForm = async () => {
    if (checkAdminpermissionNew()) {
      if (investigationId.value) {
        const res = await investigationsStore.reactivateInvestigation(investigationId.value);
        if (res) {
          await loadExecute();
          await checkParentClosure();
        }
      }
    } else if (
      checkIfCurrentUserIsCollaboratorWithoutEditAll() &&
      haveOnlyEditPermision() &&
      checkAdminOpenPermission()
    ) {
      if (investigationId.value) {
        const res = await investigationsStore.reactivateInvestigation(investigationId.value);
        if (res) {
          await loadExecute();
          await checkParentClosure();
        }
      }
    } else {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nemáte oprávnění otevřít vyšetřování.'
      });
    }
  };

  const signAndCloseForm = async () => {
    if (checkAdminpermissionNew()) {
      const res = await formsStore.signAndCloseForm();
      if (res) {
        await clearLocalStorage();
        loadExecute();
      }
    } else if (
      checkIfCurrentUserIsCollaboratorWithoutEditAll() &&
      haveOnlyEditPermision() &&
      checkAdminSignPermission()
    ) {
      if (investigation.value) {
        const res = await formsStore.signAndCloseForm();
        if (res) {
          await clearLocalStorage();
          loadExecute();
        }
      }
    } else {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nemáte oprávnění uzavřít vyšetřování.'
      });
    }
  };

  const checkAdminSignPermission = () => {
    return isAllowed(['sign_experiments']);
  };

  const checkAdminOpenPermission = () => {
    return isAllowed(['reopen_experiments']);
  };

  const checkAdminPermission = () => {
    return false;
  };

  const checkAdminpermissionNew = () => {
    return isAllowed(['edit_all']);
  };

  let saveInterval: number;

  const saveEvery5Minutes = () => {
    saveInterval = setInterval(async () => {
      if (!isUpdateDataSameAsOriginal.value) {
        await submitFormToValidate();
      }
    }, 300000);
  };

  onUnmounted(() => {
    clearInterval(saveInterval);
  });
</script>
<template>
  <LoaderWrapper v-if="!project || !department || !chapter || !investigation" />
  <template v-else>
    <TopPageBreadcrumb :title="investigation.form_name" :_breadcrumbs="breadcrumbItems" />
    <v-row class="justify-content-end">
      <v-col cols="12">
        <UiParentCard class="pa-0" :loading="loading || !baseDataLoaded">
          <template #action>
            <v-row justify="space-between" class="align-center">
              <v-col cols="12" md="10">
                <div class="d-flex gap-2 justify-start flex-wrap">
                  <v-btn
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="!checkAdminPermission() && (!baseDataLoaded || isParentClosed)"
                    @click.prevent="redirectToInsertAnalyticalRequest"
                  >
                    Vložit analýzu
                  </v-btn>

                  <v-btn
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="!checkAdminPermission() && (!baseDataLoaded || isParentClosed)"
                    @click.prevent="exportFormAsPdf"
                  >
                    Exportovat do PDF
                  </v-btn>
                  <v-btn
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="!checkAdminPermission() && (!baseDataLoaded || isParentClosed)"
                    @click.prevent="exportFormAsWord"
                  >
                    Exportovat do Wordu
                  </v-btn>
                  <v-btn
                    v-if="!isReadOnly && investigation?.status !== 'canceled'"
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="
                      !checkAdminPermission() && (!baseDataLoaded || isReadOnly || isParentClosed)
                    "
                    @click.prevent="
                      async () => {
                        if (!isReadOnly) {
                          if (
                            await ConfirmRef?.open(
                              'Potvrzení',
                              'Opravdu chcete uložit změny a podepsat šetření ?',
                              {
                                color: 'error',
                                notclosable: true,
                                zIndex: 2400
                              }
                            )
                          ) {
                            signAndCloseForm();
                          }
                        } else {
                          reloadPageWithoutSave();
                        }
                      }
                    "
                  >
                    Podepsat
                  </v-btn>
                  <v-btn
                    v-if="
                      (investigation?.status === 'canceled' ||
                        investigation?.status === 'signed') &&
                      !openPermissions
                    "
                    size="small"
                    variant="flat"
                    color="primary"
                    @click.prevent="askForReopen"
                  >
                    Požádat o otevření
                  </v-btn>
                  <v-btn
                    v-else-if="
                      investigation?.status === 'canceled' || investigation?.status === 'signed'
                    "
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="!checkAdminPermission() && (!baseDataLoaded || isParentClosed)"
                    @click.prevent="reopenForm"
                  >
                    Otevřít šetření
                  </v-btn>

                  <v-btn
                    :disabled="
                      (!checkAdminPermission() && !havePermisionForChannelog) || !baseDataLoaded
                    "
                    variant="flat"
                    color="primary"
                    size="small"
                    @click.prevent="showLogs"
                  >
                    Změny
                  </v-btn>

                  <v-btn
                    v-if="checkIfParentHaveAnalyticalDepartment()"
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="!checkAdminPermission() && (!baseDataLoaded || isParentClosed)"
                    @click.prevent="redirectToNewAnalyticalRequest"
                  >
                    Generovat požadavek
                  </v-btn>

                  <v-btn
                    size="small"
                    variant="flat"
                    color="primary"
                    href="#fileSection"
                    :disabled="!checkAdminPermission() && (!baseDataLoaded || isParentClosed)"
                  >
                    Nahrát soubor
                  </v-btn>

                  <v-btn
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="
                      !checkAdminPermission() &&
                      (!baseDataLoaded || isParentClosed || !checkIfCurrentUserIsOwner())
                    "
                    @click.prevent="formsStore.showInviteModal = true"
                  >
                    Přizvat uživatele
                  </v-btn>

                  <v-btn
                    v-if="isReadOnly"
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="!checkAdminPermission() && (!baseDataLoaded || isParentClosed)"
                    @click.prevent="formsStore.showAddConclusionModal = true"
                  >
                    Přidat závěr
                  </v-btn>

                  <!--
                  <v-btn
                    size="small"
                    variant="flat"
                    color="primary"
                    @click.prevent="redirectToNewStadardRdAnalyticalRequest"
                  >
                    Prohlásit za standard
                  </v-btn>
                  -->
                </div>
              </v-col>
              <v-col cols="12" md="2">
                <div class="d-flex gap-2 justify-end flex-wrap">
                  <v-btn
                    size="small"
                    variant="flat"
                    color="error"
                    @click.prevent="handleCancelClick"
                  >
                    {{ isUpdateDataSameAsOriginal ? 'Zpět' : 'Zrušit' }}
                  </v-btn>
                  <v-btn
                    v-if="!isReadOnly"
                    size="small"
                    variant="flat"
                    color="primary"
                    type="submit"
                    form="investigation-edit-form"
                    :disabled="!checkAdminPermission() && (!baseDataLoaded || isParentClosed)"
                  >
                    Uložit
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </template>
          <v-row justify="space-between" class="align-center">
            <v-col cols="12" class="d-flex gap-2 justify-end flex-wrap v-card-subtitle">
              Poslední změna: {{ toTimeLocale(investigation.updated_at) }}
            </v-col>
          </v-row>
          <v-form
            v-if="updateData"
            id="investigation-edit-form"
            ref="CreateSyntheticTemplateForm"
            :readonly="!checkAdminPermission() && (isReadOnly || isParentClosed)"
            @submit.prevent="submitFormToValidate"
          >
            <!-- Active Users Indicator -->
            <v-row v-if="webSocket.isConnected()">
              <v-col cols="12" class="d-flex justify-end align-center">
                <ActiveUsersIndicator />
              </v-col>
            </v-row>

            <v-row>
              <v-col
                v-if="
                  investigation.owner &&
                  (investigation.owner.first_name || investigation.owner.last_name)
                "
                cols="12"
                md="6"
              >
                <v-label class="mb-2">Vlastník</v-label>
                <div>
                  <v-chip class="ma-1" color="primary" variant="tonal" size="small">
                    {{ investigation.owner.first_name }} {{ investigation.owner.last_name }}
                    <span v-if="investigation.owner.user_email" class="ml-2 text-caption">
                      ({{ investigation.owner.user_email }})
                    </span>
                  </v-chip>
                </div>
              </v-col>
              <v-col
                v-if="investigation.authors && investigation.authors.length > 0"
                cols="12"
                md="6"
              >
                <v-label class="mb-2">Spoluautoři</v-label>
                <div>
                  <v-chip
                    v-for="author in investigation.authors"
                    :key="author.user_id"
                    class="ma-1"
                    color="primary"
                    variant="tonal"
                    size="small"
                  >
                    {{ author.first_name }} {{ author.last_name }}
                    <span v-if="author.user_email" class="ml-2 text-caption">
                      ({{ author.user_email }})
                    </span>
                  </v-chip>
                </div>
              </v-col>
              <v-col cols="12">
                <v-label class="mb-2">Název</v-label>
                <FieldLockWrapper field-name="form_name" :disabled="isReadOnly || isParentClosed">
                  <v-text-field
                    v-model="updateData.form_name"
                    :rules="!isReadOnly && !isParentClosed ? itemRequiredRule : []"
                    single-line
                    placeholder="Zadejte název"
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></v-text-field>
                </FieldLockWrapper>
              </v-col>

              <v-col cols="12">
                <v-label class="mb-2">Číslo šarže</v-label>
                <v-text-field
                  v-model="updateData.batch_number"
                  single-line
                  hide-details="auto"
                  variant="outlined"
                  rounded="sm"
                  :readonly="true"
                ></v-text-field>
              </v-col>

              <v-col cols="12">
                <v-label class="mb-2">Číslo ovlivněné šarže</v-label>
                <FieldLockWrapper
                  field-name="affected_batch_number"
                  :disabled="isReadOnly || isParentClosed"
                >
                  <v-text-field
                    v-model="updateData.affected_batch_number"
                    single-line
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></v-text-field>
                </FieldLockWrapper>
              </v-col>

              <v-col cols="12" md="6">
                <v-label class="mb-2">Vliv na kvalitu</v-label>

                <v-radio-group
                  v-model="updateData.impact_on_quality"
                  single-line
                  hide-details="auto"
                  variant="outlined"
                  rounded="sm"
                  inline
                  @update:model-value="impactOnQualityRadio.onRadioChange"
                >
                  <v-radio label="Ano" :value="true"></v-radio>
                  <v-radio label="Ne" :value="false"></v-radio>
                </v-radio-group>
              </v-col>

              <v-col cols="12" md="6">
                <v-label class="mb-2">Vliv na výtěžek</v-label>

                <v-radio-group
                  v-model="updateData.impact_on_yield"
                  single-line
                  hide-details="auto"
                  variant="outlined"
                  rounded="sm"
                  inline
                  @update:model-value="impactOnYieldRadio.onRadioChange"
                >
                  <v-radio label="Ano" :value="true"></v-radio>
                  <v-radio label="Ne" :value="false"></v-radio>
                </v-radio-group>
              </v-col>

              <v-col cols="12">
                <v-label class="mb-2">Popis problému</v-label>

                <EditorTextarea
                  v-model="updateData.problem_description"
                  :show-edit-button="false"
                  :disabled="!checkAdminPermission() && (isReadOnly || isParentClosed)"
                  :config="
                    !checkAdminPermission() && (isReadOnly || isParentClosed)
                      ? ({ statusbar: true, resize: true } as EditorManager & RawEditorOptions)
                      : ({ statusbar: true, resize: true, min_height: 200 } as EditorManager &
                          RawEditorOptions)
                  "
                  :enable-web-socket="true"
                  field-name="problem_description"
                ></EditorTextarea>
              </v-col>

              <v-col cols="12">
                <v-label class="mb-2">Šetření ve výrobě</v-label>

                <EditorTextarea
                  v-model="updateData.investigation_in_production"
                  :show-edit-button="false"
                  :disabled="!checkAdminPermission() && (isReadOnly || isParentClosed)"
                  :config="
                    !checkAdminPermission() && (isReadOnly || isParentClosed)
                      ? ({ statusbar: true, resize: true } as EditorManager & RawEditorOptions)
                      : ({ statusbar: true, resize: true, min_height: 200 } as EditorManager &
                          RawEditorOptions)
                  "
                  :enable-web-socket="true"
                  field-name="investigation_in_production"
                ></EditorTextarea>
              </v-col>

              <v-col cols="12">
                <v-label class="mb-2">Šetření v laboratoři</v-label>

                <EditorTextarea
                  v-model="updateData.investigation_in_laboratory"
                  :show-edit-button="false"
                  :disabled="!checkAdminPermission() && (isReadOnly || isParentClosed)"
                  :config="
                    !checkAdminPermission() && (isReadOnly || isParentClosed)
                      ? ({ statusbar: true, resize: true } as EditorManager & RawEditorOptions)
                      : ({ statusbar: true, resize: true, min_height: 200 } as EditorManager &
                          RawEditorOptions)
                  "
                  :enable-web-socket="true"
                  field-name="investigation_in_laboratory"
                ></EditorTextarea>
              </v-col>

              <v-col cols="12">
                <v-label class="mb-2">Doporučení</v-label>
                <EditorTextarea
                  v-model="updateData.recommendations"
                  :show-edit-button="false"
                  :disabled="!checkAdminPermission() && (isReadOnly || isParentClosed)"
                  :config="
                    !checkAdminPermission() && (isReadOnly || isParentClosed)
                      ? ({ statusbar: true, resize: true } as EditorManager & RawEditorOptions)
                      : ({ statusbar: true, resize: true, min_height: 200 } as EditorManager &
                          RawEditorOptions)
                  "
                  :enable-web-socket="true"
                  field-name="recommendations"
                ></EditorTextarea>
              </v-col>

              <v-col cols="12">
                <v-label class="mb-2">Závěr</v-label>
                <EditorTextarea
                  v-model="updateData.conclusion_text"
                  :show-edit-button="false"
                  :disabled="!checkAdminPermission() && (isReadOnly || isParentClosed)"
                  :config="
                    !checkAdminPermission() && (isReadOnly || isParentClosed)
                      ? ({ statusbar: true, resize: true } as EditorManager & RawEditorOptions)
                      : ({ statusbar: true, resize: true, min_height: 200 } as EditorManager &
                          RawEditorOptions)
                  "
                  :enable-web-socket="true"
                  field-name="conclusion_text"
                  :table-primary-key="
                    investigation?.conclusions.find((c) => c.type === 'main')?.conclusion_id
                  "
                ></EditorTextarea>
              </v-col>

              <v-col v-if="other_conclusions.length > 0" cols="12">
                <v-label class="mb-2">Ostatní závěry</v-label>
              </v-col>
              <v-col v-for="(conclusion, index) in other_conclusions" :key="index" cols="12">
                <v-label class="mb-2">Vytvořeno: {{ toLocale(conclusion.created_at) }}</v-label>

                <EditorTextarea
                  v-model="conclusion.conclusion_text"
                  :show-edit-button="false"
                  :disabled="!checkAdminPermission() && (isReadOnly || isParentClosed)"
                  :config="
                    !checkAdminPermission() && (isReadOnly || isParentClosed)
                      ? ({ statusbar: true, resize: true } as EditorManager & RawEditorOptions)
                      : ({ statusbar: true, resize: true, min_height: 200 } as EditorManager &
                          RawEditorOptions)
                  "
                  :enable-web-socket="true"
                  field-name="conclusion_text"
                  :table-primary-key="conclusion.conclusion_id"
                ></EditorTextarea>
              </v-col>
            </v-row>

            <AnalyticalRequestsSection
              :batch-number="completeBatches || investigation.batch_number"
              @reload="loadExecute"
            />
            <section class="my-5">
              <span class="text-h4">Přílohy</span>
            </section>
            <v-row>
              <v-col v-if="!checkAdminPermission() && (isReadOnly || isParentClosed)" cols="12">
                <FileUploader
                  v-model="investigation.files"
                  :disabled="true"
                  :process-save-file="addFileToFormWithWebSocket"
                  :process-remove-file="deleteFileFromFormWithWebSocket"
                  :uppy-options="{
                    height: 250
                  }"
                />
              </v-col>
              <v-col v-else cols="12">
                <FileUploader
                  v-model="investigation.files"
                  :process-save-file="addFileToFormWithWebSocket"
                  :process-remove-file="deleteFileFromFormWithWebSocket"
                  :uppy-options="{
                    height: 250
                  }"
                />
              </v-col>
            </v-row>
            <FileSection
              :is-read-only="isReadOnly"
              :files="investigation.files"
              :custom-remove-file="true"
              @reload="getAllFiles"
              @file-remove="
                async (file_id: number) => {
                  const res = await deleteFileFromFormWithWebSocket(file_id);
                  if (res) {
                    getAllFiles();
                  }
                }
              "
            />
          </v-form>
        </UiParentCard>
      </v-col>

      <ConfirmDlg ref="ConfirmRef" />
      <ChangelogPanel v-if="logsStore.showDrawer" v-model:show="logsStore.showDrawer" />
      <AddPeopleToInvestigationModal
        v-if="formsStore.showInviteModal"
        v-model:show="formsStore.showInviteModal"
        @reload="loadExecute"
      />
      <AddConclusionModal
        v-if="formsStore.showAddConclusionModal"
        v-model:show="formsStore.showAddConclusionModal"
        @reload="loadExecute"
      />
      <TemplateRename
        v-if="baseDataLoaded"
        v-model:show="showTemplateModal"
        :experiment="investigation"
        @update-name="saveExperimentAsTemplate"
        @reload="loadExecute"
      />
    </v-row>
  </template>
</template>
<style scoped>
  :deep(.uppy-Dashboard .uppy-Dashboard-inner) {
    height: 250px !important;
    min-height: 250px !important;
  }
</style>
