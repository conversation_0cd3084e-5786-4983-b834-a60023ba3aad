<template>
  <v-card
    variant="outlined"
    :color="
      isParentClosed
        ? 'error'
        : investigation.isLocked
          ? 'warning'
          : investigation.isActive
            ? 'primary'
            : 'error'
    "
    :class="
      'card-hover-border bg-containerBg' + (investigation.isActive ? '' : ' strikethrough-tex')
    "
    :subtitle="`Štítky: ${investigation.tags && investigation.tags.length > 0 ? investigation.joinTags : '<PERSON>z <PERSON>'}`"
    :title="formattedTitle"
    :disabled="!checkProjectTypePermisions()"
    @click.prevent.stop="openInvestigation"
  >
    <template #prepend>
      <LockOutlined v-if="investigation.isLocked" :style="{ fontSize: '28px' }" />
      <ExperimentOutlined v-else :style="{ fontSize: '28px' }" />
      <div v-if="!isTabletOrSmaller">
        <div v-if="investigation.batch_number" class="absolute-center">
          <p class="v-card-title">{{ investigation.batch_number.batch_number }}</p>
        </div>
        <div v-else class="absolute-center">
          <p class="v-card-title">Šetření nemá číslo <PERSON></p>
        </div>
        <div class="absolute-center-2">
          <p>{{ formattedDescription }}</p>
        </div>
      </div>
    </template>
    <template #append>
      <v-menu>
        <template #activator="{ props }">
          <v-btn
            size="x-small"
            v-bind="props"
            variant="text"
            style="height: auto"
            @click.prevent.stop="props.isActive = true"
          >
            <EllipsisOutlined :style="{ fontSize: '28px' }" />
          </v-btn>
        </template>

        <v-list elevation="24" density="compact" class="py-0">
          <v-list-item v-if="!isParentClosed" :value="investigation.form_id + '_tags'">
            <v-list-item-title @click="handleClick(toggleTagsModal, 'tags')">
              Správa štítků
            </v-list-item-title>
          </v-list-item>
          <v-list-item v-if="!isParentClosed" :value="investigation.form_id + '_pdf'">
            <v-list-item-title @click="exportAsPdf()">Exportovat jako PDF</v-list-item-title>
          </v-list-item>
          <v-list-item v-if="!isParentClosed" :value="investigation.form_id + '_word'">
            <v-list-item-title @click="exportAsWord()">Exportovat jako Word</v-list-item-title>
          </v-list-item>
          <v-list-item
            v-if="investigation.isActive && !isParentClosed"
            :value="investigation.form_id + '_close'"
          >
            <v-list-item-title @click.prevent="handleClick(closeForm, 'close')">
              Uzavřít šetření
            </v-list-item-title>
          </v-list-item>
          <v-list-item
            v-if="!investigation.isActive && !isParentClosed && !openPermissions"
            :value="investigation.form_id + '_ask_reopen'"
          >
            <v-list-item-title @click.prevent="askForReopen()">
              Požádat o otevření
            </v-list-item-title>
          </v-list-item>
          <v-list-item
            v-if="!investigation.isActive && !isParentClosed && openPermissions"
            :value="investigation.form_id + '_reopen'"
          >
            <v-list-item-title @click.prevent="handleClick(reopenForm, 'open')">
              Otevřít šetření
            </v-list-item-title>
          </v-list-item>
          <v-list-item v-if="!isParentClosed" :value="investigation.form_id + '_move'">
            <v-list-item-title @click="openMoveModal">Přesunout do jiné kapitoly</v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>
    </template>
  </v-card>
  <ConfirmDlg ref="ConfirmRef" />
  <TagModal
    v-if="investigation.tagIsLoaded"
    v-model:show="showExperimentTagsModal"
    :tags="investigation.tagLists"
    @update-tags="handleUpdateTags"
  />
  <MoveToChapterModal
    v-model:show="showMoveModal"
    :chapters="chapters"
    :loading="moveLoading"
    @confirm="handleMoveToChapter"
  />
</template>
<script lang="ts" setup>
  import TagModal, { type TagListI } from '@/components/shared/TagModal.vue';
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import { useAuthStore } from '@/stores/auth';
  import { type Investigation, useInvestigationsStore } from '@/stores/investigations';
  import { useFormsStore } from '@/stores/forms';
  import { UserLockTableName, useUserLock } from '@/stores/userLock';
  import { EllipsisOutlined, ExperimentOutlined, LockOutlined } from '@ant-design/icons-vue';
  import { ref, onMounted, onUnmounted, computed } from 'vue';
  import { notification } from 'ant-design-vue';
  import { useRouter, useRoute } from 'vue-router';
  import { isAllowed } from '@/utils/directive/isAllowed';
  import { useProjectsStore } from '@/stores/projects';
  import { storeToRefs } from 'pinia';
  import MoveToChapterModal from '@/components/shared/MoveToChapterModal.vue';

  const ConfirmRef = ref<InstanceType<typeof ConfirmDlg> | null>(null);

  const emits = defineEmits(['reload']);
  const props = defineProps<{
    investigation: Investigation;
    isParentClosed: boolean;
  }>();

  const router = useRouter();
  const route = useRoute();
  const auth = useAuthStore();
  const userLock = useUserLock();
  const formsStore = useFormsStore();
  const investigationStore = useInvestigationsStore();
  const projectsStore = useProjectsStore();

  const { user } = storeToRefs(auth);
  const { project_permision } = storeToRefs(projectsStore);

  const project_department_id = computed(() => route.params.project_department_id as string);
  const projectDepartmentId = computed(() => parseInt(project_department_id.value));

  const exportAsPdf = async () => {
    formsStore.form_id = props.investigation.form_id;
    const res = await formsStore.exportFormAsPdf(props.investigation?.form_name);
  };
  const exportAsWord = async () => {
    formsStore.form_id = props.investigation.form_id;
    const res = await formsStore.exportFormAsWord(props.investigation?.form_name);
  };

  const redirectToInvestigationDetailLink = (id: number) => {
    return router.resolve({
      name: 'Investigation',
      params: { form_id: id.toString() }
    });
  };

  const checkAdminSignPermission = () => {
    return isAllowed(['sign_experiments']);
  };

  const checkAdminOpenPermission = () => {
    return isAllowed(['reopen_experiments']);
  };

  const openInvestigation = async () => {
    if (props.investigation.isLocked) {
      if (
        auth.user?.user_id &&
        props.investigation.edited_by?.user_id &&
        (await ConfirmRef.value?.open(
          'Šetření je uzamčeno',
          `Právě edituje uživatel ${props.investigation.edited_by?.user?.getName} (${props.investigation.edited_by?.user?.user_email}). Chcete pokračovat?`,
          {
            color: 'error',
            notclosable: true,
            zIndex: 2400
          }
        ))
      ) {
        await userLock.unlockRowForUser(
          props.investigation.edited_by.user_id,
          UserLockTableName.INVESTIGATION,
          props.investigation.form_id
        );
        await userLock.lockRowForUser(
          auth.user.user_id,
          UserLockTableName.INVESTIGATION,
          props.investigation.form_id
        );
        router.push(redirectToInvestigationDetailLink(props.investigation.form_id));
      }
    } else {
      router.push(redirectToInvestigationDetailLink(props.investigation.form_id));
    }
  };
  const checkProjectTypePermisions = () => {
    return (
      isAllowed(['view_technological_department']) || isAllowed(['edit_technological_department'])
    );
  };
  const isTabletOrSmaller = ref(false);

  const updateIsTabletOrSmaller = () => {
    isTabletOrSmaller.value = window.matchMedia('(max-width: 768px)').matches;
  };

  onMounted(() => {
    updateLenght();
    updateIsTabletOrSmaller();
    window.addEventListener('resize', updateIsTabletOrSmaller);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', updateIsTabletOrSmaller);
  });

  const showExperimentTagsModal = ref(false);

  const toggleTagsModal = () => {
    showExperimentTagsModal.value = !showExperimentTagsModal.value;
  };

  const handleUpdateTags = async (tags: TagListI[]) => {
    await investigationStore.updateTags(props.investigation, tags);
    emits('reload');
  };

  const closeForm = async () => {
    if (
      await ConfirmRef.value?.open('Opravdu chcete uzavřít šetření?', '', {
        color: 'error',
        notclosable: true,
        zIndex: 2400
      })
    ) {
      const res = await investigationStore.closeInvestigation(props.investigation.form_id, false);
      if (res) emits('reload');
    }
  };
  const reopenForm = async () => {
    await investigationStore.reactivateInvestigation(props.investigation.form_id);
    emits('reload');
  };

  const havePermision = ref<boolean>(true);
  const missingPermison = ref<string>();
  const checkProjectTypePermisionsForEdit = () => {
    if (isAllowed(['edit_technological_department']) || isAllowed(['edit_all'])) {
      havePermision.value = true;
      return true;
    } else {
      missingPermison.value = 'edit_technological_department';
      havePermision.value = false;
    }

    if (!havePermision.value) {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nemáte oprávnění pro editaci šetření: ' + missingPermison.value + '.'
      });
    }
    return false;
  };

  const checkAdminPermission = () => {
    return isAllowed(['edit_all']);
  };

  const checkIfCurrentUserIsOwner = () => {
    if (props.investigation?.owner === null || user.value === null) {
      return false;
    }
    return props.investigation?.owner?.user_id === user.value?.user_id;
  };

  const checkIfCurrentUserIsCollaborator = () => {
    if (props.investigation?.collaborators === null && user.value === null) {
      return false;
    }
    return (
      props.investigation?.collaborators.some(
        (collaborator) => collaborator.user_id === user.value?.user_id
      ) || checkIfCurrentUserIsOwner()
    );
  };
  const askForReopen = async () => {
    if (props.investigation.form_id) {
      await formsStore.requestReopenForm(props.investigation.form_id);
    }
  };

  const openPermissions = computed(() => {
    if (checkAdminPermission()) {
      return true;
    } else if (
      checkIfCurrentUserIsCollaborator() &&
      isAllowed(['edit_technological_department']) &&
      checkAdminOpenPermission()
    ) {
      return true;
    }
    return false;
  });

  const handleClick = (action: () => void, type: string): void => {
    if (checkAdminPermission()) {
      action();
      return;
    } else if (
      type === 'close' &&
      checkAdminSignPermission() &&
      checkIfCurrentUserIsCollaborator() &&
      isAllowed(['edit_technological_department'])
    ) {
      action();
      return;
    } else if (
      type === 'open' &&
      checkAdminOpenPermission() &&
      checkIfCurrentUserIsCollaborator() &&
      isAllowed(['edit_technological_department'])
    ) {
      action();
      return;
    } else if (type === 'close') {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nemáte oprávnění uzavřít šetření.'
      });
      return;
    } else if (type === 'open') {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nemáte oprávnění otevřít šetření.'
      });
      return;
    }
    if (checkProjectTypePermisionsForEdit()) {
      action();
    }
  };

  const showMoveModal = ref(false);
  const chapters = ref<any[]>([]);
  const moveLoading = ref(false);

  async function openMoveModal() {
    moveLoading.value = true;
    const department = await projectsStore.getDepartment(projectDepartmentId.value);
    chapters.value = department?.chapters?.filter((c: any) => c.chapter_id !== props.investigation.chapter_id) || [];
    moveLoading.value = false;
    showMoveModal.value = true;
  }

  async function handleMoveToChapter(chapter_id: number) {
    moveLoading.value = true;
    formsStore.form_id = props.investigation.form_id;
    const res = await formsStore.moveToDifferentChapter(chapter_id);
    moveLoading.value = false;
    if (res) emits('reload');
  }

  defineExpose({
    toggleTagsModal,
    handleUpdateTags,
    closeForm,
    reopenForm
  });

  const maxLegth = ref(40);

  const formattedTitle = computed(() => {
    if (!props.investigation.form_name) return '';
    return props.investigation.form_name.length > maxLegth.value
      ? `${props.investigation.form_name.slice(0, maxLegth.value)}...`
      : props.investigation.form_name;
  });

  const formattedDescription = computed(() => {
    if (!props.investigation.batch_description) return '';
    return props.investigation.batch_description.length > maxLegth.value
      ? `${props.investigation.batch_description.slice(0, maxLegth.value)}...`
      : props.investigation.batch_description;
  });
  const updateLenght = () => {
    if (window.innerWidth < 1465) {
      maxLegth.value = 20;
    } else {
      maxLegth.value = 40;
    }
  };
</script>
<style scoped lang="scss">
  .absolute-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }

  .absolute-center-2 {
    position: absolute;
    top: 75%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }
</style>
