<template>
  <section class="my-5">
    <span class="text-h4">Šet<PERSON><PERSON><PERSON></span>
  </section>
  <v-row v-if="investigations.length > 0">
    <v-col
      v-for="investigation in investigations
        .filter((chapter) =>
          investigationSearch
            ? chapter.form_name
                .toUpperCase()
                .includes(investigationSearch.toUpperCase().replace(/\s+/g, '')) ||
              chapter.joinTags
                .toUpperCase()
                .includes(investigationSearch.toUpperCase().replace(/\s+/g, ''))
            : true
        )
        .sort((a, b) => {
          const statusOrder = { created: 1, in_progress: 2, signed: 3, canceled: 4, completed: 5 };
          if (statusOrder[a.status] !== statusOrder[b.status]) {
            return statusOrder[a.status] - statusOrder[b.status];
          }
          return a.created_at > b.created_at ? -1 : 1;
        })"
      :key="investigation.form_id"
      cols="12"
    >
      <InvestigationItem
        :investigation="investigation"
        :is-parent-closed="isParentClosed"
        @reload="$emit('reload')"
      />
    </v-col>
  </v-row>
  <NotFoundItem v-else>
    <template #notFound>Nebyly nalezeny žádné šetření</template>
  </NotFoundItem>
</template>

<script lang="ts" setup>
  import NotFoundItem from '@/components/shared/NotFoundItem.vue';
  import InvestigationItem from './InvestigationItem.vue';
  import type { Investigation } from '@/stores/investigations';

  defineEmits(['reload']);
  defineProps<{
    investigations: Investigation[];
    investigationSearch: string | undefined;
    isParentClosed: boolean;
  }>();
</script>
