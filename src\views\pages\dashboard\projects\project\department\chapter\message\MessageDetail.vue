<script setup lang="ts">
  import type { EditorManager, RawEditorOptions } from 'tinymce';
  import type { BreadcrumbI } from '@/components/shared/BaseBreadcrumb.vue';
  import ChangelogPanel from '@/components/shared/ChangelogPanel.vue';
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import EditorTextarea from '@/components/shared/EditorTextareaWebSocket.vue';
  import FileSection from '@/components/shared/file/FileSection.vue';
  import FileUploader from '@/components/shared/file/FileUploader.vue';
  import TopPageBreadcrumb from '@/components/shared/TopPageBreadcrumb.vue';
  import UiParentCard from '@/components/shared/UiParentCard.vue';
  import LoaderWrapper from '@/layouts/dashboard/LoaderWrapper.vue';
  import AnalyticalRequestsSection from '@/stores/analyticalRequests/components/AnalyticalRequestsSection.vue';
  import AddPeopleToMessageModal from './components/AddPeopleToMessageModal.vue';
  import { ConclusionType } from '@/stores/experiments';
  import { useFormsStore } from '@/stores/forms';
  import { useLogsStore } from '@/stores/logs';
  import { useMessagesStore, type MessageUpdateDataI } from '@/stores/messages';
  import { useProjectsStore } from '@/stores/projects';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { useAuthStore } from '@/stores/auth';
  import { notification } from 'ant-design-vue';
  import { storeToRefs } from 'pinia';
  import { computed, onBeforeUnmount, onMounted, onUnmounted, ref, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import AddConclusionModal from '../experiment/components/AddConclusionModal.vue';
  import { toTimeLocale } from '@/utils/locales';
  import { isAllowed } from '@/utils/directive/isAllowed';
  import { setPageTitle } from '@/utils/title';

  // WebSocket imports
  import ActiveUsersIndicator from '@/components/shared/websocket/ActiveUsersIndicator.vue';
  import FieldLockWrapper from '@/components/shared/websocket/FieldLockWrapper.vue';
  import { useFormWebSocket } from '@/composables/useFormWebSocket';

  const ConfirmRef = ref<undefined | typeof ConfirmDlg>(undefined);

  const route = useRoute();
  const router = useRouter();
  const logsStore = useLogsStore();
  const formsStore = useFormsStore();
  const projectsStore = useProjectsStore();
  const authStore = useAuthStore();
  const messagesStore = useMessagesStore();

  const { project, department, chapter, project_permision } = storeToRefs(projectsStore);
  const { message } = storeToRefs(messagesStore);
  const { loading, completeBatches } = storeToRefs(formsStore);
  const { user } = storeToRefs(authStore);

  const baseDataLoaded = ref(false);
  const isParentClosed = ref(false);

  // WebSocket setup
  const formId = computed(() => message.value?.form_id || null);
  const webSocket = useFormWebSocket({
    formId,
    autoConnect: true,
    enableNotifications: true
  });

  const checkAdminPermission = () => {
    return false;
  };

  const checkAdminpermissionNew = () => {
    return isAllowed(['edit_all']);
  };

  const checkAdminViewPermission = () => {
    return isAllowed(['view_all']);
  };

  onMounted(async () => {
    if (checkProjectTypePermisions() || checkAdminPermission() || checkAdminViewPermission()) {
      await showChangelog();
      await loadExecuteWithLocalStorageCheck();
    } else {
      notification.error({
        message: 'Chyba',
        description: 'Nemáte oprávnění pro prohlížení této stránky.'
      });
      router.push({ name: 'ListOfProjects' });
    }
    await checkParentClosure();
    if (checkAdminpermissionNew() && !isReadOnly.value && !isParentClosed.value) {
      saveEvery5Minutes();
    }
  });

  const havePermisionForChannelog = ref<boolean>(false);
  const showChangelog = async () => {
    if (isAllowed(['view_changelog_technological'])) {
      havePermisionForChannelog.value = true;
    } else {
      havePermisionForChannelog.value = false;
    }
  };
  const havePermision = ref<boolean>(true);
  const missingPermison = ref<string>();
  const checkPermision = () => {
    if (isAllowed(['view_changelog_technological'])) {
      havePermision.value = true;
      return true;
    } else {
      missingPermison.value = 'view_changelog_technological';
      havePermision.value = false;
    }
    if (!havePermision.value) {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nemáte oprávnění pro prohlížení changelogu: ' + missingPermison.value + '.'
      });
    }
    return false;
  };

  const showLogs = () => {
    if (checkPermision()) {
      if (message.value?.message_id) {
        const config = [
          {
            table_name: ['form', 'attempt'],
            table_primary_key: [message.value.message_id]
          },
          message.value?.conclusions.length
            ? {
                table_name: ['conclusion'],
                table_primary_key: message.value.conclusions.map((c) => c.conclusion_id)
              }
            : {},
          message.value?.files.length
            ? {
                table_name: ['file'],
                table_primary_key: message.value.files.map((file) => file.file_id)
              }
            : {}
        ];
        logsStore.setMultiColumnsAsActiveFilter(config);
      } else {
        notification.error({ message: 'Nepodařilo se načíst data pro zobrazení změn' });
      }

      logsStore.showDrawer = true;
    }
  };
  const chapterSearch = ref<string | undefined>(undefined);

  const project_id = computed(() => route.params.project_id as string);
  const projectId = computed(() => parseInt(project_id.value));

  const project_department_id = computed(() => route.params.project_department_id as string);
  const projectDepartmentId = computed(() => parseInt(project_department_id.value));

  const chapter_id = computed(() => route.params.chapter_id as string);
  const chapterId = computed(() => parseInt(chapter_id.value));

  const form_id = computed(() => route.params.form_id as string);
  const messageId = computed(() => parseInt(form_id.value));

  const redirectToNewAnalyticalRequest = async () => {
    if (project.value && department.value && chapter.value && message.value) {
      const go = router.resolve({
        name: 'NewAnalyticalRequest',
        params: {
          project_id: project.value.project_id,
          project_department_id: department.value.project_department_id,
          chapter_id: chapter.value.chapter_id,
          form_id: message.value.form_id
        }
      });

      if (!isReadOnly.value) {
        if (
          isUpdateDataSameAsOriginal.value ||
          (await ConfirmRef.value?.open(
            'Potvrzení',
            'Opravdu chcete pokračovat bez uložení změn?',
            {
              color: 'error',
              notclosable: true,
              zIndex: 2400,
              onOk: () => {
                alert('ok');
                router.push(go.fullPath);
              }
            }
          ))
        ) {
          router.push(go.fullPath);
        }
      } else {
        router.push(go);
      }
    } else {
      notification.error({ message: 'Nepodařilo se přesměrovat na nový analytický požadavek' });
    }
  };
  const redirectToNewStadardRdAnalyticalRequest = async () => {
    if (project.value && department.value && chapter.value && message.value) {
      const go = router.resolve({
        name: 'NewAnalyticalRequest',
        params: {
          project_id: project.value.project_id,
          project_department_id: department.value.project_department_id,
          chapter_id: chapter.value.chapter_id,
          form_id: message.value.form_id
        },
        query: {
          requestType: 'STANDARD_RD'
        }
      });

      if (!isReadOnly.value) {
        if (
          isUpdateDataSameAsOriginal.value ||
          (await ConfirmRef.value?.open(
            'Potvrzení',
            'Opravdu chcete pokračovat bez uložení změn?',
            {
              color: 'error',
              notclosable: true,
              zIndex: 2400,
              onOk: () => {
                alert('ok');
                router.push(go.fullPath);
              }
            }
          ))
        ) {
          router.push(go.fullPath);
        }
      } else {
        router.push(go);
      }
    } else {
      notification.error({ message: 'Nepodařilo se přesměrovat na nový analytický požadavek' });
    }
  };

  const redirectToInsertAnalyticalRequest = async () => {
    if (project.value && department.value && chapter.value && message.value) {
      const go = router.resolve({
        name: 'InsertAnalyticalRequest',
        params: {
          project_id: project.value.project_id,
          project_department_id: department.value.project_department_id,
          chapter_id: chapter.value.chapter_id,
          form_id: message.value.form_id
        }
      });

      if (!isReadOnly.value) {
        if (
          isUpdateDataSameAsOriginal.value ||
          (await ConfirmRef.value?.open(
            'Potvrzení',
            'Opravdu chcete pokračovat bez uložení změn?',
            {
              color: 'error',
              notclosable: true,
              zIndex: 2400,
              onOk: () => {
                router.push(go.fullPath);
              }
            }
          ))
        ) {
          router.push(go.fullPath);
        }
      } else {
        router.push(go);
      }
    } else {
      notification.error({ message: 'Nepodařilo se přesměrovat na nový analytický požadavek' });
    }
  };

  const isUpdateDataSameAsOriginal = computed(() => {
    if (updateData.value && message.value) {
      return (
        updateData.value.form_name === message.value.form_name &&
        updateData.value.message_text === message.value.message_text &&
        updateData.value.batch_description === message.value.batch_description &&
        updateData.value.message_text === message.value.message_text
      );
    }

    return false;
  });

  const checkIfCurrentUserIsOwner = () => {
    if (isAllowed(['edit_all'])) {
      return true;
    }
    if (message.value?.owner === null || user.value === null) {
      return false;
    }
    return message.value?.owner?.user_id === user.value?.user_id;
  };

  const checkIfCurrentUserIsCollaborator = () => {
    if (isAllowed(['edit_all'])) {
      return true;
    }
    if (message.value?.collaborators === null && user.value === null) {
      return false;
    }
    return (
      message.value?.collaborators.some(
        (collaborator) => collaborator.user_id === user.value?.user_id
      ) || checkIfCurrentUserIsOwner()
    );
  };

  const loadExecute = async () => {
    baseDataLoaded.value = false;
    chapterSearch.value = undefined;

    if (projectId.value) {
      if (project.value) {
        if (project.value.project_id !== projectId.value) {
          await projectsStore.getProjectById(projectId.value);
        }
      } else {
        await projectsStore.getProjectById(projectId.value);
      }
    }

    if (projectDepartmentId.value) {
      if (department.value) {
        if (department.value.project_department_id !== projectDepartmentId.value) {
          await projectsStore.getDepartmentById(projectDepartmentId.value);
        }
      } else {
        await projectsStore.getDepartmentById(projectDepartmentId.value);
      }
    }

    if (chapterId.value) {
      if (chapter.value) {
        if (chapter.value.chapter_id !== chapterId.value) {
          await projectsStore.getChapterById(chapterId.value);
        }
      } else {
        await projectsStore.getChapterById(chapterId.value);
      }
    }

    if (messageId.value) {
      await messagesStore.getMessageById(messageId.value);
      if (message.value?.batch_number.batch_number) {
        setPageTitle(message.value.batch_number?.batch_number);
      }
    }

    if (
      department.value &&
      project.value &&
      chapter.value &&
      message.value &&
      project.value.departments.find(
        (d) =>
          department.value && d.project_department_id === department.value.project_department_id
      ) &&
      department.value.chapters.find(
        (c) => chapter.value && c.chapter_id === chapter.value.chapter_id
      ) &&
      message.value.chapter_id === chapter.value.chapter_id
    ) {
      updateData.value = {
        form_name: message.value.form_name,
        message_text: message.value.message_text,
        batch_description: message.value.batch_description,
        conclusion_text:
          message.value?.conclusions.find((conclusion) => conclusion.type === ConclusionType.MAIN)
            ?.conclusion_text ?? '',
        chemicals: []
      };

      formsStore.form_id = message.value.form_id;
    } else {
      if (project.value && department.value && chapter.value) {
        router.push({
          name: 'ChapterDetail',
          params: {
            project_id: project.value.project_id.toString(),
            chapter_id: chapter.value.chapter_id.toString(),
            project_department_id: department.value.project_department_id.toString()
          }
        });
      } else if (project.value && department.value) {
        router.push({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value.project_id.toString(),
            project_department_id: department.value.project_department_id.toString()
          }
        });
      } else if (project.value) {
        router.push({
          name: 'ProjectDetail',
          params: { project_id: project.value.project_id.toString() }
        });
      } else {
        router.push({ name: 'ListOfProjects' });
      }
    }
    baseDataLoaded.value = true;
  };

  const loadExecuteWithLocalStorageCheck = async () => {
    baseDataLoaded.value = false;
    chapterSearch.value = undefined;

    if (projectId.value) {
      if (project.value) {
        if (project.value.project_id !== projectId.value) {
          await projectsStore.getProjectById(projectId.value);
        }
      } else {
        await projectsStore.getProjectById(projectId.value);
      }
    }

    if (projectDepartmentId.value) {
      if (department.value) {
        if (department.value.project_department_id !== projectDepartmentId.value) {
          await projectsStore.getDepartmentById(projectDepartmentId.value);
        }
      } else {
        await projectsStore.getDepartmentById(projectDepartmentId.value);
      }
    }

    if (chapterId.value) {
      if (chapter.value) {
        if (chapter.value.chapter_id !== chapterId.value) {
          await projectsStore.getChapterById(chapterId.value);
        }
      } else {
        await projectsStore.getChapterById(chapterId.value);
      }
    }

    if (messageId.value) {
      await messagesStore.getMessageById(messageId.value);
    }

    if (
      department.value &&
      project.value &&
      chapter.value &&
      message.value &&
      project.value.departments.find(
        (d) =>
          department.value && d.project_department_id === department.value.project_department_id
      ) &&
      department.value.chapters.find(
        (c) => chapter.value && c.chapter_id === chapter.value.chapter_id
      ) &&
      message.value.chapter_id === chapter.value.chapter_id
    ) {
      updateData.value = {
        form_name: message.value.form_name,
        message_text: message.value.message_text,
        batch_description: message.value.batch_description,
        conclusion_text:
          message.value?.conclusions.find((conclusion) => conclusion.type === ConclusionType.MAIN)
            ?.conclusion_text ?? '',
        chemicals: []
      };

      formsStore.form_id = message.value.form_id;
    } else {
      if (project.value && department.value && chapter.value) {
        router.push({
          name: 'ChapterDetail',
          params: {
            project_id: project.value.project_id.toString(),
            chapter_id: chapter.value.chapter_id.toString(),
            project_department_id: department.value.project_department_id.toString()
          }
        });
      } else if (project.value && department.value) {
        router.push({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value.project_id.toString(),
            project_department_id: department.value.project_department_id.toString()
          }
        });
      } else if (project.value) {
        router.push({
          name: 'ProjectDetail',
          params: { project_id: project.value.project_id.toString() }
        });
      } else {
        router.push({ name: 'ListOfProjects' });
      }
    }
    //await checkLocalStorage();
    baseDataLoaded.value = true;
  };

  watch([project_id, project_department_id, chapter_id], () => {
    loadExecuteWithLocalStorageCheck();
  });

  const breadcrumbItems = computed<BreadcrumbI[]>(() => {
    return [
      {
        title: 'Projekty',
        disabled: false,
        href: router.resolve({ name: 'Projects' }).href
      },
      {
        title: project.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'ProjectDetail',
          params: { project_id: project.value?.project_id }
        }).href
      },
      {
        title: department.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id
          }
        }).href
      },
      {
        title: chapter.value?.chapter_title ?? '',
        disabled: false,
        href: router.resolve({
          name: 'ChapterDetail',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id,
            chapter_id: chapter.value?.chapter_id
          }
        }).href
      },
      {
        title: message.value?.form_name ?? '',
        disabled: true,
        href: router.resolve({
          name: 'Message',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id,
            chapter_id: chapter.value?.chapter_id,
            form_id: message.value?.form_id
          }
        }).href
      }
    ];
  });

  const updateData = ref<MessageUpdateDataI | undefined>(undefined);
  const reloadPageWithoutSave = () => {
    // window reload current page
    // window.location.reload();
    loadExecute();
  };

  const CreateSyntheticTemplateForm = ref();
  async function submitFormToValidate() {
    if (CreateSyntheticTemplateForm.value.isValid && updateData.value) {
      // Unlock all fields before submitting
      webSocket.webSocketStore.unlockAllMyFields();

      baseDataLoaded.value = false;
      const res = await messagesStore.updateMessage(updateData.value);
      if (res) {
        clearLocalStorage();
        loadExecute();
      }
    }
    baseDataLoaded.value = true;
  }

  const exportFormAsPdf = async () => {
    const res = await formsStore.exportFormAsPdf(message.value?.form_name);
    if (res) {
      notification.success({ message: 'Zpráva byl úspěšně exportován' });
    } else {
      notification.error({ message: 'Nepodařilo se exportovat zprávu' });
    }
  };

  const exportFormAsWord = async () => {
    const res = await formsStore.exportFormAsWord(message.value?.form_name);
    if (res) {
      notification.success({ message: 'Zpráva bylo úspěšně exportováno' });
    } else {
      notification.error({ message: 'Nepodařilo se exportovat zprávu' });
    }
  };

  const scrollToHash = () => {
    const hash = window.location.hash;
    if (hash) {
      const element = document.querySelector(hash);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    }
  };

  // WebSocket-enhanced file operations
  const addFileToFormWithWebSocket = async (file_id: number) => {
    console.log('📁 Adding file with WebSocket notification (Message):', file_id);

    // Perform the actual file operation
    const result = await formsStore.addFileToForm(file_id);

    // Send WebSocket notification to other users
    if (webSocket.isConnected()) {
      console.log('📤 Sending WebSocket notification for files update (add):', file_id);
      webSocket.webSocketStore.sendRealTimeUpdate('files', message.value?.files || []);
    }

    return result;
  };

  const deleteFileFromFormWithWebSocket = async (file_id: number) => {
    console.log('🗑️ Deleting file with WebSocket notification (Message):', file_id);

    // Perform the actual file operation
    const result = await formsStore.deleteFileFromForm(file_id);

    // Send WebSocket notification to other users
    if (webSocket.isConnected()) {
      console.log('📤 Sending WebSocket notification for files update (delete):', file_id);
      webSocket.webSocketStore.sendRealTimeUpdate('files', message.value?.files || []);
    }

    return result;
  };

  const getAllFiles = async () => {
    const files = await formsStore.getAllFiles();
    if (message.value && files !== false) {
      message.value.files = files;
    }
  };

  // Watch for incoming WebSocket file updates from other users
  watch(
    () => webSocket.webSocketStore.fieldUpdates['files'],
    (newUpdate) => {
      if (
        newUpdate &&
        newUpdate.userId !== webSocket.webSocketStore.currentUserId &&
        message.value
      ) {
        console.log('📥 Received files update from another user (Message):', newUpdate.value);
        message.value.files = newUpdate.value || [];
      }
    },
    { deep: true }
  );

  onMounted(() => {
    window.addEventListener('hashchange', scrollToHash);
    scrollToHash();
  });

  onBeforeUnmount(() => {
    window.removeEventListener('hashchange', scrollToHash);
  });

  const isReadOnly = computed(() => {
    return message.value?.isReadOnly ?? true;
  });

  const redirectToChapterDetail = () => {
    if (project.value && department.value && chapter.value) {
      router.push({
        name: 'ChapterDetail',
        params: {
          project_id: project.value.project_id.toString(),
          project_department_id: department.value.project_department_id.toString(),
          chapter_id: chapter.value.chapter_id.toString()
        }
      });
    } else {
      router.push({ name: 'ListOfProjects' });
    }
  };
  const checkProjectTypePermisions = () => {
    return (
      isAllowed(['view_technological_department']) || isAllowed(['edit_technological_department'])
    );
  };

  const handleCancelClick = async () => {
    if (!isUpdateDataSameAsOriginal.value) {
      const confirmed =
        ConfirmRef.value &&
        (await ConfirmRef.value.open(
          'Potvrzení',
          'Opravdu chcete zrušit změny a načíst data ze serveru?',
          {
            color: 'error',
            notclosable: true,
            zIndex: 2400
          }
        ));
      if (confirmed) {
        reloadPageWithoutSave();
      }
    } else {
      redirectToChapterDetail();
    }
  };
  const combinedData = computed(() => ({
    ...updateData.value
  }));
  watch(
    combinedData,
    (newVal) => {
      if (!isReadOnly.value) {
        if (messageId.value && baseDataLoaded.value) {
          const messageFormStore = JSON.parse(localStorage.getItem('messageFormStore') || '{}');
          messageFormStore[messageId.value] = newVal;
          localStorage.setItem('messageFormStore', JSON.stringify(messageFormStore));
        }
      }
    },
    { deep: true }
  );

  const clearLocalStorage = async () => {
    const messageFormStore = JSON.parse(localStorage.getItem('messageFormStore') || '{}');
    if (messageId.value && messageFormStore[messageId.value]) {
      delete messageFormStore[messageId.value];
      localStorage.setItem('messageFormStore', JSON.stringify(messageFormStore));
    }
  };
  const haveOnlyViewPermision = () => {
    if (isAllowed(['edit_technological_department'])) {
      return false;
    } else if (isAllowed(['view_technological_department'])) {
      return true;
    }
  };
  const checkParentClosure = async () => {
    if (
      project.value?.status === 'deactivated' ||
      project.value?.status === 'closed' ||
      department.value?.status === 'closed' ||
      chapter.value?.status === 'closed' ||
      haveOnlyViewPermision() ||
      !checkIfCurrentUserIsCollaborator()
    ) {
      isParentClosed.value = true;
    } else {
      isParentClosed.value = false;
    }
  };

  const checkIfParentHaveAnalyticalDepartment = () => {
    if (project.value?.departments.find((d) => d.type === 'analytical')) {
      return true;
    }
    return false;
  };

  const haveOnlyEditPermision = () => {
    if (isAllowed(['edit_technological_department'])) {
      return true;
    }
  };

  const openPermissions = computed(() => {
    if (checkAdminpermissionNew()) {
      return true;
    } else if (
      checkIfCurrentUserIsCollaboratorWithoutEditAll() &&
      haveOnlyEditPermision() &&
      checkAdminOpenPermission()
    ) {
      return true;
    }
    return false;
  });

  const checkIfCurrentUserIsCollaboratorWithoutEditAll = () => {
    if (message.value?.collaborators === null && user.value === null) {
      return false;
    }
    return (
      message.value?.collaborators.some(
        (collaborator) => collaborator.user_id === user.value?.user_id
      ) || checkIfCurrentUserIsOwner()
    );
  };

  const askForReopen = async () => {
    if (message.value && message.value.form_id) {
      await formsStore.requestReopenForm(message.value.form_id);
    }
  };

  const reopenForm = async () => {
    if (checkAdminpermissionNew()) {
      if (messageId.value) {
        const res = await messagesStore.reactivateMessage(messageId.value);
        if (res) {
          await loadExecute();
          await checkParentClosure();
        }
      }
    } else if (
      checkIfCurrentUserIsCollaboratorWithoutEditAll() &&
      haveOnlyEditPermision() &&
      checkAdminOpenPermission()
    ) {
      if (messageId.value) {
        const res = await messagesStore.reactivateMessage(messageId.value);
        if (res) {
          await loadExecute();
          await checkParentClosure();
        }
      }
    } else {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nemáte oprávnění otevřít zprávu.'
      });
    }
  };

  const signAndCloseForm = async () => {
    if (checkAdminpermissionNew()) {
      const res = await formsStore.signAndCloseForm();
      if (res) {
        await clearLocalStorage();
        loadExecute();
      }
    } else if (
      checkIfCurrentUserIsCollaboratorWithoutEditAll() &&
      haveOnlyEditPermision() &&
      checkAdminSignPermission()
    ) {
      if (message.value) {
        const res = await formsStore.signAndCloseForm();
        if (res) {
          await clearLocalStorage();
          loadExecute();
        }
      }
    } else {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nemáte oprávnění uzavřít zprávu.'
      });
    }
  };

  const checkAdminSignPermission = () => {
    return isAllowed(['sign_experiments']);
  };

  const checkAdminOpenPermission = () => {
    return isAllowed(['reopen_experiments']);
  };

  let saveInterval: number;

  const saveEvery5Minutes = () => {
    saveInterval = setInterval(async () => {
      if (!isUpdateDataSameAsOriginal.value) {
        await submitFormToValidate();
      }
    }, 300000);
  };

  onUnmounted(() => {
    clearInterval(saveInterval);
  });
</script>
<template>
  <LoaderWrapper v-if="!project || !department || !chapter || !message" />
  <template v-else>
    <TopPageBreadcrumb :title="message.form_name" :_breadcrumbs="breadcrumbItems" />
    <v-row class="justify-content-end">
      <v-col cols="12">
        <UiParentCard class="pa-0" :loading="loading || !baseDataLoaded">
          <template #action>
            <v-row justify="space-between" class="align-center">
              <v-col cols="12" md="10">
                <div class="d-flex gap-2 justify-start flex-wrap">
                  <v-btn
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="!checkAdminPermission() && (!baseDataLoaded || isParentClosed)"
                    @click.prevent="redirectToInsertAnalyticalRequest"
                  >
                    Vložit analýzu
                  </v-btn>

                  <v-btn
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="!checkAdminPermission() && (!baseDataLoaded || isParentClosed)"
                    @click.prevent="exportFormAsPdf"
                  >
                    Exportovat do PDF
                  </v-btn>
                  <v-btn
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="!checkAdminPermission() && (!baseDataLoaded || isParentClosed)"
                    @click.prevent="exportFormAsWord"
                  >
                    Exportovat do Wordu
                  </v-btn>
                  <v-btn
                    v-if="!isReadOnly && message?.status !== 'canceled'"
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="
                      !checkAdminPermission() && (!baseDataLoaded || isReadOnly || isParentClosed)
                    "
                    @click.prevent="
                      async () => {
                        if (!isReadOnly) {
                          if (
                            await ConfirmRef?.open(
                              'Potvrzení',
                              'Opravdu chcete uložit změny a podepsat zprávu ?',
                              {
                                color: 'error',
                                notclosable: true,
                                zIndex: 2400
                              }
                            )
                          ) {
                            signAndCloseForm();
                          }
                        } else {
                          reloadPageWithoutSave();
                        }
                      }
                    "
                  >
                    Podepsat
                  </v-btn>
                  <v-btn
                    v-if="
                      (message?.status === 'canceled' || message?.status === 'signed') &&
                      !openPermissions
                    "
                    size="small"
                    variant="flat"
                    color="primary"
                    @click.prevent="askForReopen"
                  >
                    Požádat o otevření
                  </v-btn>
                  <v-btn
                    v-else-if="message?.status === 'canceled' || message?.status === 'signed'"
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="!checkAdminPermission() && (!baseDataLoaded || isParentClosed)"
                    @click.prevent="reopenForm"
                  >
                    Otevřít zprávu
                  </v-btn>
                  <v-btn
                    :disabled="!havePermisionForChannelog || !baseDataLoaded"
                    variant="flat"
                    color="primary"
                    size="small"
                    @click.prevent="showLogs"
                  >
                    Změny
                  </v-btn>

                  <v-btn
                    v-if="checkIfParentHaveAnalyticalDepartment()"
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="!checkAdminPermission() && (!baseDataLoaded || isParentClosed)"
                    @click.prevent="redirectToNewAnalyticalRequest"
                  >
                    Generovat požadavek
                  </v-btn>

                  <v-btn
                    size="small"
                    variant="flat"
                    color="primary"
                    href="#fileSection"
                    :disabled="!baseDataLoaded || isParentClosed"
                  >
                    Nahrát soubor
                  </v-btn>

                  <v-btn
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="
                      !checkAdminPermission() &&
                      (!baseDataLoaded || isParentClosed || !checkIfCurrentUserIsOwner())
                    "
                    @click.prevent="formsStore.showInviteModal = true"
                  >
                    Přizvat uživatele
                  </v-btn>

                  <v-btn
                    v-if="isReadOnly"
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="!checkAdminPermission() && (!baseDataLoaded || isParentClosed)"
                    @click.prevent="formsStore.showAddConclusionModal = true"
                  >
                    Přidat závěr
                  </v-btn>
                  <!--
                  <v-btn
                    size="small"
                    variant="flat"
                    color="primary"
                    @click.prevent="redirectToNewStadardRdAnalyticalRequest"
                  >
                    Prohlásit za standard
                  </v-btn>
                 -->
                </div>
              </v-col>
              <v-col cols="12" md="2">
                <div class="d-flex gap-2 justify-end flex-wrap">
                  <v-btn
                    size="small"
                    variant="flat"
                    color="error"
                    @click.prevent="handleCancelClick"
                  >
                    {{ isUpdateDataSameAsOriginal ? 'Zpět' : 'Zrušit' }}
                  </v-btn>
                  <v-btn
                    v-if="!isReadOnly"
                    size="small"
                    variant="flat"
                    color="primary"
                    type="submit"
                    form="message-edit-form"
                    :disabled="!checkAdminPermission() && (!baseDataLoaded || isParentClosed)"
                  >
                    Uložit
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </template>
          <v-row justify="space-between" class="align-center">
            <v-col cols="12" class="d-flex gap-2 justify-end flex-wrap v-card-subtitle">
              Poslední změna: {{ toTimeLocale(message.updated_at) }}
            </v-col>
          </v-row>
          <v-form
            v-if="updateData"
            id="message-edit-form"
            ref="CreateSyntheticTemplateForm"
            :readonly="!checkAdminPermission() && (isReadOnly || isParentClosed)"
            @submit.prevent="submitFormToValidate"
          >
            <!-- Active Users Indicator -->
            <v-row v-if="webSocket.isConnected()">
              <v-col cols="12" class="d-flex justify-end align-center">
                <ActiveUsersIndicator />
              </v-col>
            </v-row>

            <v-row>
              <v-col
                v-if="message.owner && (message.owner.first_name || message.owner.last_name)"
                cols="12"
                md="6"
              >
                <v-label class="mb-2">Vlastník</v-label>
                <div>
                  <v-chip class="ma-1" color="primary" variant="tonal" size="small">
                    {{ message.owner.first_name }} {{ message.owner.last_name }}
                    <span v-if="message.owner.user_email" class="ml-2 text-caption">
                      ({{ message.owner.user_email }})
                    </span>
                  </v-chip>
                </div>
              </v-col>
              <v-col v-if="message.authors && message.authors.length > 0" cols="12" md="6">
                <v-label class="mb-2">Spoluautoři</v-label>
                <div>
                  <v-chip
                    v-for="author in message.authors"
                    :key="author.user_id"
                    class="ma-1"
                    color="primary"
                    variant="tonal"
                    size="small"
                  >
                    {{ author.first_name }} {{ author.last_name }}
                    <span v-if="author.user_email" class="ml-2 text-caption">
                      ({{ author.user_email }})
                    </span>
                  </v-chip>
                </div>
              </v-col>
              <v-col cols="12">
                <v-label class="mb-2">Název</v-label>
                <FieldLockWrapper
                  field-name="form_name"
                  :disabled="isReadOnly || isParentClosed"
                  :real-time-sync="true"
                  :show-typing-indicators="true"
                >
                  <v-text-field
                    v-model="updateData.form_name"
                    :rules="!isReadOnly && !isParentClosed ? itemRequiredRule : []"
                    single-line
                    placeholder="Zadejte název"
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></v-text-field>
                </FieldLockWrapper>
              </v-col>

              <v-col cols="12">
                <v-label class="mb-2">Šarže</v-label>
                <v-text-field
                  v-model="message.batch_number.batch_number"
                  single-line
                  hide-details="auto"
                  variant="outlined"
                  rounded="sm"
                  :readonly="true"
                ></v-text-field>
              </v-col>

              <v-col cols="12">
                <v-label class="mb-2">Textové pole</v-label>

                <EditorTextarea
                  v-model="updateData.message_text"
                  :show-edit-button="false"
                  :disabled="!checkAdminPermission() && (isReadOnly || isParentClosed)"
                  :config="
                    !checkAdminPermission() && (isReadOnly || isParentClosed)
                      ? ({ statusbar: true, resize: true } as EditorManager & RawEditorOptions)
                      : ({ statusbar: true, resize: true, min_height: 200 } as EditorManager &
                          RawEditorOptions)
                  "
                  :enable-web-socket="true"
                  field-name="message_text"
                ></EditorTextarea>
              </v-col>
            </v-row>

            <AnalyticalRequestsSection
              :batch-number="completeBatches || message.batch_number"
              @reload="loadExecute"
            />

            <section class="my-5">
              <span class="text-h4">Přílohy</span>
            </section>
            <v-row>
              <v-col v-if="!checkAdminPermission() && (isReadOnly || isParentClosed)" cols="12">
                <FileUploader
                  v-model="message.files"
                  :disabled="true"
                  :process-save-file="addFileToFormWithWebSocket"
                  :process-remove-file="deleteFileFromFormWithWebSocket"
                  :uppy-options="{
                    height: 250
                  }"
                />
              </v-col>
              <v-col v-else cols="12">
                <FileUploader
                  v-model="message.files"
                  :process-save-file="addFileToFormWithWebSocket"
                  :process-remove-file="deleteFileFromFormWithWebSocket"
                  :uppy-options="{
                    height: 250
                  }"
                />
              </v-col>
            </v-row>

            <FileSection
              :is-read-only="isReadOnly"
              :files="message.files"
              :custom-remove-file="true"
              @reload="getAllFiles"
              @file-remove="
                async (file_id: number) => {
                  const res = await deleteFileFromFormWithWebSocket(file_id);
                  if (res) {
                    getAllFiles();
                  }
                }
              "
            />
          </v-form>
        </UiParentCard>
      </v-col>

      <ConfirmDlg ref="ConfirmRef" />
      <ChangelogPanel v-if="logsStore.showDrawer" v-model:show="logsStore.showDrawer" />
      <AddPeopleToMessageModal
        v-if="formsStore.showInviteModal"
        v-model:show="formsStore.showInviteModal"
        @reload="loadExecute"
      />
      <AddConclusionModal
        v-if="formsStore.showAddConclusionModal"
        v-model:show="formsStore.showAddConclusionModal"
        @reload="loadExecute"
      />
    </v-row>
  </template>
</template>
<style scoped>
  :deep(.uppy-Dashboard .uppy-Dashboard-inner) {
    height: 250px !important;
    min-height: 250px !important;
  }
</style>
