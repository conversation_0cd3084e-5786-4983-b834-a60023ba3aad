<template>
  <v-dialog v-model="showState" class="customer-modal" :z-index="1010">
    <v-card>
      <v-form
        ref="CreateColumnForm"
        class="createColumnForm"
        @submit.prevent="submitFormToValidate"
      >
        <v-card-title class="pa-5">
          <span class="text-h5">Přizvat uživatele do zprávy</span>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col>
              <v-row>
                <v-col cols="12">
                  <v-label class="mb-2"></v-label>
                  <UsersSelect
                    v-model:selected-users="selectedUsers"
                    clearable
                    :what-to-show="['user_email']"
                  ></UsersSelect>
                </v-col>
              </v-row>
            </v-col>
          </v-row>

          <v-row
            v-if="message?.collaborators"
            style="max-height: 360px; overflow-y: auto"
            class="mt-4"
          >
            <v-col v-for="user in message.collaborators" :key="user.user_id" cols="12">
              <v-card
                variant="outlined"
                class="customListCard card-hover-border bg-containerBg"
                :subtitle="`Status: ${getStatusTitle(user.user.status) || 'Neznámý'}`"
                :title="`${getfullName(user.user.first_name, user.user.last_name)} - ${user.user.user_email}`"
                pa-2
                :data-user-id="user.user_id"
              >
                <template #append>
                  <v-menu>
                    <template #activator="{ props }">
                      <v-btn
                        size="x-small"
                        v-bind="props"
                        variant="text"
                        style="height: auto"
                        @click.prevent.stop="props.isActive = true"
                      >
                        <EllipsisOutlined :style="{ fontSize: '28px' }" />
                      </v-btn>
                    </template>
                    <v-list elevation="24" density="compact" class="py-0">
                      <v-list-item v-for="(item, index) in actionDD" :key="index" :value="index">
                        <v-list-item-title @click="item.action(user.user_id)">
                          {{ item.title }}
                        </v-list-item-title>
                      </v-list-item>

                      <slot name="customMenuItems" :user_id="user.user_id"></slot>
                    </v-list>
                  </v-menu>
                </template>
              </v-card>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" variant="text" @click="formsStore.showInviteModal = false">
            Zrušit
          </v-btn>
          <v-btn color="primary" variant="flat" type="submit">Přidat</v-btn>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
  <ConfirmDlg ref="_ConfirmRef" />
</template>

<script setup lang="ts">
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import { useMessagesStore } from '@/stores/messages';
  import { storeToRefs } from 'pinia';
  import UsersSelect from '@/components/shared/UserSelectForForm.vue';
  import { User } from '@/stores/auth';
  import { useFormsStore } from '@/stores/forms';
  import { useVModel } from '@vueuse/core';
  import { computed, ref } from 'vue';
  import { EllipsisOutlined } from '@ant-design/icons-vue';

  const emits = defineEmits(['reload', 'update:show']);

  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    }
  });

  const showState = useVModel(props, 'show');

  const messageStore = useMessagesStore();
  const formsStore = useFormsStore();

  const { message } = storeToRefs(messageStore);

  const selectedUsers = ref<number | User | number[] | User[] | undefined>(undefined);
  const CreateColumnForm = ref();

  const _ConfirmRef = ref<InstanceType<typeof ConfirmDlg> | null>(null);

  const user_id = computed(() => {
    const value = selectedUsers.value;

    if (Array.isArray(value)) {
      if (value.length > 0) {
        const firstItem = value[0];
        if (typeof firstItem === 'number') {
          return firstItem;
        } else if (typeof firstItem === 'object' && firstItem !== null && 'user_id' in firstItem) {
          return (firstItem as User).user_id;
        }
      }
    } else {
      if (typeof value === 'number') {
        return value;
      } else if (typeof value === 'object' && value !== null && 'user_id' in value) {
        return (value as User).user_id;
      }
    }

    return undefined;
  });

  async function submitFormToValidate() {
    if (CreateColumnForm.value.isValid && user_id.value) {
      const res = await formsStore.addUserToForm(user_id.value);
      if (res) {
        emits('reload');
      }
    }
  }

  const removeUserFromMessage = async (id: number) => {
    if (
      await _ConfirmRef.value?.open('Opravdu chcete odebrat uživatele?', '', {
        color: 'error',
        notclosable: true,
        zIndex: 2400
      })
    ) {
      const res = await formsStore.removeUserFromForm(id);
      if (res) {
        selectedUsers.value = undefined;
        emits('reload');
      }
    }
  };

  const getStatusTitle = (status: string) => {
    return status === 'active' ? 'Aktivní' : 'Neaktivní';
  };
  const getfullName = (first_name: string, last_name: string) => {
    return `${first_name.charAt(0).toUpperCase()}${first_name.slice(1)} ${last_name.charAt(0).toUpperCase()}${last_name.slice(1)}`;
  };

  const actionDD = ref([{ title: 'Odebrat uživatele', action: removeUserFromMessage }]);
</script>
