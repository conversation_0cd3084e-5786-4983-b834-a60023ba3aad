<template>
  <section class="my-5">
    <span class="text-h4">Zpr<PERSON>vy</span>
  </section>
  <v-row v-if="messages.length > 0">
    <v-col
      v-for="message in messages
        .filter((chapter) =>
          messageSearch
            ? chapter.form_name
                .toUpperCase()
                .includes(messageSearch.toUpperCase().replace(/\s+/g, '')) ||
              chapter.joinTags
                .toUpperCase()
                .includes(messageSearch.toUpperCase().replace(/\s+/g, ''))
            : true
        )
        .sort((a, b) => {
          const statusOrder = { created: 1, in_progress: 2, signed: 3, canceled: 4, completed: 5 };
          if (statusOrder[a.status] !== statusOrder[b.status]) {
            return statusOrder[a.status] - statusOrder[b.status];
          }
          return a.created_at > b.created_at ? -1 : 1;
        })"
      :key="message.form_id"
      cols="12"
    >
      <MessageItem
        :message="message"
        :is-parent-closed="isParentClosed"
        @reload="$emit('reload')"
      />
    </v-col>
  </v-row>
  <NotFoundItem v-else>
    <template #notFound>Nebyly nalezeny žádné zprávy</template>
  </NotFoundItem>
</template>

<script lang="ts" setup>
  import NotFoundItem from '@/components/shared/NotFoundItem.vue';
  import MessageItem from './MessageItem.vue';
  import type { Message } from '@/stores/messages';

  defineEmits(['reload']);
  defineProps<{
    messages: Message[];
    messageSearch: string | undefined;
    isParentClosed: boolean;
  }>();
</script>
