<template>
  <LoaderWrapper
    v-if="
      !methodGet || modalOptions?.updateData === undefined || modalOptions?.baseData === undefined
    "
  />
  <template v-else>
    <TopPageBreadcrumb title="Editace metody" :_breadcrumbs="breadcrumbItems" />
    <v-row class="justify-content-end">
      <v-col cols="12">
        <UiParentCard class="pa-0" :loading="loading || !baseDataLoaded">
          <template #action>
            <v-row justify="space-between" class="align-center">
              <v-col cols="12" md="9">
                <div class="d-flex gap-2 justify-start flex-wrap">
                  <v-btn
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="!baseDataLoaded"
                    @click.prevent="redirectToHistory"
                  >
                    Historie vývoje
                  </v-btn>
                  <v-btn
                    v-if="!isMethodClosed"
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="isMethodClosed || isParentClosed || !baseDataLoaded"
                    @click.prevent="closeMethod"
                  >
                    Uzavřít metodu
                  </v-btn>
                  <v-btn
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="!baseDataLoaded || isParentClosed"
                    @click.prevent="exportMethodAsPdf"
                  >
                    Exportovat vývoj do PDF
                  </v-btn>
                  <v-btn
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="!baseDataLoaded || isParentClosed"
                    @click.prevent="exportMethodAsWord"
                  >
                    Exportovat vývoj do Wordu
                  </v-btn>
                  <v-btn
                    v-if="isMethodClosed"
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="isParentClosed || !baseDataLoaded"
                    @click.prevent="openMethod"
                  >
                    Otevřít metodu
                  </v-btn>
                </div>
              </v-col>
              <v-col cols="12" md="3">
                <div class="d-flex gap-2 justify-end flex-wrap">
                  <v-btn
                    size="small"
                    variant="flat"
                    color="error"
                    @click.prevent="handleCancelClick"
                  >
                    {{ isUpdateDataSameAsOriginal ? 'Zpět' : 'Zrušit' }}
                  </v-btn>

                  <v-btn
                    size="small"
                    variant="flat"
                    color="primary"
                    :disabled="
                      isReadOnly || isParentClosed || !project_permision || !baseDataLoaded
                    "
                    type="submit"
                    form="experiment-edit-form"
                  >
                    Uložit
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </template>
          <v-form
            v-if="modalOptions && baseDataLoaded"
            id="experiment-edit-form"
            ref="MethodForm"
            class="MethodForm"
            :readonly="isReadOnly || isParentClosed || !project_permision"
            @submit.prevent="submitFormToValidate"
          >
            <v-row>
              <v-col>
                <v-col v-if="modalOptions.updateData" cols="12">
                  <v-label class="mb-2">Název analytické metody</v-label>
                  <v-text-field
                    v-model="modalOptions.updateData.method_name"
                    :rules="itemRequiredRule"
                    single-line
                    placeholder="Zadejte název metody"
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></v-text-field>
                </v-col>
                <v-col v-if="modalOptions.updateData" cols="12">
                  <v-label class="mb-2">Přístroj</v-label>
                  <v-autocomplete
                    v-model="modalOptions.updateData.instrument_id"
                    v-model:search="instrumentSearch"
                    hide-details
                    rounded="sm"
                    :rules="
                      isReadOnly || isParentClosed || !project_permision ? [] : itemRequiredRule
                    "
                    :items="
                      [...instruments.values()].map((instrument) => {
                        return {
                          value: instrument.instrument_id,
                          title: `${instrument.instrument_shortcut} (${instrument.instrument_id})`,
                          instrument: instrument
                        };
                      })
                    "
                    variant="outlined"
                    color="primary"
                    label="Vyberte přístroj"
                    single-line
                    class="autocomplete"
                    :no-data-text="'Žádná další políčka'"
                    :slim="true"
                    :loading="instrumentLoading"
                  >
                    <template #chip>
                      <v-chip
                        label
                        variant="tonal"
                        color="primary"
                        size="large"
                        class="my-1 text-subtitle-1 font-weight-regular"
                      ></v-chip>
                    </template>

                    <template #item="{ props, item }">
                      <v-list-item v-bind="props" :title="''">
                        <div class="player-wrapper pa-2">
                          <h6 class="text-subtitle-1 mb-0">
                            {{ item.raw.instrument.instrument_shortcut }} ({{
                              item.raw.instrument.instrument_id
                            }})
                          </h6>
                        </div>
                      </v-list-item>
                    </template>
                  </v-autocomplete>
                </v-col>

                <v-col v-if="modalOptions.updateData && methodGet.technique.using_column" cols="12">
                  <v-label class="mb-2">Kolona</v-label>
                  <v-autocomplete
                    v-model="modalOptions.updateData.kolona_id"
                    v-model:search="kolonaSearch"
                    hide-details
                    rounded="sm"
                    :readonly="
                      !methodGet?.technique?.using_column ||
                      isReadOnly ||
                      isParentClosed ||
                      !project_permision
                    "
                    :rules="
                      isReadOnly ||
                      isParentClosed ||
                      !project_permision ||
                      !methodGet?.technique?.using_column
                        ? []
                        : itemRequiredRule
                    "
                    :items="
                      [...kolons.values()].map((kolona) => {
                        return {
                          value: kolona.kolona_id,
                          title: `${kolona.name} (${kolona.manufacturer})`,
                          kolona: kolona
                        };
                      })
                    "
                    variant="outlined"
                    color="primary"
                    label="Vyberte kolonu"
                    single-line
                    class="autocomplete"
                    :no-data-text="'Žádná další políčka'"
                    :slim="true"
                    :loading="colonsLoading"
                  >
                    <template #chip>
                      <v-chip
                        label
                        variant="tonal"
                        color="primary"
                        size="large"
                        class="my-1 text-subtitle-1 font-weight-regular"
                      ></v-chip>
                    </template>

                    <template #item="{ props, item }">
                      <v-list-item v-bind="props" :title="''">
                        <div class="player-wrapper pa-2">
                          <h6 class="text-subtitle-1 mb-0">
                            {{ item.raw.kolona.name }} ({{ item.raw.kolona.manufacturer }})
                          </h6>
                        </div>
                      </v-list-item>
                    </template>
                  </v-autocomplete>
                </v-col>
                <v-col v-if="modalOptions.updateData" cols="12">
                  <v-label class="mb-2">Gradient</v-label>
                  <EditorTextarea
                    v-model="modalOptions.updateData.gradient"
                    :disabled="isReadOnly || isParentClosed || !project_permision"
                    :show-edit-button="false"
                    :config="
                      isReadOnly || isParentClosed || !project_permision
                        ? ({ statusbar: true, resize: true } as EditorManager & RawEditorOptions)
                        : ({ statusbar: true, resize: true, min_height: 200 } as EditorManager &
                            RawEditorOptions)
                    "
                  ></EditorTextarea>
                </v-col>
                <v-col v-if="modalOptions.updateData" cols="12">
                  <v-label class="mb-2">Sekvence</v-label>
                  <EditorTextarea
                    v-model="modalOptions.updateData.sequence_name"
                    :disabled="isReadOnly || isParentClosed || !project_permision"
                    :show-edit-button="false"
                    :config="
                      isReadOnly || isParentClosed || !project_permision
                        ? ({ statusbar: true, resize: true } as EditorManager & RawEditorOptions)
                        : ({ statusbar: true, resize: true, min_height: 200 } as EditorManager &
                            RawEditorOptions)
                    "
                  ></EditorTextarea>
                </v-col>
                <v-col v-if="modalOptions.updateData" cols="12">
                  <v-label class="mb-2">Parametry</v-label>
                  <ParametersFormTable
                    v-model:="modalOptions.updateData.parameters"
                    :disabled="isReadOnly || isParentClosed || !project_permision"
                  ></ParametersFormTable>
                </v-col>
                <v-col v-if="modalOptions.updateData" cols="12">
                  <v-label class="mb-2">Příprava standardu a vzorku</v-label>
                  <EditorTextarea
                    v-model="modalOptions.updateData.preparation_of_standard_and_sample"
                    :disabled="isReadOnly || isParentClosed || !project_permision"
                    :show-edit-button="false"
                    :config="
                      isReadOnly || isParentClosed || !project_permision
                        ? ({ statusbar: true, resize: true } as EditorManager & RawEditorOptions)
                        : ({ statusbar: true, resize: true, min_height: 200 } as EditorManager &
                            RawEditorOptions)
                    "
                  ></EditorTextarea>
                </v-col>
                <v-col v-if="modalOptions.updateData" cols="12">
                  <v-label class="mb-2">Poznámka</v-label>
                  <EditorTextarea
                    v-model="modalOptions.updateData.note"
                    :disabled="isReadOnly || isParentClosed || !project_permision"
                    :show-edit-button="false"
                    :config="
                      {
                        statusbar: true,
                        resize: true,
                        height: 150
                      } as EditorManager & RawEditorOptions
                    "
                  ></EditorTextarea>
                </v-col>
                <v-col v-if="modalOptions.updateData" cols="12">
                  <v-label class="mb-2">Výsledek</v-label>
                  <EditorTextarea
                    v-model="modalOptions.updateData.result"
                    :disabled="isReadOnly || isParentClosed || !project_permision"
                    :show-edit-button="false"
                    :config="
                      isReadOnly || isParentClosed || !project_permision
                        ? ({ statusbar: true, resize: true } as EditorManager & RawEditorOptions)
                        : ({ statusbar: true, resize: true, min_height: 200 } as EditorManager &
                            RawEditorOptions)
                    "
                  ></EditorTextarea>
                </v-col>
                <v-col cols="12">
                  <v-label class="mb-2">Přílohy</v-label>
                  <template v-if="!isAllowed(['edit_all']) && (isReadOnly || isParentClosed)">
                    <FileUploader
                      v-model="methodGet.files"
                      :disabled="true"
                      :process-save-file="methodsStore.addFileToMethod"
                      :process-remove-file="methodsStore.deleteFileFromMethod"
                      :uppy-options="{
                        height: 250
                      }"
                    />
                  </template>
                  <template v-else>
                    <FileUploader
                      v-model="methodGet.files"
                      :process-save-file="methodsStore.addFileToMethod"
                      :process-remove-file="methodsStore.deleteFileFromMethod"
                      :uppy-options="{
                        height: 250
                      }"
                    />
                  </template>
                </v-col>
              </v-col>
            </v-row>
            <v-col cols="12">
              <FileSection
                :is-read-only="isReadOnly"
                :files="methodGet.files"
                :custom-remove-file="true"
                @reload="getAllFiles"
                @file-remove="
                  async (file_id: number) => {
                    const res = await methodsStore.deleteFileFromMethod(file_id);
                    if (res) {
                      getAllFiles();
                    }
                  }
                "
              />
            </v-col>
          </v-form>
        </UiParentCard>
      </v-col>
    </v-row>
    <ConfirmDlg ref="ConfirmRef" />
    <MethodSaveModal
      v-if="baseDataLoaded && showCreateMethodModal"
      v-model:show="showCreateMethodModal"
      @reload="loadExecute"
      @update-method="updateMethod"
    />
  </template>
</template>
<script setup lang="ts">
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import EditorTextarea from '@/components/shared/EditorTextarea.vue';
  import type { BreadcrumbI } from '@/components/shared/BaseBreadcrumb.vue';
  import { useInstrumentsStore } from '@/stores/instruments';
  import { Technique, useTechniquesStore } from '@/stores/techniques';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { useDebounceFn } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import UiParentCard from '@/components/shared/UiParentCard.vue';
  import type { EditorManager, RawEditorOptions } from 'tinymce';
  import { computed, ref, watch, onMounted } from 'vue';
  import { Method, useMethodsStore, MethodStatus, MethodSaveType } from '@/stores/method/methods';
  import ParametersFormTable from './components/ParametersFormTable.vue';
  import { useRoute, useRouter } from 'vue-router';
  import { useAnalyticalDepartmentStore } from '@/stores/analyticalDepartment';
  import { useProjectsStore } from '@/stores/projects';
  import { notification } from 'ant-design-vue';
  import LoaderWrapper from '@/layouts/dashboard/LoaderWrapper.vue';
  import TopPageBreadcrumb from '@/components/shared/TopPageBreadcrumb.vue';
  import { useColumnsStore } from '@/stores/columns';
  import FileUploader from '@/components/shared/file/FileUploader.vue';
  import FileSection from '@/components/shared/file/FileSection.vue';
  import MethodSaveModal from './components/MethodSaveModal.vue';
  import { isAllowed } from '@/utils/directive/isAllowed';
  import { setPageTitle } from '@/utils/title';
  const methodGetKey = ref(0);

  const ConfirmRef = ref<undefined | typeof ConfirmDlg>(undefined);
  const baseDataLoaded = ref(false);
  const projectsStore = useProjectsStore();
  const techniquesStore = useTechniquesStore();
  const analyticalDepartmentStore = useAnalyticalDepartmentStore();
  const technique = ref<Technique | undefined>(undefined);
  const methods = ref<Method[] | undefined>([]);
  const { project, department, chapter, project_permision } = storeToRefs(projectsStore);
  const route = useRoute();
  const router = useRouter();
  const instrumentsStore = useInstrumentsStore();
  const {
    items: instruments,
    loading: instrumentLoading,
    search: searchInstrument,
    options: instrumentsOptions,
    fixedFilterOptions: instrumentOptions
  } = storeToRefs(instrumentsStore);

  const methodsStore = useMethodsStore();
  const { modalOptions, loading, methodGet } = storeToRefs(methodsStore);

  const columnsStore = useColumnsStore();
  const {
    options,
    loading: colonsLoading,
    items: kolons,
    columns: kolonsColumns,
    search: searchKolumn,
    fixedFilterOptions: optionsColumns
  } = storeToRefs(columnsStore);

  const MethodForm = ref();

  async function submitFormToValidate() {
    if (MethodForm.value.isValid && modalOptions.value?.updateData && methodGet.value) {
      //modalOptions.value.updateData.files_ids = [...modalOptions.value.updateData.files_ids];
      await methodsStore.getMethodVersions(
        methodGet.value.method_id,
        methodGet.value.current_version.version_id
      );
      await toggleModal();
    }
  }

  onMounted(async () => {
    baseDataLoaded.value = false;
    await checkReloadPermisions();
    baseDataLoaded.value = true;
  });

  const hasEditPermission = ref(false);
  const hasViewPermission = ref(false);

  const checkAnalyticalPermissions = () => {
    // First check admin permissions (edit_all or view_all)
    if (isAllowed(['edit_all'])) {
      hasEditPermission.value = true;
      hasViewPermission.value = true;
      return true;
    }

    if (isAllowed(['view_all'])) {
      hasEditPermission.value = false;
      hasViewPermission.value = true;
      return true;
    }

    // If user has general analytical department permissions, they get access
    if (isAllowed(['view_analytical_department']) || isAllowed(['edit_analytical_department'])) {
      hasEditPermission.value = isAllowed(['edit_analytical_department']);
      hasViewPermission.value =
        isAllowed(['view_analytical_department']) || hasEditPermission.value;
      return true;
    }

    // If user doesn't have general analytical permissions, check specific permission
    if (isAllowed(['access_analytical_methods'])) {
      hasEditPermission.value = true;
      hasViewPermission.value = true;
      return true;
    }

    return false;
  };

  const checkReloadPermisions = async () => {
    if (!checkAnalyticalPermissions()) {
      notification.error({
        message: 'Chyba',
        description:
          'Nemáte oprávnění pro zobrazení této stránky. Chybí oprávnění: view_analytical_department, edit_analytical_department nebo access_analytical_methods'
      });
      router.push({ name: 'ListOfProjects' });
      return;
    }

    await loadExecute();
  };

  const isReadOnly = computed(() => {
    return !hasEditPermission.value || !methodGet.value?.isActive;
  });

  const instrumentSearch = ref('');

  watch(instrumentSearch, () => {
    debouncedProjectInstrumentSearch();
  });

  const debouncedProjectInstrumentSearch = useDebounceFn(() => {
    if (instrumentLoading.value === false && instrumentSearch.value !== '') {
      searchInstrument.value = instrumentSearch.value;
      if (analyticalTechniqueUd.value) {
        instrumentOptions.value = [
          {
            column: 'status',
            value: MethodStatus.ACTIVE
          },
          {
            column: 'technique_id',
            value: analyticalTechniqueUd.value
          }
        ];
        instrumentsStore.getInstruments({
          search: '',
          totalItems: 0,
          options: { page: 1, rowsPerPage: 25, sortBy: [] },
          fixedFilterOptions: instrumentOptions.value,
          search_columns: ['instrument_shortcut']
        });
      }
    }
  }, 350);

  const chapterSearch = ref<string | undefined>(undefined);

  const project_id = computed(() => route.params.project_id as string);
  const projectId = computed(() => parseInt(project_id.value));

  const project_department_id = computed(() => route.params.project_department_id as string);
  const projectDepartmentId = computed(() => parseInt(project_department_id.value));

  const chapter_id = computed(() => route.params.chapter_id as string);
  const chapterId = computed(() => parseInt(chapter_id.value));

  const analytical_technique_id = computed(() => route.params.analytical_technique_id as string);
  const analyticalTechniqueUd = computed(() => parseInt(analytical_technique_id.value));

  const method_id = computed(() => route.params.method_id as string);
  const methodId = computed(() => parseInt(method_id.value));

  const loadExecute = async () => {
    baseDataLoaded.value = false;
    chapterSearch.value = undefined;

    if (projectId.value) {
      if (project.value) {
        if (project.value.project_id !== projectId.value) {
          await projectsStore.getProjectById(projectId.value);
        }
      } else {
        await projectsStore.getProjectById(projectId.value);
      }
    }

    if (projectDepartmentId.value) {
      if (department.value) {
        if (department.value.project_department_id !== projectDepartmentId.value) {
          await projectsStore.getDepartmentById(projectDepartmentId.value);
        }
      } else {
        await projectsStore.getDepartmentById(projectDepartmentId.value);
      }
    }

    if (chapterId.value) {
      await projectsStore.getChapterById(chapterId.value);
    }

    if (analyticalTechniqueUd.value) {
      analyticalDepartmentStore.project_id = projectId.value;
      technique.value = await techniquesStore.getTechnique(analyticalTechniqueUd.value);
      methods.value = await analyticalDepartmentStore.getProjectMethodDevelopmentTechniqueDetail(
        analyticalTechniqueUd.value
      );
      if (modalOptions.value?.updateData) {
        setPageTitle(modalOptions.value?.updateData.method_name ?? '');
      }
    }
    if (methodId.value) {
      await methodsStore.showEditModal(methodId.value);
      if (methodGet.value) {
        if (methodGet.value?.technique?.using_column) {
          columnsStore.setParamsFromLocation();
          if (analyticalTechniqueUd.value) {
            optionsColumns.value = [
              {
                column: 'techniques__analytical_technique_id',
                value: analyticalTechniqueUd.value
              }
            ];
            await columnsStore.getColumns({
              search: '',
              totalItems: 0,
              options: { page: 1, rowsPerPage: 25, sortBy: [] },
              fixedFilterOptions: optionsColumns.value,
              search_columns: ['name', 'manufacturer']
            });
            if (
              kolons.value &&
              methodGet.value &&
              methodGet.value.kolona &&
              methodGet.value.kolona.kolona_id
            ) {
              if (
                !kolons.value.find((kolona) => kolona.kolona_id === methodGet.value?.kolona_id) &&
                methodGet.value.technique &&
                methodGet.value.technique_id
              ) {
                kolons.value.push({
                  kolona_id: methodGet.value.kolona.kolona_id,
                  technique: methodGet.value.kolona.technique!,
                  techniques: methodGet.value.kolona.techniques!,
                  technique_id: methodGet.value.kolona.technique_id!,
                  name: methodGet.value.kolona.name,
                  chemistry: methodGet.value.kolona.chemistry,
                  diameter: methodGet.value.kolona.diameter,
                  length: methodGet.value.kolona.length,
                  particles: methodGet.value.kolona.particles,
                  serial_number: methodGet.value.kolona.serial_number,
                  catalog_number: methodGet.value.kolona.catalog_number,
                  manufacturer: methodGet.value.kolona.manufacturer,
                  used_since: methodGet.value.kolona.used_since,
                  note: methodGet.value.kolona.note,
                  status: methodGet.value.kolona.status
                });
              }
            }
          }
        }
        if (analyticalTechniqueUd.value) {
          instrumentOptions.value = [
            {
              column: 'status',
              value: MethodStatus.ACTIVE
            },
            {
              column: 'technique_id',
              value: analyticalTechniqueUd.value
            }
          ];
          const res = await instrumentsStore.getInstruments({
            search: '',
            totalItems: 0,
            options: { page: 1, rowsPerPage: 25, sortBy: [] },
            fixedFilterOptions: instrumentOptions.value,
            search_columns: ['instrument_shortcut']
          });
          if (res && instruments.value && methodGet.value.instrument_id) {
            if (
              !instruments.value.find(
                (instrument) => instrument.instrument_id === methodGet.value?.instrument_id
              ) &&
              methodGet.value.technique &&
              methodGet.value.technique_id
            ) {
              instruments.value.push({
                instrument_id: methodGet.value.instrument.instrument_id,
                technique_id: methodGet.value.instrument.technique!,
                newTechnique_id: methodGet.value.instrument.technique_id!,
                instrument_shortcut: methodGet.value.instrument.instrument_shortcut,
                status: methodGet.value.instrument.status
              });
            }
          }
        }
      }
    }
    if (
      technique.value &&
      department.value &&
      project.value &&
      chapter.value &&
      methods.value &&
      project.value.departments.find(
        (d) =>
          department.value && d.project_department_id === department.value.project_department_id
      ) &&
      department.value.chapters.find(
        (c) => chapter.value && c.chapter_id === chapter.value.chapter_id
      ) &&
      analyticalTechniqueUd.value
    ) {
      await checkParentClosure();
    } else {
      notification.error({
        message: 'Chyba',
        description: 'Nepodařilo se načíst data nebo nebyla vybrána metoda'
      });

      if (methodGet.value) {
        router.push({
          name: 'ListOfMethodForTechniqueUpdate',
          params: { method_id: methodId.value.toString() }
        });
      }
      if (chapter.value) {
        router.push({
          name: 'ChapterDetail',
          params: {
            project_id: project.value?.project_id.toString(),
            project_department_id: department.value?.project_department_id.toString(),
            chapter_id: chapter.value?.chapter_id.toString()
          }
        });
      } else if (project.value && department.value) {
        router.push({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value.project_id.toString(),
            project_department_id: department.value.project_department_id.toString()
          }
        });
      } else if (project.value) {
        router.push({
          name: 'ProjectDetail',
          params: { project_id: project.value.project_id.toString() }
        });
      } else {
        router.push({ name: 'ListOfProjects' });
      }
    }
    baseDataLoaded.value = true;
  };
  watch([method_id], async () => {
    await loadExecute();
  });

  const reloadPageWithoutSave = () => {
    loadExecute();
  };

  const redirectPageWithoutSave = () => {
    router.push({
      name: 'ListOfMethodForTechnique',
      params: {
        project_id: project.value?.project_id,
        project_department_id: department.value?.project_department_id,
        chapter_id: chapter.value?.chapter_id,
        analytical_technique_id: analyticalTechniqueUd.value
      }
    });
  };

  const isUpdateDataSameAsOriginal = computed(() => {
    let parametersCheck = true;
    if (modalOptions.value?.updateData && modalOptions.value?.baseData) {
      if (
        modalOptions.value?.baseData.parameters?.length !==
        modalOptions.value?.updateData.parameters?.length
      ) {
        parametersCheck = false;
      } else if (
        modalOptions.value?.updateData.parameters.length > 0 &&
        modalOptions.value?.baseData?.parameters &&
        modalOptions.value?.baseData.parameters.length > 0
      ) {
        for (let i = 0; i < modalOptions.value.updateData.parameters.length; i++) {
          if (
            modalOptions.value.baseData.parameters[i].parameter !==
              modalOptions.value.updateData.parameters[i].parameter ||
            modalOptions.value.baseData.parameters[i].value !==
              modalOptions.value.updateData.parameters[i].value
          ) {
            parametersCheck = false;
          }
        }
      }

      return (
        modalOptions.value.updateData.method_name === modalOptions.value.baseData.method_name &&
        modalOptions.value.updateData.instrument_id === modalOptions.value.baseData.instrument_id &&
        modalOptions.value.updateData.kolona_id === modalOptions.value.baseData.kolona_id &&
        modalOptions.value.updateData.gradient === modalOptions.value.baseData.gradient &&
        modalOptions.value.updateData.sequence_name === modalOptions.value.baseData.sequence_name &&
        modalOptions.value.updateData.preparation_of_standard_and_sample ===
          modalOptions.value.baseData.preparation_of_standard_and_sample &&
        modalOptions.value.updateData.note === modalOptions.value.baseData.note &&
        modalOptions.value.updateData.result === modalOptions.value.baseData.result &&
        modalOptions.value.baseData.parameters?.length ===
          modalOptions.value.updateData.parameters?.length &&
        parametersCheck
      );
    }
    return true;
  });

  const getMethoStatus = (status: string) => {
    switch (status) {
      case 'save_into_new_branch':
        return MethodSaveType.NEWBRANCH;
      case 'save_as_new_method':
        return MethodSaveType.NEWMETHOD;
      case 'save_and_close_method':
        return MethodSaveType.CLOSEMETHOD;
      case 'save_into_spec_branch':
        return MethodSaveType.NEWBRANCH;
      case 'save_into_main_branch':
        return MethodSaveType.NEWBRANCHMAIN;
      default:
        return MethodSaveType.NEWBRANCH;
    }
  };

  const updateMethod = async (type: string) => {
    await toggleModal();
    if (
      getMethoStatus(type) === MethodSaveType.NEWMETHOD ||
      getMethoStatus(type) === MethodSaveType.CLOSEMETHOD
    ) {
      router.push({
        name: 'ListOfMethodForTechnique',
        params: {
          project_id: project.value?.project_id,
          project_department_id: department.value?.project_department_id,
          chapter_id: chapter.value?.chapter_id,
          analytical_technique_id: analyticalTechniqueUd.value
        }
      });
    } else {
      await loadExecute();
    }
  };
  const breadcrumbItems = computed<BreadcrumbI[]>(() => {
    return [
      {
        title: 'Projekty',
        disabled: false,
        href: router.resolve({ name: 'Projects' }).href
      },
      {
        title: project.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'ProjectDetail',
          params: { project_id: project.value?.project_id }
        }).href
      },
      {
        title: department.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id
          }
        }).href
      },
      {
        title: chapter.value?.chapter_title ?? '',
        disabled: false,
        href: router.resolve({
          name: 'ChapterDetail',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id,
            chapter_id: chapter.value?.chapter_id
          }
        }).href
      },
      {
        title: technique.value?.name ?? '',
        disabled: false,
        href: router.resolve({
          name: 'ListOfMethodForTechnique',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id,
            chapter_id: chapter.value?.chapter_id,
            analytical_technique_id: analyticalTechniqueUd.value
          }
        }).href
      },
      {
        title: 'Uprava metody',
        disabled: true,
        href: router.resolve({
          name: 'ListOfMethodForTechniqueUpdate',
          params: {
            method_id: methodId.value.toString()
          }
        }).href
      }
    ];
  });

  const kolonaSearch = ref('');
  watch(kolonaSearch, () => {
    debouncedSearchKolumn();
  });
  const debouncedSearchKolumn = useDebounceFn(() => {
    if (colonsLoading.value === false && kolonaSearch.value !== '') {
      searchKolumn.value = kolonaSearch.value;
      if (analyticalTechniqueUd.value) {
        optionsColumns.value = [
          {
            column: 'techniques__analytical_technique_id',
            value: analyticalTechniqueUd.value
          }
        ];
        columnsStore.getColumns({
          search: searchKolumn.value,
          totalItems: 0,
          options: { page: 1, rowsPerPage: 25, sortBy: [] },
          fixedFilterOptions: optionsColumns.value,
          search_columns: ['name', 'manufacturer']
        });
      }
    }
  }, 350);

  const showCreateMethodModal = ref(false);
  const toggleModal = async () => {
    showCreateMethodModal.value = !showCreateMethodModal.value;
  };

  const redirectToHistory = () => {
    router.push({
      name: 'MethodHistory',
      params: { project_id: route.params.project_id, method_id: route.params.method_id }
    });
  };
  const isParentClosed = ref(false);
  const isMethodClosed = computed(() => methodGet.value?.status === 'closed');
  const checkParentClosure = async () => {
    if (
      project.value?.status === 'deactivated' ||
      project.value?.status === 'closed' ||
      department.value?.status === 'closed' ||
      chapter.value?.status === 'closed'
    ) {
      isParentClosed.value = true;
    }
  };

  const closeMethod = async () => {
    if (methodGet.value) {
      if (modalOptions.value?.updateData) {
        //modalOptions.value.updateData.files_ids = [...modalOptions.value.updateData.files_ids];
        modalOptions.value.updateData.parameters = filterFields(
          modalOptions.value.updateData.parameters
        );
        modalOptions.value.updateData.save_type = MethodSaveType.CLOSEMETHOD;
        const res = await methodsStore.updateMethod();
        if (res) {
          await loadExecute();
        }
      }
    }
  };

  const openMethod = async () => {
    if (methodGet.value) {
      const res = await methodsStore.reOpenMethod(methodGet.value.method_id);
      if (res) {
        await loadExecute();
      }
    }
  };

  const exportMethodAsPdf = async () => {
    if (methodGet.value && modalOptions.value?.updateData) {
      const res = await methodsStore.exportMethodAsPdf(
        methodGet.value.method_id,
        modalOptions.value?.updateData.method_name
      );
      if (res) {
        notification.success({ message: 'Metoda byla úspěšně exportována' });
      } else {
        notification.error({ message: 'Nepodařilo se exportovat metodu' });
      }
    }
  };

  const exportMethodAsWord = async () => {
    if (methodGet.value && modalOptions.value?.updateData) {
      const res = await methodsStore.exportMethodAsWord(
        methodGet.value.method_id,
        modalOptions.value?.updateData.method_name
      );
      if (res) {
        notification.success({ message: 'Metoda byla úspěšně exportována' });
      } else {
        notification.error({ message: 'Nepodařilo se exportovat metodu' });
      }
    }
  };

  export interface Field {
    parameter: string | undefined;
    value: string | undefined;
  }

  function filterFields(fields: Field[]): Field[] {
    return fields.filter(
      (field) =>
        field.parameter !== undefined &&
        field.parameter !== null &&
        field.parameter.trim() !== '' &&
        field.value !== undefined &&
        field.value !== null &&
        field.value.trim() !== ''
    );
  }

  const handleCancelClick = async () => {
    if (!isUpdateDataSameAsOriginal.value) {
      const confirmed =
        ConfirmRef.value &&
        (await ConfirmRef.value.open(
          'Potvrzení',
          'Opravdu chcete zrušit změny a načíst data ze serveru?',
          {
            color: 'error',
            notclosable: true,
            zIndex: 2400
          }
        ));
      if (confirmed) {
        reloadPageWithoutSave();
      }
    } else {
      redirectPageWithoutSave();
    }
  };

  const getAllFiles = async () => {
    if (methodGet && methodGet.value && methodGet.value.method_id) {
      const res = await methodsStore.getMethod(methodGet.value.method_id);
      if (methodGet.value && res) {
        if (res.files) {
          methodGet.value.files = res.files;
        }
      }
    }
  };
</script>
