<template>
  <LoaderWrapper
    v-if="!baseDataLoaded || project === null || department === null || chapter === null"
  />
  <template v-else>
    <TopPageBreadcrumb :title="chapter.chapter_title" :_breadcrumbs="breadcrumbItems" />
    <v-row>
      <v-col cols="12" md="12">
        <UiParentCard class="pa-0" :loading="!baseDataLoaded">
          <template #action>
            <v-row justify="space-between" class="align-center">
              <v-col cols="12" md="7">
                <v-text-field
                  v-model="chapterSearch"
                  type="text"
                  variant="outlined"
                  persistent-placeholder
                  placeholder="Hledat"
                  hide-details
                >
                  <template #prepend-inner>
                    <SearchOutlined :style="{ fontSize: '14px' }" />
                  </template>
                </v-text-field>
              </v-col>
              <v-col cols="12" md="5">
                <div class="d-flex gap-2 justify-end flex-wrap">
                  <v-btn
                    variant="flat"
                    color="primary"
                    :disabled="isParentClosed || isReadOnly || !project_permision"
                    @click.prevent="filesStore.showFileUploaderModal = true"
                  >
                    Nahrát soubory
                  </v-btn>

                  <v-btn
                    variant="flat"
                    color="primary"
                    :loading="methodsLoading"
                    :disabled="isParentClosed || isReadOnly || !project_permision"
                    @click.prevent="methodsStore.showNewMethodModal(projectId)"
                  >
                    Přidat metodu
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </template>
          <MethodsSection
            v-if="methods"
            :methods="methods"
            :method-search="chapterSearch"
            :is-parent-closed="isParentClosed"
            @reload="loadExecute"
          />
          <FileSection
            :files="chapter.files"
            :file-search="chapterSearch"
            :is-read-only="isParentClosed || isReadOnly || !project_permision"
            @reload="reloadChapter"
          />
        </UiParentCard>

        <FileUploaderModal
          v-model:show="filesStore.showFileUploaderModal"
          @submit-files="processFileUploads"
        />
        <MethodModal
          v-if="technique"
          v-model:show="methodsStore.showMethodModal"
          :technique="technique"
          @reload="loadExecute"
        />
        <!-- </template> -->
      </v-col>
    </v-row>
  </template>
</template>
<script setup lang="ts">
  import type { BreadcrumbI } from '@/components/shared/BaseBreadcrumb.vue';
  import FileSection from '@/components/shared/file/FileSection.vue';
  import FileUploaderModal from '@/components/shared/file/FileUploaderModal.vue';
  import TopPageBreadcrumb from '@/components/shared/TopPageBreadcrumb.vue';
  import UiParentCard from '@/components/shared/UiParentCard.vue';
  import LoaderWrapper from '@/layouts/dashboard/LoaderWrapper.vue';
  import { useAnalyticalDepartmentStore } from '@/stores/analyticalDepartment';
  import { useChaptersStore } from '@/stores/chapters';
  import { useFilesStore } from '@/stores/files';
  import MethodModal from '@/stores/method/components/MethodModal.vue';
  import MethodsSection from '@/stores/method/components/MethodsSection.vue';
  import { Method, useMethodsStore } from '@/stores/method/methods';
  import { useProjectsStore } from '@/stores/projects';
  import { TechniqueType, useTechniquesStore, type Technique } from '@/stores/techniques';
  import { useUIStore } from '@/stores/ui';
  import { SearchOutlined } from '@ant-design/icons-vue';
  import { storeToRefs } from 'pinia';
  import { computed, onMounted, ref, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { notification } from 'ant-design-vue';
  import { isAllowed } from '@/utils/directive/isAllowed';
  import { setPageTitle } from '@/utils/title';
  const baseDataLoaded = ref(false);

  const route = useRoute();
  const router = useRouter();
  const uiStore = useUIStore();

  const filesStore = useFilesStore();
  const methodsStore = useMethodsStore();
  const chaptersStore = useChaptersStore();
  const projectsStore = useProjectsStore();
  const techniquesStore = useTechniquesStore();
  const analyticalDepartmentStore = useAnalyticalDepartmentStore();

  const technique = ref<Technique | undefined>(undefined);
  const methods = ref<Method[] | undefined>([]);

  const { loading: methodsLoading } = storeToRefs(methodsStore);
  const { loading, project, department, chapter, project_permision } = storeToRefs(projectsStore);

  watch(loading, () => {
    uiStore.isLoading = loading.value;
  });

  const reloadChapter = async () => {
    if (department.value)
      await projectsStore.getDepartmentById(department.value.project_department_id);
    if (chapter.value) await projectsStore.getChapterById(chapter.value.chapter_id);
  };

  const processFileUploads = async (fileIds: number[] | undefined) => {
    if (!fileIds || !chapter.value?.chapter_id) {
      notification.error({
        message: 'Nepodařilo se nahrát soubory',
        description:
          'Kapitola: ' +
          chapter.value?.chapter_title +
          ' nebyla nalezena nebo nebyly vybrány žádné soubory: ' +
          (fileIds?.join(', ') ?? '/')
      });
      return;
    }

    await chaptersStore.processMultipleFiles(chapter.value.chapter_id, fileIds);
    await reloadChapter();
  };

  onMounted(async () => {
    await checkReloadPermisions();
  });
  const hasEditPermission = ref(false);
  const hasViewPermission = ref(false);

  const checkAnalyticalPermissions = () => {
    // First check admin permissions (edit_all or view_all)
    if (isAllowed(['edit_all'])) {
      hasEditPermission.value = true;
      hasViewPermission.value = true;
      return true;
    }

    if (isAllowed(['view_all'])) {
      hasEditPermission.value = false;
      hasViewPermission.value = true;
      return true;
    }

    // If user has general analytical department permissions, they get access
    if (isAllowed(['view_analytical_department']) || isAllowed(['edit_analytical_department'])) {
      hasEditPermission.value = isAllowed(['edit_analytical_department']);
      hasViewPermission.value =
        isAllowed(['view_analytical_department']) || hasEditPermission.value;
      return true;
    }

    // If user doesn't have general analytical permissions, check specific permission
    if (isAllowed(['access_analytical_methods'])) {
      hasEditPermission.value = true;
      hasViewPermission.value = true;
      return true;
    }

    return false;
  };

  const checkReloadPermisions = async () => {
    if (!checkAnalyticalPermissions()) {
      notification.error({
        message: 'Chyba',
        description:
          'Nemáte oprávnění pro zobrazení této stránky. Chybí oprávnění: view_analytical_department, edit_analytical_department nebo access_analytical_methods'
      });
      router.push({ name: 'ListOfProjects' });
      return;
    }

    await loadExecute();
  };

  const isReadOnly = computed(() => {
    return !hasEditPermission.value;
  });

  const chapterSearch = ref<string | undefined>(undefined);

  const project_id = computed(() => route.params.project_id as string);
  const projectId = computed(() => parseInt(project_id.value));

  const project_department_id = computed(() => route.params.project_department_id as string);
  const projectDepartmentId = computed(() => parseInt(project_department_id.value));

  const chapter_id = computed(() => route.params.chapter_id as string);
  const chapterId = computed(() => parseInt(chapter_id.value));

  const analytical_technique_id = computed(() => route.params.analytical_technique_id as string);
  const analyticalTechniqueUd = computed(() => parseInt(analytical_technique_id.value));

  const loadExecute = async () => {
    baseDataLoaded.value = false;
    chapterSearch.value = undefined;

    if (projectId.value) {
      if (project.value) {
        if (project.value.project_id !== projectId.value) {
          await projectsStore.getProjectById(projectId.value);
        }
      } else {
        await projectsStore.getProjectById(projectId.value);
      }
    }

    if (projectDepartmentId.value) {
      if (department.value) {
        if (department.value.project_department_id !== projectDepartmentId.value) {
          await projectsStore.getDepartmentById(projectDepartmentId.value);
        }
      } else {
        await projectsStore.getDepartmentById(projectDepartmentId.value);
      }
    }

    if (chapterId.value) {
      await projectsStore.getChapterById(chapterId.value);
    }

    if (analyticalTechniqueUd.value) {
      analyticalDepartmentStore.project_id = projectId.value;
      technique.value = await techniquesStore.getTechnique(analyticalTechniqueUd.value);
      methods.value = await analyticalDepartmentStore.getProjectMethodDevelopmentTechniqueDetail(
        analyticalTechniqueUd.value
      );
      setPageTitle(technique.value?.name ?? '');
    }

    if (
      technique.value &&
      technique.value.type === TechniqueType.INTERNAL &&
      department.value &&
      project.value &&
      chapter.value &&
      project.value.departments.find(
        (d) =>
          department.value && d.project_department_id === department.value.project_department_id
      ) &&
      department.value.chapters.find(
        (c) => chapter.value && c.chapter_id === chapter.value.chapter_id
      ) &&
      analyticalTechniqueUd.value
    ) {
      await checkParentClosure();
      baseDataLoaded.value = true;
    } else {
      notification.error({
        message: 'Chyba',
        description: 'Nepodařilo se načíst data nebo nebyla vybrána interní analytická technika'
      });
      if (chapter.value) {
        router.push({
          name: 'ChapterDetail',
          params: {
            project_id: project.value?.project_id.toString(),
            project_department_id: department.value?.project_department_id.toString(),
            chapter_id: chapter.value?.chapter_id.toString()
          }
        });
      } else if (project.value && department.value) {
        router.push({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value.project_id.toString(),
            project_department_id: department.value.project_department_id.toString()
          }
        });
      } else if (project.value) {
        router.push({
          name: 'ProjectDetail',
          params: { project_id: project.value.project_id.toString() }
        });
      } else {
        router.push({ name: 'ListOfProjects' });
      }
    }
  };

  watch([project_id, project_department_id, chapter_id], () => {
    loadExecute();
  });

  const breadcrumbItems = computed<BreadcrumbI[]>(() => {
    return [
      {
        title: 'Projekty',
        disabled: false,
        href: router.resolve({ name: 'Projects' }).href
      },
      {
        title: project.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'ProjectDetail',
          params: { project_id: project.value?.project_id }
        }).href
      },
      {
        title: department.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id
          }
        }).href
      },
      {
        title: chapter.value?.chapter_title ?? '',
        disabled: false,
        href: router.resolve({
          name: 'ChapterDetail',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id,
            chapter_id: chapter.value?.chapter_id
          }
        }).href
      },
      {
        title: technique.value?.name ?? 'Metody',
        disabled: true,
        href: router.resolve({
          name: 'ListOfMethodForTechnique',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id,
            chapter_id: chapter.value?.chapter_id,
            analytical_technique_id: analyticalTechniqueUd.value
          }
        }).href
      }
    ];
  });
  const checkParentClosure = async () => {
    if (
      project.value?.status === 'deactivated' ||
      project.value?.status === 'closed' ||
      department.value?.status === 'closed' ||
      chapter.value?.status === 'closed'
    ) {
      isParentClosed.value = true;
    }
  };

  const isParentClosed = ref(false);
</script>
