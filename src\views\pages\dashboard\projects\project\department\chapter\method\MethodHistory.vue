<template>
  <LoaderWrapper v-if="!baseDataLoaded" />
  <template v-else>
    <TopPageBreadcrumb title="Historie vývoje" :_breadcrumbs="breadcrumbItems" />
    <v-row class="justify-content-end">
      <v-col cols="12">
        <UiParentCard class="pa-0" :loading="!baseDataLoaded">
          <template #action>
            <v-row justify="space-between" class="align-center">
              <v-col cols="12" md="9">
                <div class="d-flex gap-2 justify-start flex-wrap"></div>
              </v-col>
              <v-col cols="12" md="3">
                <div class="d-flex gap-2 justify-end flex-wrap">
                  <v-btn
                    size="small"
                    variant="flat"
                    color="error"
                    @click="
                      router.push({
                        name: 'ListOfMethodForTechniqueUpdate',
                        params: { method_id: methodId.toString() }
                      })
                    "
                  >
                    Zpět
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </template>
          <History />
        </UiParentCard>
      </v-col>
    </v-row>
  </template>
</template>
<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue';
  import LoaderWrapper from '@/layouts/dashboard/LoaderWrapper.vue';
  import TopPageBreadcrumb from '@/components/shared/TopPageBreadcrumb.vue';
  import UiParentCard from '@/components/shared/UiParentCard.vue';
  import History from './components/HistoryChart.vue';
  import EditorTextarea from '@/components/shared/EditorTextarea.vue';
  import type { BreadcrumbI } from '@/components/shared/BaseBreadcrumb.vue';
  import { useInstrumentsStore } from '@/stores/instruments';
  import { Technique, TechniqueType, useTechniquesStore } from '@/stores/techniques';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { useDebounceFn } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import type { EditorManager, RawEditorOptions } from 'tinymce';
  import { useMethodsStore, type Method, MethodStatus } from '@/stores/method/methods';
  import ParametersFormTable from './components/ParametersFormTable.vue';
  import { useRoute, useRouter } from 'vue-router';
  import { useAnalyticalDepartmentStore } from '@/stores/analyticalDepartment';
  import { useProjectsStore } from '@/stores/projects';
  import { notification } from 'ant-design-vue';
  import { useColumnsStore } from '@/stores/columns';
  import FileUploader from '@/components/shared/file/FileUploader.vue';
  import MethodSaveModal from './components/MethodSaveModal.vue';
  import { isAllowed } from '@/utils/directive/isAllowed';
  import { setPageTitle } from '@/utils/title';

  const methodsStore = useMethodsStore();
  const projectsStore = useProjectsStore();
  const techniquesStore = useTechniquesStore();
  const analyticalDepartmentStore = useAnalyticalDepartmentStore();
  const technique = ref<Technique | undefined>(undefined);
  const methods = ref<Method[] | undefined>([]);
  const { project, department, chapter } = storeToRefs(projectsStore);
  const { methodGet } = storeToRefs(methodsStore);
  const route = useRoute();
  const router = useRouter();
  onMounted(async () => {
    await checkReloadPermisions();
  });

  const checkAdminPermission = () => {
    return isAllowed(['edit_all']);
  };

  const checkAdminViewPermission = () => {
    return isAllowed(['view_all']);
  };

  const checkReloadPermisions = async () => {
    if (checkProjectTypePermisions() || checkAdminPermission() || checkAdminViewPermission()) {
      await loadExecute();
    } else {
      notification.error({
        message: 'Chyba',
        description: 'Nemáte oprávnění pro zobrazení této stránky'
      });
      router.push({ name: 'ListOfProjects' });
    }
  };
  const checkProjectTypePermisions = () => {
    return isAllowed(['view_analytical_department']) || isAllowed(['edit_analytical_department']);
  };

  const baseDataLoaded = ref(false);
  const breadcrumbItems = computed<BreadcrumbI[]>(() => {
    return [
      {
        title: 'Projekty',
        disabled: false,
        href: router.resolve({ name: 'Projects' }).href
      },
      {
        title: project.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'ProjectDetail',
          params: { project_id: project.value?.project_id }
        }).href
      },
      {
        title: department.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id
          }
        }).href
      },
      {
        title: chapter.value?.chapter_title ?? '',
        disabled: false,
        href: router.resolve({
          name: 'ChapterDetail',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id,
            chapter_id: chapter.value?.chapter_id
          }
        }).href
      },
      {
        title: technique.value?.name ?? '',
        disabled: false,
        href: router.resolve({
          name: 'ListOfMethodForTechnique',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id,
            chapter_id: chapter.value?.chapter_id,
            analytical_technique_id: analyticalTechniqueUd.value
          }
        }).href
      },
      {
        title: 'Úprava metody',
        disabled: false,
        href: router.resolve({
          name: 'ListOfMethodForTechniqueUpdate',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id,
            chapter_id: chapter.value?.chapter_id,
            analytical_technique_id: analyticalTechniqueUd.value,
            method_id: methodId.value
          }
        }).href
      },
      {
        title: 'Historie vývoje',
        disabled: true,
        href: router.resolve({
          name: 'ListOfMethodForTechniqueUpdate',
          params: {
            method_id: methodId.value.toString()
          }
        }).href
      }
    ];
  });
  const chapterSearch = ref<string | undefined>(undefined);

  const project_id = computed(() => route.params.project_id as string);
  const projectId = computed(() => parseInt(project_id.value));

  const project_department_id = computed(() => route.params.project_department_id as string);
  const projectDepartmentId = computed(() => parseInt(project_department_id.value));

  const chapter_id = computed(() => route.params.chapter_id as string);
  const chapterId = computed(() => parseInt(chapter_id.value));

  const analytical_technique_id = computed(() => route.params.analytical_technique_id as string);
  const analyticalTechniqueUd = computed(() => parseInt(analytical_technique_id.value));

  const method_id = computed(() => route.params.method_id as string);
  const methodId = computed(() => parseInt(method_id.value));

  const loadExecute = async () => {
    baseDataLoaded.value = false;
    chapterSearch.value = undefined;
    if (projectId.value) {
      if (project.value) {
        if (project.value.project_id !== projectId.value) {
          await projectsStore.getProjectById(projectId.value);
        }
      } else {
        await projectsStore.getProjectById(projectId.value);
      }
    }

    if (projectDepartmentId.value) {
      if (department.value) {
        if (department.value.project_department_id !== projectDepartmentId.value) {
          await projectsStore.getDepartmentById(projectDepartmentId.value);
        }
      } else {
        await projectsStore.getDepartmentById(projectDepartmentId.value);
      }
    }

    if (chapterId.value) {
      await projectsStore.getChapterById(chapterId.value);
    }

    if (analyticalTechniqueUd.value) {
      analyticalDepartmentStore.project_id = projectId.value;
      technique.value = await techniquesStore.getTechnique(analyticalTechniqueUd.value);
      methods.value = await analyticalDepartmentStore.getProjectMethodDevelopmentTechniqueDetail(
        analyticalTechniqueUd.value
      );
    }
    if (methodId.value) {
      await methodsStore.getMethodHistory(methodId.value);
    }
    setPageTitle(methodGet.value?.method_name ?? '');

    if (
      technique.value &&
      department.value &&
      project.value &&
      chapter.value &&
      methods.value &&
      project.value.departments.find(
        (d) =>
          department.value && d.project_department_id === department.value.project_department_id
      ) &&
      department.value.chapters.find(
        (c) => chapter.value && c.chapter_id === chapter.value.chapter_id
      ) &&
      analyticalTechniqueUd.value
    ) {
      baseDataLoaded.value = true;
    } else {
      notification.error({
        message: 'Chyba',
        description: 'Nepodařilo se načíst data nebo nebyla vybrána verze'
      });

      if (methodGet.value) {
        router.push({
          name: 'ListOfMethodForTechniqueUpdate',
          params: { method_id: methodId.value.toString() }
        });
      }
      if (chapter.value) {
        router.push({
          name: 'ChapterDetail',
          params: {
            project_id: project.value?.project_id.toString(),
            project_department_id: department.value?.project_department_id.toString(),
            chapter_id: chapter.value?.chapter_id.toString()
          }
        });
      } else if (project.value && department.value) {
        router.push({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value.project_id.toString(),
            project_department_id: department.value.project_department_id.toString()
          }
        });
      } else if (project.value) {
        router.push({
          name: 'ProjectDetail',
          params: { project_id: project.value.project_id.toString() }
        });
      } else {
        router.push({ name: 'ListOfProjects' });
      }
    }
  };
</script>
