<template>
  <LoaderWrapper v-if="!baseDataLoaded && !versionA && !versionB" />
  <template v-else>
    <TopPageBreadcrumb title="Porovnání verzí" :_breadcrumbs="breadcrumbItems" />
    <v-row class="justify-content-end">
      <v-col cols="12">
        <UiParentCard class="pa-0" :loading="!baseDataLoaded">
          <template #action>
            <v-row justify="space-between" class="align-center">
              <v-col cols="12" md="9"></v-col>
              <v-col cols="12" md="3">
                <div class="d-flex gap-2 justify-end flex-wrap">
                  <v-btn
                    size="small"
                    variant="flat"
                    color="error"
                    @click="
                      router.push({
                        name: 'MethodHistory',
                        params: { method_id: methodId.toString() }
                      })
                    "
                  >
                    Zpět
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </template>
          <v-row>
            <v-col cols="12" md="6">
              <v-col cols="12">
                <v-table density="compact">
                  <template #default>
                    <tbody v-if="versionA">
                      <tr>
                        <td class="text-left font-weight-bold pl-0 pl-0" style="padding: 0 2px">
                          Analytická metoda:
                        </td>
                        <td>{{ versionA?.data.method_name || '/' }}</td>
                      </tr>
                      <tr>
                        <td class="text-left font-weight-bold pl-0" style="padding: 0 2px">
                          Název sekvence:
                        </td>
                        <td class="html" v-html="versionA?.data.sequence_name || '/'"></td>
                      </tr>
                    </tbody>
                  </template>
                </v-table>
              </v-col>
              <v-col cols="12">
                <span class="text-left font-weight-bold pl-0 pl-0"></span>
                <template v-if="versionA?.data.parameters && versionA.data.parameters.length > 0">
                  <template v-for="(field, index) in versionA?.data.parameters" :key="index">
                    <v-row>
                      <v-col cols="12" sm="6">
                        <v-label class="mb-2">Parametr</v-label>
                        <v-text-field
                          v-model="field.parameter"
                          single-line
                          hide-details="auto"
                          variant="outlined"
                          rounded="sm"
                          :readonly="true"
                        ></v-text-field>
                      </v-col>

                      <v-col cols="10" sm="6">
                        <v-label class="mb-2">Hodnota</v-label>
                        <v-text-field
                          v-model="field.value"
                          single-line
                          hide-details="auto"
                          variant="outlined"
                          rounded="sm"
                          :readonly="true"
                        ></v-text-field>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col cols="12">
                        <v-divider class="my-5"></v-divider>
                      </v-col>
                    </v-row>
                  </template>
                </template>
                <template v-else>
                  <v-row>
                    <v-col cols="12" sm="6">
                      <v-label class="mb-2">Parametr</v-label>
                      <v-text-field
                        single-line
                        hide-details="auto"
                        variant="outlined"
                        rounded="sm"
                        :readonly="true"
                      ></v-text-field>
                    </v-col>

                    <v-col cols="10" sm="6">
                      <v-label class="mb-2">Hodnota</v-label>
                      <v-text-field
                        single-line
                        hide-details="auto"
                        variant="outlined"
                        rounded="sm"
                        :readonly="true"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12">
                      <v-divider class="my-5"></v-divider>
                    </v-col>
                  </v-row>
                </template>
              </v-col>
              <v-col cols="12">
                <v-label class="mb-2">Gradient</v-label>
                <EditorTextarea
                  v-if="versionA"
                  v-model="versionA.data.gradient"
                  :disabled="true"
                  :show-edit-button="false"
                  :config="
                    {
                      statusbar: false,
                      resize: true,
                      min_height: 200
                    } as EditorManager & RawEditorOptions
                  "
                ></EditorTextarea>
              </v-col>
              <v-col cols="12">
                <v-label class="mb-2">Příprava standardu a vzorku</v-label>
                <EditorTextarea
                  v-if="versionA"
                  v-model="versionA.data.preparation_of_standard_and_sample"
                  :disabled="true"
                  :show-edit-button="false"
                  :config="
                    {
                      statusbar: false,
                      resize: true,
                      min_height: 200,
                      plugins: 'autoresize'
                    } as EditorManager & RawEditorOptions
                  "
                ></EditorTextarea>
              </v-col>
              <v-col cols="12">
                <v-label class="mb-2">Poznámka</v-label>
                <EditorTextarea
                  v-if="versionA"
                  v-model="versionA.data.note"
                  :disabled="true"
                  :show-edit-button="false"
                  :config="
                    {
                      statusbar: false,
                      resize: true,
                      min_height: 200
                    } as EditorManager & RawEditorOptions
                  "
                ></EditorTextarea>
              </v-col>
              <v-col cols="12">
                <v-label class="mb-2">Výsledek</v-label>
                <EditorTextarea
                  v-if="versionA"
                  v-model="versionA.data.result"
                  :disabled="true"
                  :show-edit-button="false"
                  :config="
                    {
                      statusbar: false,
                      resize: true,
                      min_height: 200
                    } as EditorManager & RawEditorOptions
                  "
                ></EditorTextarea>
              </v-col>
              <v-col cols="12">
                <v-label class="mb-2">Přílohy</v-label>
                <FileUploader
                  v-if="versionA && versionA.data.files"
                  v-model="versionA.data.files"
                  :disabled="true"
                  :uppy-options="{
                    height: 250,
                    restrictions: {
                      maxNumberOfFiles: 10,
                      minNumberOfFiles: 0,
                      allowedFileTypes: null
                    }
                  }"
                />
              </v-col>
            </v-col>
            <v-col cols="12" md="6">
              <v-col cols="12">
                <v-table density="compact">
                  <template #default>
                    <tbody v-if="versionB">
                      <tr>
                        <td class="text-left font-weight-bold pl-0 pl-0" style="padding: 0 2px">
                          Analytická metoda:
                        </td>
                        <td>{{ versionB?.data.method_name || '/' }}</td>
                      </tr>
                      <tr>
                        <td class="text-left font-weight-bold pl-0" style="padding: 0 2px">
                          Název sekvence:
                        </td>
                        <td class="html" v-html="versionB?.data.sequence_name || '/'"></td>
                      </tr>
                    </tbody>
                  </template>
                </v-table>
              </v-col>
              <v-col cols="12">
                <span class="text-left font-weight-bold pl-0 pl-0"></span>
                <template v-if="versionB?.data.parameters && versionB.data.parameters.length > 0">
                  <template v-for="(field, index) in versionB?.data.parameters" :key="index">
                    <v-row>
                      <v-col cols="12" sm="6">
                        <v-label class="mb-2">Parametr</v-label>
                        <v-text-field
                          v-model="field.parameter"
                          single-line
                          hide-details="auto"
                          variant="outlined"
                          rounded="sm"
                          :readonly="true"
                        ></v-text-field>
                      </v-col>

                      <v-col cols="10" sm="6">
                        <v-label class="mb-2">Hodnota</v-label>
                        <v-text-field
                          v-model="field.value"
                          single-line
                          hide-details="auto"
                          variant="outlined"
                          rounded="sm"
                          :readonly="true"
                        ></v-text-field>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col cols="12">
                        <v-divider class="my-5"></v-divider>
                      </v-col>
                    </v-row>
                  </template>
                </template>
                <template v-else>
                  <v-row>
                    <v-col cols="12" sm="6">
                      <v-label class="mb-2">Parametr</v-label>
                      <v-text-field
                        single-line
                        hide-details="auto"
                        variant="outlined"
                        rounded="sm"
                        :readonly="true"
                      ></v-text-field>
                    </v-col>

                    <v-col cols="10" sm="6">
                      <v-label class="mb-2">Hodnota</v-label>
                      <v-text-field
                        single-line
                        hide-details="auto"
                        variant="outlined"
                        rounded="sm"
                        :readonly="true"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col cols="12">
                      <v-divider class="my-5"></v-divider>
                    </v-col>
                  </v-row>
                </template>
              </v-col>
              <v-col cols="12">
                <v-label class="mb-2">Gradient</v-label>
                <EditorTextarea
                  v-if="versionB"
                  v-model="versionB.data.gradient"
                  :disabled="true"
                  :show-edit-button="false"
                  :config="
                    {
                      statusbar: false,
                      resize: true,
                      min_height: 200,
                      plugins: 'autoresize'
                    } as EditorManager & RawEditorOptions
                  "
                ></EditorTextarea>
              </v-col>
              <v-col cols="12">
                <v-label class="mb-2">Příprava standardu a vzorku</v-label>
                <EditorTextarea
                  v-if="versionB"
                  v-model="versionB.data.preparation_of_standard_and_sample"
                  :disabled="true"
                  :show-edit-button="false"
                  :config="
                    {
                      statusbar: false,
                      resize: true,
                      min_height: 200
                    } as EditorManager & RawEditorOptions
                  "
                ></EditorTextarea>
              </v-col>
              <v-col cols="12">
                <v-label class="mb-2">Poznámka</v-label>
                <EditorTextarea
                  v-if="versionB"
                  v-model="versionB.data.note"
                  :disabled="true"
                  :show-edit-button="false"
                  :config="
                    {
                      statusbar: false,
                      resize: true,
                      min_height: 200
                    } as EditorManager & RawEditorOptions
                  "
                ></EditorTextarea>
              </v-col>
              <v-col cols="12">
                <v-label class="mb-2">Výsledek</v-label>
                <EditorTextarea
                  v-if="versionB"
                  v-model="versionB.data.result"
                  :disabled="true"
                  :show-edit-button="false"
                  :config="
                    {
                      statusbar: false,
                      resize: true,
                      min_height: 200
                    } as EditorManager & RawEditorOptions
                  "
                ></EditorTextarea>
              </v-col>
              <v-col cols="12">
                <v-label class="mb-2">Přílohy</v-label>
                <FileUploader
                  v-if="versionB && versionB.data.files"
                  v-model="versionB.data.files"
                  :disabled="true"
                  :uppy-options="{
                    height: 250,
                    restrictions: {
                      maxNumberOfFiles: 10,
                      minNumberOfFiles: 0,
                      allowedFileTypes: null
                    }
                  }"
                />
              </v-col>
            </v-col>
          </v-row>
        </UiParentCard>
      </v-col>
    </v-row>
  </template>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted } from 'vue';
  import LoaderWrapper from '@/layouts/dashboard/LoaderWrapper.vue';
  import TopPageBreadcrumb from '@/components/shared/TopPageBreadcrumb.vue';
  import UiParentCard from '@/components/shared/UiParentCard.vue';
  import EditorTextarea from '@/components/shared/EditorTextarea.vue';
  import type { BreadcrumbI } from '@/components/shared/BaseBreadcrumb.vue';
  import { Technique, useTechniquesStore } from '@/stores/techniques';
  import { storeToRefs } from 'pinia';
  import type { EditorManager, RawEditorOptions } from 'tinymce';
  import { useMethodsStore, type Method, MethodStatus } from '@/stores/method/methods';
  import { useRoute, useRouter } from 'vue-router';
  import { useAnalyticalDepartmentStore } from '@/stores/analyticalDepartment';
  import { useProjectsStore } from '@/stores/projects';
  import { notification } from 'ant-design-vue';
  import FileUploader from '@/components/shared/file/FileUploader.vue';
  import { isAllowed } from '@/utils/directive/isAllowed';
  import { setPageTitle } from '@/utils/title';

  const methodsStore = useMethodsStore();
  const projectsStore = useProjectsStore();
  const techniquesStore = useTechniquesStore();
  const analyticalDepartmentStore = useAnalyticalDepartmentStore();
  const technique = ref<Technique | undefined>(undefined);
  const methods = ref<Method[] | undefined>([]);
  const { project, department, chapter } = storeToRefs(projectsStore);
  const { methodGet, versionA, versionB, differences } = storeToRefs(methodsStore);
  const route = useRoute();
  const router = useRouter();
  onMounted(async () => {
    await checkReloadPermisions();
  });

  const checkAdminViewPermission = () => {
    return isAllowed(['view_all']);
  };
  const checkAdminPermission = () => {
    return isAllowed(['edit_all']);
  };

  const checkReloadPermisions = async () => {
    if (checkProjectTypePermisions() || checkAdminViewPermission() || checkAdminPermission()) {
      await loadExecute();
    } else {
      notification.error({
        message: 'Chyba',
        description: 'Nemáte oprávnění pro zobrazení této stránky'
      });
      router.push({ name: 'ListOfProjects' });
    }
  };
  const checkProjectTypePermisions = () => {
    return isAllowed(['view_analytical_department']) || isAllowed(['edit_analytical_department']);
  };
  const baseDataLoaded = ref(false);
  const breadcrumbItems = computed<BreadcrumbI[]>(() => {
    return [
      {
        title: 'Projekty',
        disabled: false,
        href: router.resolve({ name: 'Projects' }).href
      },
      {
        title: project.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'ProjectDetail',
          params: { project_id: project.value?.project_id }
        }).href
      },
      {
        title: department.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id
          }
        }).href
      },
      {
        title: chapter.value?.chapter_title ?? '',
        disabled: false,
        href: router.resolve({
          name: 'ChapterDetail',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id,
            chapter_id: chapter.value?.chapter_id
          }
        }).href
      },
      {
        title: technique.value?.name ?? '',
        disabled: false,
        href: router.resolve({
          name: 'ListOfMethodForTechnique',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id,
            chapter_id: chapter.value?.chapter_id,
            analytical_technique_id: analyticalTechniqueUd.value
          }
        }).href
      },
      {
        title: 'Úprava metody',
        disabled: false,
        href: router.resolve({
          name: 'ListOfMethodForTechniqueUpdate',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id,
            chapter_id: chapter.value?.chapter_id,
            analytical_technique_id: analyticalTechniqueUd.value,
            method_id: methodId.value
          }
        }).href
      },
      {
        title: 'Historie vývoje',
        disabled: false,
        href: router.resolve({
          name: 'MethodHistory',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id,
            chapter_id: chapter.value?.chapter_id,
            analytical_technique_id: analyticalTechniqueUd.value,
            method_id: methodId.value
          }
        }).href
      },
      {
        title: 'Porovnání verzí',
        disabled: true,
        href: router.resolve({
          name: 'VersionCompare',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id,
            chapter_id: chapter.value?.chapter_id,
            analytical_technique_id: analyticalTechniqueUd.value,
            method_id: methodId.value,
            version_a_id: versionAId.value,
            version_b_id: versionBId.value
          }
        }).href
      }
    ];
  });
  const chapterSearch = ref<string | undefined>(undefined);

  const project_id = computed(() => route.params.project_id as string);
  const projectId = computed(() => parseInt(project_id.value));

  const project_department_id = computed(() => route.params.project_department_id as string);
  const projectDepartmentId = computed(() => parseInt(project_department_id.value));

  const chapter_id = computed(() => route.params.chapter_id as string);
  const chapterId = computed(() => parseInt(chapter_id.value));

  const analytical_technique_id = computed(() => route.params.analytical_technique_id as string);
  const analyticalTechniqueUd = computed(() => parseInt(analytical_technique_id.value));

  const method_id = computed(() => route.params.method_id as string);
  const methodId = computed(() => parseInt(method_id.value));

  const method_version_id = computed(() => route.params.method_version_id as string);
  const methodVersionId = computed(() => parseInt(method_version_id.value));

  const version_a_id = computed(() => route.params.version_a_id as string);
  const versionAId = computed(() => parseInt(version_a_id.value));

  const version_b_id = computed(() => route.params.version_b_id as string);
  const versionBId = computed(() => parseInt(version_b_id.value));

  const loadExecute = async () => {
    baseDataLoaded.value = false;
    chapterSearch.value = undefined;
    if (projectId.value) {
      if (project.value) {
        if (project.value.project_id !== projectId.value) {
          await projectsStore.getProjectById(projectId.value);
        }
      } else {
        await projectsStore.getProjectById(projectId.value);
      }
    }

    if (projectDepartmentId.value) {
      if (department.value) {
        if (department.value.project_department_id !== projectDepartmentId.value) {
          await projectsStore.getDepartmentById(projectDepartmentId.value);
        }
      } else {
        await projectsStore.getDepartmentById(projectDepartmentId.value);
      }
    }

    if (chapterId.value) {
      await projectsStore.getChapterById(chapterId.value);
    }

    if (analyticalTechniqueUd.value) {
      analyticalDepartmentStore.project_id = projectId.value;
      technique.value = await techniquesStore.getTechnique(analyticalTechniqueUd.value);
      methods.value = await analyticalDepartmentStore.getProjectMethodDevelopmentTechniqueDetail(
        analyticalTechniqueUd.value
      );
    }

    if (versionAId.value && versionBId.value && methodVersionId.value) {
      await methodsStore.getComparisonBranches(
        methodVersionId.value,
        versionAId.value,
        versionBId.value
      );
    }
    setPageTitle(methodGet.value?.method_name ?? '');

    if (
      technique.value &&
      department.value &&
      project.value &&
      chapter.value &&
      methods.value &&
      project.value.departments.find(
        (d) =>
          department.value && d.project_department_id === department.value.project_department_id
      ) &&
      department.value.chapters.find(
        (c) => chapter.value && c.chapter_id === chapter.value.chapter_id
      ) &&
      analyticalTechniqueUd.value &&
      versionAId.value &&
      versionBId.value &&
      methodVersionId.value
    ) {
      baseDataLoaded.value = true;
    } else {
      notification.error({
        message: 'Chyba',
        description: 'Nepodařilo se načíst data nebo nebyla verze metody'
      });

      if (methodGet.value) {
        router.push({
          name: 'ListOfMethodForTechniqueUpdate',
          params: { method_id: methodId.value.toString() }
        });
      }
      if (chapter.value) {
        router.push({
          name: 'ChapterDetail',
          params: {
            project_id: project.value?.project_id.toString(),
            project_department_id: department.value?.project_department_id.toString(),
            chapter_id: chapter.value?.chapter_id.toString()
          }
        });
      } else if (project.value && department.value) {
        router.push({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value.project_id.toString(),
            project_department_id: department.value.project_department_id.toString()
          }
        });
      } else if (project.value) {
        router.push({
          name: 'ProjectDetail',
          params: { project_id: project.value.project_id.toString() }
        });
      } else {
        router.push({ name: 'ListOfProjects' });
      }
    }
  };
</script>

<style lang="less" scoped>
  td.html {
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
  }
  td.text-left {
    min-width: 130px;
  }
  td:not(.text-left) {
    width: 100%;
  }
</style>
