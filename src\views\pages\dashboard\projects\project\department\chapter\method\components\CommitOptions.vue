<template>
  <LoaderWrapper v-if="false" />
  <template v-else>
    <v-dialog v-model="showState" class="customer-modal" style="z-index: 1010" max-width="1200px">
      <v-card :loading="loading">
        <v-card-title class="pa-5">
          <span class="text-h5">Možnosti úpravy</span>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text class="pa-6">
          <v-row justify="center" class="align-center">
            <v-col cols="12">
              <div class="d-flex flex-wrap flex-column align-center gap-3">
                <!-- First row - 3 buttons -->
                <div class="w-100 d-flex gap-3 justify-center flex-wrap">
                  <v-btn
                    class="equal-width-btn large-btn"
                    size="large"
                    variant="flat"
                    color="primary"
                    @click.stop="compareBranches"
                  >
                    Porovnat s vybranou verzí
                  </v-btn>
                  <v-btn
                    class="equal-width-btn large-btn"
                    size="large"
                    variant="flat"
                    color="primary"
                    @click.stop="switchToMainVersion"
                  >
                    Přepnout na aktivní verzi
                  </v-btn>
                  <v-btn
                    class="equal-width-btn large-btn"
                    size="large"
                    variant="flat"
                    color="primary"
                    @click.stop="createMethod"
                  >
                    Odčlenit verzi
                  </v-btn>
                </div>
                <!-- Second row - 3 buttons -->
                <div class="w-100 d-flex gap-3 justify-center flex-wrap">
                  <v-btn
                    class="equal-width-btn large-btn"
                    size="large"
                    variant="flat"
                    color="success"
                    @click.stop="setAsPrefillVersion"
                  >
                    Nastavit pro předvyplňění
                  </v-btn>
                  <v-btn
                    class="equal-width-btn large-btn"
                    size="large"
                    variant="flat"
                    color="primary"
                    @click.stop="exportMethodAsPdf"
                  >
                    Exportovat do PDF
                  </v-btn>
                  <v-btn
                    class="equal-width-btn large-btn"
                    size="large"
                    variant="flat"
                    color="primary"
                    @click.stop="exportMethodAsWord"
                  >
                    Exportovat do Wordu
                  </v-btn>
                </div>
              </div>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" variant="text" @click="showState = false">Zavřít</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <HistoryMethodDetail
      v-if="showSecondaryModal"
      v-model:show="showSecondaryModal"
      :modal-type="modalType"
      :version-id="version_id"
      @reload="updateMethod()"
      @update-commit="compareVersions"
    />
    <ConfirmDlg ref="ConfirmRef" />
  </template>
</template>
<script setup lang="ts">
  import { useVModel } from '@vueuse/core';
  import LoaderWrapper from '@/layouts/dashboard/LoaderWrapper.vue';
  import { storeToRefs } from 'pinia';
  import { ref } from 'vue';
  import { useMethodsStore } from '@/stores/method/methods';
  import { notification } from 'ant-design-vue';
  import HistoryMethodDetail from './MethodHistorymodal.vue';
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  const ConfirmRef = ref<undefined | typeof ConfirmDlg>(undefined);
  const emits = defineEmits(['update:show', 'updateCommit', 'reload']);
  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    },
    versionId: {
      type: Number,
      required: false,
      default: null
    },
    branchId: {
      type: Number,
      required: false,
      default: null
    }
  });

  const selectedBranchId = ref<number | null>(null);
  const showState = useVModel(props, 'show');
  const methodsStore = useMethodsStore();
  const { loading, methodGet, currentVersion, methodVersions, specificVersion } =
    storeToRefs(methodsStore);
  const compareBranches = async () => {
    toggleModal('porovnat');
  };
  const modalType = ref('');
  const createBranch = async () => {
    if (!currentVersion.value || !props.versionId || !specificVersion.value) {
      notification.error({
        message: 'Není vybrána žádná verze'
      });
      return;
    }
    toggleModal('vetvit');
  };

  const exportMethodAsPdf = async () => {
    if (currentVersion.value && specificVersion.value && props.versionId) {
      const res = await methodsStore.exportMethodAsPdf(
        currentVersion.value?.method_id,
        `${currentVersion.value?.description} - ${specificVersion.value.description}`,
        props.versionId
      );
      if (res) {
        notification.success({ message: 'Metoda byla úspěšně exportována' });
      } else {
        notification.error({ message: 'Nepodařilo se exportovat metodu' });
      }
    }
  };

  const exportMethodAsWord = async () => {
    if (currentVersion.value && specificVersion.value && props.versionId) {
      const res = await methodsStore.exportMethodAsWord(
        currentVersion.value?.method_id,
        `${currentVersion.value?.description} - ${specificVersion.value.description}`,
        props.versionId
      );
      if (res) {
        notification.success({ message: 'Metoda byla úspěšně exportována' });
      } else {
        notification.error({ message: 'Nepodařilo se exportovat metodu' });
      }
    }
  };

  const switchToMainVersion = async () => {
    if (!currentVersion.value || !props.versionId || !specificVersion.value) {
      notification.error({
        message: 'Chyba',
        description: 'Není vybrána žádná verze'
      });
      return;
    }
    if (props.versionId) {
      if (
        await ConfirmRef.value?.open('Opravdu chcete změnit verzi na aktuální?', '', {
          color: 'warning',
          notclosable: true,
          zIndex: 2400
        })
      ) {
        const res = await methodsStore.switchToVersion(
          currentVersion.value.method_id,
          props.versionId
        );
        if (res) {
          emits('reload');
        }
      }
    }
  };
  const version_id = ref();
  const showSecondaryModal = ref(false);
  const toggleModal = async (type: string) => {
    modalType.value = type;
    version_id.value = props.versionId;
    showSecondaryModal.value = !showSecondaryModal.value;
  };
  const createMethod = async () => {
    if (!specificVersion.value || !currentVersion.value || !props.versionId) {
      notification.error({
        message: 'Není vybrána žádná verze'
      });
      return;
    }
    toggleModal('clenit');
  };

  const updateMethod = () => {
    showSecondaryModal.value = false;
    emits('reload');
  };
  const compareVersions = (selectedBranchId: number) => {
    showSecondaryModal.value = false;
    if (
      currentVersion.value &&
      currentVersion.value.version_id &&
      specificVersion.value &&
      specificVersion.value.version_id
    ) {
      emits(
        'updateCommit',
        currentVersion.value.method_id,
        selectedBranchId,
        specificVersion.value.version_id
      );
    }
  };
  const setAsPrefillVersion = async () => {
    if (!props.versionId || !currentVersion.value) {
      notification.error({
        message: 'Chyba',
        description: 'Není vybrána žádná verze.'
      });
      return;
    }
    if (
      await ConfirmRef.value?.open('Nastavit tuto verzi jako předvyplňovací?', '', {
        color: 'warning',
        notclosable: true,
        zIndex: 2400
      })
    ) {
      const res = await methodsStore.setAsPrefill(currentVersion.value.method_id, props.versionId);
      if (res) {
        notification.success({ message: 'Verze byla nastavena jako předvyplňovací.' });
        emits('reload');
      }
    }
  };
</script>
<style scope>
  .equal-width-btn {
    width: 220px;
    min-width: 220px;
  }
  @media screen and (max-width: 1200px) {
    .equal-width-btn {
      width: 200px;
      min-width: 200px;
    }
  }
  @media screen and (max-width: 768px) {
    .equal-width-btn {
      width: 100%;
      min-width: auto;
    }
  }
</style>
