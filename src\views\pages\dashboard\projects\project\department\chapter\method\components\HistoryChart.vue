<template>
  <v-row>
    <v-col cols="12" class="graphContainer">
      <div ref="gitgraphContainer" class="customClass"></div>
    </v-col>
  </v-row>
  <ComitOptions
    v-if="showCommitModal"
    v-model:show="showCommitModal"
    :version-id="saveClickedVersion"
    :branch-id="saveClickedBranch"
    @update-commit="updateCommit"
    @reload="reload"
  />
</template>

<script setup lang="ts">
  import { onMounted, ref, computed } from 'vue';
  import { createGitgraph, templateExtend, TemplateName, Orientation } from '@gitgraph/js';
  import { storeToRefs } from 'pinia';
  import { useMethodsStore, type MethodGetI, type CurentVersionI } from '@/stores/method/methods';
  import { useRoute, useRouter } from 'vue-router';
  import ComitOptions from './CommitOptions.vue';
  import { notification } from 'ant-design-vue';
  import { isAllowed } from '@/utils/directive/isAllowed';
  import { useProjectsStore } from '@/stores/projects';
  const route = useRoute();
  const router = useRouter();
  const methodsStore = useMethodsStore();
  const projectsStore = useProjectsStore();

  const { methodGet } = storeToRefs(methodsStore);
  const { project, department, chapter, project_permision } = storeToRefs(projectsStore);

  const gitgraphContainer = ref<HTMLDivElement | null>(null);

  const method_id = computed(() => route.params.method_id as string);
  const methodId = computed(() => parseInt(method_id.value));

  const generateGitGraph = (data: MethodGetI, gitgraph: any) => {
    const branchMap: { [key: number]: any } = {};
    const commitMap: { [key: number]: any } = {};
    const versionsMap = new Map<number, CurentVersionI>();

    const collectVersions = (version: CurentVersionI) => {
      if (!versionsMap.has(version.version_id)) {
        versionsMap.set(version.version_id, version);
        if (version.parent_version) {
          collectVersions(version.parent_version);
        }
        if (version.child_versions && version.child_versions.length > 0) {
          version.child_versions.forEach((child) => {
            collectVersions(child);
          });
        }
      }
    };

    collectVersions(data.current_version);

    const sortedVersions = Array.from(versionsMap.values()).sort(
      (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
    );

    const rootVersion = sortedVersions.find((v) => !v.parent_version_id);
    if (!rootVersion) {
      return;
    }

    const mainBranch = gitgraph.branch(rootVersion.branch.name || 'main');
    branchMap[rootVersion.branch_id] = mainBranch;

    sortedVersions.forEach((version) => {
      const {
        version_id,
        branch_id,
        description,
        branch,
        created_at,
        parent_version_id,
        is_default_for_prefill
      } = version;

      let gitBranch;
      if (branchMap[branch_id]) {
        gitBranch = branchMap[branch_id];
      } else {
        const parentVersion = sortedVersions.find((v) => v.version_id === parent_version_id);
        const parentBranch = parentVersion ? branchMap[parentVersion.branch_id] : mainBranch;

        gitBranch = parentBranch.branch({ name: branch.name });
        branchMap[branch_id] = gitBranch;
      }

      const formattedMessage = `${new Date(created_at).toLocaleString()} ${description || `Commit ${version_id}`}`;
      let dotColor = '#8c8c8c';
      if (is_default_for_prefill) {
        dotColor = '#00a854';
      } else if (version_id === data.current_version.version_id) {
        dotColor = '#2aa1af';
      }

      const commit = gitBranch.commit({
        subject: formattedMessage,
        onClick: () => handleCommitClick(version_id, branch_id),
        onMessageClick: () => handleMessageClick(version_id, branch_id),
        style: {
          dot: {
            color: dotColor
          }
        }
      });

      commitMap[version_id] = commit;

      /*
    if (version.child_versions && version.child_versions.length > 0) {
      version.child_versions.forEach(child => {
        const childBranch = branchMap[child.branch_id] || gitBranch.branch({ name: child.branch.name });
        branchMap[child.branch_id] = childBranch;
      });
    }
*/
    });
  };

  const handleCommitClick = async (versionId: number, branchId: number) => {
    if (!methodId.value) return;
    if (!versionId) return;
    if (isAllowed(['edit_all'])) {
      saveClickedVersion.value = versionId;
      saveClickedBranch.value = branchId;
      const res = await methodsStore.getMethodVersions(methodId.value, versionId);
      {
        if (res) {
          await toggleModal();
        }
      }
      return;
    }

    if (!project_permision.value) {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nejste přiřazený k danému projektu.'
      });
      return;
    }
    if (isParentClosed.value) {
      notification.error({
        message: 'Metoda uzavřená, nelze provádět změny'
      });
      return;
    }
    if (isReadOnly.value) {
      notification.error({
        message: 'Nemáte oprávnění editovat metodu'
      });
      return;
    }
    saveClickedVersion.value = versionId;
    saveClickedBranch.value = branchId;
    const res = await methodsStore.getMethodVersions(methodId.value, versionId);
    {
      if (res) {
        await toggleModal();
      }
    }
  };

  const handleMessageClick = (versionId: number, branchId: number) => {
    if (versionId) {
      router.push({ name: 'MethodDetail', params: { method_version_id: versionId.toString() } });
    }
  };

  const saveClickedVersion = ref<number>();
  const saveClickedBranch = ref<number>();
  const loadGraph = async () => {
    if (!methodGet.value || !gitgraphContainer.value) return;
    gitgraphContainer.value.innerHTML = '';

    const gitgraph = createGitgraph(gitgraphContainer.value, {
      //orientation: Orientation.VerticalReverse,
      template: templateExtend(TemplateName.Metro, {
        colors: ['#2aa1afBF', '#2aa1afBF', '#2aa1afBF', '#2aa1afBF'],
        commit: {
          message: {
            displayHash: false,
            displayAuthor: false,
            font: 'normal 12pt Arial'
          },
          spacing: 90
        }
      })
    });
    generateGitGraph(methodGet.value, gitgraph);
  };

  onMounted(async () => {
    await checkParentClosure();
    await checkReloadPermisions();
  });

  const hasEditPermission = ref(false);
  const hasViewPermission = ref(false);

  const checkAnalyticalPermissions = () => {
    // First check admin permissions (edit_all or view_all)
    if (isAllowed(['edit_all'])) {
      hasEditPermission.value = true;
      hasViewPermission.value = true;
      return true;
    }

    if (isAllowed(['view_all'])) {
      hasEditPermission.value = false;
      hasViewPermission.value = true;
      return true;
    }

    // If user has general analytical department permissions, they get access
    if (isAllowed(['view_analytical_department']) || isAllowed(['edit_analytical_department'])) {
      hasEditPermission.value = isAllowed(['edit_analytical_department']);
      hasViewPermission.value =
        isAllowed(['view_analytical_department']) || hasEditPermission.value;
      return true;
    }

    // If user doesn't have general analytical permissions, check specific permission
    if (isAllowed(['access_analytical_methods'])) {
      hasEditPermission.value = true;
      hasViewPermission.value = true;
      return true;
    }

    return false;
  };

  const checkReloadPermisions = async () => {
    if (!checkAnalyticalPermissions()) {
      notification.error({
        message: 'Chyba',
        description:
          'Nemáte oprávnění pro zobrazení této stránky. Chybí oprávnění: view_analytical_department, edit_analytical_department nebo access_analytical_methods'
      });
      router.push({ name: 'ListOfProjects' });
      return;
    }

    await loadGraph();
  };

  const isReadOnly = computed(() => {
    return !hasEditPermission.value;
  });

  const showCommitModal = ref(false);
  const toggleModal = async () => {
    showCommitModal.value = !showCommitModal.value;
  };

  const updateCommit = async (method: number, version_a_id: number, version_b_id: number) => {
    await toggleModal();
    if (version_a_id && version_b_id) {
      router.push({
        name: 'VersionCompare',
        params: {
          method_version_id: method.toString(),
          version_a_id: version_a_id.toString(),
          version_b_id: version_b_id.toString()
        }
      });
    }
  };

  const reload = async () => {
    await toggleModal();
    if (methodId.value) {
      await methodsStore.getMethodHistory(methodId.value);
    }
    await loadGraph();
  };
  const isParentClosed = ref(false);
  const checkParentClosure = async () => {
    if (
      project.value?.status === 'deactivated' ||
      project.value?.status === 'closed' ||
      department.value?.status === 'closed' ||
      chapter.value?.status === 'closed' ||
      methodGet.value?.status === 'closed'
    ) {
      isParentClosed.value = true;
    }
  };
</script>

<style>
  g:hover {
    cursor: pointer;
  }
</style>

<style scoped>
  .customClass {
    width: 100%;
    height: 100%;
  }
  @media (max-width: 768px) {
    .graphContainer {
      width: 90vw;
      overflow-x: auto;
    }
  }
</style>
