<template>
  <v-dialog v-model="showState" class="customer-modal" style="z-index: 1010">
    <v-card :loading="loading">
      <v-form
        ref="MethodFormCreate"
        class="MethodFormCreate"
        @submit.prevent="submitFormToValidate"
      >
        <v-card-title class="pa-5">
          <span class="text-h5">{{ title }}</span>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row v-if="props.modalType === 'vetvit'">
            <v-col cols="12">
              <v-label class="mb-2">Název větev pro větvení</v-label>
              <v-text-field
                v-model="newBranchName"
                single-line
                hide-details="auto"
                variant="outlined"
                rounded="sm"
                placeholder="Název větve"
                :rules="itemRequiredRule"
              ></v-text-field>
            </v-col>
            <v-col>
              <v-label class="mb-2">Popis změny</v-label>
              <v-text-field
                v-model="newBranchDescription"
                single-line
                hide-details="auto"
                variant="outlined"
                rounded="sm"
                placeholder="Zadejte popis změny"
                :rules="itemRequiredRule"
              ></v-text-field>
            </v-col>
          </v-row>
          <v-row v-if="props.modalType === 'clenit'">
            <v-col cols="12">
              <v-label class="mb-2">Název metody pro odčlenění</v-label>
              <v-text-field
                v-model="newBranchName"
                single-line
                hide-details="auto"
                variant="outlined"
                rounded="sm"
                placeholder="Název větve"
                :rules="itemRequiredRule"
              ></v-text-field>
            </v-col>
            <v-col>
              <v-label class="mb-2">Popis změny</v-label>
              <v-text-field
                v-model="newBranchDescription"
                single-line
                hide-details="auto"
                variant="outlined"
                rounded="sm"
                placeholder="Zadejte popis změny"
                :rules="itemRequiredRule"
              ></v-text-field>
            </v-col>
          </v-row>
          <v-row v-if="props.modalType === 'porovnat'">
            <v-col cols="12">
              <v-autocomplete
                v-if="methodGet && methodVersions"
                v-model="selectedBranchId"
                :items="
                  methodVersions
                    .filter((version) => version.version_id !== specificVersion?.version_id)
                    .map((version) => {
                      return {
                        value: version.version_id,
                        title: `${version.version_id} (${version.branch.name}) - ${version.description} (${toTimeLocale(version.created_at)})`,
                        version: version
                      };
                    })
                "
                :clearable="true"
                hide-details
                rounded="sm"
                variant="outlined"
                color="primary"
                label="Vyberte verzi pro porovnání"
                single-line
                class="autocomplete"
                :no-data-text="'Žádná další políčka'"
                :slim="true"
                :rules="itemRequiredRule"
              >
                <template #chip>
                  <v-chip
                    label
                    variant="tonal"
                    color="primary"
                    size="large"
                    class="my-1 text-subtitle-1 font-weight-regular"
                  ></v-chip>
                </template>

                <template #item="{ props: branchProp, item }">
                  <v-list-item v-bind="branchProp" :title="''">
                    <div class="player-wrapper pa-2">
                      <h6 class="text-subtitle-1 mb-0">
                        {{ item.raw.version.version_id }} ({{ item.raw.version.branch.name }}) -
                        {{ item.raw.version.description }}
                      </h6>
                      <small class="text-h6 text-lightText">
                        Vytvořena: {{ toTimeLocale(item.raw.version.created_at) }}
                      </small>
                    </div>
                  </v-list-item>
                </template>
              </v-autocomplete>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" variant="text" @click="showState = false">Zavřít</v-btn>
          <v-btn color="primary" variant="flat" type="submit" :loading="loading">Uložit</v-btn>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
  import { useDebounceFn, useVModel } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import { computed, onMounted, ref, watch, onUnmounted, version } from 'vue';
  import { MethodSaveType, MethodStatus, type SimpleVersionChange } from '@/stores/method/methods';
  import { itemRequiredRule } from '@/utils/formValidation';
  import {
    useMethodsStore,
    type Method,
    type CurentVersionI,
    type MethodUpdateDto
  } from '@/stores/method/methods';
  import { toTimeLocale } from '@/utils/locales';
  export interface Field {
    parameter: string | undefined;
    value: string | undefined;
  }
  const emits = defineEmits(['update:show', 'reload', 'updateCommit']);
  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    },
    modalType: {
      type: String,
      required: false,
      default: ''
    },
    versionId: {
      type: Number,
      required: true
    }
  });
  const selectedBranchId = ref<number | null>(null);
  const newBranchName = ref<string>('');
  const newBranchDescription = ref<string>('');
  const showState = useVModel(props, 'show');
  const methodsStore = useMethodsStore();
  const { loading, methodGet, specificVersion, methodVersions } = storeToRefs(methodsStore);
  const MethodFormCreate = ref();
  async function submitFormToValidate() {
    if (MethodFormCreate.value.isValid) {
      if (props.modalType === 'vetvit') {
        const data = {
          branch_name: newBranchName.value,
          description: newBranchDescription.value
        } as SimpleVersionChange;
        const res = await methodsStore.changeVersion(data, props.versionId);
        if (res) {
          emits('reload');
        }
      } else if (props.modalType === 'clenit') {
        if (methodGet.value && methodGet.value.files && specificVersion.value) {
          const data = {
            method_name: specificVersion.value.data.method_name ?? undefined,
            instrument_id: specificVersion.value.data.instrument_id ?? null,
            kolona_id: specificVersion.value.data.kolona_id ?? null,
            sequence_name: specificVersion.value.data.sequence_name ?? '',
            preparation_of_standard_and_sample:
              specificVersion.value.data.preparation_of_standard_and_sample ?? '',
            note: specificVersion.value.data.note ?? '',
            result: specificVersion.value.data.result ?? '',
            status: specificVersion.value.data.status ?? MethodStatus.ACTIVE,
            parameters: specificVersion.value.data.parameters?.map((param) => {
              return {
                parameter: param.parameter,
                value: param.value
              };
            }) as Field[],
            description_of_the_change: newBranchDescription.value,
            gradient: specificVersion.value.data.gradient ?? '',
            save_type: MethodSaveType.NEWMETHOD,
            files_ids: methodGet.value.files.map((file) => file.file_id) ?? [],
            save_branch_id: specificVersion.value.branch_id,
            new_branch_or_method_name: newBranchName.value
          } as unknown as MethodUpdateDto;
          const res = await methodsStore.updateMethodForHistory(data);
          if (res) {
            emits('reload');
          }
        }
      } else if (props.modalType === 'porovnat') {
        emits('updateCommit', selectedBranchId.value);
      }
    }
  }
  const title = computed(() => {
    return props.modalType === 'vetvit'
      ? 'Název a popis větve'
      : props.modalType === 'clenit'
        ? 'Název a popis nové metody'
        : props.modalType === 'porovnat'
          ? 'Porovnat s verzí'
          : '';
  });
  onUnmounted(() => {
    newBranchName.value = '';
    newBranchDescription.value = '';
  });
</script>
