<template>
  <LoaderWrapper
    v-if="
      !methodGet ||
      modalOptions?.updateData === undefined ||
      modalOptions?.baseData === undefined ||
      loaded
    "
  />
  <template v-else>
    <v-dialog v-model="showState" class="customer-modal" style="z-index: 1010">
      <v-card :loading="loading">
        <v-form
          ref="MethodFormCreate"
          class="MethodFormCreate"
          @submit.prevent="submitFormToValidate"
        >
          <v-card-title class="pa-5">
            <span class="text-h5">Uložit změny</span>
          </v-card-title>
          <v-divider></v-divider>
          <v-card-text>
            <v-row>
              <v-col cols="12">
                <v-label class="mb-2">Popis změny</v-label>
                <v-text-field
                  v-model="modalOptions.updateData.description_of_the_change"
                  single-line
                  hide-details="auto"
                  variant="outlined"
                  rounded="sm"
                  placeholder="Zadejte popis změny"
                  :rules="itemRequiredRule"
                ></v-text-field>
              </v-col>
            </v-row>
            <v-row v-if="haveChildVersions">
              <v-col cols="12">
                <v-label class="mb-2">{{ title }}</v-label>
                <v-text-field
                  v-model="modalOptions.updateData.new_branch_or_method_name"
                  single-line
                  hide-details="auto"
                  variant="outlined"
                  rounded="sm"
                  placeholder="Zadejte název"
                  :rules="itemRequiredRule"
                ></v-text-field>
              </v-col>
            </v-row>
          </v-card-text>
          <v-divider></v-divider>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn color="error" variant="text" @click="showState = false">Zavřít</v-btn>
            <v-btn color="primary" variant="flat" type="submit" :loading="loading">Uložit</v-btn>
          </v-card-actions>
        </v-form>
      </v-card>
    </v-dialog>
  </template>
</template>
<script setup lang="ts">
  import { useVModel } from '@vueuse/core';
  import LoaderWrapper from '@/layouts/dashboard/LoaderWrapper.vue';
  import { storeToRefs } from 'pinia';
  import { computed, ref, onUnmounted } from 'vue';
  import { MethodSaveType } from '@/stores/method/methods';
  import { itemRequiredRule } from '@/utils/formValidation';
  import {
    useMethodsStore,
    type CurentVersionI,
    type MethodVersions,
    type SimpleVersionChange
  } from '@/stores/method/methods';
  const emits = defineEmits(['update:show', 'updateMethod']);
  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    }
  });
  const loaded = ref(false);
  const showState = useVModel(props, 'show');
  const methodsStore = useMethodsStore();
  const { modalOptions, loading, methodGet, methodVersions } = storeToRefs(methodsStore);
  const MethodFormCreate = ref();
  const haveChildVersions = computed(() => {
    return methodGet.value?.current_version?.child_versions !== null;
  });
  async function submitFormToValidate() {
    if (MethodFormCreate.value.isValid && modalOptions.value?.updateData) {
      modalOptions.value.updateData.files_ids = [...modalOptions.value.updateData.files_ids];
      modalOptions.value.updateData.parameters = filterFields(
        modalOptions.value.updateData.parameters
      );
      if (modalOptions.value.isEditing && !modalOptions.value.isCreating) {
        if (
          methodGet.value &&
          methodGet.value.current_version &&
          methodGet.value.current_version.branch_id &&
          !haveChildVersions.value
        ) {
          modalOptions.value.updateData.save_type = MethodSaveType.SPECBRANCH;
          modalOptions.value.updateData.save_branch_id = methodGet.value.current_version.branch_id;
        } else if (
          methodGet.value &&
          methodGet.value.current_version &&
          methodGet.value.current_version.branch_id &&
          haveChildVersions.value
        ) {
          modalOptions.value.updateData.save_type = MethodSaveType.NEWBRANCH;
        } else {
          return;
        }
        const res = await methodsStore.updateMethod();
        if (res) {
          emits('updateMethod', modalOptions.value.updateData.save_type);
        }
      }
    }
  }
  const title = computed(() => {
    return modalOptions?.value?.updateData?.save_type === MethodSaveType.NEWMETHOD
      ? 'Název metody'
      : modalOptions?.value?.updateData?.save_type === MethodSaveType.NEWBRANCH
        ? 'Název větve'
        : modalOptions?.value?.updateData?.save_type === MethodSaveType.SPECBRANCH
          ? 'Název metody'
          : '';
  });
  export interface Field {
    parameter: string | undefined;
    value: string | undefined;
  }
  function filterFields(fields: Field[]): Field[] {
    return fields.filter(
      (field) =>
        field.parameter !== undefined &&
        field.parameter !== null &&
        field.parameter.trim() !== '' &&
        field.value !== undefined &&
        field.value !== null &&
        field.value.trim() !== ''
    );
  }
  onUnmounted(() => {
    if (modalOptions.value?.updateData) {
      modalOptions.value.updateData.description_of_the_change = undefined;
      modalOptions.value.updateData.new_branch_or_method_name = undefined;
      modalOptions.value.updateData.save_type = MethodSaveType.NEWBRANCH;
      modalOptions.value.updateData.save_branch_id = null;
    }
  });
  function collectBranches(version: CurentVersionI) {
    const branches: { value: any; title: any; branch: any }[] = [];
    const seenBranches = new Set();

    function traverse(version: CurentVersionI) {
      if (version.branch) {
        const branchKey = `${version.branch.branch_id}-${version.branch.name}`;
        if (!seenBranches.has(branchKey)) {
          branches.push({
            value: version.branch.branch_id,
            title: version.branch.name,
            branch: version.branch
          });
          seenBranches.add(branchKey);
        }
      }
      if (version.parent_version) {
        traverse(version.parent_version);
      }
    }

    traverse(version);
    return branches;
  }

  const flattenedBranches = computed(() => {
    if (methodGet.value && methodGet.value.current_version) {
      return collectBranches(methodGet.value.current_version);
    }
    return [];
  });

  function findNewestVerions(versions: MethodVersions[]) {
    return versions.reduce((acc, version) => {
      if (version.created_at > acc.created_at) {
        return version;
      }
      return acc;
    });
  }

  const checkIfNewestVersionIsCurrentVersion = computed(() => {
    if (methodGet.value && methodGet.value.current_version && methodVersions.value) {
      const newestVersion = findNewestVerions(methodVersions.value);
      return newestVersion.version_id === methodGet.value.current_version.version_id;
    }
    return false;
  });
</script>
