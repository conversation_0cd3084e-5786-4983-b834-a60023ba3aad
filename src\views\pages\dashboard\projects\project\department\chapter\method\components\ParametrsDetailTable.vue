<template>
  <template v-for="(field, index) in specificVersion?.data.parameters" :key="index">
    <v-row>
      <v-col cols="12" sm="6">
        <v-label class="mb-2">Parametr</v-label>
        <v-text-field
          v-model="field.parameter"
          single-line
          hide-details="auto"
          variant="outlined"
          rounded="sm"
          :readonly="true"
        ></v-text-field>
      </v-col>

      <v-col cols="10" sm="6">
        <v-label class="mb-2">Hodnota</v-label>
        <v-text-field
          v-model="field.value"
          single-line
          hide-details="auto"
          variant="outlined"
          rounded="sm"
          :readonly="true"
        ></v-text-field>
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="12">
        <v-divider class="my-5"></v-divider>
      </v-col>
    </v-row>
  </template>
</template>

<script setup lang="ts">
  import { useMethodsStore } from '@/stores/method/methods';
  import { storeToRefs } from 'pinia';
  const methodsStore = useMethodsStore();
  const { specificVersion } = storeToRefs(methodsStore);
</script>
