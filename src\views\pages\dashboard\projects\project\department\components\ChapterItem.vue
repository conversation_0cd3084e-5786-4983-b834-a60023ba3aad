<template>
  <v-card
    variant="outlined"
    :color="chapter.isActive && !isParentClosed ? 'primary' : 'error'"
    :class="'card-hover-border bg-containerBg' + (chapter.isActive ? '' : ' strikethrough-tex')"
    :to="redirectToChapterDetailLink(chapter.chapter_id)"
    :subtitle="`Štítky: ${chapter.tags && chapter.tags.length > 0 ? chapter.joinTags : '<PERSON>z <PERSON>'}`"
    :title="chapter.chapter_title"
    :disabled="!checkProjectTypePermisions(department?.getName)"
  >
    <template #prepend>
      <FolderOutlined :style="{ fontSize: '28px' }" />
    </template>
    <template #append>
      <ChapterEdit v-if="chapter" :chapter="chapter" @reload="$emit('reload')" />
    </template>
  </v-card>
</template>
<script lang="ts" setup>
  import type { Chapter } from '@/stores/chapters';
  import { FolderOutlined } from '@ant-design/icons-vue';
  import { useRouter } from 'vue-router';
  import ChapterEdit from '../../../components/ChapterEdit.vue';
  import { useProjectsStore } from '@/stores/projects';
  import { storeToRefs } from 'pinia';
  import { isAllowed } from '@/utils/directive/isAllowed';
  const projectsStore = useProjectsStore();
  const { department } = storeToRefs(projectsStore);

  defineEmits(['reload']);
  const props = defineProps<{
    chapter: Chapter;
    isParentClosed: boolean;
  }>();

  const router = useRouter();
  const redirectToChapterDetailLink = (id: number) => {
    return router.resolve({
      name: 'ChapterDetail',
      params: { chapter_id: id.toString() }
    });
  };

  const checkProjectTypePermisions = (project_type: string | undefined) => {
    if (!project_type) return false;

    if (project_type === 'Analytické oddělení') {
      if (isAllowed(['edit_all'])) {
        return true;
      }

      if (isAllowed(['view_analytical_department']) || isAllowed(['edit_analytical_department'])) {
        return true;
      }

      const chapterTitle = props.chapter.chapter_title;

      if (chapterTitle === 'Vyvoj metody') {
        return isAllowed(['access_analytical_methods']);
      } else if (chapterTitle === 'Vzorky RD') {
        return isAllowed(['access_analytical_samples_rd']);
      } else if (chapterTitle === 'Vzorky QC a VT') {
        return isAllowed(['access_analytical_samples_qc_vt']);
      } else if (chapterTitle === 'Standardy') {
        return isAllowed(['access_analytical_standards']);
      }

      return false;
    } else if (project_type === 'Syntetické oddělení') {
      return isAllowed(['view_syntetic_department']) || isAllowed(['edit_syntetic_department']);
    } else {
      return (
        isAllowed(['view_technological_department']) || isAllowed(['edit_technological_department'])
      );
    }
  };
</script>
