<template>
  <section class="mb-5">
    <span class="text-h4"><PERSON><PERSON>oly</span>
  </section>
  <v-row v-if="chapters.length > 0">
    <v-col
      v-for="chapter in chapters
        .filter((chapter) =>
          chapterSearch
            ? chapter.chapter_title
                .toUpperCase()
                .includes(chapterSearch.toUpperCase().replace(/\s+/g, '')) ||
              chapter.joinTags
                .toUpperCase()
                .includes(chapterSearch.toUpperCase().replace(/\s+/g, ''))
            : true
        )
        .sort((a, b) => {
          const priority: { [key: string]: number } = {
            'Vyvoj metody': 1,
            'Vzorky RD': 2,
            'Vzorky QC a VT': 3,
            Standardy: 4,
            Specifikace: 5,
            Certifikace: 6
          };

          const aPriority = priority[a.chapter_title as keyof typeof priority] || 7;
          const bPriority = priority[b.chapter_title as keyof typeof priority] || 7;

          if (aPriority !== bPriority) {
            return aPriority - bPriority;
          }

          if (a.status === 'active' && b.status === 'closed') return -1;
          if (a.status === 'closed' && b.status === 'active') return 1;
          return a.created_at > b.created_at ? -1 : 1;
        })"
      :key="chapter.chapter_id"
      cols="12"
    >
      <ChapterItem
        :chapter="chapter"
        :is-parent-closed="isParentClosed"
        @reload="$emit('reload')"
      />
    </v-col>
  </v-row>
  <NotFoundItem v-else>
    <template #notFound>Nebyly nalezeny žádné kapitoly</template>
  </NotFoundItem>
</template>

<script lang="ts" setup>
  import NotFoundItem from '@/components/shared/NotFoundItem.vue';
  import type { Chapter } from '@/stores/chapters';
  import ChapterItem from './ChapterItem.vue';

  defineEmits(['reload']);
  defineProps<{
    chapters: Chapter[];
    chapterSearch: string | undefined;
    isParentClosed: boolean;
  }>();
</script>
