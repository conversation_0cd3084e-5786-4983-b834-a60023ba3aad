<script setup lang="ts">
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import { EllipsisOutlined } from '@ant-design/icons-vue';
  import { ref, type PropType } from 'vue';
  import { isAllowed } from '@/utils/directive/isAllowed';
  import { notification } from 'ant-design-vue';
  import { useProjectsStore, type Department } from '@/stores/projects';
  import Bruteforce from '@/components/shared/CloseSection.vue';
  import { storeToRefs } from 'pinia';
  const projectsStore = useProjectsStore();
  const ConfirmRef = ref<InstanceType<typeof ConfirmDlg> | null>(null);
  const emit = defineEmits(['reload']);
  const props = defineProps({
    department: {
      type: Object as PropType<Department>,
      required: true
    }
  });
  const { project_permision } = storeToRefs(projectsStore);
  const selectedDepartmentId = ref();
  const handleDeleteDepartment = async () => {
    selectedDepartmentId.value = props.department.project_department_id;
    if (!selectedDepartmentId.value) return;
    if (checkAdminPermission()) {
      const res = await projectsStore.checkIfCanCloseDepartment(selectedDepartmentId.value);
      if (res && res.count_of_missing_dependencies === 0) {
        if (
          await ConfirmRef.value?.open('Opravdu chcete uzavřít oddělení?', '', {
            color: 'error',
            notclosable: true,
            zIndex: 2400
          })
        ) {
          const res = await projectsStore.closeDepartment(
            props.department.project_department_id,
            false
          );
          if (res) emit('reload');
        }
        return;
      } else if (res && res.count_of_missing_dependencies > 0) {
        notification.error({
          message: 'Nelze uzavřít oddělení',
          description: `Oddělení nelze uzavřít, protože chybí uzavřít ${res.count_of_missing_dependencies} závislostí.`
        });
        switchModal();
      }
      return;
    } else if (!project_permision.value) {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nejste přiřazený k danému projektu.'
      });
      return;
    }
    const res = await projectsStore.checkIfCanCloseDepartment(selectedDepartmentId.value);
    if (res && res.count_of_missing_dependencies === 0) {
      if (
        await ConfirmRef.value?.open('Opravdu chcete uzavřít oddělení?', '', {
          color: 'error',
          notclosable: true,
          zIndex: 2400
        })
      ) {
        const res = await projectsStore.closeDepartment(
          props.department.project_department_id,
          false
        );
        if (res) emit('reload');
      }
    } else if (res && res.count_of_missing_dependencies > 0) {
      notification.error({
        message: 'Nelze uzavřít oddělení',
        description: `Oddělení nelze uzavřít, protože chybí uzavřít ${res.count_of_missing_dependencies} závislostí.`
      });
      switchModal();
    }
  };
  const checkAdminPermission = () => {
    return isAllowed(['edit_all']);
  };
  const handleOpenDepartment = async () => {
    selectedDepartmentId.value = props.department.project_department_id;
    if (!selectedDepartmentId.value) return;
    if (checkAdminPermission()) {
      const res = await projectsStore.reactivateDepartment(selectedDepartmentId.value);
      if (res) emit('reload');
      return;
    } else if (!project_permision.value) {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nejste přiřazený k danému projektu.'
      });
      return;
    }
    selectedDepartmentId.value = props.department.project_department_id;
    const res = await projectsStore.reactivateDepartment(selectedDepartmentId.value);
    if (res) emit('reload');
  };
  const openBruteForce = ref(false);
  const switchModal = () => {
    openBruteForce.value = !openBruteForce.value;
  };
  const closeAndReload = () => {
    openBruteForce.value = false;
    emit('reload');
  };

  defineExpose({
    handleOpenDepartment,
    handleDeleteDepartment
  });

  const handleClick = (project_type: string | undefined, action: () => void) => {
    if (checkProjectTypePermisionsForEdit(project_type)) {
      action();
    }
  };

  const havePermision = ref<boolean>(true);
  const missingPermison = ref<string>();

  const checkProjectTypePermisionsForEdit = (project_type: string | undefined) => {
    if (!project_type) return false;
    if (project_type === 'Analytické oddělení') {
      if (isAllowed(['edit_analytical_department'])) {
        havePermision.value = true;
        return true;
      } else {
        missingPermison.value = 'edit_analytical_department';
        havePermision.value = false;
      }
    } else if (project_type === 'Syntetické oddělení') {
      if (isAllowed(['edit_syntetic_department'])) {
        havePermision.value = true;
        return true;
      } else {
        missingPermison.value = 'edit_syntetic_department';
        havePermision.value = false;
      }
    } else if (project_type === 'Technologické oddělení') {
      if (isAllowed(['edit_technological_department'])) {
        havePermision.value = true;
        return true;
      } else {
        missingPermison.value = 'edit_technological_department';
        havePermision.value = false;
      }
    }
    if (!havePermision.value) {
      notification.error({
        message: 'Nedostatečné oprávnění',
        description: 'Nemáte oprávnění pro editaci oddělení: ' + missingPermison.value + '.'
      });
    }
    return false;
  };
</script>

<template>
  <v-menu>
    <template #activator="{ props: activatorProps }">
      <v-btn
        size="x-small"
        v-bind="activatorProps"
        variant="text"
        style="height: auto"
        @click.prevent.stop="activatorProps.isActive = true"
      >
        <EllipsisOutlined :style="{ fontSize: '28px' }" />
      </v-btn>
    </template>

    <v-list elevation="24" density="compact" class="py-0">
      <v-list-item v-if="department.isActive" :value="department.project_department_id + '_close'">
        <v-list-item-title
          @click.prevent="handleClick(department?.getName, handleDeleteDepartment)"
        >
          Uzavřít oddělení
        </v-list-item-title>
      </v-list-item>
      <v-list-item v-if="!department.isActive" :value="department.project_department_id + '_open'">
        <v-list-item-title @click.prevent="handleClick(department?.getName, handleOpenDepartment)">
          Otevřít oddělení
        </v-list-item-title>
      </v-list-item>
    </v-list>
  </v-menu>
  <ConfirmDlg ref="ConfirmRef" />
  <Bruteforce
    v-if="openBruteForce"
    :id="selectedDepartmentId"
    v-model:show="openBruteForce"
    :what-to-close="'department'"
    @update="closeAndReload"
  />
</template>
