<script setup lang="ts">
  import type { BreadcrumbI } from '@/components/shared/BaseBreadcrumb.vue';
  import CustomChemicalTable from '@/components/shared/chemicalTable/CustomChemicalTable.vue';
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import EditorTextarea from '@/components/shared/EditorTextarea.vue';
  import FileSection from '@/components/shared/file/FileSection.vue';
  import FileUploader from '@/components/shared/file/FileUploader.vue';
  import TopPageBreadcrumb from '@/components/shared/TopPageBreadcrumb.vue';
  import UiParentCard from '@/components/shared/UiParentCard.vue';
  import type { NewChemicalDataI, ChemicalI } from '@/stores/chemicals';
  import { AttemptTemplateStatus, useAttemptTemplates } from '@/stores/attemptTemplates';
  import { File, useFilesStore } from '@/stores/files';
  import { useProjectsStore } from '@/stores/projects';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { toolbar } from '@/utils/SetupTinyMCE';
  import { storeToRefs } from 'pinia';
  import type { EditorManager, RawEditorOptions } from 'tinymce';
  import { computed, onMounted, ref, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';

  const forceRefreshKey = ref(0);

  const ConfirmRef = ref<undefined | typeof ConfirmDlg>(undefined);
  const ChemicalTableRef = ref<undefined | typeof CustomChemicalTable>(undefined);

  const route = useRoute();
  const router = useRouter();
  const baseDataLoaded = ref(false);
  const filesStore = useFilesStore();
  const projectsStore = useProjectsStore();
  const attemptTemplatesStore = useAttemptTemplates();

  const { project, department } = storeToRefs(projectsStore);
  const { loading: fileUploading } = storeToRefs(filesStore);
  const { attemptTemplate, modalOptions, loading } = storeToRefs(attemptTemplatesStore);

  const project_id = computed(() => route.params.project_id as string);
  const projectId = computed(() => parseInt(project_id.value));

  const attempt_template_id = computed(() => route.params.attempt_template_id as string);
  const attemptTemplateId = computed(() => parseInt(attempt_template_id.value));

  const project_department_id = computed(() => route.params.project_department_id as string);
  const projectDepartmentId = computed(() => parseInt(project_department_id.value));

  const loadExecute = async () => {
    baseDataLoaded.value = false;

    if (projectId.value) {
      if (project.value) {
        if (project.value.project_id !== projectId.value) {
          await projectsStore.getProjectById(projectId.value);
        }
      } else {
        await projectsStore.getProjectById(projectId.value);
      }
    }

    if (projectDepartmentId.value) {
      if (department.value) {
        if (department.value.project_department_id !== projectDepartmentId.value) {
          await projectsStore.getDepartmentById(projectDepartmentId.value);
        }
      } else {
        await projectsStore.getDepartmentById(projectDepartmentId.value);
      }
    }

    if (attemptTemplateId.value) {
      if (attemptTemplate.value) {
        if (attemptTemplate.value.attempt_template_id !== attemptTemplateId.value) {
          const _attemptTemplate = await attemptTemplatesStore.getAttemptTemplate(
            attemptTemplateId.value
          );
          if (_attemptTemplate) {
            attemptTemplate.value = _attemptTemplate;
          }
        }
      } else {
        const _attemptTemplate = await attemptTemplatesStore.getAttemptTemplate(
          attemptTemplateId.value
        );
        if (_attemptTemplate) {
          attemptTemplate.value = _attemptTemplate;
        }
      }
    }

    if (project.value && department.value && attemptTemplate.value) {
      await attemptTemplatesStore.showPreviewModal(attemptTemplate.value.attempt_template_id);
      if (modalOptions.value?.updateData?.reaction_scheme_file_id) {
        getReactionSchemeFileId(modalOptions.value.updateData.reaction_scheme_file_id);
      }
      forceRefreshKey.value++;
      baseDataLoaded.value = true;
    } else {
      if (projectDepartmentId.value) {
        router.push({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value?.project_id,
            project_department_id: projectDepartmentId.value
          }
        });
      } else if (project.value) {
        router.push({ name: 'ProjectDetail', params: { project_id: project.value.project_id } });
      }
      router.push({ name: 'ListOfProjects' });
    }
  };

  onMounted(async () => {
    await loadExecute();
  });

  watch([project_id, project_department_id, attempt_template_id], () => {
    loadExecute();
  });

  const validateInput = (concentration: number | string) => {
    const input = concentration.toString();
    if (input) {
      if (/^\d+([.,]\d+)?$/.test(input)) {
        return parseFloat(input.replace(',', '.'));
      } else {
        return -1;
      }
    } else {
      return -1;
    }
  };

  const CreateSyntheticTemplateForm = ref();
  async function submitFormToValidate() {
    if (CreateSyntheticTemplateForm.value.isValid) {
      const chemicals: ChemicalI[] =
        ChemicalTableRef.value?.getAllChemicalInBaseUnitToUpdateModel();
      if (modalOptions.value?.updateData && chemicals) {
        modalOptions.value.updateData.chemicals = chemicals;
        modalOptions.value.updateData.chemicals.forEach((chemical) => {
          if (chemical.concentration)
            chemical.concentration = validateInput(chemical.concentration);
        });
      }
      const res = await attemptTemplatesStore.updateAttemptTemplate();
      if (res) {
        loadExecute();
      }
    }
  }

  const breadcrumbItems = computed<BreadcrumbI[]>(() => {
    return [
      {
        title: 'Projekty',
        disabled: false,
        href: router.resolve({ name: 'Projects' }).href
      },
      {
        title: project.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'ProjectDetail',
          params: { project_id: project.value?.project_id }
        }).href
      },
      {
        title: department.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id
          }
        }).href
      },
      {
        title: 'Šablony pro technologické pokusy',
        disabled: false,
        href: router.resolve({ name: 'AttemptTemplates', params: { project_id: project_id.value } })
          .href
      },
      {
        title: 'Editace šablony',
        disabled: true,
        href: '#'
      }
    ];
  });

  const reloadPageWithoutSave = () => {
    loadExecute();
  };

  const selectedReactionSchemeFile = ref<File[]>([]);
  const selectedReactionSchemeFileUrl = ref<string | undefined>(undefined);
  const getReactionSchemeFileId = async (fileId: number) => {
    selectedReactionSchemeFileUrl.value = await filesStore.getFileUrl(fileId);
  };

  watch(selectedReactionSchemeFile, (files) => {
    if (modalOptions.value?.updateData) {
      modalOptions.value.updateData.reaction_scheme_file_id = files[0].file_id;
    }
  });

  watch(
    () => modalOptions.value?.updateData?.reaction_scheme_file_id,
    async (fileId) => {
      if (fileId) {
        getReactionSchemeFileId(fileId);
      }
    }
  );

  const isUpdateDataSameAsOriginal = computed(() => {
    if (modalOptions.value?.updateData && attemptTemplate.value && ChemicalTableRef.value) {
      const currentChemicals = ChemicalTableRef.value?.getAllChemicalInBaseUnitToUpdateModel();

      const areChemicalsMatching = currentChemicals.every(
        (chem: NewChemicalDataI, index: number) => {
          const matchingChem = attemptTemplate.value?.chemicals[index];
          if (!matchingChem) {
            return false;
          }

          const isConcentrationEqual = chem.concentration == matchingChem.concentration;

          const isDensityEqual = chem.density == matchingChem.density;

          const isEquivalentEqual = chem.equivalent == matchingChem.equivalent;

          const isGramsEqual = chem.grams == matchingChem.grams;

          const isMolarMassEqual = chem.molar_mass == matchingChem.molar_mass;

          const isMolesEqual = chem.moles == matchingChem.moles;

          const isNameEqual = chem.name == matchingChem.name;

          const isNotesEqual = chem.notes == matchingChem.notes;

          const isTypeEqual = chem.type == matchingChem.type;

          const isVolumeMlEqual = chem.volume_ml == matchingChem.volume_ml;

          return (
            isConcentrationEqual &&
            isDensityEqual &&
            isEquivalentEqual &&
            isGramsEqual &&
            isMolarMassEqual &&
            isMolesEqual &&
            isNameEqual &&
            isNotesEqual &&
            isTypeEqual &&
            isVolumeMlEqual
          );
        }
      );

      const isMatching =
        areChemicalsMatching &&
        modalOptions.value.updateData.template_name === attemptTemplate.value.template_name &&
        (modalOptions.value.updateData.reaction_scheme_file_id ?? null) ===
          (attemptTemplate.value.reaction_scheme_file_id ?? null) &&
        modalOptions.value.updateData.reaction_procedure ===
          attemptTemplate.value.reaction_procedure &&
        modalOptions.value.updateData.status === attemptTemplate.value.status &&
        modalOptions.value.updateData.apparatus === attemptTemplate.value.apparatus &&
        modalOptions.value.updateData.batch_description === attemptTemplate.value.batch_description;

      return isMatching;
    }
    return false;
  });
</script>
<template>
  <TopPageBreadcrumb title="Editace šablony" :_breadcrumbs="breadcrumbItems" />
  <v-row class="justify-content-end">
    <v-col cols="12">
      <UiParentCard class="pa-0" :loading="loading || fileUploading">
        <template #action>
          <v-row justify="space-between" class="align-center">
            <v-col cols="12">
              <div class="d-flex gap-2 justify-end flex-wrap">
                <v-btn
                  variant="flat"
                  color="error"
                  :disabled="!baseDataLoaded"
                  @click.prevent="
                    async () => {
                      if (!isUpdateDataSameAsOriginal) {
                        if (
                          await ConfirmRef?.open(
                            'Potvrzení',
                            'Opravdu chcete zrušit změny a načíst data ze serveru?',
                            {
                              color: 'error',
                              notclosable: true,
                              zIndex: 2400
                            }
                          )
                        ) {
                          reloadPageWithoutSave();
                        }
                      } else {
                        router.push(
                          router.resolve({
                            name: 'AttemptTemplates',
                            params: { project_id: project_id }
                          })
                        );
                      }
                    }
                  "
                >
                  {{ isUpdateDataSameAsOriginal ? 'Zpět' : 'Zrušit' }}
                </v-btn>

                <v-btn
                  v-if="project?.project_id"
                  variant="flat"
                  color="primary"
                  :disabled="!baseDataLoaded"
                  type="submit"
                  form="template-add-form"
                >
                  Uložit šablonu
                </v-btn>
              </div>
            </v-col>
          </v-row>
        </template>
        <v-form
          v-if="modalOptions?.updateData && modalOptions?.baseData !== undefined"
          id="template-add-form"
          ref="CreateSyntheticTemplateForm"
          @submit.prevent="submitFormToValidate"
        >
          <v-row v-if="modalOptions?.updateData">
            <v-col cols="12" md="6">
              <v-label class="mb-2">Název</v-label>
              <v-text-field
                v-model="modalOptions.updateData.template_name"
                :rules="itemRequiredRule"
                single-line
                placeholder="Zadejte název"
                hide-details="auto"
                variant="outlined"
                rounded="sm"
              ></v-text-field>
            </v-col>

            <v-col cols="12" md="6">
              <v-label class="mb-2">Popis šarže</v-label>
              <v-text-field
                v-model="modalOptions.updateData.batch_description"
                :rules="itemRequiredRule"
                single-line
                placeholder="Zadejte název"
                hide-details="auto"
                variant="outlined"
                rounded="sm"
              ></v-text-field>
            </v-col>

            <v-col cols="12">
              <v-label class="mb-2">Schéma reakce</v-label>
              <div v-if="modalOptions.updateData.reaction_scheme_file_id" class="image-container">
                <v-img
                  :src="selectedReactionSchemeFileUrl"
                  :height="400"
                  aspect-ratio="16/9"
                  contain
                  rounded
                  class="image"
                ></v-img>
                <v-btn
                  absolute
                  top
                  right
                  color="error"
                  class="floating-button"
                  @click="
                    async () => {
                      if (modalOptions?.updateData)
                        modalOptions.updateData.reaction_scheme_file_id = null;
                    }
                  "
                >
                  <v-icon>mdi-close</v-icon>
                </v-btn>
              </div>

              <FileUploader
                v-else
                v-model="selectedReactionSchemeFile"
                :process-save-file="
                  async (file_id: number) => {
                    if (modalOptions?.updateData)
                      modalOptions.updateData.reaction_scheme_file_id = file_id;
                  }
                "
                :process-remove-file="
                  async (file_id: number) => {
                    if (modalOptions?.updateData)
                      modalOptions.updateData.reaction_scheme_file_id = null;
                  }
                "
                :uppy-options="{
                  height: 250
                }"
              />
            </v-col>

            <v-col cols="12">
              <v-label class="mb-2">Aparatura</v-label>
              <v-text-field
                v-model="modalOptions.updateData.apparatus"
                single-line
                placeholder="Zadejte aparaturu"
                hide-details="auto"
                variant="outlined"
                rounded="sm"
              ></v-text-field>
            </v-col>

            <v-col cols="12">
              <v-label class="mb-2">Tabulka chemikálií</v-label>
              <CustomChemicalTable
                v-if="modalOptions.updateData.chemicals"
                :key="forceRefreshKey"
                ref="ChemicalTableRef"
                @save-order="submitFormToValidate"
                :init-value="modalOptions.baseData.chemicals"
              />
            </v-col>
            <v-col cols="12">
              <v-label class="mb-2">Postup reakce</v-label>
              <EditorTextarea
                v-model="modalOptions.updateData.reaction_procedure"
                :show-edit-button="false"
                :config="
                  {
                    statusbar: true,
                    resize: true,
                    min_height: 200,
                    plugins: 'lists link table autoresize',
                    toolbar:
                      'table tabledelete | tableprops tablerowprops tablecellprops | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol'
                  } as EditorManager & RawEditorOptions
                "
              ></EditorTextarea>
            </v-col>

            <v-col cols="12">
              <v-label class="mb-2">Status</v-label>
              <v-autocomplete
                v-model="modalOptions.updateData.status"
                :items="[
                  { value: AttemptTemplateStatus.ACTIVE, title: 'Aktivní' },
                  { value: AttemptTemplateStatus.DELETED, title: 'Archivováno' }
                ]"
                rounded="sm"
                color="primary"
                single-line
                hide-details
                variant="outlined"
                :no-data-text="'Žádná další políčka'"
              ></v-autocomplete>
            </v-col>

            <v-col v-if="modalOptions.baseData" cols="12">
              <v-label class="mb-2">Přílohy</v-label>
              <FileUploader
                v-model="modalOptions.baseData.files"
                :process-save-file="attemptTemplatesStore.addFileToAttemptTemplate"
                :process-remove-file="
                  async (file_id: number) => {
                    const res = await attemptTemplatesStore.deleteFileFromAttemptTemplate(file_id);
                    if (res) loadExecute();

                    return res;
                  }
                "
                :uppy-options="{
                  height: 250
                }"
              />
            </v-col>
          </v-row>
          <FileSection :files="modalOptions.baseData?.files ?? []" :file-search="undefined" />
        </v-form>
      </UiParentCard>
    </v-col>
    <ConfirmDlg ref="ConfirmRef" />
  </v-row>
</template>
