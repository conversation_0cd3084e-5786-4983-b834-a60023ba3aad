<script setup lang="ts">
  import type { EditorManager, RawEditorOptions } from 'tinymce';
  import type { BreadcrumbI } from '@/components/shared/BaseBreadcrumb.vue';
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import EditorTextarea from '@/components/shared/EditorTextarea.vue';
  import FileUploader from '@/components/shared/file/FileUploader.vue';
  import TopPageBreadcrumb from '@/components/shared/TopPageBreadcrumb.vue';
  import UiParentCard from '@/components/shared/UiParentCard.vue';
  import { useExperimentTemplates } from '@/stores/experimentTemplates';
  import { File, useFilesStore } from '@/stores/files';
  import { useProjectsStore } from '@/stores/projects';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { storeToRefs } from 'pinia';
  import { computed, onMounted, ref, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import FileSection from '@/components/shared/file/FileSection.vue';
  import { ChemicalType, type NewChemicalDataI } from '@/stores/chemicals';
  import CustomChemicalTable from '@/components/shared/chemicalTable/CustomChemicalTable.vue';
  const ChemicalTableRef = ref<undefined | typeof CustomChemicalTable>(undefined);
  const ConfirmRef = ref<undefined | typeof ConfirmDlg>(undefined);
  const forceRefreshKey = ref(0);
  const route = useRoute();
  const router = useRouter();
  const baseDataLoaded = ref(false);
  const filesStore = useFilesStore();
  const projectsStore = useProjectsStore();

  const { project, department } = storeToRefs(projectsStore);
  const { loading: fileUploading } = storeToRefs(filesStore);
  const syntheticTemplatesStore = useExperimentTemplates();

  const project_id = computed(() => route.params.project_id as string);
  const projectId = computed(() => parseInt(project_id.value));

  const project_department_id = computed(() => route.params.project_department_id as string);
  const projectDepartmentId = computed(() => parseInt(project_department_id.value));

  const loadExecute = async () => {
    baseDataLoaded.value = false;

    if (projectId.value) {
      if (project.value) {
        if (project.value.project_id !== projectId.value) {
          await projectsStore.getProjectById(projectId.value);
        }
      } else {
        await projectsStore.getProjectById(projectId.value);
      }
    }

    if (projectDepartmentId.value) {
      if (department.value) {
        if (department.value.project_department_id !== projectDepartmentId.value) {
          await projectsStore.getDepartmentById(projectDepartmentId.value);
        }
      } else {
        await projectsStore.getDepartmentById(projectDepartmentId.value);
      }
    }

    if (project.value && department.value) {
      syntheticTemplatesStore.showNewExperimentTemplateModal(project.value.project_id);
      baseDataLoaded.value = true;
      forceRefreshKey.value++;
    } else {
      if (project.value) {
        router.push({ name: 'ProjectDetail', params: { project_id: project.value.project_id } });
      }
      router.push({ name: 'ListOfProjects' });
    }
    if (modalOptions.value?.newData) {
      modalOptions.value.newData.chemicals
        .filter((ch) => ch.type === ChemicalType.CHEMICAL)
        .map(
          (ch) =>
            ({
              name: ch.name,
              density: ch.density,
              molar_mass: ch.molar_mass,
              notes: ch.notes,
              grams: ch.grams,
              moles: ch.moles,
              equivalent: ch.equivalent,
              volume_ml: ch.volume_ml,
              concentration: ch.concentration,
              type: ch.type
            }) as NewChemicalDataI
        );
    }
  };

  onMounted(async () => {
    await loadExecute();
  });

  watch([project_id], () => {
    loadExecute();
  });

  const { modalOptions, loading } = storeToRefs(syntheticTemplatesStore);
  const validateInput = (concentration: number | string) => {
    const input = concentration.toString();
    if (input) {
      if (/^\d+([.,]\d+)?$/.test(input)) {
        return parseFloat(input.replace(',', '.'));
      } else {
        return -1;
      }
    } else {
      return -1;
    }
  };
  const CreateSyntheticTemplateForm = ref();
  async function submitFormToValidate() {
    if (CreateSyntheticTemplateForm.value.isValid && modalOptions.value) {
      const chemicals: NewChemicalDataI[] =
        ChemicalTableRef.value?.getAllChemicalInBaseUnitToUpdateModel();
      if (modalOptions.value?.newData && chemicals) {
        modalOptions.value.newData.chemicals = chemicals;
        modalOptions.value.newData.chemicals.forEach((chemical) => {
          if (chemical.concentration)
            chemical.concentration = validateInput(chemical.concentration);
        });
      }
      if (modalOptions.value?.newData) {
        modalOptions.value.newData.files_ids = modalOptions.value.newData.files.map(
          (file: File) => file.file_id
        );
      }

      const res = await syntheticTemplatesStore.createExperimentTemplate();
      if (res)
        router.push({ name: 'ExperimentTemplates', params: { project_id: project_id.value } });
    }
  }

  const breadcrumbItems = computed<BreadcrumbI[]>(() => {
    return [
      {
        title: 'Projekty',
        disabled: false,
        href: router.resolve({ name: 'Projects' }).href
      },
      {
        title: project.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'ProjectDetail',
          params: { project_id: project.value?.project_id }
        }).href
      },
      {
        title: department.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id
          }
        }).href
      },
      {
        title: 'Šablony pro syntetické oddělení',
        disabled: false,
        href: router.resolve({
          name: 'ExperimentTemplates',
          params: { project_id: project_id.value }
        }).href
      },
      {
        title: 'Nová šablona',
        disabled: true,
        href: '#'
      }
    ];
  });

  const selectedReactionSchemeFile = ref<File[]>([]);
  const selectedReactionSchemeFileUrl = ref<string | undefined>(undefined);
  const getReactionSchemeFileId = async (fileId: number) => {
    selectedReactionSchemeFileUrl.value = await filesStore.getFileUrl(fileId);
  };

  watch(selectedReactionSchemeFile, (files) => {
    if (modalOptions.value?.newData) {
      modalOptions.value.newData.reaction_scheme_file_id = files[0].file_id;
    }
  });

  watch(
    () => modalOptions.value?.newData?.reaction_scheme_file_id,
    async (fileId) => {
      if (fileId) {
        getReactionSchemeFileId(fileId);
      }
    }
  );

  const getAllFiles = async (file_id: number) => {
    if (modalOptions.value && modalOptions.value.newData && modalOptions.value.newData.files) {
      const foundFile = modalOptions.value.newData.files.find((f) => f.file_id === file_id);

      if (foundFile && modalOptions.value.newData.files.indexOf(foundFile) > -1) {
        modalOptions.value.newData.files.splice(
          modalOptions.value.newData.files.indexOf(foundFile),
          1
        );
      }
    }
  };
  watch(
    () => modalOptions.value?.newData,
    (newData) => {
      if (newData && modalOptions.value?.isCreating) {
        localStorage.setItem('experimentTemplatePageStore', JSON.stringify(newData));
      }
    },
    { deep: true }
  );
</script>
<template>
  <TopPageBreadcrumb title="Nová šablona" :_breadcrumbs="breadcrumbItems" />
  <v-row class="justify-content-end">
    <v-col cols="12">
      <UiParentCard class="pa-0" :loading="loading || fileUploading">
        <template #action>
          <v-row justify="space-between" class="align-center">
            <v-col cols="12">
              <div class="d-flex gap-2 justify-end flex-wrap">
                <v-btn
                  v-if="project?.project_id"
                  variant="flat"
                  color="error"
                  :disabled="!baseDataLoaded"
                  @click="
                    router.push(
                      router.resolve({
                        name: 'ExperimentTemplates',
                        params: { project_id: project_id }
                      })
                    )
                  "
                >
                  Zpět
                </v-btn>

                <v-btn
                  v-if="project?.project_id"
                  variant="flat"
                  color="primary"
                  :disabled="!baseDataLoaded"
                  type="submit"
                  form="template-add-form"
                >
                  Uložit šablonu
                </v-btn>
              </div>
            </v-col>
          </v-row>
        </template>
        <v-form
          v-if="modalOptions?.newData"
          id="template-add-form"
          ref="CreateSyntheticTemplateForm"
          @submit.prevent="submitFormToValidate"
        >
          <v-row>
            <v-col cols="12">
              <v-label class="mb-2">Název</v-label>
              <v-text-field
                v-model="modalOptions.newData.template_name"
                :rules="itemRequiredRule"
                single-line
                placeholder="Zadejte název"
                hide-details="auto"
                variant="outlined"
                rounded="sm"
              ></v-text-field>
            </v-col>
            <v-col cols="12">
              <v-label class="mb-2">Schéma reakce</v-label>
              <div v-if="modalOptions.newData.reaction_scheme_file_id" class="image-container">
                <v-img
                  :src="selectedReactionSchemeFileUrl"
                  :height="400"
                  aspect-ratio="16/9"
                  contain
                  rounded
                  class="image"
                ></v-img>
                <v-btn
                  absolute
                  top
                  right
                  color="error"
                  class="floating-button"
                  @click="
                    async () => {
                      if (modalOptions?.newData)
                        modalOptions.newData.reaction_scheme_file_id = null;
                    }
                  "
                >
                  <v-icon>mdi-close</v-icon>
                </v-btn>
              </div>

              <FileUploader
                v-else
                v-model="selectedReactionSchemeFile"
                :process-save-file="
                  async (file_id: number) => {
                    if (modalOptions?.newData)
                      modalOptions.newData.reaction_scheme_file_id = file_id;
                  }
                "
                :process-remove-file="
                  async (file_id: number) => {
                    if (modalOptions?.newData) modalOptions.newData.reaction_scheme_file_id = null;
                  }
                "
                :uppy-options="{
                  height: 250
                }"
              />
            </v-col>

            <v-col cols="12" class="mb-4">
              <v-label class="mb-2">Tabulka chemikálií</v-label>
              <CustomChemicalTable
                v-if="modalOptions.newData.chemicals"
                :key="forceRefreshKey"
                ref="ChemicalTableRef"
                @save-order="submitFormToValidate"
              />
            </v-col>

            <v-col cols="12">
              <v-label class="mb-2">Postup reakce</v-label>
              <EditorTextarea
                v-model="modalOptions.newData.reaction_procedure"
                :show-edit-button="false"
                :config="
                  {
                    statusbar: true,
                    resize: true
                  } as EditorManager & RawEditorOptions
                "
              ></EditorTextarea>
            </v-col>

            <v-col cols="12">
              <v-label class="mb-2">Přílohy</v-label>
              <FileUploader v-model="modalOptions.newData.files" />
            </v-col>
            <v-col cols="12">
              <FileSection
                :files="modalOptions.newData.files"
                :custom-remove-file="true"
                @reload="getAllFiles"
                @file-remove="
                  async (file_id: number) => {
                    const res = await filesStore.deleteFile(file_id);
                    if (res) {
                      getAllFiles(file_id);
                    }
                  }
                "
              />
            </v-col>
          </v-row>
        </v-form>
      </UiParentCard>
    </v-col>

    <ConfirmDlg ref="ConfirmRef" />
  </v-row>
</template>
