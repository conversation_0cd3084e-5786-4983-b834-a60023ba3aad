<script setup lang="ts">
  import type { BreadcrumbI } from '@/components/shared/BaseBreadcrumb.vue';
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import CustomTable from '@/components/shared/CustomTable.vue';
  import TopPageBreadcrumb from '@/components/shared/TopPageBreadcrumb.vue';
  import LoaderWrapper from '@/layouts/dashboard/LoaderWrapper.vue';
  import { ExperimentTemplateStatus, useExperimentTemplates } from '@/stores/experimentTemplates';
  import { useProjectsStore, type GetAllOptions } from '@/stores/projects';
  import { EditOutlined, EyeOutlined, SearchOutlined } from '@ant-design/icons-vue';
  import { useDebounceFn } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import { computed, onMounted, reactive, ref, watch } from 'vue';
  import { onBeforeRouteUpdate, useRoute, useRouter } from 'vue-router';
  import type { Header } from 'vue3-easy-data-table';

  const ConfirmRef = ref<undefined | typeof ConfirmDlg>(undefined);

  const route = useRoute();
  const router = useRouter();
  const baseDataLoaded = ref(false);

  const projectsStore = useProjectsStore();
  const { project, department } = storeToRefs(projectsStore);

  const experimentTemplatesStore = useExperimentTemplates();
  const { loading, experimentTemplates } = storeToRefs(experimentTemplatesStore);
  experimentTemplatesStore.showExperimentTemplateModal = false;

  const loadFromServer = async () => {
    const res = await experimentTemplatesStore.getExperimentTemplates(
      true,
      optionsExperimentTemplates
    );
    optionsExperimentTemplates.totalItems = res.totalItems;
  };

  const debouncedSearch = useDebounceFn(() => {
    if (loading.value === false) loadFromServer();
  }, 350);

  const project_id = computed(() => route.params.project_id as string);
  const projectId = computed(() => parseInt(project_id.value));

  const project_department_id = computed(() => route.params.project_department_id as string);
  const projectDepartmentId = computed(() => parseInt(project_department_id.value));

  const loadExecute = async () => {
    baseDataLoaded.value = false;

    if (projectId.value) {
      if (project.value) {
        if (project.value.project_id !== projectId.value) {
          await projectsStore.getProjectById(projectId.value);
        }
      } else {
        await projectsStore.getProjectById(projectId.value);
      }
    }

    if (projectDepartmentId.value) {
      if (department.value) {
        if (department.value.project_department_id !== projectDepartmentId.value) {
          await projectsStore.getDepartmentById(projectDepartmentId.value);
        }
      } else {
        await projectsStore.getDepartmentById(projectDepartmentId.value);
      }
    }

    if (project.value && department.value) {
      optionsExperimentTemplates.fixedFilterOptions = [
        {
          column: 'project_id',
          value: project.value?.project_id ?? null
        }
      ];

      debouncedSearch();

      baseDataLoaded.value = true;
    } else {
      if (project.value) {
        router.push({ name: 'ProjectDetail', params: { project_id: project.value.project_id } });
      }
      router.push({ name: 'ListOfProjects' });
    }
  };

  onMounted(async () => {
    await loadExecute();
    experimentTemplatesStore.showExperimentTemplateModal = false;
  });

  const optionsExperimentTemplates = reactive<GetAllOptions>({
    search: '',
    totalItems: undefined,
    options: {
      page: 1,
      rowsPerPage: 100,
      sortBy: ['status', 'updated_at'],
      sortType: ['asc']
    },
    search_columns: ['template_name'],
    fixedFilterOptions: [
      {
        column: 'status',
        value: ExperimentTemplateStatus.ACTIVE
      }
    ]
  });

  watch([project_id], () => {
    loadExecute();
  });

  watch(
    () => [
      optionsExperimentTemplates.search,
      optionsExperimentTemplates.options?.sortBy,
      optionsExperimentTemplates.options?.sortType
    ],
    () => {
      if (loading.value === false && optionsExperimentTemplates.search !== undefined)
        debouncedSearch();
    },
    { deep: true }
  );

  onBeforeRouteUpdate((to, from, next) => {
    if (to.path === from.path && to.query !== from.query) {
      experimentTemplatesStore.setParamsFromLocation();
      debouncedSearch();
    }

    next();
  });

  const headers: Header[] = [
    { text: 'ID', value: 'experiment_template_id', sortable: true },
    { text: 'Název', value: 'template_name', sortable: true },
    { text: 'Stav', value: 'status', sortable: true },
    { text: 'Akce', value: 'action' }
  ];

  const redirectToAddExperimentTemplate = (project_id: number) => {
    return router.resolve({ name: 'ExperimentNewTemplate', params: { project_id: project_id } });
  };

  const redirectToEditExperimentTemplate = (experiment_template_id: number) => {
    return router.resolve({
      name: 'ExperimentEditTemplate',
      params: { experiment_template_id: experiment_template_id }
    });
  };

  const redirectToPreviewExperimentTemplate = (experiment_template_id: number) => {
    return router.resolve({
      name: 'ExperimentPreviewTemplate',
      params: { experiment_template_id: experiment_template_id }
    });
  };

  const breadcrumbItems = computed<BreadcrumbI[]>(() => {
    return [
      {
        title: 'Projekty',
        disabled: false,
        href: router.resolve({ name: 'Projects' }).href
      },
      {
        title: project.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'ProjectDetail',
          params: { project_id: project.value?.project_id }
        }).href
      },
      {
        title: department.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id
          }
        }).href
      },
      {
        title: 'Šablony pro syntetické oddělení',
        disabled: true,
        href: router.resolve({
          name: 'ExperimentTemplates',
          params: { project_id: project_id.value }
        }).href
      }
    ];
  });

  const removeExperimentTemplate = async (experiment_template_id: number) => {
    const res = await experimentTemplatesStore.deleteExperimentTemplate(experiment_template_id);
    if (res) {
      await loadExecute();
    }
  };
</script>
<template>
  <LoaderWrapper v-if="!project" />
  <template v-else>
    <TopPageBreadcrumb title="Šablony pro syntetické oddělení" :_breadcrumbs="breadcrumbItems" />
    <v-row>
      <v-col cols="12" md="12">
        <v-card elevation="0" variant="outlined" class="withbg pageSize">
          <v-card-item>
            <v-row justify="space-between" class="align-center">
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="optionsExperimentTemplates.search"
                  type="text"
                  variant="outlined"
                  persistent-placeholder
                  placeholder="Hledat šablonu"
                  hide-details
                >
                  <template #prepend-inner>
                    <SearchOutlined :style="{ fontSize: '14px' }" />
                  </template>
                </v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <div class="d-flex gap-2 justify-end flex-wrap">
                  <v-btn
                    v-if="project?.project_id"
                    variant="flat"
                    color="primary"
                    :disabled="!baseDataLoaded"
                    :to="redirectToAddExperimentTemplate(project.project_id)"
                  >
                    Přidat šablonu
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </v-card-item>
          <v-divider></v-divider>
          <v-card-text>
            <CustomTable
              v-model:server-options="optionsExperimentTemplates.options"
              :server-items-length="optionsExperimentTemplates.totalItems"
              :loading="loading || !baseDataLoaded"
              :headers="headers"
              :items="experimentTemplates"
              multi-sort
            >
              <template #item-status="{ status }">
                <v-chip
                  v-if="status === ExperimentTemplateStatus.ACTIVE"
                  color="success"
                  size="small"
                  label
                >
                  Aktivní
                </v-chip>
                <v-chip
                  v-if="status === ExperimentTemplateStatus.DELETED"
                  color="error"
                  size="small"
                  label
                >
                  Archivováno
                </v-chip>
              </template>

              <template #item-action="{ experiment_template_id }">
                <div class="operation-wrapper">
                  <v-btn
                    icon
                    color="secondary"
                    variant="text"
                    rounded="sm"
                    :to="redirectToPreviewExperimentTemplate(experiment_template_id)"
                  >
                    <EyeOutlined />
                  </v-btn>
                  <v-btn
                    icon
                    color="primary"
                    variant="text"
                    rounded="sm"
                    :to="redirectToEditExperimentTemplate(experiment_template_id)"
                  >
                    <EditOutlined />
                  </v-btn>
                  <v-btn
                    icon
                    variant="text"
                    color="error"
                    @click.prevent.stop="
                      async () => {
                        if (
                          await ConfirmRef?.open('Potvrzení', 'Opravdu chcete smazat šablonu?', {
                            color: 'error',
                            notclosable: true,
                            zIndex: 1011
                          })
                        ) {
                          removeExperimentTemplate(experiment_template_id);
                        }
                      }
                    "
                  >
                    <v-icon :icon="'mdi-delete'"></v-icon>
                  </v-btn>
                </div>
              </template>
            </CustomTable>
          </v-card-text>
        </v-card>
      </v-col>

      <ConfirmDlg ref="ConfirmRef" />
      <!-- <ExperimentTemplateModal v-model:show="experimentTemplatesStore.showExperimentTemplateModal" /> -->
    </v-row>
  </template>
</template>
