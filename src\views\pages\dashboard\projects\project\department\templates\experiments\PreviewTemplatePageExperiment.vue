<script setup lang="ts">
  import type { BreadcrumbI } from '@/components/shared/BaseBreadcrumb.vue';
  import CustomChemicalTable from '@/components/shared/chemicalTable/CustomChemicalTable.vue';
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import EditorTextarea from '@/components/shared/EditorTextarea.vue';
  import FileSection from '@/components/shared/file/FileSection.vue';
  import FileUploader from '@/components/shared/file/FileUploader.vue';
  import TopPageBreadcrumb from '@/components/shared/TopPageBreadcrumb.vue';
  import UiParentCard from '@/components/shared/UiParentCard.vue';
  import type { NewChemicalDataI } from '@/stores/chemicals';
  import { ExperimentTemplateStatus, useExperimentTemplates } from '@/stores/experimentTemplates';
  import { File, useFilesStore } from '@/stores/files';
  import { useProjectsStore } from '@/stores/projects';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { toolbar } from '@/utils/SetupTinyMCE';
  import { storeToRefs } from 'pinia';
  import type { EditorManager, RawEditorOptions } from 'tinymce';
  import { computed, onMounted, ref, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';

  const ConfirmRef = ref<undefined | typeof ConfirmDlg>(undefined);
  const ChemicalTableRef = ref<undefined | typeof CustomChemicalTable>(undefined);

  const forceRefreshKey = ref(0);

  const route = useRoute();
  const router = useRouter();
  const baseDataLoaded = ref(false);
  const filesStore = useFilesStore();
  const projectsStore = useProjectsStore();
  const experimentTemplatesStore = useExperimentTemplates();

  const { project, department } = storeToRefs(projectsStore);
  const { loading: fileUploading } = storeToRefs(filesStore);
  const { experimentTemplate, modalOptions, loading } = storeToRefs(experimentTemplatesStore);

  const project_id = computed(() => route.params.project_id as string);
  const projectId = computed(() => parseInt(project_id.value));

  const experiment_template_id = computed(() => route.params.experiment_template_id as string);
  const experimentTemplateId = computed(() => parseInt(experiment_template_id.value));

  const project_department_id = computed(() => route.params.project_department_id as string);
  const projectDepartmentId = computed(() => parseInt(project_department_id.value));

  const loadExecute = async () => {
    baseDataLoaded.value = false;

    if (projectId.value) {
      if (project.value) {
        if (project.value.project_id !== projectId.value) {
          await projectsStore.getProjectById(projectId.value);
        }
      } else {
        await projectsStore.getProjectById(projectId.value);
      }
    }

    if (projectDepartmentId.value) {
      if (department.value) {
        if (department.value.project_department_id !== projectDepartmentId.value) {
          await projectsStore.getDepartmentById(projectDepartmentId.value);
        }
      } else {
        await projectsStore.getDepartmentById(projectDepartmentId.value);
      }
    }

    if (experimentTemplateId.value) {
      if (experimentTemplate.value) {
        if (experimentTemplate.value.experiment_template_id !== experimentTemplateId.value) {
          const _experimentTemplate = await experimentTemplatesStore.getExperimentTemplate(
            experimentTemplateId.value
          );
          if (_experimentTemplate) {
            experimentTemplate.value = _experimentTemplate;
          }
        }
      } else {
        const _experimentTemplate = await experimentTemplatesStore.getExperimentTemplate(
          experimentTemplateId.value
        );
        if (_experimentTemplate) {
          experimentTemplate.value = _experimentTemplate;
        }
      }
    }

    if (project.value && department.value && experimentTemplate.value) {
      experimentTemplatesStore.showPreviewModal(experimentTemplate.value.experiment_template_id);
      experimentTemplatesStore.showExperimentTemplateModal = false;

      if (modalOptions.value?.updateData?.reaction_scheme_file_id) {
        getReactionSchemeFileId(modalOptions.value.updateData.reaction_scheme_file_id);
      }
      forceRefreshKey.value++;
      baseDataLoaded.value = true;
    } else {
      if (projectDepartmentId.value) {
        router.push({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value?.project_id,
            project_department_id: projectDepartmentId.value
          }
        });
      } else if (project.value) {
        router.push({ name: 'ProjectDetail', params: { project_id: project.value.project_id } });
      }
      router.push({ name: 'ListOfProjects' });
    }
  };

  onMounted(async () => {
    loadExecute();
  });

  watch([project_id, project_department_id, experiment_template_id], () => {
    loadExecute();
  });

  const CreateSyntheticTemplateForm = ref();
  async function submitFormToValidate() {}

  const breadcrumbItems = computed<BreadcrumbI[]>(() => {
    return [
      {
        title: 'Projekty',
        disabled: false,
        href: router.resolve({ name: 'Projects' }).href
      },
      {
        title: project.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'ProjectDetail',
          params: { project_id: project.value?.project_id }
        }).href
      },
      {
        title: department.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id
          }
        }).href
      },
      {
        title: 'Šablony pro syntetické oddělení',
        disabled: false,
        href: router.resolve({
          name: 'ExperimentTemplates',
          params: { project_id: project_id.value }
        }).href
      },
      {
        title: 'Náhled šablony',
        disabled: true,
        href: '#'
      }
    ];
  });

  const reloadPageWithoutSave = () => {
    // window reload current page
    // window.location.reload();
    loadExecute();
  };

  const selectedReactionSchemeFile = ref<File[]>([]);
  const selectedReactionSchemeFileUrl = ref<string | undefined>(undefined);
  const getReactionSchemeFileId = async (fileId: number) => {
    selectedReactionSchemeFileUrl.value = await filesStore.getFileUrl(fileId);
  };

  watch(selectedReactionSchemeFile, (files) => {
    if (modalOptions.value?.updateData) {
      modalOptions.value.updateData.reaction_scheme_file_id = files[0].file_id;
    }
  });

  watch(
    () => modalOptions.value?.updateData?.reaction_scheme_file_id,
    async (fileId) => {
      if (fileId) {
        getReactionSchemeFileId(fileId);
      }
    }
  );

  const isReadOnly = computed(() => {
    return modalOptions.value?.isCreating === false && modalOptions.value?.isEditing === false;
  });
</script>
<template>
  <TopPageBreadcrumb title="Náhled šablony" :_breadcrumbs="breadcrumbItems" />
  <v-row class="justify-content-end">
    <v-col cols="12">
      <UiParentCard class="pa-0" :loading="loading || fileUploading">
        <template #action>
          <v-row justify="space-between" class="align-center">
            <v-col cols="12">
              <div class="d-flex gap-2 justify-end flex-wrap">
                <v-btn
                  variant="flat"
                  color="error"
                  :disabled="!baseDataLoaded"
                  @click.prevent="
                    router.push(
                      router.resolve({
                        name: 'ExperimentTemplates',
                        params: { project_id: project_id }
                      })
                    )
                  "
                >
                  Zpět
                </v-btn>
              </div>
            </v-col>
          </v-row>
        </template>
        <v-form
          v-if="modalOptions?.updateData"
          id="template-add-form"
          ref="CreateSyntheticTemplateForm"
          :readonly="isReadOnly"
          @submit.prevent="submitFormToValidate"
        >
          <v-row v-if="modalOptions?.updateData">
            <v-col>
              <v-label class="mb-2">Název</v-label>
              <v-text-field
                v-model="modalOptions.updateData.template_name"
                :rules="itemRequiredRule"
                single-line
                placeholder="Zadejte název"
                hide-details="auto"
                variant="outlined"
                rounded="sm"
              ></v-text-field>
            </v-col>

            <v-col cols="12">
              <v-label class="mb-2">Schéma reakce</v-label>
              <div v-if="modalOptions.updateData.reaction_scheme_file_id" class="image-container">
                <v-img
                  :src="selectedReactionSchemeFileUrl"
                  :height="400"
                  aspect-ratio="16/9"
                  contain
                  rounded
                  class="image"
                ></v-img>
                <v-btn
                  absolute
                  top
                  right
                  color="error"
                  class="floating-button"
                  :disabled="isReadOnly"
                  @click="
                    async () => {
                      if (modalOptions?.updateData)
                        modalOptions.updateData.reaction_scheme_file_id = null;
                    }
                  "
                >
                  <v-icon>mdi-close</v-icon>
                </v-btn>
              </div>

              <FileUploader
                v-else
                v-model="selectedReactionSchemeFile"
                :process-save-file="
                  async (file_id: number) => {
                    if (modalOptions?.updateData)
                      modalOptions.updateData.reaction_scheme_file_id = file_id;
                  }
                "
                :process-remove-file="
                  async () => {
                    if (modalOptions?.updateData)
                      modalOptions.updateData.reaction_scheme_file_id = null;
                  }
                "
                :disabled="isReadOnly"
                :uppy-options="{
                  height: 250
                }"
              />
            </v-col>

            <v-col cols="12" class="mb-4">
              <v-label class="mb-2">Tabulka chemikálií</v-label>
              <CustomChemicalTable
                ref="ChemicalTableRef"
                :key="forceRefreshKey"
                :init-value="modalOptions.baseData?.chemicals"
                :is-editable="!isReadOnly"
                @save-order="submitFormToValidate"
              />
            </v-col>
            <v-col cols="12">
              <v-label class="mb-2">Postup reakce</v-label>

              <EditorTextarea
                v-model="modalOptions.updateData.reaction_procedure"
                :show-edit-button="false"
                :disabled="isReadOnly"
                :config="
                  {
                    min_height: 200
                  } as EditorManager & RawEditorOptions
                "
              ></EditorTextarea>
            </v-col>

            <v-col cols="12">
              <v-label class="mb-2">Status</v-label>
              <v-autocomplete
                v-model="modalOptions.updateData.status"
                :readonly="isReadOnly"
                :items="[
                  { value: ExperimentTemplateStatus.ACTIVE, title: 'Aktivní' },
                  { value: ExperimentTemplateStatus.DELETED, title: 'Archivováno' }
                ]"
                rounded="sm"
                color="primary"
                single-line
                hide-details
                variant="outlined"
                :no-data-text="'Žádná další políčka'"
              ></v-autocomplete>
            </v-col>
          </v-row>

          <FileSection
            :is-read-only="isReadOnly"
            :files="modalOptions.baseData?.files ?? []"
            :file-search="undefined"
          />
        </v-form>
      </UiParentCard>
    </v-col>

    <ConfirmDlg ref="ConfirmRef" />
  </v-row>
</template>
