<script setup lang="ts">
  import type { BreadcrumbI } from '@/components/shared/BaseBreadcrumb.vue';
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import EditorTextarea from '@/components/shared/EditorTextarea.vue';
  import FileSection from '@/components/shared/file/FileSection.vue';
  import FileUploader from '@/components/shared/file/FileUploader.vue';
  import TopPageBreadcrumb from '@/components/shared/TopPageBreadcrumb.vue';
  import UiParentCard from '@/components/shared/UiParentCard.vue';
  import { useInvestigationTemplates } from '@/stores/investigationsTemplates';
  import { File, useFilesStore } from '@/stores/files';
  import { useProjectsStore } from '@/stores/projects';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { storeToRefs } from 'pinia';
  import { computed, onMounted, ref, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { toolbar } from '@/utils/SetupTinyMCE';
  import type { EditorManager, RawEditorOptions } from 'tinymce';
  import { ChemicalType, type NewChemicalDataI } from '@/stores/chemicals';
  import CustomChemicalTable from '@/components/shared/chemicalTable/CustomChemicalTable.vue';
  const ChemicalTableRef = ref<undefined | typeof CustomChemicalTable>(undefined);
  const ConfirmRef = ref<undefined | typeof ConfirmDlg>(undefined);
  const forceRefreshKey = ref(0);
  const route = useRoute();
  const router = useRouter();
  const baseDataLoaded = ref(false);
  const filesStore = useFilesStore();
  const projectsStore = useProjectsStore();

  const { project, department } = storeToRefs(projectsStore);
  const { loading: fileUploading } = storeToRefs(filesStore);
  const investigationTemplatesStore = useInvestigationTemplates();

  const project_id = computed(() => route.params.project_id as string);
  const projectId = computed(() => parseInt(project_id.value));

  const project_department_id = computed(() => route.params.project_department_id as string);
  const projectDepartmentId = computed(() => parseInt(project_department_id.value));

  const loadExecute = async () => {
    baseDataLoaded.value = false;
    forceRefreshKey.value++;
    if (projectId.value) {
      if (project.value) {
        if (project.value.project_id !== projectId.value) {
          await projectsStore.getProjectById(projectId.value);
        }
      } else {
        await projectsStore.getProjectById(projectId.value);
      }
    }

    if (projectDepartmentId.value) {
      if (department.value) {
        if (department.value.project_department_id !== projectDepartmentId.value) {
          await projectsStore.getDepartmentById(projectDepartmentId.value);
        }
      } else {
        await projectsStore.getDepartmentById(projectDepartmentId.value);
      }
    }

    if (project.value && department.value) {
      investigationTemplatesStore.showNewInvestigationTemplateModal(project.value.project_id);
      baseDataLoaded.value = true;
    } else {
      if (project.value) {
        router.push({ name: 'ProjectDetail', params: { project_id: project.value.project_id } });
      }
      router.push({ name: 'ListOfProjects' });
    }
  };

  onMounted(async () => {
    await loadExecute();
  });

  watch([project_id], () => {
    loadExecute();
  });

  const { modalOptions, loading } = storeToRefs(investigationTemplatesStore);
  const validateInput = (concentration: number | string) => {
    const input = concentration.toString();
    if (input) {
      if (/^\d+([.,]\d+)?$/.test(input)) {
        return parseFloat(input.replace(',', '.'));
      } else {
        return -1;
      }
    } else {
      return -1;
    }
  };

  const CreateSyntheticTemplateForm = ref();
  async function submitFormToValidate() {
    if (CreateSyntheticTemplateForm.value.isValid && modalOptions.value) {
      const chemicals: NewChemicalDataI[] =
        ChemicalTableRef.value?.getAllChemicalInBaseUnitToUpdateModel();
      if (modalOptions.value?.newData && chemicals) {
        modalOptions.value.newData.chemicals = chemicals;
        modalOptions.value.newData.chemicals.forEach((chemical) => {
          if (chemical.concentration)
            chemical.concentration = validateInput(chemical.concentration);
        });
      }
      switch (true) {
        case modalOptions.value.isEditing &&
          !modalOptions.value.isCreating &&
          modalOptions.value.newData !== undefined: {
          return investigationTemplatesStore.updateInvestigationTemplate();
        }
        case !modalOptions.value.isEditing &&
          modalOptions.value.isCreating &&
          modalOptions.value.newData !== undefined: {
          const res = await investigationTemplatesStore.createInvestigationTemplate();
          if (res)
            router.push({
              name: 'InvestigationTemplates',
              params: { project_id: project_id.value }
            });
          break;
        }
      }
    }
  }

  const breadcrumbItems = computed<BreadcrumbI[]>(() => {
    return [
      {
        title: 'Projekty',
        disabled: false,
        href: router.resolve({ name: 'Projects' }).href
      },
      {
        title: project.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'ProjectDetail',
          params: { project_id: project.value?.project_id }
        }).href
      },
      {
        title: department.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id
          }
        }).href
      },
      {
        title: 'Šablony pro technologické šetření',
        disabled: false,
        href: router.resolve({
          name: 'InvestigationTemplates',
          params: { project_id: project_id.value }
        }).href
      },
      {
        title: 'Nová šablona',
        disabled: true,
        href: '#'
      }
    ];
  });

  const getAllFiles = async (file_id: number) => {
    if (modalOptions.value && modalOptions.value.newData && modalOptions.value.newData.files) {
      const foundFile = modalOptions.value.newData.files.find((f) => f.file_id === file_id);

      if (foundFile && modalOptions.value.newData.files.indexOf(foundFile) > -1) {
        modalOptions.value.newData.files.splice(
          modalOptions.value.newData.files.indexOf(foundFile),
          1
        );
      }
    }
  };
  const isReadOnly = false;
  watch(
    () => modalOptions.value?.newData,
    (newData) => {
      if (newData && modalOptions.value?.isCreating) {
        localStorage.setItem('investigationTemplatePageStore', JSON.stringify(newData));
      }
    },
    { deep: true }
  );
</script>
<template>
  <TopPageBreadcrumb title="Nová šablona" :_breadcrumbs="breadcrumbItems" />
  <v-row class="justify-content-end">
    <v-col cols="12">
      <UiParentCard class="pa-0" :loading="loading || fileUploading">
        <template #action>
          <v-row justify="space-between" class="align-center">
            <v-col cols="12">
              <div class="d-flex gap-2 justify-end flex-wrap">
                <v-btn
                  v-if="project?.project_id"
                  variant="flat"
                  color="error"
                  :disabled="!baseDataLoaded"
                  @click="
                    router.push(
                      router.resolve({
                        name: 'InvestigationTemplates',
                        params: { project_id: project_id }
                      })
                    )
                  "
                >
                  Zpět
                </v-btn>

                <v-btn
                  v-if="project?.project_id"
                  variant="flat"
                  color="primary"
                  :disabled="!baseDataLoaded"
                  type="submit"
                  form="template-add-form"
                >
                  Uložit šablonu
                </v-btn>
              </div>
            </v-col>
          </v-row>
        </template>
        <v-form
          v-if="modalOptions?.newData"
          id="template-add-form"
          ref="CreateSyntheticTemplateForm"
          @submit.prevent="submitFormToValidate"
        >
          <v-row>
            <v-col cols="12" md="12">
              <v-label class="mb-2">Název</v-label>
              <v-text-field
                v-model="modalOptions.newData.template_name"
                :rules="itemRequiredRule"
                single-line
                placeholder="Zadejte název"
                hide-details="auto"
                variant="outlined"
                rounded="sm"
              ></v-text-field>
            </v-col>

            <v-col cols="12" md="6">
              <v-label class="mb-2">Vliv na kvalitu</v-label>
              <v-radio-group
                v-model="modalOptions.newData.impact_on_quality"
                single-line
                hide-details="auto"
                variant="outlined"
                rounded="sm"
                inline
              >
                <v-radio label="Ano" :value="true"></v-radio>
                <v-radio label="Ne" :value="false"></v-radio>
              </v-radio-group>
            </v-col>

            <v-col cols="12" md="6">
              <v-label class="mb-2">Vliv na výtěžek</v-label>
              <v-radio-group
                v-model="modalOptions.newData.impact_on_yield"
                single-line
                hide-details="auto"
                variant="outlined"
                rounded="sm"
                inline
                :readonly="isReadOnly"
              >
                <v-radio label="Ano" :value="true"></v-radio>
                <v-radio label="Ne" :value="false"></v-radio>
              </v-radio-group>
            </v-col>

            <v-col cols="12">
              <v-label class="mb-2">Popis problému</v-label>
              <EditorTextarea
                v-model="modalOptions.newData.problem_description"
                :show-edit-button="false"
                :config="
                  isReadOnly
                    ? ({
                        statusbar: true,
                        resize: true,
                        min_height: 200,
                        toolbar: isReadOnly ? false : toolbar
                      } as EditorManager & RawEditorOptions)
                    : ({
                        statusbar: true,
                        resize: true,
                        min_height: 200,
                        toolbar: isReadOnly ? false : toolbar
                      } as EditorManager & RawEditorOptions)
                "
              ></EditorTextarea>
            </v-col>

            <v-col cols="12">
              <v-label class="mb-2">Šetření ve výrobě</v-label>
              <EditorTextarea
                v-model="modalOptions.newData.investigation_in_production"
                :show-edit-button="false"
                :config="
                  isReadOnly
                    ? ({
                        statusbar: true,
                        resize: true,
                        min_height: 200,
                        toolbar: isReadOnly ? false : toolbar
                      } as EditorManager & RawEditorOptions)
                    : ({
                        statusbar: true,
                        resize: true,
                        min_height: 200,
                        toolbar: isReadOnly ? false : toolbar
                      } as EditorManager & RawEditorOptions)
                "
              ></EditorTextarea>
            </v-col>

            <v-col cols="12">
              <v-label class="mb-2">Šetření v laboratoři</v-label>
              <EditorTextarea
                v-model="modalOptions.newData.investigation_in_laboratory"
                :show-edit-button="false"
                :config="
                  isReadOnly
                    ? ({
                        statusbar: true,
                        resize: true,
                        min_height: 200,
                        toolbar: isReadOnly ? false : toolbar
                      } as EditorManager & RawEditorOptions)
                    : ({
                        statusbar: true,
                        resize: true,
                        min_height: 200,
                        toolbar: isReadOnly ? false : toolbar
                      } as EditorManager & RawEditorOptions)
                "
              ></EditorTextarea>
            </v-col>

            <v-col cols="12" class="mb-4">
              <v-label class="mb-2">Tabulka chemikálií</v-label>
              <CustomChemicalTable
                v-if="modalOptions.newData.chemicals"
                :key="forceRefreshKey"
                ref="ChemicalTableRef"
                @save-order="submitFormToValidate"
              />
            </v-col>

            <v-col cols="12">
              <v-label class="mb-2">Přílohy</v-label>
              <FileUploader
                v-model="modalOptions.newData.files"
                :process-remove-file="
                  investigationTemplatesStore.deleteFileFromInvestigationTemplate
                "
                :uppy-options="{
                  height: 250,
                  restrictions: {
                    maxNumberOfFiles: 10,
                    minNumberOfFiles: 0,
                    allowedFileTypes: null
                  }
                }"
              />
            </v-col>
            <v-col cols="12">
              <FileSection
                :files="modalOptions.newData.files"
                :custom-remove-file="true"
                @reload="getAllFiles"
                @file-remove="
                  async (file_id: number) => {
                    const res = await filesStore.deleteFile(file_id);
                    if (res) {
                      getAllFiles(file_id);
                    }
                  }
                "
              />
            </v-col>
          </v-row>
        </v-form>
      </UiParentCard>
    </v-col>

    <ConfirmDlg ref="ConfirmRef" />
  </v-row>
</template>
