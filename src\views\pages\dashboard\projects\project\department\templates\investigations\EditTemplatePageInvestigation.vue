<script setup lang="ts">
  import type { BreadcrumbI } from '@/components/shared/BaseBreadcrumb.vue';
  import CustomChemicalTable from '@/components/shared/chemicalTable/CustomChemicalTable.vue';
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import EditorTextarea from '@/components/shared/EditorTextarea.vue';
  import FileSection from '@/components/shared/file/FileSection.vue';
  import FileUploader from '@/components/shared/file/FileUploader.vue';
  import TopPageBreadcrumb from '@/components/shared/TopPageBreadcrumb.vue';
  import UiParentCard from '@/components/shared/UiParentCard.vue';
  import type { NewChemicalDataI, ChemicalI } from '@/stores/chemicals';
  import {
    InvestigationTemplateStatus,
    useInvestigationTemplates
  } from '@/stores/investigationsTemplates';
  import { File, useFilesStore } from '@/stores/files';
  import { useProjectsStore } from '@/stores/projects';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { toolbar } from '@/utils/SetupTinyMCE';
  import { storeToRefs } from 'pinia';
  import type { EditorManager, RawEditorOptions } from 'tinymce';
  import { computed, onMounted, ref, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';

  const ConfirmRef = ref<undefined | typeof ConfirmDlg>(undefined);
  const ChemicalTableRef = ref<undefined | typeof CustomChemicalTable>(undefined);

  const forceRefreshKey = ref(0);

  const route = useRoute();
  const router = useRouter();
  const baseDataLoaded = ref(false);
  const filesStore = useFilesStore();
  const projectsStore = useProjectsStore();
  const investigationTemplatesStore = useInvestigationTemplates();

  const { project, department } = storeToRefs(projectsStore);
  const { loading: fileUploading } = storeToRefs(filesStore);
  const { investigationTemplate, modalOptions, loading } = storeToRefs(investigationTemplatesStore);

  const project_id = computed(() => route.params.project_id as string);
  const projectId = computed(() => parseInt(project_id.value));

  const investigation_template_id = computed(
    () => route.params.investigation_template_id as string
  );
  const investigationTemplateId = computed(() => parseInt(investigation_template_id.value));

  const project_department_id = computed(() => route.params.project_department_id as string);
  const projectDepartmentId = computed(() => parseInt(project_department_id.value));

  const loadExecute = async () => {
    baseDataLoaded.value = false;

    if (projectId.value) {
      if (project.value) {
        if (project.value.project_id !== projectId.value) {
          await projectsStore.getProjectById(projectId.value);
        }
      } else {
        await projectsStore.getProjectById(projectId.value);
      }
    }

    if (projectDepartmentId.value) {
      if (department.value) {
        if (department.value.project_department_id !== projectDepartmentId.value) {
          await projectsStore.getDepartmentById(projectDepartmentId.value);
        }
      } else {
        await projectsStore.getDepartmentById(projectDepartmentId.value);
      }
    }

    if (investigationTemplateId.value) {
      if (investigationTemplate.value) {
        if (
          investigationTemplate.value.investigation_template_id !== investigationTemplateId.value
        ) {
          const _investigationTemplate = await investigationTemplatesStore.getInvestigationTemplate(
            investigationTemplateId.value
          );
          if (_investigationTemplate) {
            investigationTemplate.value = _investigationTemplate;
          }
        }
      } else {
        const _investigationTemplate = await investigationTemplatesStore.getInvestigationTemplate(
          investigationTemplateId.value
        );
        if (_investigationTemplate) {
          investigationTemplate.value = _investigationTemplate;
        }
      }
    }

    if (project.value && department.value && investigationTemplate.value) {
      investigationTemplatesStore.showEditModal(
        investigationTemplate.value.investigation_template_id
      );

      forceRefreshKey.value++;
      baseDataLoaded.value = true;
    } else {
      if (projectDepartmentId.value) {
        router.push({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value?.project_id,
            project_department_id: projectDepartmentId.value
          }
        });
      } else if (project.value) {
        router.push({ name: 'ProjectDetail', params: { project_id: project.value.project_id } });
      }
      router.push({ name: 'ListOfProjects' });
    }
  };

  onMounted(async () => {
    loadExecute();
  });

  watch([project_id, project_department_id, investigation_template_id], () => {
    loadExecute();
  });

  const validateInput = (concentration: number | string) => {
    const input = concentration.toString();
    if (input) {
      if (/^\d+([.,]\d+)?$/.test(input)) {
        return parseFloat(input.replace(',', '.'));
      } else {
        return -1;
      }
    } else {
      return -1;
    }
  };

  const CreateSyntheticTemplateForm = ref();
  async function submitFormToValidate() {
    if (CreateSyntheticTemplateForm.value.isValid) {
      const chemicals: ChemicalI[] =
        ChemicalTableRef.value?.getAllChemicalInBaseUnitToUpdateModel();
      if (modalOptions.value?.updateData && chemicals) {
        modalOptions.value.updateData.chemicals = chemicals;
        modalOptions.value.updateData.chemicals.forEach((chemical) => {
          if (chemical.concentration)
            chemical.concentration = validateInput(chemical.concentration);
        });
      }
      const res = await investigationTemplatesStore.updateInvestigationTemplate();
      if (res) {
        loadExecute();
      }
    }
  }

  const breadcrumbItems = computed<BreadcrumbI[]>(() => {
    return [
      {
        title: 'Projekty',
        disabled: false,
        href: router.resolve({ name: 'Projects' }).href
      },
      {
        title: project.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'ProjectDetail',
          params: { project_id: project.value?.project_id }
        }).href
      },
      {
        title: department.value?.getName ?? '',
        disabled: false,
        href: router.resolve({
          name: 'DepartmentDetail',
          params: {
            project_id: project.value?.project_id,
            project_department_id: department.value?.project_department_id
          }
        }).href
      },
      {
        title: 'Šablony pro technologické šetření',
        disabled: false,
        href: router.resolve({
          name: 'InvestigationTemplates',
          params: { project_id: project_id.value }
        }).href
      },
      {
        title: 'Editace šablony',
        disabled: true,
        href: '#'
      }
    ];
  });

  const reloadPageWithoutSave = () => {
    loadExecute();
  };

  const isReadOnly = computed(() => {
    return false;
  });

  const isUpdateDataSameAsOriginal = computed(() => {
    if (modalOptions.value?.updateData && investigationTemplate.value && ChemicalTableRef.value) {
      const currentChemicals = ChemicalTableRef.value?.getAllChemicalInBaseUnitToUpdateModel();

      const areChemicalsMatching = currentChemicals.every(
        (chem: NewChemicalDataI, index: number) => {
          const matchingChem = investigationTemplate.value?.chemicals[index];
          if (!matchingChem) {
            return false;
          }

          const isConcentrationEqual = chem.concentration == matchingChem.concentration;

          const isDensityEqual = chem.density == matchingChem.density;

          const isEquivalentEqual = chem.equivalent == matchingChem.equivalent;

          const isGramsEqual = chem.grams == matchingChem.grams;

          const isMolarMassEqual = chem.molar_mass == matchingChem.molar_mass;

          const isMolesEqual = chem.moles == matchingChem.moles;

          const isNameEqual = chem.name == matchingChem.name;

          const isNotesEqual = chem.notes == matchingChem.notes;

          const isTypeEqual = chem.type == matchingChem.type;

          const isVolumeMlEqual = chem.volume_ml == matchingChem.volume_ml;

          return (
            isConcentrationEqual &&
            isDensityEqual &&
            isEquivalentEqual &&
            isGramsEqual &&
            isMolarMassEqual &&
            isMolesEqual &&
            isNameEqual &&
            isNotesEqual &&
            isTypeEqual &&
            isVolumeMlEqual
          );
        }
      );

      const isMatching =
        areChemicalsMatching &&
        modalOptions.value.updateData.template_name === investigationTemplate.value.template_name &&
        modalOptions.value.updateData.impact_on_quality ===
          investigationTemplate.value.impact_on_quality &&
        modalOptions.value.updateData.impact_on_yield ===
          investigationTemplate.value.impact_on_yield &&
        modalOptions.value.updateData.problem_description ===
          investigationTemplate.value.problem_description &&
        modalOptions.value.updateData.investigation_in_production ===
          investigationTemplate.value.investigation_in_production &&
        modalOptions.value.updateData.investigation_in_laboratory ===
          investigationTemplate.value.investigation_in_laboratory &&
        modalOptions.value.updateData.status === investigationTemplate.value.status;
      return isMatching;
    }
    return false;
  });
</script>
<template>
  <TopPageBreadcrumb title="Editace šablony" :_breadcrumbs="breadcrumbItems" />
  <v-row class="justify-content-end">
    <v-col cols="12">
      <UiParentCard class="pa-0" :loading="loading || fileUploading">
        <template #action>
          <v-row justify="space-between" class="align-center">
            <v-col cols="12">
              <div class="d-flex gap-2 justify-end flex-wrap">
                <v-btn
                  variant="flat"
                  color="error"
                  :disabled="!baseDataLoaded"
                  @click.prevent="
                    async () => {
                      if (!isUpdateDataSameAsOriginal) {
                        if (
                          await ConfirmRef?.open(
                            'Potvrzení',
                            'Opravdu chcete zrušit změny a načíst data ze serveru?',
                            {
                              color: 'error',
                              notclosable: true,
                              zIndex: 2400
                            }
                          )
                        ) {
                          reloadPageWithoutSave();
                        }
                      } else {
                        router.push(
                          router.resolve({
                            name: 'InvestigationTemplates',
                            params: { project_id: project_id }
                          })
                        );
                      }
                    }
                  "
                >
                  {{ isUpdateDataSameAsOriginal ? 'Zpět' : 'Zrušit' }}
                </v-btn>

                <v-btn
                  v-if="project?.project_id"
                  variant="flat"
                  color="primary"
                  :disabled="!baseDataLoaded"
                  type="submit"
                  form="template-add-form"
                >
                  Uložit šablonu
                </v-btn>
              </div>
            </v-col>
          </v-row>
        </template>
        <v-form
          v-if="modalOptions?.updateData && modalOptions?.baseData !== undefined"
          id="template-add-form"
          ref="CreateSyntheticTemplateForm"
          @submit.prevent="submitFormToValidate"
        >
          <v-row v-if="modalOptions?.updateData">
            <v-col cols="12" md="12">
              <v-label class="mb-2">Název</v-label>
              <v-text-field
                v-model="modalOptions.updateData.template_name"
                :rules="itemRequiredRule"
                single-line
                placeholder="Zadejte název"
                hide-details="auto"
                variant="outlined"
                rounded="sm"
              ></v-text-field>
            </v-col>

            <v-col cols="12" md="6">
              <v-label class="mb-2">Vliv na kvalitu</v-label>
              <v-radio-group
                v-model="modalOptions.updateData.impact_on_quality"
                single-line
                hide-details="auto"
                variant="outlined"
                rounded="sm"
                inline
              >
                <v-radio label="Ano" :value="true"></v-radio>
                <v-radio label="Ne" :value="false"></v-radio>
              </v-radio-group>
            </v-col>

            <v-col cols="12" md="6">
              <v-label class="mb-2">Vliv na výtěžek</v-label>
              <v-radio-group
                v-model="modalOptions.updateData.impact_on_yield"
                single-line
                hide-details="auto"
                variant="outlined"
                rounded="sm"
                inline
              >
                <v-radio label="Ano" :value="true"></v-radio>
                <v-radio label="Ne" :value="false"></v-radio>
              </v-radio-group>
            </v-col>

            <v-col cols="12">
              <v-label class="mb-2">Popis problému</v-label>
              <EditorTextarea
                v-model="modalOptions.updateData.problem_description"
                :show-edit-button="false"
                :config="
                  isReadOnly
                    ? ({
                        statusbar: true,
                        resize: true,
                        min_height: 200,
                        toolbar: isReadOnly ? false : toolbar
                      } as EditorManager & RawEditorOptions)
                    : ({
                        statusbar: true,
                        resize: true,
                        min_height: 200,
                        toolbar: isReadOnly ? false : toolbar
                      } as EditorManager & RawEditorOptions)
                "
              ></EditorTextarea>
            </v-col>

            <v-col cols="12">
              <v-label class="mb-2">Šetření ve výrobě</v-label>
              <EditorTextarea
                v-model="modalOptions.updateData.investigation_in_production"
                :show-edit-button="false"
                :config="
                  isReadOnly
                    ? ({
                        statusbar: true,
                        resize: true,
                        min_height: 200,
                        toolbar: isReadOnly ? false : toolbar
                      } as EditorManager & RawEditorOptions)
                    : ({
                        statusbar: true,
                        resize: true,
                        min_height: 200,
                        toolbar: isReadOnly ? false : toolbar
                      } as EditorManager & RawEditorOptions)
                "
              ></EditorTextarea>
            </v-col>

            <v-col cols="12">
              <v-label class="mb-2">Šetření v laboratoři</v-label>
              <EditorTextarea
                v-model="modalOptions.updateData.investigation_in_laboratory"
                :show-edit-button="false"
                :config="
                  isReadOnly
                    ? ({
                        statusbar: true,
                        resize: true,
                        min_height: 200,
                        toolbar: isReadOnly ? false : toolbar
                      } as EditorManager & RawEditorOptions)
                    : ({
                        statusbar: true,
                        resize: true,
                        min_height: 200,
                        toolbar: isReadOnly ? false : toolbar
                      } as EditorManager & RawEditorOptions)
                "
              ></EditorTextarea>
            </v-col>

            <v-col cols="12">
              <v-label class="mb-2">Tabulka chemikálií</v-label>
              <CustomChemicalTable
                v-if="modalOptions.updateData.chemicals"
                :key="forceRefreshKey"
                ref="ChemicalTableRef"
                :init-value="modalOptions.baseData.chemicals"
                @save-order="submitFormToValidate"
              />
            </v-col>

            <v-col cols="12">
              <v-label class="mb-2">Status</v-label>
              <v-autocomplete
                v-model="modalOptions.updateData.status"
                :items="[
                  { value: InvestigationTemplateStatus.ACTIVE, title: 'Aktivní' },
                  { value: InvestigationTemplateStatus.DELETED, title: 'Archivováno' }
                ]"
                rounded="sm"
                color="primary"
                single-line
                hide-details
                variant="outlined"
                :no-data-text="'Žádná další políčka'"
              ></v-autocomplete>
            </v-col>

            <v-col v-if="modalOptions.baseData" cols="12">
              <v-label class="mb-2">Přílohy</v-label>
              <FileUploader
                v-model="modalOptions.baseData.files"
                :process-save-file="investigationTemplatesStore.addFileToInvestigationTemplate"
                :process-remove-file="
                  async (file_id: number) => {
                    const res =
                      await investigationTemplatesStore.deleteFileFromInvestigationTemplate(
                        file_id
                      );
                    if (res) loadExecute();

                    return res;
                  }
                "
                :uppy-options="{
                  height: 250
                }"
              />
            </v-col>
          </v-row>
          <FileSection :files="modalOptions.baseData?.files ?? []" :file-search="undefined" />
        </v-form>
      </UiParentCard>
    </v-col>
    <ConfirmDlg ref="ConfirmRef" />
  </v-row>
</template>
