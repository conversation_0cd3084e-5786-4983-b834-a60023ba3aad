<template>
  <LoaderWrapper v-if="!baseDataLoaded" />
  <template v-else>
    <TopPageBreadcrumb :title="'Seznam vzorků'" :_breadcrumbs="breadcrumbItems" />
    <v-row>
      <v-col cols="12" md="12">
        <v-card
          elevation="0"
          variant="outlined"
          class="withbg pageSize"
          :loading="mainSampleTechniqueOptions.loading"
        >
          <v-card-item>
            <v-row justify="space-between" class="align-center">
              <v-col cols="12" md="9">
                <v-text-field
                  v-model="mainSampleTechniqueOptions.search"
                  type="text"
                  variant="outlined"
                  persistent-placeholder
                  placeholder="Hledat vzorek"
                  hide-details
                >
                  <template #prepend-inner>
                    <SearchOutlined :style="{ fontSize: '14px' }" />
                  </template>
                </v-text-field>
              </v-col>
              <v-col cols="12" md="3">
                <div class="d-flex gap-2 justify-end flex-wrap">
                  <v-btn
                    variant="flat"
                    color="primary"
                    @click.prevent="redirectToInsertAnalyticalRequest"
                  >
                    Nová analýza
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </v-card-item>
          <v-divider></v-divider>
          <v-card-text>
            <CustomTable
              id="mainSampleTechniqueTable"
              v-model:new-server-options="mainSampleTechniqueOptions"
              table-class-name="customize-table customize"
              :server-items-length="mainSampleTechniqueOptions.totalItems"
              :loading="mainSampleTechniqueOptions.loading"
              :headers="headersForSampleTechnique"
              :items="mainSampleTechniqueOptions.results"
              multi-sort
              body-expand-row-class-name="expand-row"
              @reload="loadFromServer"
              @expand-row="loadSampleTechnique"
            >
              <template #expand="sampleTechniqueDto: SampleTechniqueDto">
                <template
                  v-if="
                    sampleTechniqueExpanded ===
                    mainSampleTechniqueOptions.results.findIndex(
                      (item) =>
                        item.analytical_technique_id === sampleTechniqueDto.analytical_technique_id
                    )
                  "
                >
                  <CustomTable
                    v-if="mainSampleTechniqueProjectOptions.results.length > 0"
                    id="mainSampleTechniqueProjectTable"
                    v-model:new-server-options="mainSampleTechniqueProjectOptions"
                    table-class-name="customize-table customize"
                    :server-items-length="mainSampleTechniqueProjectOptions.totalItems"
                    :headers="headersForSampleTechniqueProject"
                    :items="mainSampleTechniqueProjectOptions.results"
                    multi-sort
                    body-expand-row-class-name="expand-row"
                    :loading="mainSampleTechniqueProjectOptions.loading"
                    @reload="
                      samplesStore.getProjectsForSpecificAnalyticalTechnique(
                        sampleTechniqueDto.analytical_technique_id
                      )
                    "
                    @expand-row="loadSampleTechniqueProject"
                  >
                    <template #expand="sampleTechniqueProjectDto: SampleTechniqueProjectDto">
                      <CustomTable
                        v-if="mainSampleTechniqueProjectSampleOptions.results.length > 0"
                        id="mainSampleTechniqueProjectSampleTable"
                        v-model:new-server-options="mainSampleTechniqueProjectSampleOptions"
                        table-class-name="customize-table customize"
                        :server-items-length="mainSampleTechniqueProjectSampleOptions.totalItems"
                        :loading="mainSampleTechniqueProjectSampleOptions.loading"
                        :headers="headersForSampleTechniqueProjectSample"
                        :items="mainSampleTechniqueProjectSampleOptions.results"
                        multi-sort
                        body-expand-row-class-name="expand-row"
                        @reload="
                          samplesStore.getSamplesForSpecificProject(
                            sampleTechniqueDto.analytical_technique_id,
                            sampleTechniqueProjectDto.project_id
                          )
                        "
                      >
                        <!-- @click-row="redirectToSampleEditPage" -->
                        <template #item-analytical_request="props: SampleDto">
                          <div style="display: flex; flex-direction: row; align-items: center">
                            <router-link
                              :to="{
                                name: 'SampleEdit',
                                params: { sample_id: props.sample_id, type: route.params.type }
                              }"
                            >
                              <v-icon color="primary">mdi-open-in-new</v-icon>
                            </router-link>
                            &nbsp;{{
                              props.analytical_request?.name ? props.analytical_request?.name : '/'
                            }}
                          </div>
                        </template>
                        <template #item-test="props: SampleDto">
                          <div style="display: flex; flex-direction: row; align-items: center">
                            <router-link
                              :to="{
                                name: 'SampleEdit',
                                params: { sample_id: props.sample_id, type: route.params.type }
                              }"
                            >
                              <v-icon color="primary">mdi-open-in-new</v-icon>
                            </router-link>
                            &nbsp;{{ props.name ? props.name : '/' }}
                          </div>
                        </template>

                        <template #item-created_at="{ created_at }: { created_at: Date }">
                          {{ toLocale(new Date(created_at)) }}
                        </template>

                        <template #item-status="{ status }: { status: SampleStatus }">
                          <v-chip
                            v-if="status === SampleStatus.CREATED"
                            color="primary"
                            size="small"
                            label
                          >
                            Vytvořeno
                          </v-chip>
                          <v-chip
                            v-if="status === SampleStatus.REANALYSIS"
                            color="warning"
                            size="small"
                            label
                          >
                            Reanalýza
                          </v-chip>
                          <v-chip
                            v-if="status === SampleStatus.CANCELLED"
                            color="error"
                            size="small"
                            label
                          >
                            Zrušeno
                          </v-chip>
                          <v-chip
                            v-if="status === SampleStatus.DONE"
                            color="success"
                            size="small"
                            label
                          >
                            Dokončeno
                          </v-chip>
                          <v-chip
                            v-if="status === SampleStatus.UPDATED"
                            color="warning"
                            size="small"
                            label
                          >
                            Rozpracované
                          </v-chip>
                        </template>

                        <template #item-updated_by_user="{ updated_by_user }">
                          <v-chip
                            v-if="updated_by_user"
                            class="mr-2"
                            color="primary"
                            size="small"
                            label
                          >
                            {{ updated_by_user?.user_email }}
                          </v-chip>
                          <v-chip v-else class="mr-2" color="grey" size="small" label>
                            Není rozpracováno
                          </v-chip>
                        </template>

                        <template
                          #item-analysis_status="{
                            analysis_status
                          }: {
                            analysis_status: AnalysisStatus;
                          }"
                        >
                          <v-chip
                            v-if="analysis_status === AnalysisStatus.NA"
                            color="warning"
                            size="small"
                            label
                          >
                            N/A
                          </v-chip>
                          <v-chip
                            v-if="analysis_status === AnalysisStatus.COMPLIANT"
                            color="success"
                            size="small"
                            label
                          >
                            Vyhovuje
                          </v-chip>
                          <v-chip
                            v-if="analysis_status === AnalysisStatus.NON_COMPLIANT"
                            color="error"
                            size="small"
                            label
                          >
                            Nevyhovuje
                          </v-chip>
                        </template>

                        <template #item-user="{ user }">
                          <v-chip class="mr-2" color="primary" size="small" label>
                            {{ user?.user_email }}
                          </v-chip>
                        </template>
                      </CustomTable>
                      <div v-else style="padding: 15px">
                        Projekt ({{ sampleTechniqueProjectDto.project_id }}) nemá vzorky
                      </div>
                    </template>
                  </CustomTable>
                  <div v-else style="padding: 15px">
                    Technika ({{ sampleTechniqueDto.analytical_technique_id }}) - nemá projekty
                  </div>
                </template>
              </template>
            </CustomTable>
          </v-card-text>
        </v-card>
      </v-col>

      <ConfirmDlg ref="ConfirmRef" />
    </v-row>
  </template>
</template>
<script setup lang="ts">
  import type { BreadcrumbI } from '@/components/shared/BaseBreadcrumb.vue';
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import CustomTable from '@/components/shared/CustomTable.vue';
  import TopPageBreadcrumb from '@/components/shared/TopPageBreadcrumb.vue';
  import LoaderWrapper from '@/layouts/dashboard/LoaderWrapper.vue';
  import { notification } from 'ant-design-vue';
  import { AnalysisStatus } from '@/stores/forms';
  import {
    SampleStatus,
    SampleType,
    useSamplesStore,
    type SampleTechniqueDto,
    type SampleTechniqueProjectDto,
    type SampleDto
  } from '@/stores/sample/samples';
  import { useUsersStore } from '@/stores/users';
  import { toLocale } from '@/utils/locales';
  import { SearchOutlined } from '@ant-design/icons-vue';
  import { storeToRefs } from 'pinia';
  import { computed, nextTick, onMounted, ref, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import type { Header } from 'vue3-easy-data-table';

  import { setPageTitle } from '@/utils/title';

  const route = useRoute();
  const router = useRouter();
  const usersStore = useUsersStore();
  const samplesStore = useSamplesStore();

  const baseDataLoaded = ref(false);

  const typeOfSample = computed(() => {
    switch (route.params.type) {
      case 'rd':
        return 'sample_rd';
      case 'qc-a-vt':
        return 'sample_qc_and_vt';
      default:
        return 'sample_rd';
    }
  });

  const typeOfSampleTitle = computed(() => {
    switch (typeOfSample.value) {
      case 'sample_rd':
        return 'RD';
      case 'sample_qc_and_vt':
        return 'QC a VT';
      default:
        return 'RD';
    }
  });

  const sampleTechniqueExpanded = ref<number | null>(null);
  const loadSampleTechnique = async (i: number): Promise<void> => {
    sampleTechniqueExpanded.value = i;
    const expandedItem = mainSampleTechniqueOptions.value.results[i];

    if (expandedItem) {
      (
        (document

          .querySelectorAll('#mainSampleTechniqueTable.customize-table table')?.[0]
          .querySelectorAll('td.can-expand i.expand-icon.expanding') ??
          []) as unknown as HTMLElement[]
      ).forEach((el) => {
        el.parentElement?.click();
      });

      await nextTick();

      selectedTechniqueId.value = expandedItem.analytical_technique_id;
      await samplesStore.getProjectsForSpecificAnalyticalTechnique(selectedTechniqueId.value);
    }
  };

  const loadSampleTechniqueProject = async (i: number): Promise<void> => {
    const expandedItem = mainSampleTechniqueProjectOptions.value.results[i];
    if (expandedItem && selectedTechniqueId.value) {
      (
        (document

          .querySelectorAll('#mainSampleTechniqueProjectTable.customize-table table')?.[0]
          .querySelectorAll('td.can-expand i.expand-icon.expanding') ??
          []) as unknown as HTMLElement[]
      ).forEach((el) => {
        el.parentElement?.click();
      });

      await nextTick();

      selectedProjectId.value = expandedItem.project_id;
      await samplesStore.getSamplesForSpecificProject(
        selectedTechniqueId.value,
        selectedProjectId.value
      );
    }
  };

  const selectedTechniqueId = ref<number | null>(null);
  const selectedProjectId = ref<number | null>(null);

  const {
    sample_filter_type,
    mainSampleTechniqueOptions,
    mainSampleTechniqueProjectOptions,
    mainSampleTechniqueProjectSampleOptions
  } = storeToRefs(samplesStore);

  const loadFromServer = async () => {
    if (typeOfSample.value !== 'sample_rd' && typeOfSample.value !== 'sample_qc_and_vt') {
      throw new Error('Invalid type of sample: ' + typeOfSample.value);
    }

    sample_filter_type.value = typeOfSample.value;

    await samplesStore.getMainSampleAnalyticalTechniques();
    if (selectedTechniqueId.value) {
      await samplesStore.getProjectsForSpecificAnalyticalTechnique(selectedTechniqueId.value);
      if (selectedProjectId.value) {
        await samplesStore.getSamplesForSpecificProject(
          selectedTechniqueId.value,
          selectedProjectId.value
        );
      }
    }
    setPageTitle('Vzorky ' + typeOfSampleTitle.value);
  };

  onMounted(async () => {
    await loadFromServer();
    baseDataLoaded.value = true;
  });

  watch(typeOfSample, async () => {
    await loadFromServer();
  });

  // const redirectToSampleEditPage = (item: ClickRowArgument) => {
  //   router.push({
  //     name: 'SampleEdit',
  //     params: {
  //       sample_id: item.sample_id
  //     }
  //   });
  // };

  const getUserNameById = async (user_id: number): Promise<string> => {
    const user = await usersStore.getUser(user_id);
    return user ? `${user.user_email}` : 'Uživatel nenalezen (ID: ' + user_id + ')';
  };

  // Updated headers to include 'Počet vzorků' (Number of Samples) in the table
  const headersForSampleTechnique: Header[] = [
    { text: 'Počet vzorků', value: 'samples_count', sortable: false },
    { text: 'Název', value: 'name', sortable: true }
  ];

  const headersForSampleTechniqueProject: Header[] = [
    { text: 'Počet vzorků', value: 'samples_count', sortable: false },
    { text: 'Název', value: 'name', sortable: true }
  ];

  const headersForSampleTechniqueProjectSample: Header[] = [
    { text: 'Status vzorku', value: 'status', sortable: false },
    { text: 'Rozpracováno', value: 'updated_by_user', sortable: false },
    { text: 'Status analýzy', value: 'analysis_status', sortable: false },
    { text: 'Vytvořeno', value: 'created_at', sortable: true },
    { text: 'Uživatel', value: 'user', sortable: false },
    { text: 'Číslo vzorku', value: 'sample_number', sortable: true },
    { text: 'Název', value: 'analytical_request', sortable: true }
  ];
  const breadcrumbItems = computed<BreadcrumbI[]>(() => {
    return [
      {
        title: 'Vzorky ' + typeOfSampleTitle.value,
        disabled: true,
        href: router.resolve({
          name: 'Samples',
          params: {
            type: route.params.type
          }
        }).href
      }
    ];
  });

  const redirectToInsertAnalyticalRequest = async () => {
    const go = router.resolve({
      name: 'NewSampleAnalyticalRequest'
    });
    router.push(go);
  };
</script>
<style lang="less" scoped>
  :deep(.vue3-easy-data-table__header tr:not(.expand-row)),
  :deep(.vue3-easy-data-table__body tr:not(.expand-row)) {
    display: flex;
    flex-direction: row-reverse;
    width: 100%;

    td,
    th {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: flex-start;

      &:first-child {
        flex: none;
        width: auto;
      }

      &:nth-child(2) {
        margin-right: auto;
        justify-content: flex-end;
      }

      &:last-child {
        flex-grow: 1;
        justify-content: flex-start;
      }
    }
  }
</style>
