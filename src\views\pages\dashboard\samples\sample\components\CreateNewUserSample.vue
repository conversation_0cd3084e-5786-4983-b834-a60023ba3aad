<script setup lang="ts">
  import UserSelect from '@/components/shared/UserSelect.vue';
  import CustomTable from '@/components/shared/CustomTable.vue';
  import { responsiveCardClass } from '@/config';
  import type { User } from '@/stores/auth';
  import { useUsersStore } from '@/stores/users';
  import { emailRules, itemRequiredRule, lastRules, passwordRules } from '@/utils/formValidation';
  import { toLocale } from '@/utils/locales';
  import { useDebounceFn, useVModel } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import { computed, onMounted, ref, watch } from 'vue';

  // stores
  const usersStore = useUsersStore();
  const {
    user,
    userModalOptions,
    permissions,
    loading,
    roles,
    userProjectsOptions,
    userProjectsTotalItems,
    userProjects
  } = storeToRefs(usersStore);

  const CreateNewUserForm = ref();
  async function submitFormToValidate() {
    if (CreateNewUserForm.value.isValid && userModalOptions.value) {
      switch (true) {
        case userModalOptions.value.isEditing && !userModalOptions.value.isCreating:
          return usersStore.updateUser();
        case !userModalOptions.value.isEditing &&
          userModalOptions.value.isCreating &&
          password.value !== undefined:
          return usersStore.createUser(password.value);

        default:
          return 'Náhled uživatele';
      }
    } else {
      if (userModalOptions.value && userModalOptions.value.newUserData) {
        userModalOptions.value.newUserData.confirm = false;
      }
    }
  }

  onMounted(() => {
    usersStore.getAllPermissions();
    usersStore.getAllRoles();
  });

  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    }
  });

  const showState = useVModel(props, 'show');
  const password = ref<string>('');
  const showTitle = computed(() => {
    if (userModalOptions.value === undefined) {
      return 'Náhled uživatele';
    }

    switch (true) {
      case userModalOptions.value.isEditing && !userModalOptions.value.isCreating:
        return 'Editace uživatele';
      case !userModalOptions.value.isEditing && userModalOptions.value.isCreating:
        return 'Nový uživatel';
      default:
        return 'Náhled uživatele';
    }
  });
  const showSuccessButtonTitle = computed(() => {
    if (userModalOptions.value === undefined) {
      return false;
    }

    switch (true) {
      case userModalOptions.value.isEditing && !userModalOptions.value.isCreating:
        return 'Upravit uživatele';
      case !userModalOptions.value.isEditing && userModalOptions.value.isCreating:
        return 'Přidat uživatele';
      default:
        return false;
    }
  });

  const onlyPreview = computed(() => {
    return (
      userModalOptions.value?.isCreating === false && userModalOptions.value?.isEditing === false
    );
  });

  const onlyEdit = computed(() => {
    return (
      userModalOptions.value?.isCreating === false && userModalOptions.value?.isEditing === true
    );
  });

  const tab = ref('one');

  watch(
    userProjectsOptions,
    () => {
      if (loading.value === false) debouncedSearch();
    },
    { deep: true }
  );

  const debouncedSearch = useDebounceFn(async () => {
    if (loading.value === false && userModalOptions.value?.user?.user_id !== undefined) {
      await usersStore.getAllUserProjects(userModalOptions.value.user.user_id);
    }
  }, 350);

  watch(tab, (newVal) => {
    if (newVal === 'two') {
      debouncedSearch();
    }
  });

  const reload = async (user_id: number) => {
    const u = await usersStore.getUser(user_id);
    if (u) {
      user.value = u;
    }
  };

  const addUser = async (laborant_id: number) => {
    if (userModalOptions.value?.user?.user_id === undefined) return;

    await usersStore.addLaboratoryToUser(userModalOptions.value.user.user_id, laborant_id);
    await reload(userModalOptions.value.user.user_id);
  };

  const removeUser = async (laborant_id: number) => {
    if (userModalOptions.value?.user?.user_id === undefined) return;

    await usersStore.deleteLaboratoryFromUser(userModalOptions.value.user.user_id, laborant_id);
    await reload(userModalOptions.value.user.user_id);
  };

  const show2 = ref(true);
</script>

<template>
  <v-dialog v-model="showState" class="customer-modal" :z-index="1010">
    <v-card :loading="loading" flat>
      <v-tabs v-if="onlyEdit" v-model="tab">
        <v-tab value="one">Správa uživatele</v-tab>
        <v-tab value="two">Projekty</v-tab>
        <v-tab value="three">Laboratorní pracovníci</v-tab>
      </v-tabs>
      <v-tabs-window v-model="tab">
        <v-tabs-window-item value="one">
          <v-form
            v-if="userModalOptions?.newUserData"
            ref="CreateNewUserForm"
            class="createNewUserForm"
            :readonly="onlyPreview"
            autocomplete="off"
            @submit.prevent="submitFormToValidate"
          >
            <v-card-title :class="responsiveCardClass">
              <span class="text-h5">{{ showTitle }}</span>
            </v-card-title>
            <v-divider></v-divider>
            <v-card-text :class="responsiveCardClass">
              <v-row>
                <v-col>
                  <v-row>
                    <v-col cols="12" sm="4">
                      <v-label class="mb-2">Jméno</v-label>
                      <v-text-field
                        v-model="userModalOptions.newUserData.first_name"
                        :rules="itemRequiredRule"
                        single-line
                        placeholder="Zadejte jméno"
                        hide-details="auto"
                        variant="outlined"
                        required
                        rounded="sm"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12" sm="4">
                      <v-label class="mb-2">Příjmení</v-label>
                      <v-text-field
                        v-model="userModalOptions.newUserData.last_name"
                        :rules="lastRules"
                        single-line
                        placeholder="Zadejte příjmení"
                        hide-details="auto"
                        variant="outlined"
                        rounded="sm"
                      ></v-text-field>
                    </v-col>
                    <v-col cols="12">
                      <v-label class="mb-2">Email</v-label>
                      <!-- type="email" -->

                      <v-text-field
                        v-model="userModalOptions.newUserData.user_email"
                        :rules="emailRules"
                        single-line
                        hide-details="auto"
                        placeholder="Zadejte email uživatele"
                        required
                        variant="outlined"
                        rounded="sm"
                        autocomplete="off"
                        :autofill="false"
                        :autocorrect="false"
                        :spellcheck="false"
                      ></v-text-field>
                    </v-col>
                    <v-col v-if="userModalOptions.isCreating" cols="12">
                      <div class="d-flex justify-space-between gap-2">
                        <div class="pb-4">
                          <h6 class="text-subtitle-1 mb-0">Potvrzení přidání uživatele</h6>
                          <p class="text-caption text-lightText mb-0 mr-5">
                            Uživatel bude vytvořen podle výše vyplněných údajů
                          </p>
                        </div>
                        <v-switch
                          v-model="userModalOptions.newUserData.confirm"
                          color="primary"
                          class="switchRight"
                          hide-details
                        ></v-switch>
                      </div>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions :class="responsiveCardClass">
              <v-spacer></v-spacer>
              <v-btn color="error" variant="text" @click="usersStore.resetModalUserData()">
                Zrušit
              </v-btn>
              <v-btn
                v-if="showSuccessButtonTitle"
                color="primary"
                variant="flat"
                type="submit"
                :loading="loading"
              >
                {{ showSuccessButtonTitle }}
              </v-btn>
            </v-card-actions>
          </v-form>
        </v-tabs-window-item>

        <v-tabs-window-item value="two">
          <v-card-title :class="responsiveCardClass">
            <span class="text-h5">Projekty</span>
          </v-card-title>
          <v-divider></v-divider>
          <v-card-text :class="responsiveCardClass">
            <CustomTable
              v-model:server-options="userProjectsOptions"
              :server-items-length="userProjectsTotalItems"
              :loading="loading"
              :headers="[
                { text: 'ID', value: 'project_id', sortable: false },
                { text: 'Název', value: 'name', sortable: false },
                { text: 'Vytvořeno', value: 'created_at', sortable: false },
                { text: 'Aktualizováno', value: 'updated_at', sortable: false },
                { text: 'Stav', value: 'status', sortable: false },
                { text: 'Zodpovědný uživatelé', value: 'responsible_users', sortable: false }
              ]"
              :items="userProjects"
              multi-sort
            >
              <template #item-created_at="{ created_at }">
                {{ toLocale(created_at) }}
              </template>

              <template #item-updated_at="{ updated_at }">
                {{ toLocale(updated_at) }}
              </template>

              <template
                #item-responsible_users="{ responsible_users }: { responsible_users: User[] }"
              >
                <v-chip
                  v-for="responsibleUser in responsible_users"
                  :key="responsibleUser.user_id"
                  class="mr-2"
                  color="primary"
                  size="small"
                  label
                >
                  {{ responsibleUser.getName }} ({{ responsibleUser.user_email }})
                </v-chip>
              </template>

              <template #item-status="{ status }">
                <v-chip v-if="status === 'active'" color="success" size="small" label>
                  Aktivní
                </v-chip>
                <v-chip v-if="status === 'inactive'" color="warning" size="small" label>
                  Neaktivní
                </v-chip>
                <v-chip v-if="status === 'deleted'" color="error" size="small" label>
                  Archivovaný
                </v-chip>
              </template>
            </CustomTable>
          </v-card-text>
        </v-tabs-window-item>

        <v-tabs-window-item value="three">
          <v-card-title :class="responsiveCardClass">
            <span class="text-h5">Laboratorní pracovníci</span>
          </v-card-title>
          <v-divider></v-divider>
          <v-card-text v-if="user" :class="responsiveCardClass">
            <UserSelect
              :users="user.userLaborants ?? []"
              @remove-user="removeUser"
              @add-user="addUser"
            ></UserSelect>
          </v-card-text>
        </v-tabs-window-item>
      </v-tabs-window>
    </v-card>
  </v-dialog>
</template>
