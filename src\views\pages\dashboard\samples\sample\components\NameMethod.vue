<template>
  <v-dialog v-model="showState" class="customer-modal" style="z-index: 1010">
    <v-card :loading="loading">
      <v-form ref="CreateTagForm" class="createTagForm" @submit.prevent="submitFormToValidate">
        <v-card-title class="pa-5">
          <span class="text-h5">Název metody</span>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col>
              <v-row>
                <v-col cols="12" md="12">
                  <v-label class="mb-2">Název metody</v-label>
                  <v-text-field
                    v-model="methodName"
                    single-line
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                    :rules="itemRequiredRule"
                    :required="true"
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" variant="text" @click="showState = false">Zavřít</v-btn>
          <v-btn color="primary" variant="flat" type="submit" :loading="loading">
            Vytvořit metodu
          </v-btn>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>
<script setup lang="ts">
  import { useVModel } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { ref } from 'vue';
  import { useSamplesStore } from '@/stores/sample/samples';

  const emits = defineEmits(['update:show', 'updateName']);
  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    }
  });
  const methodName = ref(<string>'');
  const showState = useVModel(props, 'show');
  const samplesStore = useSamplesStore();
  const { loading } = storeToRefs(samplesStore);
  const CreateTagForm = ref();
  const isInProgress = ref(false);
  async function submitFormToValidate() {
    if (CreateTagForm.value.isValid && !isInProgress.value) {
      isInProgress.value = true;
      emits('updateName', methodName.value);
    }
    isInProgress.value = false;
  }
</script>
