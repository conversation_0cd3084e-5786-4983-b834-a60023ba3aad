<template>
  <template v-for="(field, index) in parametersState" :key="index">
    <v-row>
      <v-col cols="12" sm="6">
        <v-label class="mb-2">Parametr</v-label>
        <v-text-field
          v-model="field.parameter"
          :rules="itemRequiredRule"
          single-line
          hide-details="auto"
          variant="outlined"
          rounded="sm"
        ></v-text-field>
      </v-col>

      <v-col cols="10" sm="5">
        <v-label class="mb-2">Hodnota</v-label>
        <v-text-field
          v-model="field.value"
          :rules="itemRequiredRule"
          single-line
          hide-details="auto"
          variant="outlined"
          rounded="sm"
        ></v-text-field>
      </v-col>

      <v-col cols="2" sm="1">
        <div class="d-flex justify-end align-center gap-2 h-100" style="margin-top: 0.5em">
          <v-btn
            :disabled="isReadOnly"
            variant="flat"
            color="error"
            density="compact"
            icon="mdi-close"
            @click="parametersState.splice(index, 1)"
          />
        </div>
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="12">
        <v-divider class="my-5"></v-divider>
      </v-col>
    </v-row>
  </template>
  <v-row>
    <v-col cols="12">
      <v-btn :disabled="isReadOnly" @click="addField">Přidat parametr a metodu</v-btn>
    </v-col>
  </v-row>
</template>

<script setup lang="ts">
  import type { NewSampleParametrI } from '@/stores/sample/samples';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { useVModel } from '@vueuse/core';
  import { onMounted } from 'vue';

  const props = defineProps({
    modelValue: {
      type: Array as () => NewSampleParametrI[],
      required: true
    },
    isReadOnly: {
      type: Boolean,
      required: false,
      default: false
    }
  });

  const parametersState = useVModel(props, 'modelValue');

  onMounted(async () => {
    if (parametersState.value.length === 0) {
      //addField();
    }
  });

  const addField = () => {
    parametersState.value.push({
      parameter: '',
      value: ''
    });
  };
</script>
