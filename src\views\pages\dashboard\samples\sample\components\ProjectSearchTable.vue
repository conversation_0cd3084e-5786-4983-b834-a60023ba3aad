<template>
  <v-autocomplete
    v-bind="$attrs"
    v-model="selectedProjectState"
    v-model:search="projectSelectOptions.search"
    :rules="itemRequiredRule"
    rounded="sm"
    variant="outlined"
    color="primary"
    label="Vyberte Projekt"
    single-line
    class="autocomplete"
    :no-data-text="'Žádn<PERSON> další políčka'"
    :slim="true"
    :clearable="true"
    :items="projectSelectOptions.results"
    :loading="projectSelectOptions.loading"
  >
    <template #chip>
      <v-chip
        label
        variant="tonal"
        color="primary"
        size="large"
        class="my-1 text-subtitle-1 font-weight-regular"
      ></v-chip>
    </template>

    <template #append>
      <slot name="append"></slot>
    </template>
  </v-autocomplete>
</template>

<script lang="ts" setup>
  import { itemRequiredRule } from '@/utils/formValidation';
  import {
    type PaginatorRequestDataI,
    useProjectsStore,
    SimpleProject,
    Project
  } from '@/stores/projects';
  import { useDebounceFn, useVModel } from '@vueuse/core';
  import { onMounted, ref, watch, type PropType } from 'vue';
  import type { VAutocomplete } from 'vuetify/components';

  const props = defineProps({
    projects: {
      type: Array<SimpleProject>,
      required: false,
      default: () => []
    },
    selectedProject: {
      type: [Number, undefined, null] as PropType<number | undefined | null>,
      required: true,
      default: () => undefined
    }
  });

  const projectSelectOptions = ref<PaginatorRequestDataI<SimpleProject>>({
    loading: false,
    results: [],
    search: undefined,
    search_type: 'OR',
    search_columns: ['name'],
    totalItems: 0,
    options: {
      page: 1,
      rowsPerPage: 25,
      sortBy: [],
      sortType: []
    },
    filterOptions: []
  });

  const projectsStore = useProjectsStore();
  const selectedProjectState = useVModel(props, 'selectedProject');

  const debouncedProjectUserSearch = useDebounceFn(() => {
    if (!projectSelectOptions.value.loading && projectSelectOptions.value.search !== '') {
      projectsStore.getAll(true, projectSelectOptions.value);
    }
  }, 350);
  onMounted(() => {
    projectSelectOptions.value.results = props.projects.filter(
      (simpleProject) => simpleProject instanceof SimpleProject
    ) as SimpleProject[];
  });

  watch(
    () => projectSelectOptions.value.search,
    () => {
      debouncedProjectUserSearch();
    }
  );
</script>
