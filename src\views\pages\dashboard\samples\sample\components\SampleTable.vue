<template>
  <v-table density="compact">
    <template #default>
      <tbody>
        <tr>
          <td class="text-left font-weight-bold pl-0 pl-0">Název vzorku:</td>
          <td>{{ sample?.analytical_request?.name || '/' }}</td>
        </tr>
        <tr>
          <td class="text-left font-weight-bold pl-0">Číslo vzorku:</td>
          <td>{{ sample.sample_number || '/' }}</td>
        </tr>
        <tr>
          <td class="text-left font-weight-bold pl-0">Datum vytvoření:</td>
          <td>{{ toLocale(sample.created_at) || '/' }}</td>
        </tr>
        <tr>
          <td class="text-left font-weight-bold pl-0">Číslo šarže:</td>
          <td>{{ sample?.analytical_request?.batch_number?.batch_number || '/' }}</td>
        </tr>
        <tr>
          <td class="text-left font-weight-bold pl-0">Uživatel:</td>
          <td>{{ sample.user.getName }} ({{ sample.user.user_email }})</td>
        </tr>
        <tr>
          <td class="text-left font-weight-bold pl-0">Technika:</td>
          <td>{{ sample.technique?.shortcut || '/' }}</td>
        </tr>
        <tr>
          <td class="text-left font-weight-bold pl-0">Poznámka:</td>
          <td>{{ extractTextFromHtml(sample.technique_notes) || '/' }}</td>
        </tr>
        <tr>
          <td class="text-left font-weight-bold pl-0">Subtechnika:</td>
          <td>{{ sample.sub_technique?.shortcut || '/' }}</td>
        </tr>
      </tbody>
    </template>
  </v-table>
</template>

<script lang="ts" setup>
  import { Sample } from '@/stores/sample/samples';
  import { toLocale } from '@/utils/locales';

  defineProps({
    sample: {
      type: Sample,
      required: true
    }
  });

  const extractTextFromHtml = (htmlString: string): string => {
    const div = document.createElement('div');
    div.innerHTML = htmlString;
    return div.textContent || div.innerText || '';
  };
</script>
<style lang="less" scoped>
  td.html {
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
  }
  td.text-left {
    min-width: 130px;
  }
  td:not(.text-left) {
    width: 100%;
  }
</style>
