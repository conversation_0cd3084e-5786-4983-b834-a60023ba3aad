<script setup lang="ts">
  import type { BreadcrumbI } from '@/components/shared/BaseBreadcrumb.vue';
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import NotFoundItem from '@/components/shared/NotFoundItem.vue';
  import TopPageBreadcrumb from '@/components/shared/TopPageBreadcrumb.vue';
  import UiParentCard from '@/components/shared/UiParentCard.vue';
  import LoaderWrapper from '@/layouts/dashboard/LoaderWrapper.vue';
  import { useAuthStore, type User } from '@/stores/auth';
  import { Shortcut, useUsersStore } from '@/stores/users';
  import {
    emailRules,
    itemRequiredRule,
    lastRules,
    passwordRulesOptional
  } from '@/utils/formValidation';
  import { storeToRefs } from 'pinia';
  import { computed, onMounted, ref } from 'vue';
  import { VueDraggableNext } from 'vue-draggable-next';
  import { isAllowed } from '@/utils/directive/isAllowed';
  import LinkItem from './components/LinkItem.vue';
  import LinkModal from './components/LinkModal.vue';
  import PasswordChangeModal from './components/ChangePassword.vue';

  const ConfirmRef = ref<undefined | typeof ConfirmDlg>(undefined);

  const authStore = useAuthStore();
  const usersStore = useUsersStore();
  const { user: authUser } = storeToRefs(authStore);
  const { loading, userModalOptions, showLinkModal } = storeToRefs(usersStore);

  const baseDataLoaded = ref(false);
  onMounted(async () => {
    await checkPermision();
    await loadExecute();
  });

  const userDetail = ref<User | undefined>(undefined);
  const loadExecute = async () => {
    baseDataLoaded.value = false;
    if (!authUser.value) return;

    authStore.refreshUserData();
    userDetail.value = await usersStore.getUser(authUser.value.user_id);
    if (userDetail.value) {
      userDetail.value.init();
      await usersStore.getUserPermissions(userDetail.value);
      await usersStore.getUserRoles(userDetail.value);
      userModalOptions.value = {
        newUserData: {
          first_name: userDetail.value.first_name,
          last_name: userDetail.value.last_name,
          user_email: userDetail.value.user_email,
          roles: userDetail.value.roles?.map((role) => role.system_role_id) ?? [],
          permissions:
            userDetail.value.permissions?.map((permission) => permission.system_permission_id) ??
            [],
          status: userDetail.value.status,
          name_shortcut: userDetail.value.name_shortcut,
          confirm: false
        },
        isEditing: true,
        isCreating: false,
        user: userDetail.value
      };
    }
    baseDataLoaded.value = true;
  };

  const breadcrumbItems = computed<BreadcrumbI[]>(() => {
    return [
      {
        title: 'Nastavení',
        disabled: true,
        href: '#'
      }
    ];
  });

  const reloadPageWithoutSave = async () => {
    await authStore.refreshUserData();
    window.location.reload();
  };
  const show2 = ref(false);
  const password = ref<string>('');
  const CreateSyntheticTemplateForm = ref();
  async function submitFormToValidate() {
    if (CreateSyntheticTemplateForm.value.isValid && userDetail.value) {
      const res = await usersStore.updateUser();
      if (res) {
        authUser.value?.shortcuts?.forEach((shortcut) => {
          if (authUser.value) {
            usersStore.updateShortcut(authUser.value.user_id, shortcut.shortcut_id, {
              name: shortcut.name,
              url_path: shortcut.url_path,
              order: shortcut.order
            });
          }
        });
        if (password.value && password.value.length > 0 && authUser.value) {
          await usersStore.updateUserPassword(authUser.value.user_id, password.value);
        }
        authStore.refreshUserData();
        reloadPageWithoutSave();
      }
    }
  }

  const removeLink = async (shortcut_id: number) => {
    if (
      authUser.value &&
      (await ConfirmRef.value?.open('Potvrzení', 'Opravdu chcete smazat odkaz?', {
        color: 'error',
        notclosable: true,
        zIndex: 2400
      }))
    ) {
      const res = await usersStore.deleteShortcut(authUser.value.user_id, shortcut_id);
      if (res) {
        reloadPageWithoutSave();
      }
    }
  };

  const reorderOrderLinks = (shortcuts: Shortcut[]) => {
    let index = 1;
    shortcuts.forEach((shortcut) => {
      shortcut.order = index++;
    });
  };
  const showPasswordChange = ref(false);
  const toggleModal = async () => {
    showPasswordChange.value = !showPasswordChange.value;
  };
  const havePermisionCreate = ref<boolean>(false);
  const checkPermision = async () => {
    if (isAllowed(['add_edit_users'])) {
      havePermisionCreate.value = true;
    } else {
      havePermisionCreate.value = false;
    }
  };
</script>
<template>
  <LoaderWrapper v-if="!userDetail || !userModalOptions?.newUserData" />
  <template v-else>
    <TopPageBreadcrumb title="Nastavení" :_breadcrumbs="breadcrumbItems" />
    <v-row class="justify-content-end">
      <v-col cols="12">
        <UiParentCard class="pa-0" :loading="!baseDataLoaded || loading">
          <template #action>
            <v-row justify="space-between" class="align-center">
              <v-col cols="12" md="6">
                <div class="d-flex gap-2 justify-start flex-wrap">
                  <v-btn
                    variant="flat"
                    color="primary"
                    @click.prevent="usersStore.showNewShortcutModal"
                  >
                    Přidat odkaz
                  </v-btn>
                  <v-btn variant="flat" color="primary" @click.prevent="toggleModal">
                    Změnit heslo
                  </v-btn>
                </div>
              </v-col>

              <v-col cols="12" md="6">
                <div class="d-flex gap-2 justify-end flex-wrap">
                  <v-btn
                    variant="flat"
                    color="error"
                    :disabled="!baseDataLoaded"
                    @click.prevent="
                      async () => {
                        if (
                          await ConfirmRef?.open(
                            'Potvrzení',
                            'Opravdu chcete zrušit změny a načíst data ze serveru?',
                            {
                              color: 'error',
                              notclosable: true,
                              zIndex: 2400
                            }
                          )
                        ) {
                          reloadPageWithoutSave();
                        }
                      }
                    "
                  >
                    Obnovit stránku
                  </v-btn>
                  <v-btn
                    variant="flat"
                    color="primary"
                    :disabled="!baseDataLoaded || loading"
                    type="submit"
                    form="form-edit-form"
                  >
                    Uložit
                  </v-btn>
                </div>
              </v-col>
            </v-row>
          </template>

          <v-form
            id="form-edit-form"
            ref="CreateSyntheticTemplateForm"
            @submit.prevent="submitFormToValidate"
          >
            <v-row>
              <v-col cols="12">
                <section class="my-5">
                  <span class="text-h4">Upravit uživatelská data</span>
                </section>
              </v-col>
              <v-col cols="12" sm="6">
                <v-label class="mb-2">Jméno</v-label>
                <v-text-field
                  v-model="userModalOptions.newUserData.first_name"
                  :rules="itemRequiredRule"
                  single-line
                  placeholder="Zadejte jméno"
                  hide-details="auto"
                  variant="outlined"
                  required
                  rounded="sm"
                  :disabled="!havePermisionCreate"
                ></v-text-field>
              </v-col>
              <v-col cols="12" sm="6">
                <v-label class="mb-2">Příjmení</v-label>
                <v-text-field
                  v-model="userModalOptions.newUserData.last_name"
                  :rules="lastRules"
                  single-line
                  placeholder="Zadejte příjmení"
                  hide-details="auto"
                  variant="outlined"
                  rounded="sm"
                  :disabled="!havePermisionCreate"
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <v-label class="mb-2">Email</v-label>
                <v-text-field
                  v-model="userModalOptions.newUserData.user_email"
                  :rules="emailRules"
                  single-line
                  type="email"
                  hide-details="auto"
                  placeholder="Zadejte email uživatele"
                  required
                  variant="outlined"
                  rounded="sm"
                  :disabled="!havePermisionCreate"
                ></v-text-field>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="12">
                <section class="my-5"><span class="text-h4">Vlastní odkazy</span></section>
              </v-col>
            </v-row>
            <VueDraggableNext
              v-if="authUser && authUser.shortcuts && authUser.shortcuts.length > 0"
              v-model:list="authUser.shortcuts"
              tag="div"
              class="v-row"
              :disabled="loading"
              @change="reorderOrderLinks(authUser.shortcuts)"
            >
              <v-col v-for="shortcut in authUser.shortcuts" :key="shortcut.shortcut_id" cols="12">
                <LinkItem
                  :shortcut="shortcut"
                  :disabled="loading"
                  @remove="removeLink(shortcut.shortcut_id)"
                  @edit="usersStore.showEditShortcutModal(shortcut)"
                />
              </v-col>
            </VueDraggableNext>
            <NotFoundItem v-else>
              <template #notFound>Nebyly nalezeny žádné vlastní zkratky</template>
            </NotFoundItem>
          </v-form>
        </UiParentCard>
      </v-col>

      <ConfirmDlg ref="ConfirmRef" />
      <LinkModal
        v-model:show="usersStore.showLinkModal"
        @add-link="reloadPageWithoutSave"
        @update-link="reloadPageWithoutSave"
      />
      <PasswordChangeModal
        v-if="baseDataLoaded && showPasswordChange"
        v-model:show="showPasswordChange"
      />
    </v-row>
  </template>
</template>
