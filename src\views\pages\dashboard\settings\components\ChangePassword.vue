<template>
  <v-dialog v-model="showState" class="customer-modal" style="z-index: 1010">
    <v-card :loading="loading">
      <v-form
        id="form-password-edit"
        ref="ChangePasswordForm"
        @submit.prevent="submitFormToValidate"
      >
        <v-card-title class="pa-5">
          <span class="text-h5">Změna hesla</span>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col cols="12">
              <v-label class="mb-2">Nové Heslo</v-label>
              <v-text-field
                v-model="password"
                :append-icon="show2 ? 'mdi-eye' : 'mdi-eye-off'"
                :rules="passwordRules"
                single-line
                hide-details="auto"
                placeholder="Zadejte heslo uživatele"
                variant="outlined"
                rounded="sm"
                autocomplete="off"
                :autofill="false"
                :autocorrect="false"
                :spellcheck="false"
                :type="show2 ? 'text' : 'password'"
                :required="true"
                @click:append="show2 = !show2"
              ></v-text-field>
            </v-col>
            <v-col cols="12">
              <v-label class="mb-2">Potvrzení hesla</v-label>
              <v-text-field
                v-model="passwordConfirm"
                single-line
                hide-details="auto"
                placeholder="Zadejte heslo uživatele"
                variant="outlined"
                rounded="sm"
                autocomplete="off"
                :autofill="false"
                :autocorrect="false"
                :spellcheck="false"
                type="password"
                :required="true"
                :rules="confirmPasswordRules"
                @paste.prevent
                @copy.prevent
              ></v-text-field>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" variant="text" @click="showState = false">Zavřít</v-btn>
          <v-btn color="primary" variant="flat" type="submit" :loading="loading">Uložit</v-btn>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
  <ConfirmDlg ref="ConfirmRef" />
</template>
<script setup lang="ts">
  import ConfirmDlg from '@/components/shared/ConfirmDlg.vue';
  import { useAuthStore } from '@/stores/auth';
  import { useUsersStore } from '@/stores/users';
  import { passwordRules } from '@/utils/formValidation';
  import { storeToRefs } from 'pinia';
  import { ref } from 'vue';
  import { useVModel } from '@vueuse/core';

  const ConfirmRef = ref<undefined | typeof ConfirmDlg>(undefined);

  const emits = defineEmits(['update:show', 'updateMethod']);
  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    }
  });
  const showState = useVModel(props, 'show');
  const usersStore = useUsersStore();

  const authStore = useAuthStore();
  const { user: authUser } = storeToRefs(authStore);
  const { loading } = storeToRefs(usersStore);
  const show2 = ref(false);
  const password = ref<string>('');
  const passwordConfirm = ref<string>('');
  const ChangePasswordForm = ref();
  async function submitFormToValidate() {
    if (ChangePasswordForm.value.isValid) {
      if (
        password.value &&
        passwordConfirm.value &&
        passwordConfirm.value === password.value &&
        authUser.value
      ) {
        if (
          await ConfirmRef.value?.open('Opravdu chcete změnit heslo?', 'Takto akce je nevratná', {
            color: 'error',
            notclosable: true,
            zIndex: 2400
          })
        ) {
          const res = await usersStore.updateUserPassword(authUser.value.user_id, password.value);
          if (res) {
            authStore.refreshUserData();
            showState.value = false;
          }
        }
      }
    }
  }

  const confirmPasswordRules = [
    (v: string) => !!v || 'Potvrzení hesla je povinné',
    (v: string) => v === password.value || 'Hesla se neshodují'
  ];
</script>
