<template>
  <v-card
    variant="outlined"
    color="primary"
    class="card-hover-border bg-containerBg"
    :title="shortcut.name + ' (Pořadí: ' + shortcut.order + ')'"
    :subtitle="'Odkaz: ' + shortcut.url_path"
  >
    <template #prepend>
      <DragOutlined :style="{ fontSize: '28px' }" />
    </template>
    <template #append>
      <v-btn
        :disabled="disabled ?? false"
        variant="text"
        color="primary"
        @click.prevent.stop="$emit('edit', shortcut.order)"
      >
        <v-icon color="primary">mdi-pencil</v-icon>
      </v-btn>
      <v-btn
        :disabled="disabled ?? false"
        variant="text"
        color="error"
        @click.prevent.stop="$emit('remove', shortcut.order)"
      >
        <v-icon color="error">mdi-delete</v-icon>
      </v-btn>
    </template>
  </v-card>
</template>
<script lang="ts" setup>
  import type { NewShortcutDateI } from '@/stores/users';
  import { DragOutlined } from '@ant-design/icons-vue';

  defineEmits(['remove', 'edit']);
  defineProps<{
    shortcut: NewShortcutDateI;
    disabled?: boolean;
  }>();
</script>
