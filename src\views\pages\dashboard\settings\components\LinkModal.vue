<script setup lang="ts">
  import { useAuthStore } from '@/stores/auth';
  import { useUsersStore } from '@/stores/users';
  import { itemRequiredRule } from '@/utils/formValidation';
  import { useVModel } from '@vueuse/core';
  import { storeToRefs } from 'pinia';
  import { computed, ref } from 'vue';

  const emit = defineEmits(['update:show', 'addLink', 'updateLink']);

  const props = defineProps({
    show: {
      type: Boolean,
      required: true
    }
  });

  // stores
  const authStore = useAuthStore();
  const usersStore = useUsersStore();
  const { user: authUser } = storeToRefs(authStore);
  const { loading, linkModalOptions } = storeToRefs(usersStore);

  const CreateInstrumentForm = ref();
  async function submitFormToValidate() {
    if (
      CreateInstrumentForm.value.isValid &&
      linkModalOptions.value?.newShortcutData &&
      authUser.value
    ) {
      switch (true) {
        case linkModalOptions.value.isEditing &&
          !linkModalOptions.value.isCreating &&
          linkModalOptions.value.shortcut !== undefined:
          return usersStore
            .updateShortcut(authUser.value.user_id, linkModalOptions.value.shortcut.shortcut_id, {
              name: linkModalOptions.value.newShortcutData.name,
              url_path: linkModalOptions.value.newShortcutData.url_path,
              order: linkModalOptions.value.newShortcutData.order
            })
            .then((res) => {
              if (res) {
                showState.value = false;
                emit('updateLink');
              }
            });
        case !linkModalOptions.value.isEditing && linkModalOptions.value.isCreating:
          return usersStore
            .createShortcut(authUser.value.user_id, {
              name: linkModalOptions.value.newShortcutData.name,
              url_path: linkModalOptions.value.newShortcutData.url_path,
              order: getLatestOrder()
            })
            .then((res) => {
              if (res) {
                showState.value = false;
                emit('addLink');
              }
            });

        default:
          return 'Náhled odkazu';
      }
    }
  }

  const getLatestOrder = () => {
    let order = 0;
    if (authUser.value?.shortcuts) {
      authUser.value?.shortcuts.forEach((shortcut) => {
        order = Math.max(order, shortcut.order);
      });
    } else {
      order = 1;
    }
    return order;
  };

  const showState = useVModel(props, 'show');
  const showTitle = computed(() => {
    if (linkModalOptions.value === undefined) {
      return 'Náhled odkazu';
    }

    switch (true) {
      case linkModalOptions.value.isEditing && !linkModalOptions.value.isCreating:
        return 'Editace odkazu';
      case !linkModalOptions.value.isEditing && linkModalOptions.value.isCreating:
        return 'Nový odkaz';
      default:
        return 'Náhled odkazu';
    }
  });
  const showSuccessButtonTitle = computed(() => {
    if (linkModalOptions.value === undefined) {
      return false;
    }

    switch (true) {
      case linkModalOptions.value.isEditing && !linkModalOptions.value.isCreating:
        return 'Upravit odkaz';
      case !linkModalOptions.value.isEditing && linkModalOptions.value.isCreating:
        return 'Přidat odkaz';
      default:
        return false;
    }
  });

  const onlyPreview = computed(() => {
    return (
      linkModalOptions.value?.isCreating === false && linkModalOptions.value?.isEditing === false
    );
  });
</script>
<template>
  <v-dialog v-model="showState" class="customer-modal" :z-index="1010">
    <v-card :loading="loading">
      <v-form
        v-if="linkModalOptions?.newShortcutData"
        ref="CreateInstrumentForm"
        class="createInstrumentForm"
        :readonly="onlyPreview"
        @submit.prevent="submitFormToValidate"
      >
        <v-card-title class="pa-5">
          <span class="text-h5">
            {{ showTitle }}
          </span>
        </v-card-title>
        <v-divider></v-divider>
        <v-card-text>
          <v-row>
            <v-col>
              <v-row>
                <v-col>
                  <v-label class="mb-2">Název odkazu</v-label>
                  <v-text-field
                    v-model="linkModalOptions.newShortcutData.name"
                    :rules="itemRequiredRule"
                    single-line
                    placeholder="Zadejte název odkazu"
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></v-text-field>
                </v-col>
                <v-col>
                  <v-label class="mb-2">URL odkazu</v-label>
                  <v-text-field
                    v-model="linkModalOptions.newShortcutData.url_path"
                    :rules="itemRequiredRule"
                    single-line
                    placeholder="Zadejte URL"
                    hide-details="auto"
                    variant="outlined"
                    rounded="sm"
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="error" variant="text" @click="usersStore.resetShortcutModalData()">
            Zrušit
          </v-btn>
          <v-btn
            v-if="showSuccessButtonTitle"
            color="primary"
            variant="flat"
            type="submit"
            :loading="loading"
          >
            {{ showSuccessButtonTitle }}
          </v-btn>
        </v-card-actions>
      </v-form>
    </v-card>
  </v-dialog>
</template>
