<template>
  <div class="pa-4">
    <h2>Radio Group WebSocket Test</h2>
    
    <v-card class="mb-4 pa-4">
      <h3>Connection Status</h3>
      <v-chip :color="isConnected ? 'success' : 'error'" variant="tonal">
        {{ isConnected ? 'Connected' : 'Disconnected' }}
      </v-chip>
    </v-card>

    <v-card class="mb-4 pa-4">
      <h3>Test Radio Groups</h3>
      
      <v-row>
        <v-col cols="12" md="6">
          <v-label class="mb-2">Test Field 1 (impact_on_quality)</v-label>
          <v-radio-group
            v-model="testData.impact_on_quality"
            single-line
            hide-details="auto"
            variant="outlined"
            rounded="sm"
            inline
            @update:model-value="impactOnQualityRadio.onRadioChange"
          >
            <v-radio label="Yes" :value="true"></v-radio>
            <v-radio label="No" :value="false"></v-radio>
          </v-radio-group>
          <p class="text-caption mt-2">
            Current value: {{ testData.impact_on_quality }}
          </p>
        </v-col>

        <v-col cols="12" md="6">
          <v-label class="mb-2">Test Field 2 (impact_on_yield)</v-label>
          <v-radio-group
            v-model="testData.impact_on_yield"
            single-line
            hide-details="auto"
            variant="outlined"
            rounded="sm"
            inline
            @update:model-value="impactOnYieldRadio.onRadioChange"
          >
            <v-radio label="Yes" :value="true"></v-radio>
            <v-radio label="No" :value="false"></v-radio>
          </v-radio-group>
          <p class="text-caption mt-2">
            Current value: {{ testData.impact_on_yield }}
          </p>
        </v-col>
      </v-row>
    </v-card>

    <v-card class="mb-4 pa-4">
      <h3>Debug Information</h3>
      <pre>{{ debugInfo }}</pre>
    </v-card>

    <v-card class="pa-4">
      <h3>Instructions</h3>
      <ol>
        <li>Open this page in multiple browser tabs/windows</li>
        <li>Select different radio button values in each tab</li>
        <li>Observe real-time synchronization between tabs</li>
        <li>Check browser console for WebSocket messages</li>
      </ol>
    </v-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRadioGroupWebSocket } from '@/composables/useRadioGroupWebSocket';
import { useFormWebSocket } from '@/composables/useFormWebSocket';

// Test data
const testData = ref({
  impact_on_quality: null as boolean | null,
  impact_on_yield: null as boolean | null
});

// Mock form ID for testing
const formId = computed(() => 999); // Use a test form ID

// WebSocket setup
const webSocket = useFormWebSocket({
  formId,
  autoConnect: true,
  enableNotifications: true
});

// Radio group WebSocket setup
const impactOnQualityRadio = useRadioGroupWebSocket({
  fieldName: 'impact_on_quality',
  enabled: true,
  disabled: false
});

const impactOnYieldRadio = useRadioGroupWebSocket({
  fieldName: 'impact_on_yield',
  enabled: true,
  disabled: false
});

// Set up remote update watchers
const setupRadioGroupHandlers = () => {
  impactOnQualityRadio.watchRemoteUpdates(
    computed({
      get: () => testData.value.impact_on_quality,
      set: (value) => {
        testData.value.impact_on_quality = value;
      }
    })
  );

  impactOnYieldRadio.watchRemoteUpdates(
    computed({
      get: () => testData.value.impact_on_yield,
      set: (value) => {
        testData.value.impact_on_yield = value;
      }
    })
  );
};

// Connection status
const isConnected = computed(() => webSocket.isConnected());

// Debug information
const debugInfo = computed(() => ({
  isConnected: isConnected.value,
  testData: testData.value,
  activeUsers: webSocket.activeUsers(),
  connectionStatus: webSocket.connectionStatus()
}));

onMounted(() => {
  setupRadioGroupHandlers();
  console.log('🧪 Radio Group WebSocket Test initialized');
});
</script>

<style scoped>
pre {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
}
</style>
